<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Routing Issue</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.css" />
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        #map { height: 400px; width: 100%; margin: 20px 0; }
        .controls { margin: 20px 0; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn:disabled { opacity: 0.5; cursor: not-allowed; }
        .debug { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
    </style>
</head>
<body>
    <h1>🐛 Debug Routing Issue</h1>
    
    <div class="controls">
        <button onclick="getUserLocation()" id="get-location" class="btn btn-success">Get My Location</button>
        <button onclick="testBasicRouting()" id="test-routing" class="btn btn-primary" disabled>Test Basic Routing</button>
        <button onclick="clearRoute()" id="clear-route" class="btn btn-secondary" disabled>Clear Route</button>
    </div>
    
    <div id="map"></div>
    
    <div class="debug">
        <h3>Debug Information</h3>
        <div id="debug-info">
            <p>Click "Get My Location" to start debugging...</p>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.js"></script>
    <script>
        let map;
        let userLocation = null;
        let userMarker = null;
        let destinationMarker = null;
        let routingControl = null;
        
        // Test destination (Cebu City Hall)
        const testDestination = {
            lat: 10.293,
            lng: 123.902,
            name: "Cebu City Hall"
        };
        
        function log(message, type = 'info') {
            const debugDiv = document.getElementById('debug-info');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            debugDiv.innerHTML += `<p class="${className}">[${timestamp}] ${message}</p>`;
            console.log(`[${timestamp}] ${message}`);
        }
        
        // Initialize map
        function initMap() {
            log('Initializing map...');
            map = L.map('map').setView([10.3157, 123.8854], 13);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                maxZoom: 19,
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);
            
            // Add destination marker
            destinationMarker = L.marker([testDestination.lat, testDestination.lng])
                .addTo(map)
                .bindPopup(`<strong>${testDestination.name}</strong><br>Test destination for routing`);
            
            log('Map initialized successfully', 'success');
        }
        
        // Get user location
        function getUserLocation() {
            log('Requesting user location...');
            
            if (!navigator.geolocation) {
                log('Geolocation is not supported by this browser.', 'error');
                return;
            }
            
            const btn = document.getElementById('get-location');
            btn.disabled = true;
            btn.textContent = 'Getting Location...';
            
            navigator.geolocation.getCurrentPosition(
                function(position) {
                    userLocation = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    };
                    
                    log(`User location obtained: ${userLocation.lat}, ${userLocation.lng}`, 'success');
                    
                    // Add user marker
                    if (userMarker) {
                        map.removeLayer(userMarker);
                    }
                    
                    userMarker = L.marker([userLocation.lat, userLocation.lng], {
                        icon: L.divIcon({
                            className: 'user-location-marker',
                            html: '<div style="width: 20px; height: 20px; background: #007bff; border: 3px solid white; border-radius: 50%; box-shadow: 0 2px 6px rgba(0,0,0,0.3);"></div>',
                            iconSize: [20, 20],
                            iconAnchor: [10, 10]
                        })
                    }).addTo(map).bindPopup('<strong>Your Location</strong>');
                    
                    // Center map to show both user location and destination
                    const group = new L.featureGroup([userMarker, destinationMarker]);
                    map.fitBounds(group.getBounds().pad(0.1));
                    
                    // Enable test routing button
                    document.getElementById('test-routing').disabled = false;
                    
                    btn.disabled = false;
                    btn.textContent = 'Location Found ✓';
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-primary');
                },
                function(error) {
                    log(`Error getting location: ${error.message}`, 'error');
                    btn.disabled = false;
                    btn.textContent = 'Get My Location';
                }
            );
        }
        
        // Test basic routing
        function testBasicRouting() {
            if (!userLocation) {
                log('User location not available', 'error');
                return;
            }
            
            log('Creating routing control...');
            
            if (routingControl) {
                clearRoute();
            }
            
            try {
                routingControl = L.Routing.control({
                    waypoints: [
                        L.latLng(userLocation.lat, userLocation.lng),
                        L.latLng(testDestination.lat, testDestination.lng)
                    ],
                    routeWhileDragging: false,
                    showAlternatives: false,
                    lineOptions: {
                        styles: [{
                            color: '#007bff',
                            opacity: 0.8,
                            weight: 5
                        }]
                    },
                    createMarker: function() { return null; }
                }).addTo(map);
                
                log('Routing control created, waiting for route calculation...');
                
                routingControl.on('routesfound', function(e) {
                    log('Route found successfully!', 'success');
                    const route = e.routes[0];
                    const summary = route.summary;
                    
                    const travelTimeMinutes = Math.round(summary.totalTime / 60);
                    const distanceKm = (summary.totalDistance / 1000).toFixed(1);
                    
                    log(`Route details: ${distanceKm} km, ${travelTimeMinutes} minutes`, 'success');
                    
                    document.getElementById('clear-route').disabled = false;
                    document.getElementById('test-routing').disabled = true;
                });
                
                routingControl.on('routingerror', function(e) {
                    log(`Routing error: ${e.error.message || 'Unknown error'}`, 'error');
                });
                
            } catch (error) {
                log(`Error creating routing control: ${error.message}`, 'error');
            }
        }
        
        // Clear route
        function clearRoute() {
            if (routingControl) {
                log('Clearing route...');
                map.removeControl(routingControl);
                routingControl = null;
                document.getElementById('clear-route').disabled = true;
                document.getElementById('test-routing').disabled = false;
                log('Route cleared', 'success');
            }
        }
        
        // Initialize map when page loads
        window.onload = function() {
            initMap();
        };
    </script>
</body>
</html>
