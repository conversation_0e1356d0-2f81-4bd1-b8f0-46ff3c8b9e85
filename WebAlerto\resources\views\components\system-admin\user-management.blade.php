@extends('layout.app')

@section('title', 'User Management - Technical Administrator')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-gray-50 to-slate-100 py-8">
    <div class="max-w-[1920px] mx-auto px-6 sm:px-8 lg:px-12">
        
        <!-- Header Section -->
        <div class="bg-white/90 backdrop-blur-sm rounded-xl shadow-xl border border-slate-200 p-6 mb-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div class="p-3 bg-gradient-to-br from-slate-600 to-gray-700 rounded-lg shadow-lg">
                        <i class="fas fa-users-cog text-white text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">User Management</h1>
                        <p class="text-sm text-gray-600 mt-1">Manage all system users and their access levels</p>
                    </div>
                </div>
                
                <button onclick="openCreateUserModal()" class="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-user-plus"></i>
                    <span>Create New User</span>
                </button>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="bg-white/90 backdrop-blur-sm rounded-xl shadow-xl border border-slate-200 p-6 mb-6">
            <form method="GET" action="{{ route('system-admin.user-management') }}" class="flex flex-wrap gap-4 items-end">
                <div class="flex-1 min-w-[200px]">
                    <label for="city" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-city mr-1"></i>Filter by City
                    </label>
                    <select name="city" id="city" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" onchange="this.form.submit()">
                        <option value="">All Cities</option>
                        @foreach($cities as $city)
                            <option value="{{ $city }}" {{ $selectedCity == $city ? 'selected' : '' }}>
                                {{ $city }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="flex-1 min-w-[200px]">
                    <label for="barangay" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-map-marker-alt mr-1"></i>Filter by Barangay
                    </label>
                    <select name="barangay" id="barangay" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" onchange="this.form.submit()">
                        <option value="">All Barangays</option>
                        @foreach($barangays as $barangay)
                            <option value="{{ $barangay }}" {{ $selectedBarangay == $barangay ? 'selected' : '' }}>
                                {{ $barangay }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="flex-1 min-w-[200px]">
                    <label for="role" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user-tag mr-1"></i>Filter by Role
                    </label>
                    <select name="role" id="role" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" onchange="this.form.submit()">
                        <option value="">All Roles</option>
                        <option value="system_admin" {{ $selectedRole == 'system_admin' ? 'selected' : '' }}>Technical Administrator</option>
                        <option value="super_admin" {{ $selectedRole == 'super_admin' ? 'selected' : '' }}>CDRRMC Users</option>
                        <option value="admin" {{ $selectedRole == 'admin' ? 'selected' : '' }}>BDRRMC Users</option>
                    </select>
                </div>
            </form>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Users</p>
                        <p class="text-3xl font-bold text-gray-900">{{ $users->total() }}</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Technical Admins</p>
                        <p class="text-3xl font-bold text-slate-600">{{ $users->where('role', 'system_admin')->count() }}</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-slate-500 to-slate-600 rounded-xl shadow-lg">
                        <i class="fas fa-cogs text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">CDRRMC Users</p>
                        <p class="text-3xl font-bold text-blue-600">{{ $users->where('role', 'super_admin')->count() }}</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
                        <i class="fas fa-city text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">BDRRMC Users</p>
                        <p class="text-3xl font-bold text-green-600">{{ $users->where('role', 'admin')->count() }}</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                        <i class="fas fa-map-marked-alt text-white text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users Table -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200 p-6">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h3 class="text-lg font-bold text-gray-900">All System Users</h3>
                    <p class="text-sm text-gray-600">Manage user accounts and access levels</p>
                </div>
                
                <!-- Search and Filter -->
                <div class="flex items-center gap-4">
                    <div class="relative">
                        <input type="text" placeholder="Search users..." class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                    
                    <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">All Roles</option>
                        <option value="system_admin">Technical Administrator</option>
                        <option value="super_admin">CDRRMC</option>
                        <option value="admin">BDRRMC</option>
                    </select>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="border-b border-gray-200">
                            <th class="text-left py-3 px-4 font-medium text-gray-700">User</th>
                            <th class="text-left py-3 px-4 font-medium text-gray-700">Role</th>
                            <th class="text-left py-3 px-4 font-medium text-gray-700">Position</th>
                            <th class="text-left py-3 px-4 font-medium text-gray-700">Location</th>
                            <th class="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                            <th class="text-left py-3 px-4 font-medium text-gray-700">Requests</th>
                            <th class="text-left py-3 px-4 font-medium text-gray-700">Created</th>
                            <th class="text-left py-3 px-4 font-medium text-gray-700">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($users as $user)
                        <tr class="border-b border-gray-100 hover:bg-gray-50">
                            <td class="py-3 px-4">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                                        {{ strtoupper(substr($user->first_name, 0, 1) . substr($user->last_name, 0, 1)) }}
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900">
                                            {{ $user->first_name }} {{ $user->middle_name ? $user->middle_name . ' ' : '' }}{{ $user->last_name }}
                                        </p>
                                        <p class="text-sm text-gray-600">{{ $user->email }}</p>
                                    </div>
                                </div>
                            </td>
                            <td class="py-3 px-4">
                                <span class="px-2 py-1 rounded-full text-xs font-medium
                                    @if($user->role === 'system_admin') bg-slate-100 text-slate-800
                                    @elseif($user->role === 'super_admin') bg-blue-100 text-blue-800
                                    @else bg-green-100 text-green-800 @endif">
                                    {{ $user->getRoleDisplayName() }}
                                </span>
                            </td>
                            <td class="py-3 px-4 text-sm text-gray-600">{{ $user->position }}</td>
                            <td class="py-3 px-4 text-sm text-gray-600">
                                @if($user->city)
                                    <div>{{ $user->city_name }}</div>
                                    @if($user->barangay)
                                        <div class="text-xs text-gray-500">{{ $user->barangay }}</div>
                                    @endif
                                @else
                                    <span class="text-gray-400">N/A</span>
                                @endif
                            </td>
                            <td class="py-3 px-4">
                                <span class="px-2 py-1 rounded-full text-xs font-medium
                                    @if($user->status === 'Active') bg-green-100 text-green-800
                                    @else bg-red-100 text-red-800 @endif">
                                    {{ $user->status }}
                                </span>
                            </td>
                            <td class="py-3 px-4">
                                @if($user->managementRequests->count() > 0)
                                    <span class="px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                        {{ $user->managementRequests->count() }} requests
                                    </span>
                                @else
                                    <span class="text-gray-400 text-xs">No requests</span>
                                @endif
                            </td>
                            <td class="py-3 px-4 text-sm text-gray-600">{{ $user->created_at->format('M d, Y') }}</td>
                            <td class="py-3 px-4">
                                <div class="flex items-center gap-2">
                                    <button onclick="viewUser({{ $user->id }})" class="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    @if($user->id !== auth()->id())
                                    <button onclick="toggleUserStatus({{ $user->id }}, '{{ $user->status }}')" class="p-2 text-orange-600 hover:bg-orange-50 rounded-lg transition-colors" title="{{ $user->status === 'Active' ? 'Deactivate' : 'Activate' }} User">
                                        <i class="fas fa-{{ $user->status === 'Active' ? 'pause' : 'play' }}"></i>
                                    </button>
                                    <button onclick="deleteUser({{ $user->id }}, '{{ $user->first_name }} {{ $user->last_name }}')" class="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors" title="Delete User">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="py-8 text-center text-gray-500">
                                <i class="fas fa-users text-4xl mb-4"></i>
                                <p>No users found</p>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            @if($users->hasPages())
            <div class="mt-6 flex justify-center">
                {{ $users->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Create User Modal (reuse from dashboard) -->
<div id="createUserModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-white rounded-xl shadow-2xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div class="p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-bold text-gray-900">Create New User</h3>
                <button onclick="closeCreateUserModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="createUserForm" onsubmit="createUser(event)">
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">First Name <span class="text-red-500">*</span></label>
                            <input type="text" name="first_name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Enter first name">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Last Name <span class="text-red-500">*</span></label>
                            <input type="text" name="last_name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Enter last name">
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Middle Name</label>
                        <input type="text" name="middle_name" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Enter middle name (optional)">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email Address <span class="text-red-500">*</span></label>
                        <input type="email" name="email" required onblur="validateEmail(this)" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Enter email address">
                        <div id="emailValidation" class="mt-1 text-sm hidden"></div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Role <span class="text-red-500">*</span></label>
                        <select name="role" required onchange="handleRoleChange(this.value);" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Select Role</option>
                            <option value="system_admin">Technical Administrator</option>
                            <option value="super_admin">CDRRMC (City-Level)</option>
                            <option value="admin">BDRRMC (Barangay-Level)</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Position <span class="text-red-500">*</span></label>
                        <select name="position" required onchange="handlePositionChange()" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Select Position</option>
                            <!-- Options will be populated by JavaScript based on role -->
                        </select>
                        <div id="customPositionField" class="mt-2 hidden">
                            <input type="text" id="customPosition" placeholder="Enter custom position" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                    </div>

                    <div id="cityField" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-1">City <span class="text-red-500">*</span></label>
                        <select id="modalCitySelect" name="city" onchange="loadBarangays()" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Select City</option>
                        </select>
                    </div>

                    <div id="barangayField" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Barangay <span class="text-red-500">*</span></label>
                        <select name="barangay" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Select Barangay</option>
                        </select>
                    </div>

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <div class="flex items-start">
                            <i class="fas fa-info-circle text-blue-500 mt-0.5 mr-2"></i>
                            <div class="text-sm text-blue-700">
                                <p class="font-medium">Password Information</p>
                                <p>A temporary password will be automatically generated and sent to the user's email address upon successful registration.</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex gap-3 mt-6">
                    <button type="button" onclick="closeCreateUserModal()" class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        Cancel
                    </button>
                    <button type="submit" class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        Create User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- User Details Modal -->
<div id="userDetailsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-white rounded-xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div class="p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-gray-900">User Account Details</h3>
                <button onclick="closeUserDetailsModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <div id="userDetailsContent">
                <!-- User details will be populated here -->
            </div>
        </div>
    </div>
</div>

<script>
// Include the same JavaScript functions from the dashboard
function openCreateUserModal() {
    document.getElementById('createUserModal').classList.remove('hidden');
}

function closeCreateUserModal() {
    document.getElementById('createUserModal').classList.add('hidden');
    document.getElementById('createUserForm').reset();
    document.getElementById('cityField').classList.add('hidden');
    document.getElementById('barangayField').classList.add('hidden');
}

function handleRoleChange(role) {
    // Update location fields
    toggleLocationFields(role);
    // Update position options
    updatePositionOptions(role);
}

function toggleLocationFields(role = null) {
    const roleSelect = document.querySelector('select[name="role"]');
    const selectedRole = role || roleSelect.value;
    const cityField = document.getElementById('cityField');
    const barangayField = document.getElementById('barangayField');
    const citySelect = document.getElementById('modalCitySelect');
    const barangaySelect = document.querySelector('#createUserModal select[name="barangay"]');

    if (selectedRole === 'super_admin' || selectedRole === 'admin') {
        cityField.classList.remove('hidden');
        citySelect.required = true;
        loadCities();

        if (selectedRole === 'admin') {
            barangayField.classList.remove('hidden');
            barangaySelect.required = true;
        } else {
            barangayField.classList.add('hidden');
            barangaySelect.required = false;
            barangaySelect.value = '';
        }
    } else {
        cityField.classList.add('hidden');
        barangayField.classList.add('hidden');
        citySelect.required = false;
        barangaySelect.required = false;
        citySelect.value = '';
        barangaySelect.value = '';
    }
}

function updatePositionOptions(role) {
    const positionSelect = document.querySelector('select[name="position"]');

    // Clear existing options except the first one
    positionSelect.innerHTML = '<option value="">Select Position</option>';

    if (!role) {
        return;
    }

    console.log('Fetching positions for role:', role);

    // Show loading state
    const loadingOption = document.createElement('option');
    loadingOption.value = '';
    loadingOption.textContent = 'Loading positions...';
    loadingOption.disabled = true;
    positionSelect.appendChild(loadingOption);

    // Fetch positions from backend API
    fetch(`/system-admin/positions-for-role?role=${role}`, {
        method: 'GET',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log('Received positions data:', data);

        // Clear loading state
        positionSelect.innerHTML = '<option value="">Select Position</option>';

        if (data.success && data.categories) {
            // Add positions organized by categories
            Object.entries(data.categories).forEach(([categoryName, positions]) => {
                const optgroup = document.createElement('optgroup');
                optgroup.label = categoryName;

                positions.forEach(position => {
                    const option = document.createElement('option');
                    option.value = position;
                    option.textContent = position;
                    optgroup.appendChild(option);
                });

                positionSelect.appendChild(optgroup);
            });

            // Add "Other" option
            const otherOption = document.createElement('option');
            otherOption.value = 'Other';
            otherOption.textContent = 'Other (Please specify)';
            positionSelect.appendChild(otherOption);
        } else {
            console.error('Failed to load positions:', data.message || 'Unknown error');
            // Fallback to basic positions
            loadFallbackPositions(role, positionSelect);
        }
    })
    .catch(error => {
        console.error('Error fetching positions:', error);
        // Fallback to basic positions
        loadFallbackPositions(role, positionSelect);
    });
}

function loadFallbackPositions(role, positionSelect) {
    console.log('Loading fallback positions for role:', role);

    // Clear and reset
    positionSelect.innerHTML = '<option value="">Select Position</option>';

    const basicPositions = getBasicPositionsForRole(role);

    if (basicPositions.length > 0) {
        basicPositions.forEach(position => {
            const option = document.createElement('option');
            option.value = position;
            option.textContent = position;
            positionSelect.appendChild(option);
        });

        // Add "Other" option
        const otherOption = document.createElement('option');
        otherOption.value = 'Other';
        otherOption.textContent = 'Other (Please specify)';
        positionSelect.appendChild(otherOption);
    }
}

function getBasicPositionsForRole(role) {
    // Basic fallback positions that match the backend service
    const basicPositions = {
        'system_admin': [
            'Technical Administrator',
            'System Administrator',
            'IT Manager',
            'Database Administrator',
            'System Analyst'
        ],
        'super_admin': [
            'CDRRMC Chairperson',
            'CDRRMC Vice-Chairperson',
            'CDRRMC Executive Director',
            'City Mayor',
            'Vice Mayor',
            'City Administrator',
            'City Health Officer',
            'City Engineer',
            'Fire Chief',
            'Police Chief',
            'CDRRMC Operations Manager',
            'CDRRMC Planning Officer',
            'Disaster Risk Reduction Officer',
            'Emergency Response Coordinator',
            'Administrative Assistant'
        ],
        'admin': [
            'BDRRMC Chairperson',
            'BDRRMC Vice-Chairperson',
            'Barangay Captain',
            'Barangay Kagawad',
            'BDRRMC Secretary',
            'BDRRMC Treasurer',
            'Emergency Response Team Leader',
            'Search and Rescue Team Leader',
            'Medical Response Team Leader',
            'Barangay Health Worker',
            'Barangay Tanod',
            'Disaster Risk Reduction Officer',
            'Emergency Response Officer',
            'Administrative Assistant',
            'Community Volunteer'
        ]
    };

    return basicPositions[role] || [];
}

function getPositionsForRole(role) {
    const positionData = {
        'system_admin': [
            {
                label: 'Technical Administration',
                positions: [
                    'Technical Administrator',
                    'System Administrator',
                    'IT Manager',
                    'Database Administrator',
                    'System Analyst'
                ]
            }
        ],
        'super_admin': [
            {
                label: 'Executive Level',
                positions: [
                    'CDRRMC Chairperson',
                    'CDRRMC Vice-Chairperson',
                    'CDRRMC Executive Director',
                    'City Mayor',
                    'Vice Mayor',
                    'City Administrator'
                ]
            },
            {
                label: 'Department Heads',
                positions: [
                    'City Planning and Development Coordinator',
                    'City Health Officer',
                    'City Engineer',
                    'City Social Welfare and Development Officer',
                    'City Environment and Natural Resources Officer',
                    'City Agriculture Officer',
                    'City Budget Officer',
                    'City Accountant',
                    'City Treasurer',
                    'City Legal Officer',
                    'City Information Officer'
                ]
            },
            {
                label: 'Emergency Services',
                positions: [
                    'Fire Chief',
                    'Police Chief',
                    'Emergency Medical Services Director',
                    'Rescue Operations Chief'
                ]
            },
            {
                label: 'CDRRMC Staff',
                positions: [
                    'CDRRMC Operations Manager',
                    'CDRRMC Planning Officer',
                    'CDRRMC Information Officer',
                    'CDRRMC Training Officer',
                    'CDRRMC Logistics Officer',
                    'CDRRMC Communications Officer',
                    'CDRRMC Monitoring and Evaluation Officer',
                    'CDRRMC Administrative Officer',
                    'CDRRMC Finance Officer'
                ]
            },
            {
                label: 'Technical Staff',
                positions: [
                    'Senior Disaster Risk Reduction Officer',
                    'Disaster Risk Reduction Officer',
                    'Emergency Response Coordinator',
                    'Risk Assessment Specialist',
                    'Early Warning Systems Specialist',
                    'GIS Specialist',
                    'Data Analyst',
                    'Research Officer'
                ]
            },
            {
                label: 'Support Staff',
                positions: [
                    'Administrative Assistant',
                    'Executive Secretary',
                    'Records Officer',
                    'IT Support Specialist'
                ]
            }
        ],
        'admin': [
            {
                label: 'Executive Level',
                positions: [
                    'BDRRMC Chairperson',
                    'BDRRMC Vice-Chairperson',
                    'Barangay Captain',
                    'Barangay Kagawad'
                ]
            },
            {
                label: 'BDRRMC Officers',
                positions: [
                    'BDRRMC Secretary',
                    'BDRRMC Treasurer',
                    'BDRRMC Operations Officer',
                    'BDRRMC Planning Officer',
                    'BDRRMC Information Officer',
                    'BDRRMC Training Officer',
                    'BDRRMC Logistics Officer',
                    'BDRRMC Communications Officer'
                ]
            },
            {
                label: 'Emergency Response Teams',
                positions: [
                    'Emergency Response Team Leader',
                    'Search and Rescue Team Leader',
                    'Medical Response Team Leader',
                    'Evacuation Team Leader',
                    'Fire Response Team Leader',
                    'Traffic Management Team Leader',
                    'Security Team Leader'
                ]
            },
            {
                label: 'Specialized Roles',
                positions: [
                    'Barangay Health Worker',
                    'Barangay Nutrition Scholar',
                    'Barangay Tanod',
                    'Barangay Environmental Officer',
                    'Barangay Social Worker',
                    'Barangay Youth Leader',
                    'Senior Citizen Representative',
                    'Women\'s Representative',
                    'PWD Representative'
                ]
            },
            {
                label: 'Technical Staff',
                positions: [
                    'Disaster Risk Reduction Officer',
                    'Emergency Response Officer',
                    'Community Preparedness Officer',
                    'Risk Assessment Officer',
                    'Early Warning Officer',
                    'Damage Assessment Officer',
                    'Relief Operations Officer',
                    'Rehabilitation Officer'
                ]
            },
            {
                label: 'Support Staff',
                positions: [
                    'Administrative Assistant',
                    'Records Keeper',
                    'Data Encoder',
                    'Communications Operator',
                    'Equipment Custodian'
                ]
            },
            {
                label: 'Volunteers',
                positions: [
                    'Volunteer Coordinator',
                    'Community Volunteer',
                    'Emergency Response Volunteer',
                    'First Aid Volunteer',
                    'Evacuation Assistant'
                ]
            },
            {
                label: 'Education & Training',
                positions: [
                    'Training Coordinator',
                    'Community Educator',
                    'Safety Officer',
                    'Preparedness Advocate'
                ]
            }
        ]
    };

    return positionData[role] || [];
}

function handlePositionChange() {
    const positionSelect = document.querySelector('select[name="position"]');
    const customPositionField = document.getElementById('customPositionField');
    const customPositionInput = document.getElementById('customPosition');

    if (positionSelect.value === 'Other') {
        customPositionField.classList.remove('hidden');
        customPositionInput.required = true;
    } else {
        customPositionField.classList.add('hidden');
        customPositionInput.required = false;
        customPositionInput.value = '';
    }
}

async function loadCities() {
    try {
        const response = await fetch('{{ route("api.psgc.cities") }}', {
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            }
        });

        const result = await response.json();
        const citySelect = document.getElementById('modalCitySelect');

        if (!citySelect) {
            console.error('City select element not found in modal!');
            return;
        }

        citySelect.innerHTML = '<option value="">Select City</option>';

        if (result.success && result.data) {
            result.data.forEach(city => {
                const option = document.createElement('option');
                option.value = city.code;
                option.textContent = city.name;
                citySelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Failed to load cities:', error);
    }
}

async function loadBarangays() {
    const citySelect = document.getElementById('modalCitySelect');
    const barangaySelect = document.querySelector('#createUserModal select[name="barangay"]');
    
    barangaySelect.innerHTML = '<option value="">Select Barangay</option>';
    
    if (!citySelect.value) {
        return;
    }
    
    try {
        const response = await fetch(`{{ url('api/psgc/barangays') }}/${citySelect.value}`, {
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            }
        });
        
        const result = await response.json();
        
        if (result.success && result.data) {
            result.data.forEach(barangay => {
                const option = document.createElement('option');
                option.value = barangay.name;
                option.textContent = barangay.name;
                barangaySelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Failed to load barangays:', error);
    }
}

async function createUser(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData.entries());

    // Validate email before submission
    const emailInput = event.target.querySelector('input[name="email"]');
    const isEmailValid = await validateEmail(emailInput);

    if (!isEmailValid) {
        alert('Please provide a valid and available email address.');
        return;
    }

    // Remove password from data since it will be auto-generated
    delete data.password;

    // Handle custom position
    if (data.position === 'Other') {
        const customPosition = document.getElementById('customPosition').value.trim();
        if (!customPosition) {
            alert('Please enter a custom position.');
            return;
        }
        data.position = customPosition;
    }

    if (data.role === 'system_admin') {
        delete data.city;
        delete data.barangay;
    } else if (data.role === 'super_admin') {
        delete data.barangay;
    }

    try {
        const response = await fetch('{{ route("system-admin.create-user") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Accept': 'application/json'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            alert(result.message);
            closeCreateUserModal();
            location.reload();
        } else {
            if (result.errors) {
                let errorMessage = 'Validation errors:\n';
                for (const [field, errors] of Object.entries(result.errors)) {
                    errorMessage += `${field}: ${errors.join(', ')}\n`;
                }
                alert(errorMessage);
            } else {
                alert('Error: ' + result.message);
            }
        }
    } catch (error) {
        alert('An error occurred while creating the user.');
        console.error(error);
    }
}

async function viewUser(userId) {
    try {
        const response = await fetch(`{{ url('system-admin/users') }}/${userId}/view`, {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Accept': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            showUserDetailsModal(result.user);
        } else {
            alert('Error: ' + result.message);
        }
    } catch (error) {
        alert('An error occurred while loading user details.');
        console.error(error);
    }
}

async function toggleUserStatus(userId, currentStatus) {
    const action = currentStatus === 'Active' ? 'deactivate' : 'activate';

    if (!confirm(`Are you sure you want to ${action} this user account?`)) {
        return;
    }

    try {
        const response = await fetch(`{{ url('system-admin/users') }}/${userId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Accept': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            alert(result.message);
            location.reload();
        } else {
            alert('Error: ' + result.message);
        }
    } catch (error) {
        alert('An error occurred while updating user status.');
        console.error(error);
    }
}

async function deleteUser(userId, userName) {
    if (!confirm(`Are you sure you want to permanently delete the account for "${userName}"?\n\nThis action cannot be undone and will remove all associated data.`)) {
        return;
    }

    // Double confirmation for delete action
    if (!confirm(`FINAL CONFIRMATION: Delete "${userName}" permanently?`)) {
        return;
    }

    try {
        const response = await fetch(`{{ url('system-admin/users') }}/${userId}/delete`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Accept': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            alert(result.message);
            location.reload();
        } else {
            alert('Error: ' + result.message);
        }
    } catch (error) {
        alert('An error occurred while deleting the user.');
        console.error(error);
    }
}

function showUserDetailsModal(user) {
    const modal = document.getElementById('userDetailsModal');
    const content = document.getElementById('userDetailsContent');

    content.innerHTML = `
        <div class="space-y-6">
            <!-- Basic Information -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-lg font-semibold text-gray-900 mb-3">Basic Information</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Full Name</label>
                        <p class="text-gray-900">${user.first_name} ${user.middle_name ? user.middle_name + ' ' : ''}${user.last_name}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Email</label>
                        <p class="text-gray-900">${user.email}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Role</label>
                        <span class="px-2 py-1 rounded-full text-xs font-medium ${getRoleColorClass(user.role)}">${user.role_display}</span>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Position</label>
                        <p class="text-gray-900">${user.position}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Status</label>
                        <span class="px-2 py-1 rounded-full text-xs font-medium ${user.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">${user.status}</span>
                    </div>
                </div>
            </div>

            <!-- Location Information -->
            ${user.city_name || user.barangay ? `
            <div class="bg-blue-50 rounded-lg p-4">
                <h4 class="text-lg font-semibold text-gray-900 mb-3">Location Information</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    ${user.city_name ? `
                    <div>
                        <label class="block text-sm font-medium text-gray-600">City</label>
                        <p class="text-gray-900">${user.city_name}</p>
                    </div>
                    ` : ''}
                    ${user.barangay ? `
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Barangay</label>
                        <p class="text-gray-900">${user.barangay}</p>
                    </div>
                    ` : ''}
                </div>
            </div>
            ` : ''}

            <!-- Access Level -->
            <div class="bg-green-50 rounded-lg p-4">
                <h4 class="text-lg font-semibold text-gray-900 mb-3">Access Level</h4>
                <p class="text-gray-700">${user.access_description}</p>
            </div>

            <!-- Responsibilities -->
            <div class="bg-yellow-50 rounded-lg p-4">
                <h4 class="text-lg font-semibold text-gray-900 mb-3">Responsibilities</h4>
                <ul class="list-disc list-inside space-y-1 text-gray-700">
                    ${user.responsibilities.map(resp => `<li>${resp}</li>`).join('')}
                </ul>
            </div>

            <!-- Activity Summary -->
            <div class="bg-purple-50 rounded-lg p-4">
                <h4 class="text-lg font-semibold text-gray-900 mb-3">Activity Summary</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="text-center">
                        <p class="text-2xl font-bold text-purple-600">${user.management_requests_count}</p>
                        <p class="text-sm text-gray-600">Requests Made</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-purple-600">${user.targeted_requests_count}</p>
                        <p class="text-sm text-gray-600">Requests Received</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-purple-600">${user.reviewed_requests_count}</p>
                        <p class="text-sm text-gray-600">Requests Reviewed</p>
                    </div>
                </div>
            </div>

            <!-- Account Information -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-lg font-semibold text-gray-900 mb-3">Account Information</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Created</label>
                        <p class="text-gray-900">${user.created_at}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Last Updated</label>
                        <p class="text-gray-900">${user.updated_at}</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    modal.classList.remove('hidden');
}

function closeUserDetailsModal() {
    document.getElementById('userDetailsModal').classList.add('hidden');
}

function getRoleColorClass(role) {
    switch(role) {
        case 'system_admin':
            return 'bg-slate-100 text-slate-800';
        case 'super_admin':
            return 'bg-blue-100 text-blue-800';
        case 'admin':
            return 'bg-green-100 text-green-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
}

async function validateEmail(emailInput) {
    const email = emailInput.value.trim();
    const validationDiv = document.getElementById('emailValidation');

    if (!email) {
        validationDiv.classList.add('hidden');
        return;
    }

    // Basic email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        validationDiv.innerHTML = '<span class="text-red-600"><i class="fas fa-times-circle mr-1"></i>Invalid email format</span>';
        validationDiv.classList.remove('hidden');
        emailInput.classList.add('border-red-500');
        emailInput.classList.remove('border-green-500');
        return false;
    }

    try {
        // Check if email is already in use
        const response = await fetch('{{ route("system-admin.check-email") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Accept': 'application/json'
            },
            body: JSON.stringify({ email: email })
        });

        const result = await response.json();

        if (result.available) {
            validationDiv.innerHTML = '<span class="text-green-600"><i class="fas fa-check-circle mr-1"></i>Email is available</span>';
            validationDiv.classList.remove('hidden');
            emailInput.classList.add('border-green-500');
            emailInput.classList.remove('border-red-500');
            return true;
        } else {
            validationDiv.innerHTML = '<span class="text-red-600"><i class="fas fa-times-circle mr-1"></i>Email is already in use</span>';
            validationDiv.classList.remove('hidden');
            emailInput.classList.add('border-red-500');
            emailInput.classList.remove('border-green-500');
            return false;
        }
    } catch (error) {
        validationDiv.innerHTML = '<span class="text-yellow-600"><i class="fas fa-exclamation-triangle mr-1"></i>Unable to verify email availability</span>';
        validationDiv.classList.remove('hidden');
        emailInput.classList.remove('border-green-500', 'border-red-500');
        return true; // Allow submission if validation service fails
    }
}

// City-Barangay filter interaction
document.addEventListener('DOMContentLoaded', function() {
    const citySelect = document.getElementById('city');
    const barangaySelect = document.getElementById('barangay');

    if (citySelect && barangaySelect) {
        citySelect.addEventListener('change', function() {
            loadBarangaysForFilter();
        });
    }
});

async function loadBarangaysForFilter() {
    const citySelect = document.getElementById('city');
    const barangaySelect = document.getElementById('barangay');

    // Clear barangay options
    barangaySelect.innerHTML = '<option value="">All Barangays</option>';

    if (!citySelect.value) {
        return;
    }

    try {
        const response = await fetch(`{{ route('system-admin.get-barangays-by-city') }}?city=${encodeURIComponent(citySelect.value)}`, {
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Accept': 'application/json'
            }
        });

        const result = await response.json();

        if (result.barangays && Array.isArray(result.barangays)) {
            result.barangays.forEach(barangay => {
                const option = document.createElement('option');
                option.value = barangay;
                option.textContent = barangay;
                barangaySelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Failed to load barangays:', error);
    }
}
</script>
@endsection
