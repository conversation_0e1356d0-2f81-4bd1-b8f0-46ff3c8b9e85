# ALERTO Email Notification System

This document provides a comprehensive guide to the SMTP-based email notification system implemented for the ALERTO Disaster Management System.

## Overview

The email notification system provides secure, scalable, and professional communication capabilities for admin users. It includes:

- **Admin Event Notifications**: Send notifications about system events, alerts, and updates
- **Admin Invitation System**: Secure invitation-based user registration
- **SMTP Integration**: Professional email delivery using SMTP
- **Email Verification**: Built-in email verification for security

## Features

### 1. Admin Event Notifications
- Send notifications to all admin users
- Target specific barangay admins
- Send notifications to individual users
- Support for different event types (alerts, system updates, etc.)

### 2. Admin Invitation System
- Secure invitation links with expiration
- Email-based account creation
- Role-based access control
- Automatic cleanup of expired invitations

### 3. SMTP Configuration
- Professional email delivery
- Support for multiple email providers
- Queue-based processing for reliability
- Comprehensive error handling and logging

## Configuration

### 1. Environment Variables

Add the following variables to your `.env` file:

```env
# SMTP Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="ALERTO System"

# System Administrator Credentials (for development)
SYSTEM_ADMIN_EMAIL=<EMAIL>
SYSTEM_ADMIN_PASSWORD=SysAdmin@2025

# Barangay Admin Credentials (for development)
LAHUG_CHAIRMAN_EMAIL=<EMAIL>
LAHUG_OFFICER_EMAIL=<EMAIL>
LAHUG_ASSISTANT_EMAIL=<EMAIL>
LAHUG_PASSWORD=Lahug@2025

MABOLO_CHAIRMAN_EMAIL=<EMAIL>
MABOLO_OFFICER_EMAIL=<EMAIL>
MABOLO_ASSISTANT_EMAIL=<EMAIL>
MABOLO_PASSWORD=Mabolo@2025

LUZ_CHAIRMAN_EMAIL=<EMAIL>
LUZ_OFFICER_EMAIL=<EMAIL>
LUZ_ASSISTANT_EMAIL=<EMAIL>
LUZ_PASSWORD=Luz@2025

# Test User
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=password123
```

### 2. SMTP Provider Setup

#### Gmail Setup
1. Enable 2-Factor Authentication on your Gmail account
2. Generate an App Password:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate a password for "Mail"
3. Use the generated password in `MAIL_PASSWORD`

#### Other SMTP Providers
- **Outlook/Hotmail**: Use `smtp-mail.outlook.com` on port 587
- **Yahoo**: Use `smtp.mail.yahoo.com` on port 587
- **Custom SMTP**: Configure according to your provider's settings

### 3. Queue Configuration

For production, configure queues for reliable email delivery:

```env
QUEUE_CONNECTION=database
```

Run the queue worker:
```bash
php artisan queue:work
```

## Usage

### 1. Sending Admin Notifications

#### Send to All Admins
```php
use App\Services\EmailNotificationService;

$emailService = app(EmailNotificationService::class);

$emailService->notifyAllAdmins(
    'System Maintenance Notice',
    'Scheduled maintenance will occur on Sunday at 2 AM.',
    'system_maintenance'
);
```

#### Send to Specific Barangay
```php
$emailService->notifyBarangayAdmins(
    'Lahug',
    'New Emergency Alert',
    'A flood warning has been issued for Lahug area.',
    'new_alert'
);
```

#### Send to Specific User
```php
$user = User::find(1);
$emailService->notifyUser(
    $user,
    'Account Update',
    'Your account has been successfully updated.',
    'system_update'
);
```

### 2. Admin Invitations

#### Send Invitation
```php
$invitationData = [
    'email' => '<EMAIL>',
    'first_name' => 'John',
    'last_name' => 'Doe',
    'position' => 'BDRRMO Officer',
    'barangay' => 'Lahug',
    'role' => 'officer'
];

$invitation = $emailService->sendAdminInvitation($invitationData, auth()->user());
```

#### Accept Invitation
Users receive an email with a secure link. When clicked, they can set their password and activate their account.

### 3. API Endpoints

#### Admin Invitations
- `GET /admin-invitations` - List invitations
- `POST /admin-invitations` - Send new invitation
- `GET /admin-invitations/{id}` - View invitation details
- `POST /admin-invitations/{id}/resend` - Resend invitation
- `DELETE /admin-invitations/{id}` - Cancel invitation

#### Email Notifications
- `POST /email-notifications/notify-all-admins` - Send to all admins
- `POST /email-notifications/notify-barangay-admins` - Send to barangay admins
- `POST /email-notifications/notify-user` - Send to specific user
- `GET /email-notifications/admin-users` - Get admin users list
- `GET /email-notifications/barangays` - Get barangays list
- `POST /email-notifications/test-config` - Test email configuration

## Security Features

### 1. Invitation Security
- Secure tokens (64 characters)
- 7-day expiration
- Signed URLs with Laravel's temporary signed routes
- Automatic cleanup of expired invitations

### 2. Access Control
- Role-based permissions
- Barangay-based access restrictions
- Admin-only invitation management
- Super admin system-wide access

### 3. Email Verification
- Built-in Laravel email verification
- MustVerifyEmail contract implementation
- Secure verification links

## Maintenance

### 1. Cleanup Expired Invitations
Run the cleanup command manually:
```bash
php artisan invitations:cleanup-expired
```

Or set up a scheduled task in `app/Console/Kernel.php`:
```php
protected function schedule(Schedule $schedule)
{
    $schedule->command('invitations:cleanup-expired')->daily();
}
```

### 2. Queue Monitoring
Monitor failed jobs:
```bash
php artisan queue:failed
```

Retry failed jobs:
```bash
php artisan queue:retry all
```

### 3. Email Testing
Test your SMTP configuration:
```bash
php artisan tinker
```
```php
Mail::raw('Test email', function($message) {
    $message->to('<EMAIL>')->subject('Test');
});
```

## Troubleshooting

### Common Issues

#### 1. Emails Not Sending
- Check SMTP credentials in `.env`
- Verify firewall settings
- Check queue worker is running
- Review Laravel logs: `storage/logs/laravel.log`

#### 2. Invitation Links Not Working
- Check URL signing configuration
- Verify invitation hasn't expired
- Check database for invitation status

#### 3. Permission Errors
- Verify user roles and permissions
- Check middleware configuration
- Review access control logic

### Debug Commands
```bash
# Check email configuration
php artisan config:cache
php artisan config:clear

# Test queue system
php artisan queue:work --once

# Check invitation status
php artisan tinker
>>> App\Models\Invitation::pending()->get()
```

## Best Practices

### 1. Production Deployment
- Use environment variables for all credentials
- Configure proper SMTP settings
- Set up queue workers
- Enable email verification
- Regular backup of invitation data

### 2. Security
- Never commit `.env` files
- Use strong passwords for admin accounts
- Regularly rotate SMTP credentials
- Monitor failed login attempts
- Implement rate limiting

### 3. Performance
- Use queues for email sending
- Implement proper indexing on database
- Monitor email delivery rates
- Clean up expired data regularly

## Migration from Hardcoded Credentials

The system now uses environment variables instead of hardcoded credentials in seeders. This provides:

- **Security**: Credentials not in source code
- **Flexibility**: Different credentials per environment
- **Scalability**: Easy to add new admin accounts
- **Maintainability**: No code changes needed for new users

### Benefits for Your Defense
- **Professional Approach**: Industry-standard security practices
- **Scalability**: Easy to add new barangay admins
- **Security**: No hardcoded credentials in source code
- **Audit Trail**: Complete invitation and acceptance tracking
- **Real-world Ready**: Production-ready implementation

## Support

For technical support or questions about the email notification system, refer to:
- Laravel Mail Documentation: https://laravel.com/docs/mail
- Laravel Notifications: https://laravel.com/docs/notifications
- SMTP Configuration Guides: Provider-specific documentation 