<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class PSGCService
{
    protected $baseUrl = 'https://psgc.gitlab.io/api';
    protected $cacheTimeout = 3600; // 1 hour cache

    // PSGC Codes for Cebu Province and its cities
    protected $cebuProvinceCode = '072200000';
    protected $cebuCityCode = '072217000';

    /**
     * Get all cities in Cebu Province
     * Cebu Province PSGC Code: 072200000
     */
    public function getCebuProvinceCities()
    {
        $cacheKey = 'psgc_cebu_province_cities';

        // Try to get from cache first
        $cachedData = Cache::get($cacheKey);
        if ($cachedData) {
            Log::info('Retrieved Cebu Province cities from cache');
            return $cachedData;
        }

        try {
            Log::info('Fetching Cebu Province cities from PSGC API');

            // Try different possible endpoints for Cebu Province cities
            $endpoints = [
                '/provinces/072200000/cities-municipalities',
                '/provinces/072200000/cities',
                '/cities-municipalities?provinceCode=072200000',
                '/cities?province=072200000'
            ];

            $cities = null;
            foreach ($endpoints as $endpoint) {
                Log::info("Trying PSGC endpoint: {$endpoint}");
                $response = $this->makeApiRequest($endpoint);

                if ($response['success'] && !empty($response['data'])) {
                    $cities = $response['data'];
                    Log::info("Successfully fetched data from endpoint: {$endpoint}");
                    break;
                }
            }

            if (!$cities) {
                throw new \Exception('No city data found from any PSGC API endpoint');
            }

            // Transform the data to our format
            $transformedCities = collect($cities)->map(function ($city) {
                $name = $city['name'] ?? $city['city_name'] ?? $city['municipality_name'] ?? 'Unknown';
                $code = $city['code'] ?? $city['psgc_code'] ?? $city['city_code'] ?? null;

                return [
                    'name' => $name,
                    'code' => $code,
                    'psgc_code' => $code,
                    'description' => "{$name}, Cebu Province",
                    'province' => 'Cebu',
                    'status' => true,
                ];
            })->toArray();

            // Cache the result
            Cache::put($cacheKey, $transformedCities, $this->cacheTimeout);

            Log::info('Successfully fetched and cached Cebu Province cities', [
                'count' => count($transformedCities)
            ]);

            return $transformedCities;

        } catch (\Exception $e) {
            Log::error('Failed to fetch Cebu Province cities from PSGC API', [
                'error' => $e->getMessage()
            ]);

            // Return fallback data if API fails
            return $this->getFallbackCities();
        }
    }

    /**
     * Get all barangays for Cebu City
     * Cebu City PSGC Code: 072217000
     */
    public function getCebuCityBarangays()
    {
        $cacheKey = 'psgc_cebu_city_barangays';

        // Try to get from cache first
        $cachedData = Cache::get($cacheKey);
        if ($cachedData) {
            Log::info('Retrieved Cebu City barangays from cache');
            return $cachedData;
        }

        try {
            Log::info('Fetching Cebu City barangays from PSGC API');

            // Try different possible endpoints for Cebu City
            $endpoints = [
                '/cities-municipalities/072217000/barangays',
                '/cities/072217000/barangays',
                '/municipalities/072217000/barangays',
                '/barangays?cityCode=072217000',
                '/barangays?city=072217000'
            ];

            $barangays = null;
            foreach ($endpoints as $endpoint) {
                Log::info("Trying PSGC endpoint: {$endpoint}");
                $response = $this->makeApiRequest($endpoint);

                if ($response['success'] && !empty($response['data'])) {
                    $barangays = $response['data'];
                    Log::info("Successfully fetched data from endpoint: {$endpoint}");
                    break;
                }
            }

            if (!$barangays) {
                throw new \Exception('No barangay data found from any PSGC API endpoint');
            }

            // Transform the data to our format
            $transformedBarangays = collect($barangays)->map(function ($barangay) {
                // Handle different possible response structures
                $name = $barangay['name'] ?? $barangay['barangay_name'] ?? $barangay['brgy_name'] ?? 'Unknown';
                $code = $barangay['code'] ?? $barangay['psgc_code'] ?? $barangay['brgy_code'] ?? null;

                return [
                    'name' => $name,
                    'code' => $this->generateBarangayCode($name), // Generate our own code
                    'psgc_code' => $code, // Store original PSGC code
                    'description' => "Barangay {$name} - Cebu City",
                    'address' => "{$name}, Cebu City, Cebu, Philippines",
                    'status' => true,
                ];
            })->toArray();

            // Cache the result
            Cache::put($cacheKey, $transformedBarangays, $this->cacheTimeout);

            Log::info('Successfully fetched and cached Cebu City barangays', [
                'count' => count($transformedBarangays)
            ]);

            return $transformedBarangays;

        } catch (\Exception $e) {
            Log::error('Failed to fetch Cebu City barangays from PSGC API', [
                'error' => $e->getMessage()
            ]);

            // Return fallback data if API fails
            return $this->getFallbackBarangays();
        }
    }

    /**
     * Make API request with proper error handling
     */
    protected function makeApiRequest($endpoint)
    {
        try {
            $url = $this->baseUrl . $endpoint;
            
            Log::info("Making PSGC API request: {$url}");

            // Try with SSL verification first with shorter timeout
            try {
                $response = Http::withHeaders([
                    'User-Agent' => 'WebAlerto Laravel App',
                    'Accept' => 'application/json',
                ])->timeout(10)->get($url);
            } catch (\Exception $sslError) {
                Log::warning("SSL verification failed for PSGC API, retrying without verification: " . $sslError->getMessage());
                
                // Fallback without SSL verification
                $response = Http::withHeaders([
                    'User-Agent' => 'WebAlerto Laravel App',
                    'Accept' => 'application/json',
                ])->withOptions([
                    'verify' => false,
                    'timeout' => 10,
                ])->get($url);
            }

            if ($response->successful()) {
                $data = $response->json();
                
                Log::info('PSGC API request successful', [
                    'endpoint' => $endpoint,
                    'data_count' => is_array($data) ? count($data) : 'N/A'
                ]);

                return [
                    'success' => true,
                    'data' => $data,
                    'message' => 'Data retrieved successfully'
                ];
            } else {
                Log::error('PSGC API request failed', [
                    'endpoint' => $endpoint,
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);

                return [
                    'success' => false,
                    'data' => null,
                    'message' => "API request failed with status: {$response->status()}"
                ];
            }

        } catch (\Exception $e) {
            Log::error('Exception during PSGC API request', [
                'endpoint' => $endpoint,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'data' => null,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get fallback barangay data if API fails
     * Complete official list of 80 barangays in Cebu City (Source: Wikipedia/PSA)
     */
    protected function getFallbackBarangays()
    {
        Log::warning('Using fallback barangay data due to API failure');

        // Complete official list of 80 barangays in Cebu City with PSGC codes
        $barangays = [
            ['name' => 'Adlaon', 'code' => 'ADL', 'psgc_code' => '072217001'],
            ['name' => 'Agsungot', 'code' => 'AGS', 'psgc_code' => '072217002'],
            ['name' => 'Apas', 'code' => 'APA', 'psgc_code' => '072217003'],
            ['name' => 'Babag', 'code' => 'BAB', 'psgc_code' => '072217004'],
            ['name' => 'Bacayan', 'code' => 'BAC', 'psgc_code' => '072217006'],
            ['name' => 'Banilad', 'code' => 'BAN', 'psgc_code' => '072217007'],
            ['name' => 'Basak Pardo', 'code' => 'BSP', 'psgc_code' => '072217005'],
            ['name' => 'Basak San Nicolas', 'code' => 'BSN', 'psgc_code' => '072217008'],
            ['name' => 'Binaliw', 'code' => 'BIN', 'psgc_code' => '072217010'],
            ['name' => 'Bonbon', 'code' => 'BON', 'psgc_code' => '072217011'],
            ['name' => 'Budlaan', 'code' => 'BUD', 'psgc_code' => '072217013'],
            ['name' => 'Buhisan', 'code' => 'BUH', 'psgc_code' => '072217014'],
            ['name' => 'Bulacao', 'code' => 'BUL', 'psgc_code' => '072217015'],
            ['name' => 'Buot', 'code' => 'BUO', 'psgc_code' => '072217016'],
            ['name' => 'Busay', 'code' => 'BUS', 'psgc_code' => '072217017'],
            ['name' => 'Calamba', 'code' => 'CAL', 'psgc_code' => '072217018'],
            ['name' => 'Cambinocot', 'code' => 'CAM', 'psgc_code' => '072217019'],
            ['name' => 'Capitol Site', 'code' => 'CAP', 'psgc_code' => '072217020'],
            ['name' => 'Carreta', 'code' => 'CAR', 'psgc_code' => '072217021'],
            ['name' => 'Cogon Pardo', 'code' => 'CGP', 'psgc_code' => '072217024'],
            ['name' => 'Cogon Ramos', 'code' => 'COG', 'psgc_code' => '072217023'],
            ['name' => 'Day-as', 'code' => 'DAY', 'psgc_code' => '072217025'],
            ['name' => 'Duljo Fatima', 'code' => 'DUL', 'psgc_code' => '072217027'],
            ['name' => 'Ermita', 'code' => 'ERM', 'psgc_code' => '072217028'],
            ['name' => 'Guadalupe', 'code' => 'GUA', 'psgc_code' => '072217029'],
            ['name' => 'Guba', 'code' => 'GUB', 'psgc_code' => '072217030'],
            ['name' => 'Hipodromo', 'code' => 'HIP', 'psgc_code' => '072217031'],
            ['name' => 'Inayawan', 'code' => 'INA', 'psgc_code' => '072217032'],
            ['name' => 'Kalubihan', 'code' => 'KAL', 'psgc_code' => '072217033'],
            ['name' => 'Kalunasan', 'code' => 'KLN', 'psgc_code' => '072217034'],
            ['name' => 'Kamagayan', 'code' => 'KAM', 'psgc_code' => '072217035'],
            ['name' => 'Kamputhaw', 'code' => 'KMP', 'psgc_code' => '072217036'],
            ['name' => 'Kasambagan', 'code' => 'KAS', 'psgc_code' => '072217037'],
            ['name' => 'Kinasang-an Pardo', 'code' => 'KIN', 'psgc_code' => '072217038'],
            ['name' => 'Labangon', 'code' => 'LAB', 'psgc_code' => '072217040'],
            ['name' => 'Lahug', 'code' => 'LAH', 'psgc_code' => '072217041'],
            ['name' => 'Lorega San Miguel', 'code' => 'LOR', 'psgc_code' => '072217042'],
            ['name' => 'Lusaran', 'code' => 'LUS', 'psgc_code' => '072217043'],
            ['name' => 'Luz', 'code' => 'LUZ', 'psgc_code' => '072217044'],
            ['name' => 'Mabini', 'code' => 'MAB', 'psgc_code' => '072217045'],
            ['name' => 'Mabolo', 'code' => 'MBO', 'psgc_code' => '072217046'],
            ['name' => 'Malubog', 'code' => 'MAL', 'psgc_code' => '072217048'],
            ['name' => 'Mambaling', 'code' => 'MAM', 'psgc_code' => '072217049'],
            ['name' => 'Pahina Central', 'code' => 'PAH', 'psgc_code' => '072217050'],
            ['name' => 'Pahina San Nicolas', 'code' => 'PSN', 'psgc_code' => '072217051'],
            ['name' => 'Pamutan', 'code' => 'PAM', 'psgc_code' => '072217052'],
            ['name' => 'Pari-an', 'code' => 'PAR', 'psgc_code' => '072217054'],
            ['name' => 'Paril', 'code' => 'PRL', 'psgc_code' => '072217055'],
            ['name' => 'Pasil', 'code' => 'PAS', 'psgc_code' => '072217056'],
            ['name' => 'Pit-os', 'code' => 'PIT', 'psgc_code' => '072217057'],
            ['name' => 'Poblacion Pardo', 'code' => 'POB', 'psgc_code' => '072217053'],
            ['name' => 'Pulangbato', 'code' => 'PUL', 'psgc_code' => '072217059'],
            ['name' => 'Pung-ol Sibugay', 'code' => 'PUN', 'psgc_code' => '072217060'],
            ['name' => 'Punta Princesa', 'code' => 'PRI', 'psgc_code' => '072217062'],
            ['name' => 'Quiot Pardo', 'code' => 'QUI', 'psgc_code' => '072217063'],
            ['name' => 'Sambag I', 'code' => 'SA1', 'psgc_code' => '072217064'],
            ['name' => 'Sambag II', 'code' => 'SA2', 'psgc_code' => '072217065'],
            ['name' => 'San Antonio', 'code' => 'SAN', 'psgc_code' => '072217066'],
            ['name' => 'San Jose', 'code' => 'SJO', 'psgc_code' => '072217067'],
            ['name' => 'San Nicolas Proper', 'code' => 'SNP', 'psgc_code' => '072217068'],
            ['name' => 'San Roque', 'code' => 'SRO', 'psgc_code' => '072217069'],
            ['name' => 'Santa Cruz', 'code' => 'SCR', 'psgc_code' => '072217070'],
            ['name' => 'Santo Niño', 'code' => 'SNI', 'psgc_code' => '072217022'],
            ['name' => 'Sapangdaku', 'code' => 'SAP', 'psgc_code' => '072217077'],
            ['name' => 'Sawang Calero', 'code' => 'SAW', 'psgc_code' => '072217071'],
            ['name' => 'Sinsin', 'code' => 'SIN', 'psgc_code' => '072217073'],
            ['name' => 'Sirao', 'code' => 'SIR', 'psgc_code' => '072217074'],
            ['name' => 'Suba', 'code' => 'SUB', 'psgc_code' => '072217075'],
            ['name' => 'Sudlon I', 'code' => 'SU1', 'psgc_code' => '072217076'],
            ['name' => 'Sudlon II', 'code' => 'SU2', 'psgc_code' => '072217088'],
            ['name' => 'T. Padilla', 'code' => 'TPD', 'psgc_code' => '072217078'],
            ['name' => 'Tabunan', 'code' => 'TAB', 'psgc_code' => '072217079'],
            ['name' => 'Tagba-o', 'code' => 'TAG', 'psgc_code' => '072217080'],
            ['name' => 'Talamban', 'code' => 'TAL', 'psgc_code' => '072217081'],
            ['name' => 'Taptap', 'code' => 'TAP', 'psgc_code' => '072217082'],
            ['name' => 'Tejero', 'code' => 'TEJ', 'psgc_code' => '072217083'],
            ['name' => 'Tinago', 'code' => 'TIN', 'psgc_code' => '072217084'],
            ['name' => 'Tisa', 'code' => 'TIS', 'psgc_code' => '072217085'],
            ['name' => 'To-ong', 'code' => 'TOO', 'psgc_code' => '072217086'],
            ['name' => 'Zapatera', 'code' => 'ZAP', 'psgc_code' => '072217087']
        ];

        // Transform to our format
        return collect($barangays)->map(function ($barangay) {
            return [
                'name' => $barangay['name'],
                'code' => $barangay['code'],
                'psgc_code' => $barangay['psgc_code'],
                'description' => "Barangay {$barangay['name']} - Cebu City",
                'address' => "{$barangay['name']}, Cebu City, Cebu, Philippines",
                'status' => true,
            ];
        })->toArray();
    }

    /**
     * Get barangays for any city by PSGC code
     */
    public function getCityBarangays($cityCode, $cityName = null)
    {
        $cacheKey = "psgc_city_{$cityCode}_barangays";

        // Try to get from cache first
        $cachedData = Cache::get($cacheKey);
        if ($cachedData) {
            Log::info("Retrieved {$cityName} barangays from cache");
            return $cachedData;
        }

        try {
            Log::info("Fetching {$cityName} barangays from PSGC API");

            // Try different possible endpoints
            $endpoints = [
                "/cities-municipalities/{$cityCode}/barangays",
                "/cities/{$cityCode}/barangays",
                "/municipalities/{$cityCode}/barangays",
                "/barangays?cityCode={$cityCode}",
                "/barangays?city={$cityCode}"
            ];

            $barangays = null;
            foreach ($endpoints as $endpoint) {
                Log::info("Trying PSGC endpoint: {$endpoint}");
                $response = $this->makeApiRequest($endpoint);

                if ($response['success'] && !empty($response['data'])) {
                    $barangays = $response['data'];
                    Log::info("Successfully fetched data from endpoint: {$endpoint}");
                    break;
                }
            }

            if (!$barangays) {
                throw new \Exception("No barangay data found for city code: {$cityCode}");
            }

            // Transform the data to our format
            $transformedBarangays = collect($barangays)->map(function ($barangay) use ($cityName) {
                $name = $barangay['name'] ?? $barangay['barangay_name'] ?? $barangay['brgy_name'] ?? 'Unknown';
                $code = $barangay['code'] ?? $barangay['psgc_code'] ?? $barangay['brgy_code'] ?? null;

                return [
                    'name' => $name,
                    'code' => $this->generateBarangayCode($name),
                    'psgc_code' => $code,
                    'description' => "Barangay {$name} - {$cityName}",
                    'address' => "{$name}, {$cityName}, Cebu, Philippines",
                    'status' => true,
                ];
            })->toArray();

            // Cache the result
            Cache::put($cacheKey, $transformedBarangays, $this->cacheTimeout);

            Log::info("Successfully fetched and cached {$cityName} barangays", [
                'count' => count($transformedBarangays)
            ]);

            return $transformedBarangays;

        } catch (\Exception $e) {
            Log::error("Failed to fetch {$cityName} barangays from PSGC API", [
                'error' => $e->getMessage()
            ]);

            // Return empty array if API fails for other cities
            return [];
        }
    }

    /**
     * Get fallback cities data when API is unavailable
     */
    protected function getFallbackCities()
    {
        return [
            [
                'name' => 'Cebu City',
                'code' => '072217000',
                'psgc_code' => '072217000',
                'description' => 'Cebu City, Cebu Province',
                'province' => 'Cebu',
                'status' => true,
            ],
            [
                'name' => 'Mandaue City',
                'code' => '072230000',
                'psgc_code' => '072230000',
                'description' => 'Mandaue City, Cebu Province',
                'province' => 'Cebu',
                'status' => true,
            ],
            [
                'name' => 'Lapu-Lapu City',
                'code' => '072224000',
                'psgc_code' => '072224000',
                'description' => 'Lapu-Lapu City, Cebu Province',
                'province' => 'Cebu',
                'status' => true,
            ],
            [
                'name' => 'Talisay City',
                'code' => '072279000',
                'psgc_code' => '072279000',
                'description' => 'Talisay City, Cebu Province',
                'province' => 'Cebu',
                'status' => true,
            ],
            [
                'name' => 'Toledo City',
                'code' => '072281000',
                'psgc_code' => '072281000',
                'description' => 'Toledo City, Cebu Province',
                'province' => 'Cebu',
                'status' => true,
            ],
            [
                'name' => 'Danao City',
                'code' => '072213000',
                'psgc_code' => '072213000',
                'description' => 'Danao City, Cebu Province',
                'province' => 'Cebu',
                'status' => true,
            ],
            [
                'name' => 'Bogo City',
                'code' => '072209000',
                'psgc_code' => '072209000',
                'description' => 'Bogo City, Cebu Province',
                'province' => 'Cebu',
                'status' => true,
            ],
            [
                'name' => 'Carcar City',
                'code' => '072211000',
                'psgc_code' => '072211000',
                'description' => 'Carcar City, Cebu Province',
                'province' => 'Cebu',
                'status' => true,
            ],
            [
                'name' => 'Naga City',
                'code' => '072235000',
                'psgc_code' => '072235000',
                'description' => 'Naga City, Cebu Province',
                'province' => 'Cebu',
                'status' => true,
            ]
        ];
    }

    /**
     * Generate a barangay code from the name
     */
    protected function generateBarangayCode($name)
    {
        // Remove common words and get first 3 letters
        $cleanName = str_replace(['Barangay', 'brgy', 'Brgy'], '', $name);
        $cleanName = trim($cleanName);

        // Handle special cases
        $specialCases = [
            'Capitol Site' => 'CAP',
            'Cogon Ramos' => 'COG',
            'Lorega San Miguel' => 'LOR',
            'Pahina Central' => 'PAH',
            'Pung-ol Sibugay' => 'PUN',
            'Punta Princesa' => 'PRI',
            'San Antonio' => 'SAN',
            'San Jose' => 'SJO',
            'San Roque' => 'SRO',
            'Santa Cruz' => 'SCR',
            'Santo Niño' => 'SNI',
            'Sawang Calero' => 'SAW',
            'Sudlon I' => 'SU1',
            'Sudlon II' => 'SU2',
            'T. Padilla' => 'TPD',
        ];

        if (isset($specialCases[$cleanName])) {
            return $specialCases[$cleanName];
        }

        // Generate code from first 3 letters
        return strtoupper(substr(preg_replace('/[^a-zA-Z]/', '', $cleanName), 0, 3));
    }

    /**
     * Get default coordinates for a city/municipality
     */
    public function getCityCoordinates($cityName)
    {
        // Default coordinates for major Cebu Province cities/municipalities
        $cityCoordinates = [
            'Cebu City' => ['lat' => 10.3157, 'lng' => 123.8854],
            'Mandaue City' => ['lat' => 10.3237, 'lng' => 123.9227],
            'Lapu-Lapu City' => ['lat' => 10.3103, 'lng' => 123.9494],
            'Talisay City' => ['lat' => 10.2449, 'lng' => 123.8492],
            'Danao City' => ['lat' => 10.5197, 'lng' => 124.0253],
            'Toledo City' => ['lat' => 10.3773, 'lng' => 123.6414],
            'Bogo City' => ['lat' => 11.0508, 'lng' => 124.0061],
            'Carcar City' => ['lat' => 10.1077, 'lng' => 123.6386],
            'Naga City' => ['lat' => 10.2078, 'lng' => 123.7567],
            'Dalaguete' => ['lat' => 9.7611, 'lng' => 123.5358],
            'Argao' => ['lat' => 9.8836, 'lng' => 123.6089],
            'Alcoy' => ['lat' => 9.6833, 'lng' => 123.5000],
            'Moalboal' => ['lat' => 9.9333, 'lng' => 123.3833],
            'Oslob' => ['lat' => 9.5667, 'lng' => 123.3833],
            'Bantayan' => ['lat' => 11.2000, 'lng' => 123.7167],
            'Madridejos' => ['lat' => 11.2667, 'lng' => 123.7167],
            'Santa Fe' => ['lat' => 11.1500, 'lng' => 123.8000],
            'Consolacion' => ['lat' => 10.3833, 'lng' => 123.9500],
            'Liloan' => ['lat' => 10.3833, 'lng' => 123.9833],
            'Compostela' => ['lat' => 10.4500, 'lng' => 124.0167],
            'Cordova' => ['lat' => 10.2500, 'lng' => 123.9500],
            'Minglanilla' => ['lat' => 10.2333, 'lng' => 123.7833],
            'San Fernando' => ['lat' => 10.1667, 'lng' => 123.7000],
        ];

        return $cityCoordinates[$cityName] ?? $cityCoordinates['Cebu City']; // Default to Cebu City if not found
    }

    /**
     * Clear cached barangay data
     */
    public function clearCache()
    {
        Cache::forget('psgc_cebu_city_barangays');
        Log::info('PSGC barangay cache cleared');
    }
}
