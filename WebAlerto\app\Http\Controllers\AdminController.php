<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Notification;
use App\Models\Evacuation;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AdminController extends Controller
{
    public function barangayStatistics(Request $request)
    {
        // Get selected month and year from request, default to current month/year
        $selectedMonth = $request->get('month', Carbon::now()->month);
        $selectedYear = $request->get('year', Carbon::now()->year);

        // Generate months array for the filter
        $months = collect(range(1, 12))->map(function ($month) {
            return [
                'value' => $month,
                'label' => Carbon::create()->month($month)->format('F')
            ];
        });

        // Generate years array for the filter (last 5 years)
        $years = range(Carbon::now()->year - 4, Carbon::now()->year);

        // Get all barangays using PSGC-connected BarangayService
        $barangayService = app(\App\Services\BarangayService::class);
        $barangays = collect($barangayService->getActiveBarangays());

        $barangayStats = collect();

        foreach ($barangays as $barangay) {
            // Get notifications count for this barangay and period
            $notificationsCount = Notification::where('barangay', $barangay)
                ->whereMonth('created_at', $selectedMonth)
                ->whereYear('created_at', $selectedYear)
                ->count();

            // Get evacuation centers count for this barangay and period
            $evacuationCentersCount = Evacuation::where('barangay', $barangay)
                ->where('status', 'Active')
                ->whereMonth('created_at', $selectedMonth)
                ->whereYear('created_at', $selectedYear)
                ->count();

            // Get barangay users count for this barangay and period (BDRRMO Officer)
            $barangayUserRoles = ['officer'];
            $barangayUsersCount = User::where('barangay', $barangay)
                ->whereIn('role', $barangayUserRoles)
                ->where('status', 'Active')
                ->whereMonth('created_at', $selectedMonth)
                ->whereYear('created_at', $selectedYear)
                ->count();

            // Get mobile users count for this barangay and period
            $mobileUsersCount = User::where('barangay', $barangay)
                ->where('role', 'mobile_user')
                ->where('status', 'Active')
                ->whereMonth('created_at', $selectedMonth)
                ->whereYear('created_at', $selectedYear)
                ->count();

            // Get total active users count for this barangay and period
            $activeUsersCount = User::where('barangay', $barangay)
                ->where('status', 'Active')
                ->whereMonth('created_at', $selectedMonth)
                ->whereYear('created_at', $selectedYear)
                ->count();

            // Determine status based on activity
            $status = ($notificationsCount > 0 || $evacuationCentersCount > 0 || $activeUsersCount > 0) ? 'Active' : 'Inactive';

            $barangayStats->push((object) [
                'barangay' => $barangay,
                'notifications_count' => $notificationsCount,
                'evacuation_centers_count' => $evacuationCentersCount,
                'barangay_users_count' => $barangayUsersCount,
                'mobile_users_count' => $mobileUsersCount,
                'active_users_count' => $activeUsersCount,
                'status' => $status
            ]);
        }

        return view('components.admin.barangay-statistics', compact(
            'months',
            'years',
            'selectedMonth',
            'selectedYear',
            'barangayStats'
        ));
    }
}