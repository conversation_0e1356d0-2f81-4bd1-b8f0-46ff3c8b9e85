<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckBarangayAccess
{
    public function handle(Request $request, Closure $next)
    {
        // Get the authenticated user
        $user = Auth::user();

        // If no user is logged in, redirect to login
        if (!$user) {
            return redirect()->route('login');
        }

        // Get the barangay from the request (either from route parameter or query parameter)
        $requestedBarangay = $request->route('barangay') ?? $request->query('barangay');

        // If no specific barangay is requested, allow access
        if (!$requestedBarangay) {
            return $next($request);
        }

        // Check if user has access to the requested barangay
        if ($user->barangay !== $requestedBarangay) {
            // If user is not from the requested barangay, deny access
            return response()->json([
                'message' => 'You do not have access to this barangay\'s data.'
            ], 403);
        }

        return $next($request);
    }
} 