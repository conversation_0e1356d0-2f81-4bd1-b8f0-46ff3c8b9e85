{"version": 3, "sources": ["../../../../../../node_modules/@capacitor/filesystem/dist/esm/index.js"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\nimport { exposeSynapse } from '@capacitor/synapse';\nconst Filesystem = registerPlugin('Filesystem', {\n  web: () => import('./web').then(m => new m.FilesystemWeb())\n});\nexposeSynapse();\nexport * from './definitions';\nexport { Filesystem };\n"], "mappings": ";;;;;;;;;;;;;;;AAEA,IAAM,aAAa,eAAe,cAAc;AAAA,EAC9C,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,cAAc,CAAC;AAC5D,CAAC;AACD,EAAc;", "names": []}