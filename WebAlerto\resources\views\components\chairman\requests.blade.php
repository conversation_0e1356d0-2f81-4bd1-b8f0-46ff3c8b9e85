@extends('layout.app')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">My Requests</h1>
                    <p class="text-gray-600">Track your user management requests and their status</p>
                </div>
                <div class="flex items-center gap-4">
                    <!-- Statistics Cards -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 px-4 py-3">
                        <div class="text-sm text-gray-500">Pending</div>
                        <div class="text-2xl font-bold text-yellow-600">{{ $pendingCount }}</div>
                    </div>
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 px-4 py-3">
                        <div class="text-sm text-gray-500">Total Requests</div>
                        <div class="text-2xl font-bold text-blue-600">{{ $requests->total() }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Requests Table -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 class="text-lg font-semibold text-gray-900">Request History</h2>
            </div>

            @if($requests->count() > 0)
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50 border-b border-gray-200">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Request Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Target User</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requested Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reviewed Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reviewed By</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($requests as $request)
                                <tr class="hover:bg-gray-50 transition-colors duration-200">
                                    <!-- Request Type -->
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            @if($request->action_type === 'registration')
                                                <div class="flex items-center gap-2">
                                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                                        <i class="fas fa-user-plus text-green-600 text-sm"></i>
                                                    </div>
                                                    <div>
                                                        <div class="font-medium text-gray-900">Registration</div>
                                                        <div class="text-sm text-gray-500">New user account</div>
                                                    </div>
                                                </div>
                                            @elseif($request->action_type === 'deactivate')
                                                <div class="flex items-center gap-2">
                                                    <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                                        <i class="fas fa-user-slash text-yellow-600 text-sm"></i>
                                                    </div>
                                                    <div>
                                                        <div class="font-medium text-gray-900">Deactivation</div>
                                                        <div class="text-sm text-gray-500">Suspend user</div>
                                                    </div>
                                                </div>
                                            @elseif($request->action_type === 'delete')
                                                <div class="flex items-center gap-2">
                                                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                                        <i class="fas fa-user-times text-red-600 text-sm"></i>
                                                    </div>
                                                    <div>
                                                        <div class="font-medium text-gray-900">Deletion</div>
                                                        <div class="text-sm text-gray-500">Remove user</div>
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    </td>

                                    <!-- Target User -->
                                    <td class="px-6 py-4">
                                        @if($request->isRegistrationRequest())
                                            <div class="flex items-center gap-3">
                                                <div class="w-10 h-10 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center">
                                                    <span class="text-white font-bold text-sm">
                                                        {{ strtoupper(substr($request->requested_first_name, 0, 1) . substr($request->requested_last_name, 0, 1)) }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <div class="font-medium text-gray-900">{{ $request->requested_first_name }} {{ $request->requested_last_name }}</div>
                                                    <div class="text-sm text-gray-500">{{ $request->requested_email }}</div>
                                                    <div class="text-xs text-gray-400">{{ $request->requested_position }} • {{ $request->requested_barangay }}</div>
                                                </div>
                                            </div>
                                        @else
                                            <div class="flex items-center gap-3">
                                                <div class="w-10 h-10 bg-gradient-to-br from-sky-400 to-blue-500 rounded-full flex items-center justify-center">
                                                    <span class="text-white font-bold text-sm">
                                                        {{ strtoupper(substr($request->targetUser->first_name, 0, 1) . substr($request->targetUser->last_name, 0, 1)) }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <div class="font-medium text-gray-900">{{ $request->targetUser->first_name }} {{ $request->targetUser->last_name }}</div>
                                                    <div class="text-sm text-gray-500">{{ $request->targetUser->email }}</div>
                                                    <div class="text-xs text-gray-400">{{ $request->targetUser->position }} • {{ $request->targetUser->barangay }}</div>
                                                </div>
                                            </div>
                                        @endif
                                    </td>

                                    <!-- Status -->
                                    <td class="px-6 py-4">
                                        @if($request->status === 'pending')
                                            <span class="inline-flex items-center gap-1 px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                <div class="w-1.5 h-1.5 bg-yellow-500 rounded-full animate-pulse"></div>
                                                Pending Review
                                            </span>
                                        @elseif($request->status === 'approved')
                                            <span class="inline-flex items-center gap-1 px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <i class="fas fa-check text-xs"></i>
                                                Approved
                                            </span>
                                        @elseif($request->status === 'rejected')
                                            <span class="inline-flex items-center gap-1 px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                <i class="fas fa-times text-xs"></i>
                                                Rejected
                                            </span>
                                        @endif
                                    </td>

                                    <!-- Requested Date -->
                                    <td class="px-6 py-4 text-sm text-gray-900">
                                        {{ $request->created_at->format('M d, Y') }}
                                        <div class="text-xs text-gray-500">{{ $request->created_at->format('H:i') }}</div>
                                    </td>

                                    <!-- Reviewed Date -->
                                    <td class="px-6 py-4 text-sm text-gray-900">
                                        @if($request->reviewed_at)
                                            {{ $request->reviewed_at->format('M d, Y') }}
                                            <div class="text-xs text-gray-500">{{ $request->reviewed_at->format('H:i') }}</div>
                                        @else
                                            <span class="text-gray-400">-</span>
                                        @endif
                                    </td>

                                    <!-- Reviewed By -->
                                    <td class="px-6 py-4 text-sm text-gray-900">
                                        @if($request->reviewedBy)
                                            <div class="flex items-center gap-2">
                                                <div class="w-6 h-6 bg-gradient-to-br from-purple-400 to-indigo-500 rounded-full flex items-center justify-center">
                                                    <span class="text-white font-bold text-xs">
                                                        {{ strtoupper(substr($request->reviewedBy->first_name, 0, 1)) }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <div class="font-medium">{{ $request->reviewedBy->first_name }} {{ $request->reviewedBy->last_name }}</div>
                                                    <div class="text-xs text-gray-500">{{ ucfirst(str_replace('_', ' ', $request->reviewedBy->role)) }}</div>
                                                </div>
                                            </div>
                                        @else
                                            <span class="text-gray-400">-</span>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($requests->hasPages())
                    <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                        {{ $requests->links() }}
                    </div>
                @endif
            @else
                <!-- Empty State -->
                <div class="text-center py-12">
                    <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-clipboard-list text-gray-400 text-3xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No requests yet</h3>
                    <p class="text-gray-500 mb-6">You haven't made any user management requests.</p>
                    <a href="{{ route('user-management.index') }}" 
                       class="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors duration-200">
                        <i class="fas fa-plus"></i>
                        Make a Request
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<script>
// Mark requests as viewed when page loads
document.addEventListener('DOMContentLoaded', function() {
    fetch('{{ route("chairman.requests.mark-viewed") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'Content-Type': 'application/json'
        }
    });
});
</script>
@endsection
