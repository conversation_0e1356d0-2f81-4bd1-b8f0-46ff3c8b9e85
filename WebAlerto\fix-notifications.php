<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\DeviceToken;
use App\Models\Notification;
use App\Services\FCMService;
use Illuminate\Support\Facades\Log;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== WebAlerto Notification System Diagnostic ===\n\n";

// 1. Check Firebase service account file
echo "1. Checking Firebase Configuration...\n";
$serviceAccountPath = storage_path('firebase-service-account.json');
if (file_exists($serviceAccountPath)) {
    echo "✅ Firebase service account file exists\n";
    $serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);
    echo "   Project ID: " . ($serviceAccount['project_id'] ?? 'Not found') . "\n";
} else {
    echo "❌ Firebase service account file NOT found at: $serviceAccountPath\n";
}

// 2. Check CA certificate
echo "\n2. Checking SSL Certificate...\n";
$caCertPath = storage_path('certs/cacert.pem');
if (file_exists($caCertPath)) {
    echo "✅ CA certificate file exists\n";
} else {
    echo "❌ CA certificate file NOT found at: $caCertPath\n";
}

// 3. Check device tokens
echo "\n3. Checking Device Tokens...\n";
$totalTokens = DeviceToken::count();
$activeTokens = DeviceToken::where('is_active', true)->count();
echo "   Total tokens: $totalTokens\n";
echo "   Active tokens: $activeTokens\n";

if ($totalTokens > 0) {
    echo "\n   Token Details:\n";
    DeviceToken::all()->each(function($token) {
        echo "   - ID: {$token->id}, User: {$token->user_id}, Type: {$token->device_type}\n";
        echo "     Active: " . ($token->is_active ? 'Yes' : 'No') . ", Project: " . ($token->project_id ?? 'NULL') . "\n";
        echo "     Token: " . substr($token->token, 0, 20) . "...\n";
    });
}

// 4. Fix missing project_id in device tokens
echo "\n4. Fixing Device Token Project IDs...\n";
$tokensWithoutProject = DeviceToken::whereNull('project_id')->get();
if ($tokensWithoutProject->count() > 0) {
    echo "   Found {$tokensWithoutProject->count()} tokens without project_id\n";
    foreach ($tokensWithoutProject as $token) {
        $token->project_id = config('firebase.projects.app.project_id', 'last-5acaf');
        $token->save();
        echo "   ✅ Updated token ID {$token->id} with project_id: {$token->project_id}\n";
    }
} else {
    echo "   ✅ All tokens have project_id set\n";
}

// 5. Test FCM Service initialization
echo "\n5. Testing FCM Service...\n";
try {
    $fcmService = app(FCMService::class);
    echo "✅ FCM Service initialized successfully\n";
} catch (Exception $e) {
    echo "❌ FCM Service initialization failed: " . $e->getMessage() . "\n";
}

// 6. Check for real FCM tokens (not test tokens)
echo "\n6. Checking for Real FCM Tokens...\n";
$realTokens = DeviceToken::where('is_active', true)
    ->where('token', 'not like', 'test-%')
    ->get();

if ($realTokens->count() > 0) {
    echo "✅ Found {$realTokens->count()} real FCM tokens\n";
} else {
    echo "⚠️  No real FCM tokens found. Only test tokens detected.\n";
    echo "   This means your mobile app hasn't registered a real FCM token yet.\n";
    echo "   Please ensure:\n";
    echo "   - Mobile app is running on a real device (not browser)\n";
    echo "   - FCM permissions are granted\n";
    echo "   - User is logged in to register the token\n";
}

// 7. Test notification creation and sending (if real tokens exist)
if ($realTokens->count() > 0) {
    echo "\n7. Testing Notification Sending...\n";
    
    try {
        // Create a test notification
        $notification = Notification::create([
            'title' => 'Test Notification - ' . date('Y-m-d H:i:s'),
            'message' => 'This is a test notification to verify the FCM system is working.',
            'category' => 'system',
            'severity' => 'low',
            'sent' => false
        ]);
        
        echo "   ✅ Test notification created (ID: {$notification->id})\n";
        
        // Get tokens for sending
        $tokens = $realTokens->pluck('token')->toArray();
        echo "   📱 Sending to " . count($tokens) . " device(s)...\n";
        
        // Send notification
        $result = $fcmService->sendNotification($notification, $tokens);
        
        if ($result['success']) {
            echo "   ✅ Notification sent successfully!\n";
            echo "      Success count: {$result['success_count']}\n";
            echo "      Failure count: {$result['failure_count']}\n";
            echo "      Invalid tokens: {$result['invalid_tokens']}\n";
        } else {
            echo "   ❌ Notification sending failed: {$result['message']}\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ Error testing notification: " . $e->getMessage() . "\n";
    }
} else {
    echo "\n7. Skipping notification test (no real tokens available)\n";
}

echo "\n=== Diagnostic Complete ===\n";

// 8. Recommendations
echo "\n📋 RECOMMENDATIONS:\n";

if (!file_exists($caCertPath)) {
    echo "1. Download CA certificate bundle from https://curl.se/ca/cacert.pem\n";
    echo "   and save it to: $caCertPath\n";
}

if ($realTokens->count() === 0) {
    echo "2. Register a real FCM token from your mobile app:\n";
    echo "   - Open the mobile app on a real Android device\n";
    echo "   - Log in with a user account\n";
    echo "   - Check the app console logs for FCM token registration\n";
    echo "   - Verify the token appears in the database\n";
}

echo "3. Test notifications from the admin panel after fixing the above issues\n";

echo "\n🔧 NEXT STEPS:\n";
echo "1. Run this script again after making changes\n";
echo "2. Check Laravel logs: tail -f storage/logs/laravel.log\n";
echo "3. Test sending notifications from admin panel\n";

echo "\n";
