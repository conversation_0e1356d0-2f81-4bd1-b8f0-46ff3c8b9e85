{"version": 3, "sources": ["../../../../../../node_modules/@capacitor/haptics/dist/esm/index.js"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\nconst Haptics = registerPlugin('Haptics', {\n  web: () => import('./web').then(m => new m.HapticsWeb())\n});\nexport * from './definitions';\nexport { Haptics };\n"], "mappings": ";;;;;;;;;;AACA,IAAM,UAAU,eAAe,WAAW;AAAA,EACxC,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,WAAW,CAAC;AACzD,CAAC;", "names": []}