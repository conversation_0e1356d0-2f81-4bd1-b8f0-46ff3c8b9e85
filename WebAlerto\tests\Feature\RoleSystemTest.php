<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;

class RoleSystemTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test System Administrator role functionality
     */
    public function test_system_admin_role_functionality()
    {
        $systemAdmin = User::create([
            'first_name' => 'System',
            'last_name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'system_admin',
            'position' => 'Technical Administrator',
            'barangay' => 'N/A',
            'status' => 'Active',
        ]);

        // Test role identification methods
        $this->assertTrue($systemAdmin->isSystemAdmin());
        $this->assertFalse($systemAdmin->isSuperAdmin());
        $this->assertFalse($systemAdmin->isAdmin());

        // Test permissions
        $this->assertTrue($systemAdmin->canManageUserAccounts());
        $this->assertTrue($systemAdmin->canViewCityWideData());
        $this->assertTrue($systemAdmin->hasTechnicalAccess());
        $this->assertTrue($systemAdmin->hasCityWideAccess());

        // Test role display
        $this->assertEquals('Technical Administrator', $systemAdmin->getRoleDisplayName());
        $this->assertStringContains('System configurations', $systemAdmin->getAccessLevelDescription());
    }

    /**
     * Test Super Administrator (CDRRMC) role functionality
     */
    public function test_super_admin_cdrrmc_role_functionality()
    {
        $superAdmin = User::create([
            'first_name' => 'CDRRMC',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'super_admin',
            'position' => 'CDRRMC Officer',
            'barangay' => 'Cebu City',
            'status' => 'Active',
        ]);

        // Test role identification methods
        $this->assertFalse($superAdmin->isSystemAdmin());
        $this->assertTrue($superAdmin->isSuperAdmin());
        $this->assertFalse($superAdmin->isAdmin());

        // Test permissions
        $this->assertFalse($superAdmin->canManageUserAccounts());
        $this->assertTrue($superAdmin->canViewCityWideData());
        $this->assertTrue($superAdmin->canManageDisasterReports());
        $this->assertFalse($superAdmin->hasTechnicalAccess());
        $this->assertTrue($superAdmin->hasCityWideAccess());

        // Test role display
        $this->assertEquals('CDRRMC (City-Level)', $superAdmin->getRoleDisplayName());
        $this->assertStringContains('City-wide disaster monitoring', $superAdmin->getAccessLevelDescription());
    }

    /**
     * Test Administrator (BDRRMC) role functionality
     */
    public function test_admin_bdrrmc_role_functionality()
    {
        $admin = User::create([
            'first_name' => 'BDRRMC',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'position' => 'Chairman',
            'barangay' => 'Lahug',
            'status' => 'Active',
        ]);

        // Test role identification methods
        $this->assertFalse($admin->isSystemAdmin());
        $this->assertFalse($admin->isSuperAdmin());
        $this->assertTrue($admin->isAdmin());
        $this->assertTrue($admin->isChairman());

        // Test permissions
        $this->assertFalse($admin->canManageUserAccounts());
        $this->assertFalse($admin->canViewCityWideData());
        $this->assertTrue($admin->canManageDisasterReports());
        $this->assertFalse($admin->hasTechnicalAccess());
        $this->assertFalse($admin->hasCityWideAccess());

        // Test barangay access
        $this->assertTrue($admin->canAccessBarangay('Lahug'));
        $this->assertFalse($admin->canAccessBarangay('Mabolo'));

        // Test role display
        $this->assertEquals('BDRRMC (Barangay-Level)', $admin->getRoleDisplayName());
        $this->assertStringContains('Barangay-specific disaster management', $admin->getAccessLevelDescription());
    }

    /**
     * Test role responsibilities
     */
    public function test_role_responsibilities()
    {
        $systemAdmin = User::factory()->create(['role' => 'system_admin']);
        $superAdmin = User::factory()->create(['role' => 'super_admin']);
        $admin = User::factory()->create(['role' => 'admin']);

        // Test System Administrator responsibilities
        $systemAdminResponsibilities = $systemAdmin->getResponsibilities();
        $this->assertContains('Creates and manages user accounts (CDRRMC, BDRRMC)', $systemAdminResponsibilities);
        $this->assertContains('Handles system configurations and access control', $systemAdminResponsibilities);

        // Test Super Administrator (CDRRMC) responsibilities
        $superAdminResponsibilities = $superAdmin->getResponsibilities();
        $this->assertContains('Full access to all barangays\' disaster reports and data', $superAdminResponsibilities);
        $this->assertContains('Monitors and analyzes disaster incidents across Cebu City', $superAdminResponsibilities);

        // Test Administrator (BDRRMC) responsibilities
        $adminResponsibilities = $admin->getResponsibilities();
        $this->assertContains('Manages disaster reports within their own barangay only', $adminResponsibilities);
        $this->assertContains('Cannot view or edit data from other barangays', $adminResponsibilities);
    }

    /**
     * Test user management permissions
     */
    public function test_user_management_permissions()
    {
        $systemAdmin = User::factory()->create(['role' => 'system_admin']);
        $superAdmin = User::factory()->create(['role' => 'super_admin']);
        $admin = User::factory()->create(['role' => 'admin', 'barangay' => 'Lahug']);
        $otherAdmin = User::factory()->create(['role' => 'admin', 'barangay' => 'Mabolo']);

        // System Administrator can manage all users
        $this->assertTrue($systemAdmin->canManageUser($superAdmin));
        $this->assertTrue($systemAdmin->canManageUser($admin));
        $this->assertTrue($systemAdmin->canManageUser($otherAdmin));

        // Super Administrator can manage admin users but not system admins
        $this->assertFalse($superAdmin->canManageUser($systemAdmin));
        $this->assertTrue($superAdmin->canManageUser($admin));
        $this->assertTrue($superAdmin->canManageUser($otherAdmin));

        // Admin users have limited management capabilities
        $this->assertFalse($admin->canManageUser($systemAdmin));
        $this->assertFalse($admin->canManageUser($superAdmin));
        $this->assertFalse($admin->canManageUser($otherAdmin)); // Different barangay
    }

    /**
     * Test access control for different roles
     */
    public function test_access_control()
    {
        $systemAdmin = User::factory()->create(['role' => 'system_admin']);
        $superAdmin = User::factory()->create(['role' => 'super_admin']);
        $admin = User::factory()->create(['role' => 'admin', 'barangay' => 'Lahug']);

        // Test barangay access
        $this->assertTrue($systemAdmin->canAccessBarangay('Lahug'));
        $this->assertTrue($systemAdmin->canAccessBarangay('Mabolo'));
        
        $this->assertTrue($superAdmin->canAccessBarangay('Lahug'));
        $this->assertTrue($superAdmin->canAccessBarangay('Mabolo'));
        
        $this->assertTrue($admin->canAccessBarangay('Lahug'));
        $this->assertFalse($admin->canAccessBarangay('Mabolo'));

        // Test city-wide access
        $this->assertTrue($systemAdmin->hasCityWideAccess());
        $this->assertTrue($superAdmin->hasCityWideAccess());
        $this->assertFalse($admin->hasCityWideAccess());
    }
}
