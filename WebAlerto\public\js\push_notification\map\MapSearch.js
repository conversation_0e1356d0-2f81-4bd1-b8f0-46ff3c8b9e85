export class MapSearch {
    constructor(mapManager) {
        this.mapManager = mapManager;
        this.mapboxAccessToken = 'pk.eyJ1IjoianVucmVsMDcwNDA1IiwiYSI6ImNtYjNocGs1YjBxc2cydnB5OG14NmNzYTIifQ.FGsozY9ibdn28Rg91_msIg';
        this.searchUrl = 'https://api.mapbox.com/geocoding/v5/mapbox.places';
        this.reverseUrl = 'https://api.mapbox.com/geocoding/v5/mapbox.places';
    }

    async searchLocation(query) {
        try {
            // Use Mapbox Geocoding API
            const searchUrl = `${this.searchUrl}/${encodeURIComponent(query)}.json`;
            const params = new URLSearchParams({
                access_token: this.mapboxAccessToken,
                country: 'ph',
                limit: '5',
                language: 'en'
            });

            const response = await fetch(`${searchUrl}?${params}`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) throw new Error('Search failed');
            const data = await response.json();

            // Transform Mapbox response to match expected format
            return data.features.map(feature => ({
                osm_id: feature.id,
                display_name: feature.place_name,
                lat: feature.center[1],
                lon: feature.center[0],
                geojson: feature.geometry,
                boundingbox: feature.bbox
            }));
        } catch (error) {
            console.error('Search error:', error);
            throw error;
        }
    }

    async reverseGeocode(lat, lon) {
        try {
            // Use Mapbox Reverse Geocoding API
            const searchUrl = `${this.reverseUrl}/${lon},${lat}.json`;
            const params = new URLSearchParams({
                access_token: this.mapboxAccessToken,
                language: 'en'
            });

            const response = await fetch(`${searchUrl}?${params}`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) throw new Error('Reverse geocoding failed');
            const data = await response.json();

            // Transform Mapbox response to match expected format
            if (data.features && data.features.length > 0) {
                const feature = data.features[0];
                return {
                    display_name: feature.place_name,
                    lat: feature.center[1],
                    lon: feature.center[0],
                    address: feature.properties
                };
            }
            return null;
        } catch (error) {
            console.error('Reverse geocoding error:', error);
            throw error;
        }
    }
}
