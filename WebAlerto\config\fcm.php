<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Firebase Cloud Messaging Configuration
    |--------------------------------------------------------------------------
    */

    'credentials' => [
        'file' => env('FIREBASE_CREDENTIALS', storage_path('firebase-credentials.json')),
    ],

    'project_id' => env('FIREBASE_PROJECT_ID'),
    'api_key' => env('FIREBASE_API_KEY'),
    'database_url' => env('FIREBASE_DATABASE_URL'),
    'storage_bucket' => env('FIREBASE_STORAGE_BUCKET'),

    'messaging' => [
        'default_ttl' => 3600,
        'default_android_ttl' => 7200,
        'default_apns_ttl' => 3600,
    ],

    'rate_limits' => [
        'test_notifications' => env('FCM_TEST_RATE_LIMIT', 10),
        'bulk_notifications' => env('FCM_BULK_RATE_LIMIT', 100),
        'window' => env('FCM_RATE_LIMIT_WINDOW', 60), // in seconds
    ],

    'max_tokens' => env('FCM_MAX_TOKENS', 500),

    'timeouts' => [
        'default' => env('FCM_TIMEOUT', 30),
        'connect' => env('FCM_CONNECT_TIMEOUT', 10),
    ],

    'retry' => [
        'max_attempts' => env('FCM_RETRY_MAX_ATTEMPTS', 3),
        'delay' => env('FCM_RETRY_DELAY', 1000), // in milliseconds
    ],

    'logging' => [
        'channel' => env('FCM_LOG_CHANNEL', 'stack'),
        'level' => env('FCM_LOG_LEVEL', 'info'),
    ],
]; 