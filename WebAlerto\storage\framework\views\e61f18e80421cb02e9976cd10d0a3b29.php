<?php $__env->startSection('title', 'Edit Evacuation Center'); ?>

<?php $__env->startSection('content'); ?>

<!-- Add error message container -->
<div id="mapError" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-xl relative mb-4 z-[1000]" role="alert">
    <strong class="font-bold">Error!</strong>
    <span class="block sm:inline">Failed to load the map. Please check your internet connection and try again.</span>
    <button onclick="retryMapLoad()" class="absolute top-0 bottom-0 right-0 px-4 py-3">
        <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
            <title>Retry</title>
            <path d="M14.66 15.66A8 8 0 1 1 17 10h-2a6 6 0 1 0-1.76 4.24l1.42 1.42zM12 10h8l-4 4-4-4z"/>
        </svg>
    </button>
</div>

<div class="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-sky-100 py-6 sm:py-8">
    <div class="max-w-5xl mx-auto px-2 sm:px-4 md:px-6 lg:px-8">
        <!-- Back Button and Title -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-4 sm:p-6 mb-8">
            <div class="flex items-center gap-3 sm:gap-4">
                <a href="<?php echo e(route('components.evacuation_management.evacuation-dashboard')); ?>" 
                   class="p-2 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg text-white hover:from-sky-600 hover:to-blue-700 transition-all">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                </a>
                <div>
                    <h1 class="text-lg sm:text-3xl md:text-4xl font-extrabold text-gray-900">Edit Evacuation Center</h1>
                    <p class="text-gray-600 mt-1 text-xs sm:text-base">Update evacuation center details</p>
                </div>
            </div>
        </div>

        <?php if($errors->any()): ?>
            <div class="mb-6 bg-red-50/80 backdrop-blur-sm border-l-4 border-red-500 p-4 rounded-xl shadow-lg">
                <div class="flex items-center mb-2">
                    <svg class="h-5 w-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <h3 class="text-red-800 font-medium">Please correct the following errors:</h3>
                </div>
                <ul class="list-disc pl-5 space-y-1">
                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li class="text-red-700"><?php echo e($error); ?></li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php
            $disasterTypes = old('disaster_type', $evacuationCenter->disaster_type);
            $isOthersChecked = is_array($disasterTypes) && in_array('Others', $disasterTypes);
            
            $otherDisasterText = '';
            if (is_array($disasterTypes)) {
                foreach ($disasterTypes as $type) {
                    if (strpos($type, 'Others:') === 0) {
                        $otherDisasterText = trim(str_replace('Others:', '', $type));
                        $isOthersChecked = true;
                        break;
                    }
                }
            }
        ?>

        <!-- Main Form -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-4 sm:p-8">
            <div class="max-w-3xl mx-auto">
                <div class="flex items-center gap-3 sm:gap-6 mb-6 sm:mb-8">
                    <div class="p-2 sm:p-3 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg text-white text-base sm:text-xl font-semibold w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div>
                        <h2 class="text-base sm:text-2xl font-bold text-gray-900">Center Information</h2>
                        <p class="text-gray-600 mt-1 text-xs sm:text-base">Update the details of the evacuation center</p>
                    </div>
                </div>

                <form id="evacuationCenterForm" action="<?php echo e(route('components.evacuation_management.update', $evacuationCenter->id)); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>
                    <input type="hidden" id="building_name" name="building_name" value="<?php echo e(old('building_name', $evacuationCenter->building_name ?? '')); ?>">
                    
                    <div class="grid grid-cols-1 gap-4 sm:gap-8">
                        <div class="bg-sky-50 p-4 sm:p-6 rounded-lg border border-sky-100">
                            <label for="name" class="block text-base sm:text-xl font-semibold text-sky-600 mb-2 sm:mb-3">Center Name</label>
                            <input type="text" id="name" name="name" 
                                   class="w-full rounded-lg border-2 border-gray-300 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-base sm:text-lg py-2 sm:py-4 px-3 sm:px-4 bg-white" 
                                   required value="<?php echo e(old('name', $evacuationCenter->name)); ?>"
                                   placeholder="Enter center name">
                        </div>
                        <div class="bg-sky-50 p-4 sm:p-6 rounded-lg border border-sky-100">
                            <label for="capacity" class="block text-base sm:text-xl font-semibold text-sky-600 mb-2 sm:mb-3">Capacity</label>
                            <input type="number" id="capacity" name="capacity" 
                                   class="w-full rounded-lg border-2 border-gray-300 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-base sm:text-lg py-2 sm:py-4 px-3 sm:px-4 bg-white" 
                                   required value="<?php echo e(old('capacity', $evacuationCenter->capacity)); ?>"
                                   placeholder="Enter capacity">
                        </div>
                        <div class="bg-sky-50 p-4 sm:p-6 rounded-lg border border-sky-100">
                            <div class="flex justify-between items-center mb-2 sm:mb-3">
                                <label class="block text-base sm:text-xl font-semibold text-sky-600">Contact Numbers</label>
                                <button type="button" id="addContactBtn"
                                        class="px-2 py-1 text-xs sm:px-3 sm:py-1 sm:text-sm bg-sky-500 text-white rounded-lg hover:bg-sky-600 transition-colors">
                                    <i class="fas fa-plus mr-1"></i> Add Contact
                                </button>
                            </div>
                            <div id="contactContainer">
                                <?php
                                    $contacts = old('contact_numbers') ?
                                        array_map(function($number, $index) {
                                            return [
                                                'number' => $number,
                                                'network' => old('contact_networks')[$index] ?? 'Globe'
                                            ];
                                        }, old('contact_numbers'), array_keys(old('contact_numbers'))) :
                                        (is_array($evacuationCenter->contact) ? $evacuationCenter->contact :
                                        [['number' => $evacuationCenter->contact, 'network' => 'Unknown']]);
                                ?>

                                <?php $__currentLoopData = $contacts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $contact): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="contact-entry mb-3" data-index="<?php echo e($index); ?>">
                                    <div class="flex items-stretch gap-2 sm:gap-3 relative">
                                        <input type="text" name="contact_numbers[]"
                                               class="w-full rounded-lg border-2 border-gray-300 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-sm sm:text-lg px-2 sm:px-4 h-10 appearance-none leading-tight bg-white contact-number"
                                               required value="<?php echo e($contact['number']); ?>"
                                               placeholder="Enter contact number">
                                        <div class="relative w-full">
                                            <select name="contact_networks[]"
                                                    class="w-full rounded-lg border-2 border-gray-300 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-sm sm:text-lg pr-8 px-2 sm:px-4 h-10 appearance-none leading-tight bg-white">
                                                <option value="Globe" <?php echo e($contact['network'] == 'Globe' ? 'selected' : ''); ?>>Globe</option>
                                                <option value="Smart" <?php echo e($contact['network'] == 'Smart' ? 'selected' : ''); ?>>Smart</option>
                                                <option value="Sun" <?php echo e($contact['network'] == 'Sun' ? 'selected' : ''); ?>>Sun</option>
                                                <option value="TM" <?php echo e($contact['network'] == 'TM' ? 'selected' : ''); ?>>TM</option>
                                                <option value="TNT" <?php echo e($contact['network'] == 'TNT' ? 'selected' : ''); ?>>TNT</option>
                                                <option value="DITO" <?php echo e($contact['network'] == 'DITO' ? 'selected' : ''); ?>>DITO</option>
                                            </select>
                                            <div class="pointer-events-none absolute inset-y-0 right-2 flex items-center">
                                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                        
                        <div class="bg-sky-50 p-3 sm:p-6 rounded-lg border border-sky-100 disaster-type-container">
                            <label class="block text-sm sm:text-xl font-semibold text-sky-600 mb-1 sm:mb-3">Disaster Type</label>
                            <div class="space-y-2 sm:space-y-4">
                                <div class="flex items-center gap-2 sm:gap-3">
                                    <input type="checkbox" id="typhoon" name="disaster_type[]" value="Typhoon" class="h-4 w-4 sm:h-5 sm:w-5 text-green-600 border-gray-300 rounded focus:ring-green-500" <?php echo e(is_array($disasterTypes) && in_array('Typhoon', $disasterTypes) ? 'checked' : ''); ?>>
                                    <label for="typhoon" class="text-sm sm:text-lg text-gray-800">Typhoon <span class="inline-block w-3 h-3 sm:w-4 sm:h-4 rounded-full align-middle ml-1" style="background-color: #22c55e;"></span></label>
                                </div>
                                <div class="flex items-center gap-2 sm:gap-3">
                                    <input type="checkbox" id="flood" name="disaster_type[]" value="Flood" class="h-4 w-4 sm:h-5 sm:w-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500" <?php echo e(is_array($disasterTypes) && in_array('Flood', $disasterTypes) ? 'checked' : ''); ?>>
                                    <label for="flood" class="text-sm sm:text-lg text-gray-800">Flood <span class="inline-block w-3 h-3 sm:w-4 sm:h-4 rounded-full align-middle ml-1" style="background-color: #3b82f6;"></span></label>
                                </div>
                                <div class="flex items-center gap-2 sm:gap-3">
                                    <input type="checkbox" id="fire" name="disaster_type[]" value="Fire" class="h-4 w-4 sm:h-5 sm:w-5 text-red-600 border-gray-300 rounded focus:ring-red-500" <?php echo e(is_array($disasterTypes) && in_array('Fire', $disasterTypes) ? 'checked' : ''); ?>>
                                    <label for="fire" class="text-sm sm:text-lg text-gray-800">Fire <span class="inline-block w-3 h-3 sm:w-4 sm:h-4 rounded-full align-middle ml-1" style="background-color: #ef4444;"></span></label>
                                </div>
                                <div class="flex items-center gap-2 sm:gap-3">
                                    <input type="checkbox" id="earthquake" name="disaster_type[]" value="Earthquake" class="h-4 w-4 sm:h-5 sm:w-5 text-orange-500 border-gray-300 rounded focus:ring-orange-500" <?php echo e(is_array($disasterTypes) && in_array('Earthquake', $disasterTypes) ? 'checked' : ''); ?>>
                                    <label for="earthquake" class="text-sm sm:text-lg text-gray-800">Earthquake <span class="inline-block w-3 h-3 sm:w-4 sm:h-4 rounded-full align-middle ml-1" style="background-color: #f59e42;"></span></label>
                                </div>
                                <div class="flex items-center gap-2 sm:gap-3">
                                    <input type="checkbox" id="landslide" name="disaster_type[]" value="Landslide" class="h-4 w-4 sm:h-5 sm:w-5 text-yellow-700 border-gray-300 rounded focus:ring-yellow-700" <?php echo e(is_array($disasterTypes) && in_array('Landslide', $disasterTypes) ? 'checked' : ''); ?>>
                                    <label for="landslide" class="text-sm sm:text-lg text-gray-800">Landslide <span class="inline-block w-3 h-3 sm:w-4 sm:h-4 rounded-full align-middle ml-1" style="background-color: #a16207;"></span></label>
                                </div>
                                <div class="flex items-center gap-2 sm:gap-3">
                                    <input type="checkbox" id="others" name="disaster_type[]" value="Others" class="h-4 w-4 sm:h-5 sm:w-5 text-purple-600 border-gray-300 rounded focus:ring-purple-500" <?php echo e($isOthersChecked ? 'checked' : ''); ?>>
                                    <label for="others" class="text-sm sm:text-lg text-gray-800">Others <span class="inline-block w-3 h-3 sm:w-4 sm:h-4 rounded-full align-middle ml-1" style="background-color: #9333ea;"></span></label>
                                </div>
                                <!-- Custom disaster type input field -->
                                <div id="customDisasterInput" class="mt-2 sm:mt-3 <?php echo e($isOthersChecked ? '' : 'hidden'); ?>">
                                    <label for="custom_disaster_type" class="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2">Specify Disaster Type:</label>
                                    <input type="text" id="custom_disaster_type" name="other_disaster_type" 
                                           class="w-full rounded-lg border-2 border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 text-sm sm:text-base py-2 px-3 bg-white" 
                                           placeholder="Enter custom disaster type (e.g., Volcanic Eruption, Tsunami)"
                                           value="<?php echo e(old('other_disaster_type', $otherDisasterText)); ?>">
                                </div>
                            </div>
                            <div id="selectedDisasters" class="flex flex-wrap gap-2 mt-2"></div>
                            <p class="text-xs sm:text-sm text-gray-500 mt-2">If more than one disaster is selected, this center will use a <span class="inline-block w-3 h-3 sm:w-4 sm:h-4 rounded-full align-middle" style="background-color: #6b7280;"></span> gray marker on the map.</p>
                            <p class="text-xs sm:text-sm text-gray-500 mt-2">If "Others" is selected alone, this center will use a <span class="inline-block w-3 h-3 sm:w-4 sm:h-4 rounded-full align-middle" style="background-color: #9333ea;"></span> purple marker on the map.</p>
                        </div>

                        <div class="bg-sky-50 p-4 sm:p-6 rounded-lg border border-sky-100">
                            <label for="status" class="block text-base sm:text-xl font-semibold text-sky-600 mb-2 sm:mb-3">Status</label>
                            <select id="status" name="status"
                                    class="w-full rounded-lg border-2 border-gray-300 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-sm sm:text-lg py-2 sm:py-4 px-2 sm:px-4 h-9 sm:h-auto bg-white" required>
                                <option value="Active"
                                    <?php echo e(old('status', $evacuationCenter->status) == 'Active' ? 'selected' : ''); ?>>
                                    Active
                                </option>
                                <option value="Inactive"
                                    <?php echo e(old('status', $evacuationCenter->status) == 'Inactive' ? 'selected' : ''); ?>>
                                    Inactive
                                </option>
                                <option value="Under Maintenance"
                                    <?php echo e(old('status', $evacuationCenter->status) == 'Under Maintenance' ? 'selected' : ''); ?>>
                                    Under Maintenance
                                </option>
                            </select>
                        </div>
                    </div>

                    <!-- Location Section -->
                    <div class="space-y-4 sm:space-y-8 mt-8">

                        <!-- Map Container -->
                        <div class="bg-sky-50 p-3 sm:p-6 border border-sky-100 relative">
                            <label class="block text-sm sm:text-xl font-semibold text-sky-600 mb-1 sm:mb-3">Current Location (Read-Only)</label>
                            <div class="overflow-hidden border-2 border-gray-300 relative">
                                <div id="map" class="h-48 sm:h-[400px] w-full relative z-[10]" style="visibility: visible; display: block;">
                                    <div id="mapLoadingIndicator" class="absolute inset-0 bg-white flex items-center justify-center z-[15]">
                                        <div class="text-center">
                                            <div class="animate-spin h-8 w-8 sm:h-12 sm:w-12 border-4 border-sky-500 border-t-transparent"></div>
                                            <p class="text-gray-600 mt-2 text-sm sm:text-lg">Loading map...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-2 sm:mt-4 space-y-1 sm:space-y-2 text-xs sm:text-gray-600">
                                <p class="flex items-center gap-1 sm:gap-2">
                                    <svg class="h-3 w-3 sm:h-5 sm:w-5 text-sky-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    </svg>
                                    This shows the current fixed location of the evacuation center
                                </p>
                            </div>
                        </div>

                        <!-- Location Details Card -->

                        <div class="bg-sky-50 p-6 rounded-lg border border-sky-100">
                            <label class="block text-xl font-semibold text-gray-900 mb-3">Selected Location Details</label>
                            <div class="space-y-4">
                                <p id="selectedAddress" class="text-lg text-gray-700 bg-white p-4 rounded-lg border-2 border-gray-300">
                                    <?php if(old('barangay', $evacuationCenter->barangay) || old('city', $evacuationCenter->city) || old('province', $evacuationCenter->province)): ?>
                                        <?php
                                            $addressParts = array_filter([
                                                old('street_name', $evacuationCenter->street_name),
                                                old('barangay', $evacuationCenter->barangay),
                                                old('city', $evacuationCenter->city),
                                                old('province', $evacuationCenter->province)
                                            ]);
                                        ?>
                                        <?php echo e(implode(', ', $addressParts)); ?>

                                    <?php else: ?>
                                        No location selected yet
                                    <?php endif; ?>
                                </p>
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="bg-white p-4 rounded-lg border-2 border-gray-300">
                                        <label class="block text-sm font-medium text-gray-500 mb-1">Latitude</label>
                                        <p id="selectedLatitude" class="text-lg text-gray-700">

                                            <?php if(old('latitude', $evacuationCenter->latitude)): ?>
                                                <?php echo e(old('latitude', $evacuationCenter->latitude)); ?>

                                            <?php else: ?>
                                                --
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                    <div class="bg-gray-100 p-3 rounded-lg border-2 border-gray-300">
                                        <label class="block text-xs font-medium text-gray-500 mb-1">Longitude</label>
                                        <p class="text-sm text-gray-700">
                                            <?php if(old('longitude', $evacuationCenter->longitude)): ?>
                                                <?php echo e(old('longitude', $evacuationCenter->longitude)); ?>

                                            <?php else: ?>
                                                --
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Hidden form fields for location data -->
                        <input type="hidden" id="latitude" name="latitude" required value="<?php echo e(old('latitude', $evacuationCenter->latitude)); ?>">
                        <input type="hidden" id="longitude" name="longitude" required value="<?php echo e(old('longitude', $evacuationCenter->longitude)); ?>">

                        <input type="hidden" id="street_name" name="street_name" value="<?php echo e(old('street_name', $evacuationCenter->street_name)); ?>">

                        <input type="hidden" id="barangay" name="barangay" required value="<?php echo e(old('barangay', $evacuationCenter->barangay)); ?>">
                        <input type="hidden" id="city" name="city" required value="<?php echo e(old('city', $evacuationCenter->city)); ?>">
                        <input type="hidden" id="province" name="province" required value="<?php echo e(old('province', $evacuationCenter->province)); ?>">
                        <input type="hidden" id="postal_code" name="postal_code" required value="<?php echo e(old('postal_code', $evacuationCenter->postal_code)); ?>">
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-end gap-4 pt-4 sm:pt-6">
                        <a href="<?php echo e(route('components.evacuation_management.evacuation-dashboard')); ?>"
                           class="px-3 py-2 text-sm sm:px-6 sm:py-3 sm:text-base border-2 border-sky-200 text-sky-700 font-medium rounded-xl hover:bg-sky-50 transition-all duration-200">
                            Cancel
                        </a>
                        <button type="submit" id="saveButton"
                                class="px-3 py-2 text-sm sm:px-6 sm:py-3 sm:text-base bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white font-medium rounded-xl shadow-lg transition-all duration-200 transform hover:scale-105 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            Update Center
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
      integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
      crossorigin=""/>
<style>
    /* Ensure map container has proper dimensions */
    #map {
        height: 400px !important;
        width: 100% !important;
        position: relative !important;
        z-index: 10 !important;
    }

    /* Fix any potential CSS conflicts */
    .leaflet-container {
        height: 400px !important;
        width: 100% !important;
    }

    /* Ensure map tiles load properly */
    .leaflet-tile {
        max-width: none !important;
    }
</style>
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
        integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
        crossorigin=""></script>

<script>
    // Pass data from PHP to our external JavaScript file
    window.EvacuationCenterData = {
        userRole: '<?php echo e(auth()->user()->role); ?>',
        isAdmin: <?php echo e(auth()->user()->hasRole('admin') ? 'true' : 'false'); ?>,
        isSuperAdmin: <?php echo e(auth()->user()->hasRole('super_admin') ? 'true' : 'false'); ?>,
        isSystemAdmin: <?php echo e(auth()->user()->hasRole('system_admin') ? 'true' : 'false'); ?>,
        userBarangay: '<?php echo e(auth()->user()->barangay ?? ""); ?>',
        userCity: '<?php echo e(auth()->user()->city ?? ""); ?>',
        userCityName: '<?php echo e(auth()->user()->city ? (is_numeric(auth()->user()->city) ? app(\App\Services\BarangayService::class)->getCityNameFromCode(auth()->user()->city) ?? "" : auth()->user()->city) : ""); ?>',
        cityCoordinates: {
            lat: <?php echo e($cityCoordinates['lat'] ?? 10.3157); ?>,
            lng: <?php echo e($cityCoordinates['lng'] ?? 123.8854); ?>

        },
        hasOldLocation: true,
        oldLatitude: '<?php echo e(old('latitude', $evacuationCenter->latitude)); ?>',
        oldLongitude: '<?php echo e(old('longitude', $evacuationCenter->longitude)); ?>',
        isEditPage: true,
        isLocationReadOnly: true
    };
    
    // Force map to initialize properly on page load
    window.addEventListener('load', function() {
        // Small delay to ensure DOM is fully rendered
        setTimeout(function() {
            if (typeof map !== 'undefined' && map) {
                map.invalidateSize();
                if (marker) {
                    map.panTo(marker.getLatLng());
                }
            }
        }, 500);
    });
</script>
<script src="<?php echo e(asset('js/evac_management/evacuationCenter.js')); ?>" defer></script>

<?php $__env->stopSection(); ?>


<?php echo $__env->make('layout.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\laravel\WebAlerto\resources\views/components/evacuation_management/edit-evacuation-center.blade.php ENDPATH**/ ?>