{"version": 3, "sources": ["../../../../../../node_modules/@capacitor/haptics/dist/esm/definitions.js"], "sourcesContent": ["export var ImpactStyle;\n(function (ImpactStyle) {\n  /**\n   * A collision between large, heavy user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Heavy\"] = \"HEAVY\";\n  /**\n   * A collision between moderately sized user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Medium\"] = \"MEDIUM\";\n  /**\n   * A collision between small, light user interface elements\n   *\n   * @since 1.0.0\n   */\n  ImpactStyle[\"Light\"] = \"LIGHT\";\n})(ImpactStyle || (ImpactStyle = {}));\nexport var NotificationType;\n(function (NotificationType) {\n  /**\n   * A notification feedback type indicating that a task has completed successfully\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Success\"] = \"SUCCESS\";\n  /**\n   * A notification feedback type indicating that a task has produced a warning\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Warning\"] = \"WARNING\";\n  /**\n   * A notification feedback type indicating that a task has failed\n   *\n   * @since 1.0.0\n   */\n  NotificationType[\"Error\"] = \"ERROR\";\n})(NotificationType || (NotificationType = {}));\n"], "mappings": ";AAAO,IAAI;AAAA,CACV,SAAUA,cAAa;AAMtB,EAAAA,aAAY,OAAO,IAAI;AAMvB,EAAAA,aAAY,QAAQ,IAAI;AAMxB,EAAAA,aAAY,OAAO,IAAI;AACzB,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAC7B,IAAI;AAAA,CACV,SAAUC,mBAAkB;AAM3B,EAAAA,kBAAiB,SAAS,IAAI;AAM9B,EAAAA,kBAAiB,SAAS,IAAI;AAM9B,EAAAA,kBAAiB,OAAO,IAAI;AAC9B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;", "names": ["ImpactStyle", "NotificationType"]}