@extends('layout.app')

@section('title', $title)

@section('content')
<div class="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-sky-100 py-8">
    <div class="max-w-7xl mx-auto px-2 sm:px-4 md:px-6 lg:px-8">
        <!-- Header Section -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-4 sm:p-6 mb-8">
            <div class="flex items-center gap-3 sm:gap-4">
                <a href="{{ route('components.evacuation_management.evacuation-dashboard') }}"
                   class="p-2 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg text-white hover:from-sky-600 hover:to-blue-700 transition-all">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                </a>
                <div>
                    <h1 class="text-lg sm:text-3xl md:text-4xl font-extrabold text-gray-900">{{ $title }}</h1>
                    <p class="text-gray-600 mt-1 text-xs sm:text-base">Manage evacuation centers</p>
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        @if(auth()->user()->hasRole('super_admin') || auth()->user()->hasRole('system_admin'))
        <div class="bg-white/90 backdrop-blur-sm rounded-xl shadow-xl border border-sky-200 p-6 mb-6">
            <!-- Header -->
            <div class="flex items-center gap-3 mb-6">
                <div class="p-3 bg-gradient-to-br from-sky-500 to-blue-600 rounded-lg shadow-lg">
                    <i class="fas fa-filter text-white text-xl"></i>
                </div>
                <div>
                    <h2 class="text-xl font-bold text-gray-900">Filter Centers</h2>
                    <p class="text-sm text-gray-600 mt-1">Filter {{ strtolower($title) }} by barangay</p>
                </div>
            </div>

            <!-- Filter Controls -->
            <form action="{{ route('components.evacuation_management.centers-list', ['type' => $type ?? 'all']) }}" method="GET">
                <div class="grid grid-cols-1 gap-4">
                    <!-- Barangay Filter -->
                    <div class="relative">
                        <label for="barangay" class="block text-sm font-semibold text-sky-600 mb-2">Filter by Barangay</label>
                        <select name="barangay" id="barangay"
                                class="w-full rounded-lg border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 bg-white/90 py-3 px-4 text-base text-gray-700 font-medium transition-all duration-200 hover:border-blue-400"
                                onchange="this.form.submit()">
                            <option value="">All Barangays</option>
                            @foreach($barangays as $barangay)
                                <option value="{{ $barangay }}" {{ $selectedBarangay == $barangay ? 'selected' : '' }}>
                                    {{ $barangay }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </form>
        </div>
        @else
        <!-- BDRRMC users see only their barangay data - no filter needed -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div class="flex items-center">
                <i class="fas fa-map-marker-alt text-blue-600 mr-2"></i>
                <span class="text-sm font-medium text-blue-800">
                    Showing {{ strtolower($title) }} for: <strong>{{ auth()->user()->barangay }}</strong>
                </span>
            </div>
        </div>
        @endif

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            @forelse ($centers as $center)
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 overflow-hidden transform transition-all duration-200 hover:scale-105 h-full flex flex-col justify-between" data-center-id="{{ $center->id }}">
                    <div class="p-6 flex-1 flex flex-col">
                        <div class="flex justify-between items-start mb-4">
                            <h3 class="text-xl font-bold text-gray-900">{{ $center->name }}</h3>
                            <span class="inline-flex items-center px-3 py-1 rounded-xl text-sm font-medium
                                @if($center->status === 'Active')
                                    bg-green-100 text-green-800
                                @elseif($center->status === 'Under Maintenance')
                                    bg-yellow-100 text-yellow-800
                                @else
                                    bg-red-100 text-red-800
                                @endif
                            ">
                                <i class="fas
                                    @if($center->status === 'Active')
                                        fa-check-circle
                                    @elseif($center->status === 'Under Maintenance')
                                        fa-wrench
                                    @else
                                        fa-times-circle
                                    @endif
                                mr-1"></i>
                                {{ $center->status }}
                            </span>
                        </div>
                        
                        <div class="space-y-4">
                            <div class="flex items-start gap-3">
                                <div class="p-2 bg-sky-100 rounded-lg text-sky-600">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <p class="text-gray-600">
                                    @php
                                        $addressParts = array_filter([
                                            $center->building_name,
                                            $center->street_name,
                                            $center->barangay,
                                            $center->city,
                                            $center->province
                                        ]);
                                    @endphp
                                    {{ implode(', ', $addressParts) }}
                                </p>
                            </div>
                            
                            <div class="flex items-center gap-3">
                                <div class="p-2 bg-sky-100 rounded-lg text-sky-600">
                                    <i class="fas fa-users"></i>
                                </div>
                                <p class="text-gray-600">Capacity: {{ $center->capacity }}</p>
                            </div>
                            
                            <div class="flex items-start gap-3">
                                <div class="p-2 bg-sky-100 rounded-lg text-sky-600">
                                    <i class="fas fa-phone"></i>
                                </div>
                                <div class="text-gray-600">
                                    @if(is_array($center->contact))
                                        @foreach($center->contact as $contact)
                                            <div class="text-sm">
                                                {{ $contact['number'] }}
                                                <span class="text-xs text-gray-500">({{ $contact['network'] }})</span>
                                            </div>
                                        @endforeach
                                    @else
                                        {{ $center->contact }}
                                    @endif
                                </div>
                            </div>

                            <div class="flex items-start gap-3">
                                <div class="p-2 bg-sky-100 rounded-lg text-sky-600">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="flex flex-wrap gap-2">
                                    @if(is_array($center->disaster_type))
                                        @foreach($center->disaster_type as $type)
                                            @php
                                                if (strpos($type, 'Others:') === 0) {
                                                    $badgeClass = 'bg-purple-100 text-purple-800';
                                                    $displayText = trim(str_replace('Others:', '', $type));
                                                } else {
                                                    $badgeClass = match($type) {
                                                        'Typhoon' => 'bg-green-100 text-green-800',
                                                        'Flood' => 'bg-blue-100 text-blue-800',
                                                        'Fire' => 'bg-red-100 text-red-800',
                                                        'Earthquake' => 'bg-orange-100 text-orange-800',
                                                        'Landslide' => 'bg-amber-100 text-amber-800',
                                                        default => 'bg-gray-100 text-gray-800',
                                                    };
                                                    $displayText = $type;
                                                }
                                            @endphp
                                            <span class="inline-flex items-center px-2.5 py-1 rounded-xl text-xs font-medium {{ $badgeClass }}">
                                                {{ $displayText }}
                                            </span>
                                        @endforeach
                                    @else
                                        @php
                                            if (strpos($center->disaster_type, 'Others:') === 0) {
                                                $badgeClass = 'bg-purple-100 text-purple-800';
                                                $displayText = trim(str_replace('Others:', '', $center->disaster_type));
                                            } else {
                                                $badgeClass = match($center->disaster_type) {
                                                    'Typhoon' => 'bg-green-100 text-green-800',
                                                    'Flood' => 'bg-blue-100 text-blue-800',
                                                    'Fire' => 'bg-red-100 text-red-800',
                                                    'Earthquake' => 'bg-orange-100 text-orange-800',
                                                    'Landslide' => 'bg-amber-100 text-amber-800',
                                                    default => 'bg-gray-100 text-gray-800',
                                                };
                                                $displayText = $center->disaster_type;
                                            }
                                        @endphp
                                        <span class="inline-flex items-center px-2.5 py-1 rounded-xl text-xs font-medium {{ $badgeClass }}">
                                            {{ $displayText }}
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-row justify-center items-center mt-6 gap-2 w-full md:w-auto mx-auto mt-auto mb-4">
                        <button onclick="openViewModal({{ $center->id }})" 
                            class="inline-flex items-center justify-center gap-2 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white px-4 py-2 rounded-xl font-medium shadow-lg transition-all duration-200 transform hover:scale-105">
                            <i class="fas fa-eye text-sm w-5"></i>
                            View
                        </button>
                        @if(!auth()->user()->hasRole('super_admin'))
                            <a href="{{ route('components.evacuation_management.edit-evacuation-center', $center->id) }}"
                                class="inline-flex items-center justify-center gap-2 bg-gradient-to-r from-amber-500 to-yellow-600 hover:from-amber-600 hover:to-yellow-700 text-white px-4 py-2 rounded-xl font-medium shadow-lg transition-all duration-200 transform hover:scale-105">
                                <i class="fas fa-edit text-sm"></i>
                                Edit
                            </a>
                            <button onclick="openDeleteModal({{ $center->id }}, '{{ $center->name }}')"
                                class="inline-flex items-center justify-center gap-2 bg-gradient-to-r from-red-600 to-rose-600 hover:from-red-700 hover:to-rose-700 text-white px-4 py-2 rounded-xl font-medium shadow-lg transition-all duration-200 transform hover:scale-105">
                                <i class="fas fa-trash text-sm"></i>
                                Delete
                            </button>
                        @endif
                    </div>
                </div>
            @empty
                <div class="col-span-full">
                    <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-12 text-center">
                        <div class="flex flex-col items-center justify-center">
                            <div class="p-4 bg-sky-100 rounded-full text-sky-500 mb-4">
                                <i class="fas fa-building text-4xl"></i>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 mb-2">No Centers Found</h3>
                            <p class="text-gray-500">No evacuation centers match your criteria</p>
                        </div>
                    </div>
                </div>
            @endforelse
        </div>

        <!-- Pagination -->
        @if($centers->hasPages())
        <div class="mt-8">
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 px-6 py-4">
                {{ $centers->links() }}
            </div>
        </div>
        @endif
    </div>
</div>

<!-- View Modal -->
<div id="viewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 backdrop-blur-sm z-50 flex items-center justify-center p-2 sm:p-4 hidden overflow-y-auto">
    <div class="relative mx-auto w-full max-w-lg sm:max-w-3xl bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 overflow-hidden">
        <!-- Header -->
        <div class="bg-gradient-to-r from-sky-600 to-blue-600 px-6 py-4">
            <div class="flex items-center justify-between">
                <h3 class="text-xl font-semibold text-white">Evacuation Center Details</h3>
                <button onclick="closeViewModal()" class="text-white hover:text-sky-100 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>
        <!-- Content -->
        <div id="viewModalContent" class="p-6 max-h-[calc(100vh-150px)] overflow-y-auto">
            <!-- Content will be dynamically inserted here -->
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50 flex items-center justify-center p-2 sm:p-6">
    <div class="relative mx-auto p-4 sm:p-8 border w-full max-w-xs sm:max-w-[480px] shadow-lg rounded-2xl bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-bold text-gray-900 mb-2">Delete Confirmation</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-gray-600">
                    Are you sure you want to delete <span id="centerNameToDelete" class="font-medium"></span>?
                </p>
                <p class="text-gray-500 text-sm mt-1">
                    This action cannot be undone.
                </p>
            </div>
            <div class="flex justify-center gap-4 mt-4">
                <button id="deleteButton" type="button" 
                        class="px-4 py-2 bg-gradient-to-r from-red-600 to-rose-600 hover:from-red-700 hover:to-rose-700 text-white text-sm font-medium rounded-lg shadow-lg transition-all duration-200">
                    Delete
                </button>
                <button onclick="closeDeleteModal()" 
                        class="px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-lg shadow-sm hover:bg-gray-200 transition-all duration-200">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
const disasterTypeColors = {
    'Typhoon': '#22c55e',
    'Flood': '#3b82f6',
    'Fire': '#ef4444',
    'Earthquake': '#f59e42',
    'Landslide': '#a16207',
    'Others': '#9333ea',
    'Multi-disaster': '#6b7280'
};

function getDisasterTypeBadges(disasterTypes) {
    if (!disasterTypes) return '';
    const types = Array.isArray(disasterTypes) ? disasterTypes : JSON.parse(disasterTypes);
    return types.map(type => {
        let colorClass = 'bg-gray-100 text-gray-800';
        let displayText = type;
        if (type.startsWith('Others:')) {
            colorClass = 'bg-purple-100 text-purple-800';
            displayText = type.replace('Others:', '').trim();
        } else if (type === 'Others') {
             colorClass = 'bg-purple-100 text-purple-800';
        }
        else {
            switch(type) {
                case 'Typhoon': colorClass = 'bg-green-100 text-green-800'; break;
                case 'Flood': colorClass = 'bg-blue-100 text-blue-800'; break;
                case 'Fire': colorClass = 'bg-red-100 text-red-800'; break;
                case 'Earthquake': colorClass = 'bg-orange-100 text-orange-800'; break;
                case 'Landslide': colorClass = 'bg-amber-100 text-amber-800'; break;
            }
        }
        return `<span class="inline-flex items-center px-2.5 py-1 rounded-xl text-xs font-medium ${colorClass}">${displayText}</span>`;
    }).join('');
}

function openViewModal(id) {
    const viewModal = document.getElementById('viewModal');
    const modalContent = document.getElementById('viewModalContent');
    
    viewModal.classList.remove('hidden');
    
    modalContent.innerHTML = `
        <div class="flex items-center justify-center py-8">
            <div class="animate-spin rounded-full h-12 w-12 border-4 border-sky-500 border-t-transparent"></div>
        </div>
    `;
    
    fetch(`/api/evacuation-centers/${id}`)
        .then(response => response.json())
        .then(data => {
            modalContent.innerHTML = `
                <div class="space-y-6">
                    <div class="border-b border-sky-100 pb-4">
                        <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">Center Name</h4>
                        <p class="text-lg font-bold text-gray-900">${data.name}</p>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">Capacity</h4>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-users text-sky-500"></i>
                                <p class="text-gray-900">${data.capacity}</p>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">Contact Info</h4>
                            <div class="space-y-1">
                                ${Array.isArray(data.contact) ?
                                    data.contact.map(contact => `
                                        <div class="flex items-center gap-2">
                                            <i class="fas fa-phone text-sky-500"></i>
                                            <p class="text-gray-900">${contact.number}</p>
                                            <span class="text-xs text-gray-500">(${contact.network})</span>
                                        </div>
                                    `).join('') :
                                    `<div class="flex items-center gap-2">
                                        <i class="fas fa-phone text-sky-500"></i>
                                        <p class="text-gray-900">${data.contact}</p>
                                    </div>`
                                }
                            </div>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">Status</h4>
                            <span class="inline-flex items-center px-3 py-1 rounded-xl text-sm font-medium ${
                                data.status === 'Active' ? 'bg-green-100 text-green-800' : 
                                (data.status === 'Under Maintenance' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800')
                            }">
                                <i class="fas ${
                                    data.status === 'Active' ? 'fa-check-circle' : 
                                    (data.status === 'Under Maintenance' ? 'fa-wrench' : 'fa-times-circle')
                                } mr-1"></i>
                                ${data.status}
                            </span>
                        </div>
                        <div>
                            <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">Disaster Type</h4>
                            <div class="flex flex-wrap gap-2">
                                ${getDisasterTypeBadges(data.disaster_type)}
                            </div>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">Address</h4>
                        <div class="flex items-start gap-2">
                            <i class="fas fa-map-marker-alt text-sky-500 mt-1"></i>
                            <p class="text-gray-900">${[data.building_name, data.street_name, data.barangay, data.city, data.province].filter(Boolean).join(', ')}, Philippines</p>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">Location</h4>
                        <div id="viewMap" class="h-80 w-full rounded-xl border border-sky-200 shadow-sm bg-gray-50"></div>
                    </div>
                </div>
            `;

            if (data.latitude && data.longitude) {
                setTimeout(() => {
                    const map = L.map('viewMap').setView([data.latitude, data.longitude], 15);
                    
                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '© <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                        maxZoom: 19
                    }).addTo(map);
                    
                    // Disaster type to pin mapping
                    const disasterTypePins = {
                        'Typhoon': '/image/pins/forTyphoon.png',
                        'Flood': '/image/pins/forFlood.png',
                        'Fire': '/image/pins/forFire.png',
                        'Earthquake': '/image/pins/forEarthquake.png',
                        'Landslide': '/image/pins/forLandslide.png',
                        'Others': '/image/pins/forOthers.png',
                        'Multi-disaster': '/image/pins/forMultiple.png'
                    };

                    const types = Array.isArray(data.disaster_type) ? data.disaster_type : JSON.parse(data.disaster_type);

                    let pinUrl;
                    if (types.length > 1) {
                        pinUrl = disasterTypePins['Multi-disaster'];
                    } else if (types.length === 1) {
                        const type = types[0];
                        const pinType = type.startsWith('Others:') ? 'Others' : type;
                        pinUrl = disasterTypePins[pinType] || disasterTypePins['Others'];
                    } else {
                        pinUrl = disasterTypePins['Others'];
                    }

                    const markerIcon = L.icon({
                        iconUrl: pinUrl,
                        iconSize: [32, 32],
                        iconAnchor: [16, 32],
                        popupAnchor: [0, -32]
                    });

                    L.marker([data.latitude, data.longitude], { icon: markerIcon }).addTo(map);
                }, 100);
            }
        })
        .catch(error => {
            console.error('Error fetching center details:', error);
            modalContent.innerHTML = `
                <div class="text-center py-8">
                    <p class="text-red-500">Failed to load details.</p>
                </div>
            `;
        });
}

function closeViewModal() {
    document.getElementById('viewModal').classList.add('hidden');
}

// Close modals when clicking outside
window.onclick = function(event) {
    const viewModal = document.getElementById('viewModal');
    const deleteModal = document.getElementById('deleteModal');
    if (event.target === viewModal) {
        closeViewModal();
    }
    if (event.target === deleteModal) {
        closeDeleteModal();
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // The standardized filter function in app.js will handle the filter changes automatically
    // No need for custom event listeners here
});

document.getElementById('deleteForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const form = this;
    const id = form.dataset.id;
    const submitButton = form.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;
    
    // Disable button and show loading state
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...';
    
    fetch(`/evacuation/${id}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove the center from the DOM
            const centerElement = document.querySelector(`[data-center-id="${id}"]`);
            if (centerElement) {
                centerElement.remove();
            }
            
            // Close the modal
            closeDeleteModal();
            
            // Check if no centers left, show empty state
            const remainingCenters = document.querySelectorAll('[data-center-id]');
            if (remainingCenters.length === 0) {
                const centersGrid = document.querySelector('.grid.grid-cols-1.sm\\:grid-cols-2.lg\\:grid-cols-3');
                if (centersGrid) {
                    centersGrid.innerHTML = `
                        <div class="col-span-full">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-12 text-center">
                                <div class="flex flex-col items-center justify-center">
                                    <div class="p-4 bg-sky-100 rounded-full text-sky-500 mb-4">
                                        <i class="fas fa-building text-4xl"></i>
                                    </div>
                                    <h3 class="text-xl font-bold text-gray-900 mb-2">No Centers Found</h3>
                                    <p class="text-gray-500">No evacuation centers match your criteria</p>
                                </div>
                            </div>
                        </div>
                    `;
                }
            }
        } else {
            // Re-enable button if error
            submitButton.disabled = false;
            submitButton.innerHTML = originalText;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        submitButton.disabled = false;
        submitButton.innerHTML = originalText;
    });
});

function openDeleteModal(id, name) {
    document.getElementById('centerNameToDelete').textContent = name;
    const deleteModal = document.getElementById('deleteModal');
    deleteModal.classList.remove('hidden');
    
    // Set up the delete button click handler
    const deleteButton = document.getElementById('deleteButton');
    
    // Remove any existing event listeners
    const newDeleteButton = deleteButton.cloneNode(true);
    deleteButton.parentNode.replaceChild(newDeleteButton, deleteButton);
    
    // Add new event listener
    newDeleteButton.addEventListener('click', function() {
        // Show loading state with spinner
        this.disabled = true;
        this.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Deleting...';
        
        // Send delete request
        fetch(`/evacuation/${id}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Refresh the page after successful deletion
                window.location.reload();
            } else {
                // Re-enable button if error
                this.disabled = false;
                this.innerHTML = 'Delete';
                
                // Show error message
                alert(data.message || 'Failed to delete evacuation center');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            this.disabled = false;
            this.innerHTML = 'Delete';
            
            // Show error message
            alert('An unexpected error occurred');
        });
    });
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}
</script>
@endpush

@endsection






