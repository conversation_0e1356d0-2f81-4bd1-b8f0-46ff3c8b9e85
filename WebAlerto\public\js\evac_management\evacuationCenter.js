// This file contains the JavaScript logic for both the Add and Edit Evacuation Center pages.
// It handles map initialization, geocoding, marker placement, and form interactions.

// Pass data from PHP to this script by defining `window.EvacuationCenterData` in the Blade template.
const { isAdmin, userBarangay, hasOldLocation, oldLatitude, oldLongitude } = window.EvacuationCenterData || {};

let map;
let marker;
let searchTimeout;
let currentSearchId = 0; // To prevent race conditions
let mapLoadAttempts = 0;
const MAX_LOAD_ATTEMPTS = 3;
let userLocation = null;

// Disaster type to pin mapping
const disasterTypePins = {
    'Typhoon': '/image/pins/forTyphoon.png',
    'Flood': '/image/pins/forFlood.png',
    'Fire': '/image/pins/forFire.png',
    'Earthquake': '/image/pins/forEarthquake.png',
    'Landslide': '/image/pins/forLandslide.png',
    'Others': '/image/pins/forOthers.png',
    'Multi-disaster': '/image/pins/forMultiple.png'
};

// Function to get the correct pin based on selected disaster types
function getCorrectPin() {
    const disasterCheckboxes = document.querySelectorAll('input[name="disaster_type[]"]:checked');
    const selectedTypes = Array.from(disasterCheckboxes).map(cb => cb.value);

    if (selectedTypes.length === 0) {
        return disasterTypePins['Others'];
    } else if (selectedTypes.length > 1) {
        return disasterTypePins['Multi-disaster'];
    } else {
        const type = selectedTypes[0];
        return disasterTypePins[type] || disasterTypePins['Others'];
    }
}

// Disaster type color legend and badge classes
const disasterTypeColors = {
    'Typhoon': '#22c55e',
    'Flood': '#3b82f6',
    'Fire': '#ef4444',
    'Earthquake': '#f59e42',
    'Landslide': '#a16207',
    'Others': '#9333ea',
    'Custom': '',
    'Multi-disaster': '#6b7280'
};
const disasterTypeBadgeClasses = {
    'Typhoon': 'bg-green-100 text-green-800',
    'Flood': 'bg-blue-100 text-blue-800',
    'Fire': 'bg-red-100 text-red-800',
    'Earthquake': 'bg-orange-100 text-orange-800',
    'Landslide': 'bg-yellow-100 text-yellow-800',
    'Multi-disaster': 'bg-gray-200 text-gray-800'
};

// --- Disaster Type Selection ---
function getSelectedDisasters() {
    return Array.from(document.querySelectorAll('input[name="disaster_type[]"]:checked')).map(cb => cb.value);
}

function updateSelectedDisastersDisplay() {
    const selected = getSelectedDisasters();
    const container = document.getElementById('selectedDisasters');
    if (!container) return;
    container.innerHTML = '';
    if (selected.length === 0) {
        container.innerHTML = '<span class="text-gray-400 text-sm">No disaster type selected</span>';
    } else {
        selected.forEach(type => {
            const span = document.createElement('span');
            if (type === 'Others') {
                const customField = document.getElementById('custom_disaster_type');
                if (customField && customField.value.trim()) {
                    span.className = 'px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800';
                    span.textContent = customField.value.trim();
                } else {
                    span.className = 'px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800';
                    span.textContent = 'Others';
                }
            } else {
                span.className = `px-2 py-1 rounded-full text-xs font-medium ${disasterTypeBadgeClasses[type] || 'bg-gray-200 text-gray-800'}`;
                span.textContent = type;
            }
            container.appendChild(span);
        });
    }
}

// --- Map and Marker Logic ---
function updateMarker(lat, lng, geocodeResult = null) {
    if (!map) return;
    if (marker) map.removeLayer(marker);

    lat = parseFloat(parseFloat(lat).toFixed(8));
    lng = parseFloat(parseFloat(lng).toFixed(8));

    const selectedTypes = getSelectedDisasters();
    let markerColor = '#6b7280'; // Default gray

    if (selectedTypes.length === 1) {
        markerColor = disasterTypeColors[selectedTypes[0]] || '#6b7280';
    } else if (selectedTypes.length > 1) {
        markerColor = disasterTypeColors['Multi-disaster'];
    }

    // Use correct pin based on selected disaster types
    const correctPinUrl = getCorrectPin();
    const markerIcon = L.icon({
        iconUrl: correctPinUrl,
        iconSize: [32, 32],
        iconAnchor: [16, 32],
        popupAnchor: [0, -32]
    });

    // Check if location should be read-only (edit mode)
    const isReadOnly = window.EvacuationCenterData && window.EvacuationCenterData.isLocationReadOnly;

    marker = L.marker([lat, lng], { draggable: !isReadOnly, icon: markerIcon }).addTo(map);

    marker.on('dragend', function(e) {
        const position = e.target.getLatLng();
        // Pass 'true' for isDragging to prevent panning
        updateMarkerPosition(position.lat, position.lng, null, true);
    });

    // Pan to the new location and update the address fields
    updateMarkerPosition(lat, lng, geocodeResult);
}

// Function to update marker pin when disaster types change
function updateMarkerPin() {
    if (marker) {
        const correctPinUrl = getCorrectPin();
        const newIcon = L.icon({
            iconUrl: correctPinUrl,
            iconSize: [32, 32],
            iconAnchor: [16, 32],
            popupAnchor: [0, -32]
        });
        marker.setIcon(newIcon);
    }
}

function updateMarkerPosition(lat, lng, geocodeResult = null, isDragging = false) {
    document.getElementById('latitude').value = lat;
    document.getElementById('longitude').value = lng;
    document.getElementById('selectedLatitude').textContent = lat.toFixed(8);
    document.getElementById('selectedLongitude').textContent = lng.toFixed(8);
    
    // Only pan the map if we're not in the middle of a drag operation.
    if (map && !isDragging) {
        map.panTo([lat, lng]);
    }

    if (geocodeResult) {
        populateLocationFields(geocodeResult);
    } else {
        reverseGeocode(lat, lng);
    }
}

function initializeMap() {
    const mapContainer = document.getElementById('map');
    if (!mapContainer || map) return;

    try {
        document.getElementById('mapLoadingIndicator').classList.remove('hidden');
        // Check if location should be read-only (edit mode)
        const isReadOnly = window.EvacuationCenterData && window.EvacuationCenterData.isLocationReadOnly;

        map = L.map('map', {
            zoomControl: true,
            scrollWheelZoom: true,
            doubleClickZoom: !isReadOnly,
            boxZoom: false,
            keyboard: true,
            dragging: true,
            touchZoom: true
        });

        const setViewOrDefault = (lat, lng, zoom = 13) => {
            map.setView([lat, lng], zoom);
            // On the edit page, the marker should be placed immediately.
            // The `hasOldLocation` is always true on the edit page.
            if (window.EvacuationCenterData.isEditPage) {
                 setTimeout(() => updateMarker(lat, lng), 100);
            } else if (hasOldLocation) {
                 setTimeout(() => updateMarker(oldLatitude, oldLongitude), 500);
            }
        };

        if (hasOldLocation) {
            setViewOrDefault(oldLatitude, oldLongitude, 16);
        } else if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                position => {
                    userLocation = { lat: position.coords.latitude, lng: position.coords.longitude };
                    setViewOrDefault(userLocation.lat, userLocation.lng);
                },
                () => { // Geolocation failed or denied
                    const cityCoords = window.EvacuationCenterData?.cityCoordinates || { lat: 10.3157, lng: 123.8854 };
                    setViewOrDefault(cityCoords.lat, cityCoords.lng);
                }
            );
        } else { // No geolocation support
            const cityCoords = window.EvacuationCenterData?.cityCoordinates || { lat: 10.3157, lng: 123.8854 };
            setViewOrDefault(cityCoords.lat, cityCoords.lng);
        }

        // Use a more reliable tile layer with better error handling
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: 19,
            subdomains: 'abc',
            tileSize: 256,
            zoomOffset: 0
        }).addTo(map);

        // Only allow map clicks if not in read-only mode
        if (!isReadOnly) {
            map.on('click', e => updateMarker(e.latlng.lat, e.latlng.lng));
        }

        // Delay invalidateSize to ensure the container is rendered
        setTimeout(() => {
            map.invalidateSize();
            // Pan to the marker's location again after invalidation to ensure it's centered
            if (marker) {
                map.panTo(marker.getLatLng());
            } else if (hasOldLocation) {
                map.panTo([oldLatitude, oldLongitude]);
            }
        }, 100);
        
        setTimeout(() => document.getElementById('mapLoadingIndicator').classList.add('hidden'), 1000);
    } catch (error) {
        console.error('Error initializing map:', error);
        document.getElementById('mapError').classList.remove('hidden');
        document.getElementById('mapLoadingIndicator').classList.add('hidden');
    }
}

function retryMapLoad() {
    if (mapLoadAttempts < MAX_LOAD_ATTEMPTS) {
        mapLoadAttempts++;
        document.getElementById('mapError').classList.add('hidden');
        if (map) map.remove();
        map = null;
        initializeMap();
    } else {
        alert('Failed to load map after multiple attempts.');
    }
}


// --- Geocoding (Search and Reverse) ---
function geocodeAddress(address, isDirectSearch = false, searchId = null) {
    let url = `/api/geocode?address=${encodeURIComponent(address)}`;
    if (userLocation) {
        url += `&lat=${userLocation.lat}&lng=${userLocation.lng}`;
    }

    const searchResults = document.getElementById('searchResults');

    if (!isDirectSearch) {
        searchResults.innerHTML = '<div class="p-4 text-gray-500">Searching...</div>';
        searchResults.classList.remove('hidden');
    }

    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Geocoding response:', data); // Debug log

            // If a new search has been initiated, ignore the results of this old one
            if (searchId !== null && searchId < currentSearchId) {
                return;
            }

            searchResults.innerHTML = '';

            // Check for error in response
            if (data.error) {
                console.error('Geocoding API error:', data.error);
                searchResults.innerHTML = '<div class="p-4 text-center text-red-500">Search failed: ' + data.error + '</div>';
                searchResults.classList.remove('hidden');
                return;
            }

            if (!data.features || data.features.length === 0) {
                searchResults.innerHTML = '<div class="p-4 text-gray-500">No results found for "' + address + '"</div>';
                searchResults.classList.remove('hidden');
                return;
            }

            if (isDirectSearch) {
                const result = data.features[0];
                map.setView([result.center[1], result.center[0]], 16);
                // Always trigger reverse geocoding to get proper address details
                updateMarker(result.center[1], result.center[0], null);
                searchResults.classList.add('hidden');
            } else {
                // Show all results without filtering
                data.features.forEach(result => addSearchResult(result, false));
                searchResults.classList.remove('hidden');
            }
        })
        .catch(error => {
            console.error('Geocoding error:', error);
            searchResults.innerHTML = '<div class="p-4 text-center text-red-500">Search failed. Please check your internet connection and try again.</div>';
            searchResults.classList.remove('hidden');
        });
}
function addSearchResult(result, isRecommended) {
    const searchResults = document.getElementById('searchResults');
    const div = document.createElement('div');
    div.className = 'p-3 hover:bg-sky-50 cursor-pointer border-b border-gray-100 transition-colors';
    
    // The 'text' from our new API response is the full name, which is what we want.
    const fullAddress = result.text || 'No address available';
    
    div.innerHTML = `
        <div class="font-medium text-gray-900">${fullAddress}</div>
    `;
    
    div.addEventListener('click', () => {
        const lat = result.center[1];
        const lon = result.center[0];
        const selectedText = result.text || ''; // The main text from the search result
        const placeName = result.place_name || ''; // The full place name

        // Set the view and update the marker immediately
        map.setView([lat, lon], 16);
        updateMarker(lat, lon, null);

        // Use the search result data directly to avoid duplication
        // Parse the place_name to extract address components
        const addressComponents = placeName.split(', ');

        // Create a structured data object from the search result
        const searchData = {
            building_name: selectedText,
            street: '',
            barangay: '',
            city: '',
            province: '',
            postal_code: '',
            full_address: placeName
        };

        // Try to parse address components from the place_name
        if (addressComponents.length >= 2) {
            // Usually format is: "Building/Street, Barangay, City, Province, Country"
            if (addressComponents.length >= 4) {
                searchData.barangay = addressComponents[1]?.trim() || '';
                searchData.city = addressComponents[2]?.trim() || '';
                searchData.province = addressComponents[3]?.trim() || '';
            } else if (addressComponents.length >= 3) {
                searchData.city = addressComponents[1]?.trim() || '';
                searchData.province = addressComponents[2]?.trim() || '';
            } else {
                searchData.city = addressComponents[1]?.trim() || '';
            }
        }

        // Populate fields with the parsed search data
        populateLocationFields(searchData);

        // Set search bar to show the selected text (not the full place name)
        document.getElementById('search').value = selectedText;
        searchResults.classList.add('hidden');
    });
    searchResults.appendChild(div);
}

function reverseGeocode(lat, lng) {
    fetch(`/api/reverse-geocode?lat=${lat}&lng=${lng}`)
        .then(response => response.json())
        .then(data => {
            if (data && !data.error) {
                populateLocationFields(data);
            } else {
                document.getElementById('selectedAddress').textContent = `Lat: ${lat.toFixed(6)}, Lng: ${lng.toFixed(6)}`;
                console.error('Reverse geocoding error:', data.error);
            }
        })
        .catch(error => console.error('Reverse geocoding error:', error));
}

function populateLocationFields(data) {
    // Normalize and clean up the data to prevent duplicates
    const cleanData = {
        building_name: (data.building_name || '').trim(),
        street: (data.street || '').trim(),
        barangay: (data.barangay || '').trim(),
        city: (data.city || '').trim(),
        province: (data.province || '').trim(),
        postal_code: (data.postal_code || '').trim()
    };

    // Helper function to check if a value is a substring of another (case-insensitive)
    const isSubstringOf = (value1, value2) => {
        if (!value1 || !value2) return false;
        const v1 = value1.toLowerCase().trim();
        const v2 = value2.toLowerCase().trim();
        return v1.includes(v2) || v2.includes(v1);
    };

    // Helper function to check if a value is already represented in existing parts
    const isAlreadyRepresented = (value, existingParts) => {
        if (!value) return true;
        return existingParts.some(part => isSubstringOf(value, part));
    };

    // Build address parts intelligently to avoid duplication
    let addressParts = [];

    // Start with the most specific information
    if (cleanData.building_name) {
        addressParts.push(cleanData.building_name);
    }

    // Add street only if it's not already represented in building name
    if (cleanData.street && !isAlreadyRepresented(cleanData.street, addressParts)) {
        addressParts.push(cleanData.street);
    }

    // Add barangay only if it's not already represented
    if (cleanData.barangay && !isAlreadyRepresented(cleanData.barangay, addressParts)) {
        addressParts.push(cleanData.barangay);
    }

    // Add city only if it's not already represented
    if (cleanData.city && !isAlreadyRepresented(cleanData.city, addressParts)) {
        addressParts.push(cleanData.city);
    }

    // Add province only if it's not already represented
    if (cleanData.province && !isAlreadyRepresented(cleanData.province, addressParts)) {
        addressParts.push(cleanData.province);
    }

    // Create a clean, concise address display
    const displayAddress = addressParts.length > 0
        ? addressParts.join(', ')
        : data.full_address || `Lat: ${document.getElementById('latitude').value}, Lng: ${document.getElementById('longitude').value}`;

    // Update the display
    document.getElementById('selectedAddress').textContent = displayAddress;

    // Update form fields with original data (not cleaned, to preserve exact values)
    document.getElementById('street_name').value = data.street || '';
    // Only set barangay from geocode for super_admin (not admin or barangay users)
    // This prevents accidental overwrite for barangay DRRMO users
    if (window.EvacuationCenterData.isAdmin === 'true' && window.EvacuationCenterData.userBarangay === 'City Hall') {
        document.getElementById('barangay').value = data.barangay || '';
    }
    document.getElementById('city').value = data.city || '';
    document.getElementById('province').value = data.province || '';
    document.getElementById('postal_code').value = data.postal_code || '';

    // Update building_name field if it exists
    const buildingNameField = document.getElementById('building_name');
    if (buildingNameField) {
        buildingNameField.value = data.building_name || '';
    }

    const provinceWarning = document.getElementById('provinceWarning');
    if (provinceWarning) {
        provinceWarning.classList.toggle('hidden', !!data.province);
    }

    // Show text-based restriction warnings for BDRRMC users only
    const userRole = window.EvacuationCenterData.userRole;
    const selectedBarangay = cleanData.barangay;
    const selectedCity = cleanData.city;
    const userBarangay = window.EvacuationCenterData.userBarangay;
    const userCityName = window.EvacuationCenterData.userCityName;

    // Store selected data for validation during form submission
    window.selectedBarangay = selectedBarangay;
    window.selectedCity = selectedCity;

    let showWarning = false;
    let warningMessage = '';

    if (userRole === 'admin') {
        // BDRRMC: Check both barangay and city restrictions (text-based only)
        if (selectedBarangay && userBarangay && selectedBarangay.toLowerCase() !== userBarangay.toLowerCase()) {
            showWarning = true;
            warningMessage = `
                <h3 class="text-sm font-medium text-red-800">Barangay Restriction</h3>
                <p class="mt-1 text-sm text-red-700">
                    The selected location is in <strong>${selectedBarangay}</strong>, but you can only add evacuation centers within your assigned barangay: <strong>${userBarangay}</strong>.
                </p>
            `;
        }
    }
    // Note: CDRRMC users can add centers in any barangay within their city
    // The barangay dropdown is already filtered to show only accessible barangays

    // Show or hide warning based on validation result
    if (showWarning) {
        let locationWarning = document.getElementById('locationWarning');
        if (!locationWarning) {
            locationWarning = document.createElement('div');
            locationWarning.id = 'locationWarning';
            locationWarning.className = 'mt-3 p-4 bg-red-50 border border-red-200 rounded-lg';

            // Insert after the selected address display
            const addressDisplay = document.getElementById('selectedAddress');
            if (addressDisplay && addressDisplay.parentNode) {
                addressDisplay.parentNode.insertBefore(locationWarning, addressDisplay.nextSibling);
            }
        }

        locationWarning.innerHTML = `
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    ${warningMessage}
                </div>
            </div>
        `;
        locationWarning.classList.remove('hidden');
    } else {
        // Hide warning if location is valid
        const locationWarning = document.getElementById('locationWarning');
        if (locationWarning) {
            locationWarning.classList.add('hidden');
        }
    }
}


// --- Form and Step Handling ---
document.addEventListener('DOMContentLoaded', function() {
    const evacuationForm = document.getElementById('evacuationCenterForm');
    const isAddPage = !!document.getElementById('nextStep');
    window.EvacuationCenterData.isEditPage = !isAddPage;

    // --- Initialize Map ---
    // On the edit page, initialize the map immediately after the DOM is loaded.
    // On the add page, it's initialized when the user moves to step 2.
    if (!isAddPage) {
        // For edit page, initialize map with a slight delay to ensure DOM is fully rendered
        setTimeout(() => {
            initializeMap();
            // Force map to refresh size after a short delay
            setTimeout(() => {
                if (map) {
                    map.invalidateSize();
                    if (marker) {
                        map.panTo(marker.getLatLng());
                    } else if (hasOldLocation) {
                        map.panTo([oldLatitude, oldLongitude]);
                    }
                }
            }, 300);
        }, 100);
    } else { // Logic for the Add Page
        const step1 = document.getElementById('step1');
        const step2 = document.getElementById('step2');
        const nextStepBtn = document.getElementById('nextStep');

        if (nextStepBtn) {
            nextStepBtn.addEventListener('click', () => {
                // Validate step 1 before proceeding
                const requiredFields = [
                    { id: 'name', label: 'Center Name' },
                    { id: 'capacity', label: 'Capacity' },
                    { id: 'status', label: 'Status' }
                ];
                
                let isValid = true;
                let firstInvalidField = null;
                
                requiredFields.forEach(field => {
                    const element = document.getElementById(field.id);
                    const value = element.value.trim();

                    if (!value) {
                        isValid = false;
                        if (!firstInvalidField) firstInvalidField = element;
                        element.classList.add('border-red-500', 'focus:border-red-500');
                    } else {
                        element.classList.remove('border-red-500', 'focus:border-red-500');
                    }
                });

                // Validate contact numbers
                const contactNumbers = document.querySelectorAll('input[name="contact_numbers[]"]');
                if (contactNumbers.length === 0) {
                    isValid = false;
                    alert('At least one contact number is required.');
                } else {
                    contactNumbers.forEach(input => {
                        const value = input.value.trim();
                        if (!value) {
                            isValid = false;
                            if (!firstInvalidField) firstInvalidField = input;
                            input.classList.add('border-red-500', 'focus:border-red-500');
                        } else {
                            input.classList.remove('border-red-500', 'focus:border-red-500');
                        }
                    });
                }
                
                const disasterCheckboxes = document.querySelectorAll('input[name="disaster_type[]"]:checked');
                const disasterContainer = document.querySelector('.disaster-type-container');
                if (disasterCheckboxes.length === 0) {
                    isValid = false;
                    disasterContainer.classList.add('border-red-500');
                } else {
                    disasterContainer.classList.remove('border-red-500');
                }
                
                if (!isValid) {
                    if (firstInvalidField) {
                        firstInvalidField.focus();
                        firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                    return;
                }

                // Hide step 1 and show step 2
                step1.classList.add('hidden');
                step2.classList.remove('hidden');
                
                // Initialize map only when moving to step 2
                if (!map) {
                    initializeMap();
                } else {
                    // If map already exists, it was hidden. We MUST invalidate its size.
                    setTimeout(() => {
                        map.invalidateSize();
                        if (marker) {
                            map.panTo(marker.getLatLng());
                        }
                    }, 10);
                }
                
                step2.scrollIntoView({ behavior: 'smooth', block: 'start' });
                
                // Ensure search input is properly initialized
                setTimeout(() => {
                    const searchInput = document.getElementById('search');
                    if (searchInput) {
                        searchInput.focus();
                    }
                }, 200);
            });
        }
        
        // Also handle returning to a page with validation errors
        if (hasOldLocation) {
            step1.classList.add('hidden');
            step2.classList.remove('hidden');
            if (!map) {
                initializeMap();
            }
        }

        const prevStepBtn = document.getElementById('prevStep');
        if (prevStepBtn) {
            prevStepBtn.addEventListener('click', () => {
                document.getElementById('step1').classList.remove('hidden');
                document.getElementById('step2').classList.add('hidden');
                document.getElementById('step1').scrollIntoView({ behavior: 'smooth', block: 'start' });
            });
        }
    }

    // --- Disaster Type Checkboxes ---
    const othersCheckbox = document.querySelector('input[name="disaster_type[]"][value="Others"]');
    const customInputContainer = document.getElementById('customDisasterInput');
    const customInputField = document.getElementById('custom_disaster_type');

    function handleDisasterTypeChange() {
        updateSelectedDisastersDisplay();
        // If a marker already exists on the map, update its pin to reflect the new selection
        if (marker) {
            updateMarkerPin();
        }
    }

    document.querySelectorAll('input[name="disaster_type[]"]').forEach(checkbox => {
        checkbox.addEventListener('change', handleDisasterTypeChange);
    });

    if (othersCheckbox && customInputContainer && customInputField) {
        othersCheckbox.addEventListener('change', function() {
            if (this.checked) {
                customInputContainer.classList.remove('hidden');
                customInputField.focus();
            } else {
                customInputContainer.classList.add('hidden');
                customInputField.value = ''; // Clear input on uncheck
            }
        });
    }

    if (customInputField) {
        customInputField.addEventListener('input', updateSelectedDisastersDisplay);
    }

    // Initial display update on page load for all forms
    updateSelectedDisastersDisplay();

    // --- Other Initializations ---
    const searchInput = document.getElementById('search');
    const searchButton = document.getElementById('searchButton');

    // Only initialize search functionality if not in read-only mode
    const isReadOnly = window.EvacuationCenterData && window.EvacuationCenterData.isLocationReadOnly;

    if (searchButton && searchInput && !isReadOnly) {
        searchButton.addEventListener('click', () => {
            const query = searchInput.value.trim();
            if(query) geocodeAddress(query, true);
        });
        
        searchInput.addEventListener('input', () => {
            clearTimeout(searchTimeout);
            const query = searchInput.value.trim();

            // Clear results if query is empty
            if (query.length === 0) {
                const searchResults = document.getElementById('searchResults');
                searchResults.classList.add('hidden');
                return;
            }

            // Show searching indicator immediately for queries with 2+ characters
            if (query.length >= 2) {
                const searchResults = document.getElementById('searchResults');
                searchResults.innerHTML = '<div class="p-4 text-gray-500">Searching...</div>';
                searchResults.classList.remove('hidden');

                currentSearchId++;
                const thisSearchId = currentSearchId;

                // Debounce the search to avoid too many API calls
                searchTimeout = setTimeout(() => {
                    geocodeAddress(query, false, thisSearchId);
                }, 500); // Increased debounce time to reduce API calls
            }
        });
        
        searchInput.addEventListener('keydown', e => {
            if (e.key === 'Enter') { 
                e.preventDefault(); 
                clearTimeout(searchTimeout);
                const query = searchInput.value.trim();
                if(query) geocodeAddress(query, true);
            }
        });
        
        // Ensure search input is properly initialized on add page
        if (isAddPage && hasOldLocation) {
            setTimeout(() => {
                searchInput.focus();
            }, 100);
        }
    }

    // Handle multiple contact functionality
    const addContactBtn = document.getElementById('addContactBtn');
    const contactContainer = document.getElementById('contactContainer');

    if (addContactBtn && contactContainer) {
        addContactBtn.addEventListener('click', function() {
            const contactEntries = contactContainer.querySelectorAll('.contact-entry');
            const newIndex = contactEntries.length;

            const newContactEntry = document.createElement('div');
            newContactEntry.className = 'contact-entry mb-3';
            newContactEntry.setAttribute('data-index', newIndex);

            newContactEntry.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div>
                        <input type="text" name="contact_numbers[]"
                               class="w-full rounded-lg border-2 border-gray-300 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-lg py-3 px-4 bg-white contact-number"
                               required placeholder="Enter contact number">
                    </div>
                    <div class="flex gap-2">
                        <select name="contact_networks[]"
                                class="flex-1 rounded-lg border-2 border-gray-300 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-lg py-3 px-4 bg-white">
                            <option value="Globe">Globe</option>
                            <option value="Smart">Smart</option>
                            <option value="Sun">Sun</option>
                            <option value="TM">TM</option>
                            <option value="TNT">TNT</option>
                            <option value="DITO">DITO</option>
                        </select>
                        <button type="button" class="remove-contact-btn px-3 py-2 bg-red-500 text-white text-sm rounded-lg hover:bg-red-600 transition-colors">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;

            contactContainer.appendChild(newContactEntry);

            // Add event listener to the new contact number input
            const newContactInput = newContactEntry.querySelector('.contact-number');
            if (newContactInput) {
                newContactInput.setAttribute('type', 'tel');
                newContactInput.addEventListener('input', e => {
                    e.target.value = e.target.value.replace(/[^0-9]/g, '');
                });
            }

            // Add event listener to remove button
            const removeBtn = newContactEntry.querySelector('.remove-contact-btn');
            if (removeBtn) {
                removeBtn.addEventListener('click', function() {
                    newContactEntry.remove();
                });
            }
        });
    }

    // Handle existing contact number inputs
    document.querySelectorAll('.contact-number').forEach(input => {
        input.setAttribute('type', 'tel');
        input.addEventListener('input', e => {
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
        });
    });

    // Handle existing remove buttons
    document.querySelectorAll('.remove-contact-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            btn.closest('.contact-entry').remove();
        });
    });

    // Only add search results click handler if not in read-only mode
    if (!isReadOnly && searchInput) {
        document.addEventListener('click', e => {
            const searchResults = document.getElementById('searchResults');
            const searchContainer = searchInput.closest('.relative');
            if (searchResults && searchContainer && !searchContainer.contains(e.target)) {
                searchResults.classList.add('hidden');
            }
        });
    }

    const saveButton = document.getElementById('saveButton');
    if (saveButton) {
        saveButton.addEventListener('click', function(e) {
            // Validate location fields before submitting
            const requiredLocationFields = [
                { id: 'latitude', label: 'Latitude' },
                { id: 'longitude', label: 'Longitude' },
                { id: 'city', label: 'City' },
                { id: 'province', label: 'Province' }
            ];
            let missing = [];
            requiredLocationFields.forEach(field => {
                const el = document.getElementById(field.id);
                if (!el || !el.value.trim()) {
                    missing.push(field.label);
                    if (el) el.classList.add('border-red-500');
                } else {
                    if (el) el.classList.remove('border-red-500');
                }
            });
            if (missing.length > 0) {
                e.preventDefault();
                alert('Please select a location on the map. The following fields are required: ' + missing.join(', '));
                // Scroll to error message if present
                const errorBox = document.querySelector('.mb-6.bg-red-50');
                if (errorBox) errorBox.scrollIntoView({ behavior: 'smooth', block: 'center' });
                return false;
            }

            // Validate text-based restrictions for BDRRMC users only
            const userRole = window.EvacuationCenterData.userRole;
            const selectedBarangay = window.selectedBarangay || '';
            const selectedCity = window.selectedCity || '';
            const userBarangay = window.EvacuationCenterData.userBarangay;
            const userCityName = window.EvacuationCenterData.userCityName;

            if (userRole === 'admin') {
                // BDRRMC: Validate both barangay and city restrictions (text-based only)
                if (selectedBarangay && userBarangay && selectedBarangay.toLowerCase() !== userBarangay.toLowerCase()) {
                    e.preventDefault();

                    // Show error message
                    alert(`Access Restricted: You can only add evacuation centers within your assigned barangay (${userBarangay}). The selected location is in ${selectedBarangay}.`);

                    // Scroll to the location section or warning
                    const locationWarning = document.getElementById('locationWarning');
                    const locationSection = document.querySelector('.bg-sky-50');
                    const scrollTarget = locationWarning || locationSection;

                    if (scrollTarget) {
                        scrollTarget.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }

                    return false;
                }
            }
            // Note: CDRRMC users are not restricted by client-side validation
            // Their access is controlled by the barangay dropdown which only shows accessible barangays
        });
    }

    // After the map is shown or after a validation error/page reload, call:
    setTimeout(function() {
        map.invalidateSize();
    }, 200);
});
