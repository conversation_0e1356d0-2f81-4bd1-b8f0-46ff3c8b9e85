#fire-map {
  height: 100%;
  width: 100%;
  z-index: 1;
}

.map-controls {
  position: absolute;
  top: 80px;
  right: 10px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-btn {
  --background: rgba(255, 255, 255, 0.95);
  --color: #dc3545;
  --border-radius: 12px;
  width: 50px;
  height: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(220, 53, 69, 0.2);

  &:hover {
    --background: rgba(220, 53, 69, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }
}

.control-icon {
  width: 28px;
  height: 28px;
  object-fit: contain;
}

// All Centers Panel
.all-centers-panel {
  position: fixed;
  top: 0;
  right: -400px;
  width: 380px;
  height: 100vh;
  background: white;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
  z-index: 1500;
  transition: right 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  flex-direction: column;

  &.show {
    right: 0;
  }

  .panel-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    overflow: hidden;
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 2px solid var(--ion-color-danger-tint);

    .header-info {
      flex: 1;

      h3 {
        margin: 0 0 4px 0;
        font-size: 20px;
        font-weight: 700;
        color: var(--ion-color-danger);
        line-height: 1.2;
      }

      p {
        margin: 0;
        font-size: 14px;
        color: var(--ion-color-medium);
        font-weight: 500;
      }
    }

    ion-button {
      --color: var(--ion-color-medium);
      margin: 0;
    }
  }

  .centers-list {
    flex: 1;
    overflow-y: auto;
    padding-right: 4px;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: var(--ion-color-light);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--ion-color-danger-tint);
      border-radius: 2px;
    }
  }

  .center-item {
    display: flex;
    align-items: center;
    padding: 16px;
    margin-bottom: 12px;
    background: white;
    border-radius: 12px;
    border: 1px solid var(--ion-color-light);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(220, 53, 69, 0.15);
      border-color: var(--ion-color-danger-tint);
    }

    &:last-child {
      margin-bottom: 0;
    }

    .center-info {
      flex: 1;

      h4 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--ion-color-dark);
        line-height: 1.3;
      }

      .address {
        margin: 0 0 8px 0;
        font-size: 13px;
        color: var(--ion-color-medium);
        line-height: 1.4;
      }

      .center-details {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .distance, .capacity {
          font-size: 12px;
          font-weight: 500;
          padding: 2px 8px;
          border-radius: 8px;
          background: var(--ion-color-danger-tint);
          color: var(--ion-color-danger);
          width: fit-content;
        }
      }
    }

    .center-actions {
      margin-left: 12px;

      ion-icon {
        font-size: 18px;
        color: var(--ion-color-medium);
      }
    }
  }
}

.all-centers-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1400;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.show {
    opacity: 1;
    visibility: visible;
  }
}

// Header styling
ion-header {
  ion-toolbar {
    --background: #dc3545;
    --color: white;
    --border-width: 0;

    .header-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      font-size: 18px;

      ion-icon {
        font-size: 20px;
      }
    }
  }
}

// Navigation Panel
.navigation-panel {
  position: fixed;
  top: 0;
  right: -400px;
  width: 380px;
  height: 100vh;
  background: white;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
  z-index: 1600;
  transition: right 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  flex-direction: column;

  &.show {
    right: 0;
  }

  .panel-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    overflow: hidden;
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 2px solid var(--ion-color-danger-tint);

    .header-info {
      flex: 1;

      h3 {
        margin: 0 0 8px 0;
        font-size: 20px;
        font-weight: 700;
        color: var(--ion-color-danger);
        line-height: 1.2;
      }

      p {
        margin: 0;
        font-size: 14px;
        color: var(--ion-color-medium);
        font-weight: 500;
        line-height: 1.4;
      }
    }

    ion-button {
      --color: var(--ion-color-medium);
      margin: 0;
    }
  }

  .center-details {
    margin-bottom: 24px;

    .detail-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 0;
      font-size: 14px;
      color: var(--ion-color-dark);

      ion-icon {
        font-size: 18px;
        color: var(--ion-color-danger);
        min-width: 18px;
      }

      span {
        line-height: 1.4;
      }
    }
  }

  .transport-options {
    flex: 1;
    overflow-y: auto;

    h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--ion-color-dark);
    }

    .transport-option {
      display: flex;
      align-items: center;
      padding: 16px;
      margin-bottom: 12px;
      background: white;
      border: 2px solid var(--ion-color-light);
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        border-color: var(--ion-color-danger-tint);
        background: rgba(220, 53, 69, 0.05);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.selected {
        border-color: var(--ion-color-danger);
        background: rgba(220, 53, 69, 0.1);
        box-shadow: 0 4px 16px rgba(220, 53, 69, 0.2);
      }

      .transport-icon {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        background: var(--ion-color-danger-tint);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;

        ion-icon {
          font-size: 20px;
          color: var(--ion-color-danger);
        }
      }

      .transport-info {
        flex: 1;

        .mode {
          font-size: 16px;
          font-weight: 600;
          color: var(--ion-color-dark);
          margin-bottom: 4px;
          line-height: 1.2;
        }

        .details {
          font-size: 13px;
          color: var(--ion-color-medium);
          font-weight: 500;
        }
      }

      .transport-action {
        margin-left: 12px;

        ion-icon {
          font-size: 18px;
          color: var(--ion-color-medium);
        }
      }

      &.selected .transport-action ion-icon {
        color: var(--ion-color-danger);
      }
    }
  }
}

.navigation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1500;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.show {
    opacity: 1;
    visibility: visible;
  }
}

// Route Footer (shows when marker is clicked)
.route-footer {
  position: fixed;
  bottom: -100px;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid var(--ion-color-light);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1500;
  transition: bottom 0.3s ease-in-out;

  &.show {
    bottom: 0;
  }

  .footer-content {
    padding: 16px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .route-summary {
      display: flex;
      align-items: center;
      gap: 12px;
      flex: 1;

      .transport-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--ion-color-danger-tint);
        display: flex;
        align-items: center;
        justify-content: center;

        ion-icon {
          font-size: 20px;
          color: var(--ion-color-danger);
        }
      }

      .route-details {
        flex: 1;

        .destination {
          font-size: 16px;
          font-weight: 600;
          color: var(--ion-color-dark);
          margin-bottom: 2px;
          line-height: 1.2;
        }

        .route-info-footer {
          display: flex;
          align-items: center;
          gap: 8px;

          .time {
            font-size: 14px;
            font-weight: 600;
            color: var(--ion-color-danger);
          }

          .distance {
            font-size: 12px;
            color: var(--ion-color-medium);
          }
        }
      }
    }

    .footer-actions {
      ion-button {
        --border-radius: 20px;
        height: 36px;
        font-weight: 600;
      }
    }
  }
}

.transport-info {
  flex: 1;

  .mode {
    display: block;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 2px;
  }

  .details {
    display: block;
    font-size: 0.85rem;
    color: #666;
  }
}

// Leaflet map styling
.leaflet-container {
  height: 100%;
  width: 100%;
}

.leaflet-popup-content {
  margin: 8px 12px;
  line-height: 1.4;
}

.evacuation-popup {
  h3 {
    margin: 0 0 10px 0;
    color: #dc3545;
    font-size: 1.1rem;
  }

  p {
    margin: 5px 0;
    font-size: 0.9rem;
  }

  strong {
    color: #333;
  }
}

// Responsive design
@media (max-width: 768px) {
  .map-controls {
    top: 70px;
    right: 8px;
  }

  .all-centers-panel {
    width: 100%;
    right: -100%;

    &.show {
      right: 0;
    }
  }

  .navigation-panel {
    width: 100%;
    right: -100%;

    &.show {
      right: 0;
    }
  }

  .route-footer {
    .footer-content {
      padding: 12px 16px;

      .route-summary {
        gap: 10px;

        .transport-icon {
          width: 36px;
          height: 36px;

          ion-icon {
            font-size: 18px;
          }
        }

        .route-details {
          .destination {
            font-size: 14px;
          }

          .route-info-footer {
            .time {
              font-size: 13px;
            }

            .distance {
              font-size: 11px;
            }
          }
        }
      }
    }
  }
}

// Travel Mode Selector & Route Info (compressed)
.travel-mode-selector {
  position: absolute; top: 20px; left: 20px; z-index: 1000;
  background: rgba(255, 255, 255, 0.95); border-radius: 12px; padding: 8px;
  ion-segment { --background: transparent; min-height: 40px; }
  ion-segment-button { --color: var(--ion-color-medium); --color-checked: var(--ion-color-danger); --indicator-color: var(--ion-color-danger); min-height: 40px; }
  ion-icon { font-size: 16px; margin-bottom: 2px; }
  ion-label { font-size: 12px; font-weight: 500; }
}

.route-info {
  position: absolute; bottom: 20px; left: 20px; z-index: 1000; max-width: 200px;
  ion-card { margin: 0; border-radius: 12px; background: rgba(255, 255, 255, 0.95); }
  ion-card-content { padding: 12px; }
  .route-header { display: flex; align-items: center; gap: 8px; font-weight: 600; color: var(--ion-color-danger); margin-bottom: 8px; }
  .route-details { display: flex; flex-direction: column; gap: 4px; }
  .route-item { display: flex; align-items: center; gap: 8px; font-size: 14px; color: var(--ion-color-dark); }
  ion-icon { font-size: 16px; color: var(--ion-color-danger); }
}

// Pulsing markers for nearest centers (like all-maps)
:global(.pulsing-marker) {
  .pulse-container {
    position: relative;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .pulse {
    position: absolute;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    animation: pulse 2s infinite;
    opacity: 0.6;
  }

  .marker-icon {
    width: 30px;
    height: 30px;
    z-index: 2;
    position: relative;
  }

  .marker-label {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    z-index: 3;
    border: 2px solid white;
  }

  @keyframes pulse {
    0% {
      transform: scale(0.8);
      opacity: 0.8;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.4;
    }
    100% {
      transform: scale(0.8);
      opacity: 0.8;
    }
  }
}

// Popup styling for fire centers
:global(.leaflet-popup-content) {
  .evacuation-popup {
    text-align: center;
    min-width: 200px;

    h3 {
      margin: 0 0 8px 0;
      color: var(--ion-color-danger);
      font-size: 16px;
      font-weight: 600;
    }

    p {
      margin: 4px 0;
      font-size: 14px;

      strong {
        color: var(--ion-color-dark);
      }
    }

    &.nearest-popup {
      h3 {
        color: #dc3545;
        font-size: 18px;
      }
    }
  }
}
