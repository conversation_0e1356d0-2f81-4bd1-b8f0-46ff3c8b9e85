@extends('layout.app')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-sky-100 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Back Button and Title -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6 mb-8">
            <div class="flex items-center gap-4">
                <a href="{{ route('components.user-management') }}"
                   class="p-2 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg text-white hover:from-sky-600 hover:to-blue-700 transition-all">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                </a>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Mobile User Details</h1>
                    <p class="text-gray-600 mt-1">View mobile user information</p>
                </div>
            </div>
        </div>

        <!-- User Information Card -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-8">
            <div class="max-w-3xl mx-auto">
                <!-- User Avatar and Basic Info -->
                <div class="flex items-center gap-6 mb-8">
                    <div class="flex-shrink-0 h-20 w-20">
                        <div class="h-20 w-20 rounded-full bg-gradient-to-br from-sky-400 to-blue-500 flex items-center justify-center">
                            <span class="text-white font-bold text-2xl">
                                {{ strtoupper(substr($mobileUser->full_name, 0, 1)) }}
                            </span>
                        </div>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">{{ $mobileUser->full_name }}</h2>
                        <p class="text-gray-600">{{ $mobileUser->mobile_number }}</p>
                        <div class="flex items-center gap-2 mt-2">
                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                                {{ $mobileUser->status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                <i class="fas {{ $mobileUser->status === 'Active' ? 'fa-check-circle' : 'fa-times-circle' }} text-xs mr-1"></i>
                                {{ $mobileUser->status }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Detailed Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Personal Information -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                            <i class="fas fa-user text-sky-600 mr-2"></i>Personal Information
                        </h3>
                        
                        <div class="bg-gray-50 rounded-lg p-4">
                            <label class="block text-sm font-medium text-gray-600 mb-1">Full Name</label>
                            <p class="text-gray-900 font-medium">{{ $mobileUser->full_name }}</p>
                        </div>

                        <div class="bg-gray-50 rounded-lg p-4">
                            <label class="block text-sm font-medium text-gray-600 mb-1">Mobile Number</label>
                            <p class="text-gray-900 font-medium">{{ $mobileUser->mobile_number }}</p>
                        </div>

                        <div class="bg-gray-50 rounded-lg p-4">
                            <label class="block text-sm font-medium text-gray-600 mb-1">Age</label>
                            <p class="text-gray-900 font-medium">{{ $mobileUser->age }} years old</p>
                        </div>

                        <div class="bg-gray-50 rounded-lg p-4">
                            <label class="block text-sm font-medium text-gray-600 mb-1">Gender</label>
                            <p class="text-gray-900 font-medium">{{ $mobileUser->gender }}</p>
                        </div>
                    </div>

                    <!-- Location and Account Information -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                            <i class="fas fa-map-marker-alt text-sky-600 mr-2"></i>Location & Account
                        </h3>

                        <div class="bg-gray-50 rounded-lg p-4">
                            <label class="block text-sm font-medium text-gray-600 mb-1">Barangay</label>
                            <p class="text-gray-900 font-medium">{{ $mobileUser->barangay }}</p>
                        </div>

                        <div class="bg-gray-50 rounded-lg p-4">
                            <label class="block text-sm font-medium text-gray-600 mb-1">Status</label>
                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                                {{ $mobileUser->status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                <i class="fas {{ $mobileUser->status === 'Active' ? 'fa-check-circle' : 'fa-times-circle' }} text-xs mr-1"></i>
                                {{ $mobileUser->status }}
                            </span>
                        </div>

                        <div class="bg-gray-50 rounded-lg p-4">
                            <label class="block text-sm font-medium text-gray-600 mb-1">Registered On</label>
                            <p class="text-gray-900 font-medium">{{ $mobileUser->created_at->format('F d, Y \a\t g:i A') }}</p>
                        </div>

                        <div class="bg-gray-50 rounded-lg p-4">
                            <label class="block text-sm font-medium text-gray-600 mb-1">Last Updated</label>
                            <p class="text-gray-900 font-medium">{{ $mobileUser->updated_at->format('F d, Y \a\t g:i A') }}</p>
                        </div>
                    </div>
                </div>

                <!-- Address Section -->
                <div class="mt-8">
                    <h3 class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2 mb-4">
                        <i class="fas fa-home text-sky-600 mr-2"></i>Address
                    </h3>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-900">{{ $mobileUser->address }}</p>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end gap-4 mt-8 pt-6 border-t border-gray-200">
                    <a href="{{ route('components.user-management') }}"
                       class="inline-flex items-center gap-2 px-6 py-3 rounded-xl font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 transition-colors">
                        <i class="fas fa-arrow-left"></i>
                        Back to List
                    </a>
                    <a href="{{ route('components.user-management.edit', $mobileUser->id) }}"
                       class="inline-flex items-center gap-2 px-6 py-3 rounded-xl font-medium text-white bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 shadow-lg transition-all duration-200">
                        <i class="fas fa-edit"></i>
                        Edit User
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 