import{h as p,i as b}from"./chunk-ZOYALB5L.js";import{a as i,b as u,c as f,d as g,e as h}from"./chunk-BAKMWPBW.js";var O=t=>M(t),C=(t,o)=>(typeof t=="string"&&(o=t,t=void 0),O(t).includes(o)),M=(t=window)=>{if(typeof t>"u")return[];t.Ionic=t.Ionic||{};let o=t.Ionic.platforms;return o==null&&(o=t.Ionic.platforms=N(t),o.forEach(e=>t.document.documentElement.classList.add(`plt-${e}`))),o},N=t=>{let o=i.get("platform");return Object.keys(v).filter(e=>{let s=o?.[e];return typeof s=="function"?s(t):v[e](t)})},W=t=>l(t)&&!P(t),m=t=>!!(r(t,/iPad/i)||r(t,/Macintosh/i)&&l(t)),_=t=>r(t,/iPhone/i),k=t=>r(t,/iPhone|iPod/i)||m(t),I=t=>r(t,/android|sink/i),x=t=>I(t)&&!r(t,/mobile/i),L=t=>{let o=t.innerWidth,e=t.innerHeight,s=Math.min(o,e),a=Math.max(o,e);return s>390&&s<520&&a>620&&a<800},F=t=>{let o=t.innerWidth,e=t.innerHeight,s=Math.min(o,e),a=Math.max(o,e);return m(t)||x(t)||s>460&&s<820&&a>780&&a<1400},l=t=>B(t,"(any-pointer:coarse)"),H=t=>!l(t),P=t=>A(t)||y(t),A=t=>!!(t.cordova||t.phonegap||t.PhoneGap),y=t=>{let o=t.Capacitor;return!!(o?.isNative||o?.isNativePlatform&&o.isNativePlatform())},S=t=>r(t,/electron/i),T=t=>{var o;return!!(!((o=t.matchMedia)===null||o===void 0)&&o.call(t,"(display-mode: standalone)").matches||t.navigator.standalone)},r=(t,o)=>o.test(t.navigator.userAgent),B=(t,o)=>{var e;return(e=t.matchMedia)===null||e===void 0?void 0:e.call(t,o).matches},v={ipad:m,iphone:_,ios:k,android:I,phablet:L,tablet:F,cordova:A,capacitor:y,electron:S,pwa:T,mobile:l,mobileweb:W,desktop:H,hybrid:P},d,z=t=>t&&b(t)||d,D=(t={})=>{if(typeof window>"u")return;let o=window.document,e=window,s=e.Ionic=e.Ionic||{},a=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},u(e)),{persistConfig:!1}),s.config),g(e)),t);i.reset(a),i.getBoolean("persistConfig")&&f(e,a),M(e),s.config=i,s.mode=d=i.get("mode",o.documentElement.getAttribute("mode")||(C(e,"ios")?"ios":"md")),i.set("mode",d),o.documentElement.setAttribute("mode",d),o.documentElement.classList.add(d),i.getBoolean("_testing")&&i.set("animated",!1);let E=n=>{var c;return(c=n.tagName)===null||c===void 0?void 0:c.startsWith("ION-")},j=n=>["ios","md"].includes(n);p(n=>{for(;n;){let c=n.mode||n.getAttribute("mode");if(c){if(j(c))return c;E(n)&&h('Invalid ionic mode: "'+c+'", expected: "ios" or "md"')}n=n.parentElement}return d})};export{C as a,z as b,D as c};
