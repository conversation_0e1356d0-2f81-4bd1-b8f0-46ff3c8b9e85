.demo-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.header-section {
  text-align: center;
  margin-bottom: 30px;

  h1 {
    color: var(--ion-color-primary);
    margin-bottom: 10px;
  }

  p {
    color: var(--ion-color-medium);
    font-size: 16px;
  }
}

.section {
  margin-bottom: 30px;

  h2 {
    color: var(--ion-color-primary);
    margin-bottom: 15px;
    font-size: 18px;
  }
}

.destination-grid, .travel-mode-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.destination-card, .mode-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border-radius: 12px;
  background: var(--ion-color-light);
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: var(--ion-color-light-shade);
  }

  &.selected {
    background: var(--ion-color-primary-tint);
    border-color: var(--ion-color-primary);
    color: var(--ion-color-primary-shade);
  }

  ion-icon {
    font-size: 32px;
    margin-bottom: 10px;
  }

  span {
    font-weight: 500;
    text-align: center;
  }
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.status-item {
  display: flex;
  flex-direction: column;
  padding: 15px;
  background: var(--ion-color-light);
  border-radius: 8px;
  text-align: center;

  .label {
    font-size: 12px;
    color: var(--ion-color-medium);
    font-weight: 500;
    margin-bottom: 5px;
  }

  .value {
    font-size: 16px;
    font-weight: 700;
    color: var(--ion-color-dark);
  }
}

.instruction-card {
  background: var(--ion-color-primary);
  color: white;
  border-radius: 12px;
  padding: 20px;
}

.instruction-content {
  .instruction-text {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
  }

  .instruction-distance {
    font-size: 14px;
    opacity: 0.9;
  }
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.logs-container {
  background: var(--ion-color-dark);
  color: var(--ion-color-light);
  border-radius: 8px;
  padding: 15px;
  max-height: 300px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-entry {
  margin-bottom: 5px;
  word-wrap: break-word;

  &:last-child {
    margin-bottom: 0;
  }
}

.no-logs {
  text-align: center;
  color: var(--ion-color-medium);
  font-style: italic;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: var(--ion-color-light);
  border-radius: 8px;

  ion-icon {
    font-size: 24px;
    margin-right: 15px;
    flex-shrink: 0;
  }

  span {
    font-weight: 500;
    line-height: 1.4;
  }
}

// Responsive design
@media (max-width: 768px) {
  .demo-container {
    padding: 15px;
  }

  .destination-grid, .travel-mode-grid {
    grid-template-columns: 1fr;
  }

  .status-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .logs-container {
    max-height: 200px;
    font-size: 11px;
  }
}

// Animation for selected items
.destination-card.selected, .mode-card.selected {
  animation: pulse 0.3s ease-out;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
