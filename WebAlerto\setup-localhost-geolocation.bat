@echo off
echo ========================================
echo WebAlerto Localhost Geolocation Setup
echo ========================================
echo.

echo 1. Checking current environment...
echo Current directory: %CD%
echo.

echo 2. Starting Laravel development server...
echo.
echo Starting server on http://localhost:8000
echo.
echo IMPORTANT: After the server starts:
echo.
echo   Chrome Users:
echo   1. Visit: chrome://settings/content/location
echo   2. Add http://localhost:8000 to allowed sites
echo   3. OR click the lock icon when visiting the site
echo.
echo   Firefox Users:
echo   1. Visit the site and click the shield icon
echo   2. Allow location access when prompted
echo.
echo   Test Page: http://localhost:8000/test-geolocation
echo.
echo Press Ctrl+C to stop the server
echo ========================================
echo.

php artisan serve --host=localhost --port=8000
