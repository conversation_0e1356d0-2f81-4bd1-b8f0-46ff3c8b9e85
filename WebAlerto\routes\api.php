<?php

use App\Http\Controllers\Api\DeviceTokenController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\OfflineDataController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\EvacuationManagementController;
use App\Http\Controllers\AlertController;
use App\Http\Controllers\MobileUserController;
use App\Http\Controllers\Api\FCMController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes for mobile app
Route::post('auth/signup', [AuthController::class, 'signup']);
Route::post('auth/login', [AuthController::class, 'login']);
// Alternative routes for backward compatibility
Route::post('login', [AuthController::class, 'login']);
Route::post('signup', [AuthController::class, 'signup']);
Route::get('evacuation-centers', [EvacuationManagementController::class, 'apiList']);
Route::post('mobile-users', [MobileUserController::class, 'store']);
Route::apiResource('mobile-users', MobileUserController::class);

// Device token registration (public route for FCM)
Route::post('device-token', [DeviceTokenController::class, 'store']);
Route::post('device-token/deactivate', [DeviceTokenController::class, 'deactivate']);

// Public FCM routes for testing
Route::post('notifications/send', [FCMController::class, 'sendNotification']);
Route::post('notifications/test', [FCMController::class, 'testNotification']);
Route::post('notifications/user', [FCMController::class, 'sendNotificationToUser']);

// Disaster-specific test notification routes
Route::post('notifications/test-earthquake', [FCMController::class, 'sendTestEarthquakeNotification']);
Route::post('notifications/test-flood', [FCMController::class, 'sendTestFloodNotification']);
Route::post('notifications/test-typhoon', [FCMController::class, 'sendTestTyphoonNotification']);
Route::post('notifications/test-fire', [FCMController::class, 'sendTestFireNotification']);
Route::post('notifications/test-landslide', [FCMController::class, 'sendTestLandslideNotification']);
Route::post('notifications/test-other', [FCMController::class, 'sendTestOtherNotification']);

// Device tokens endpoint for admin interface
Route::get('device-tokens', [DeviceTokenController::class, 'getActiveTokens']);

// Offline data synchronization routes (public for emergency access)
Route::prefix('offline')->group(function () {
    Route::get('evacuation-centers', [OfflineDataController::class, 'getEvacuationCenters']);
    Route::get('evacuation-centers/{disasterType}', [OfflineDataController::class, 'getEvacuationCentersByType']);
    Route::get('nearest-centers', [OfflineDataController::class, 'getNearestCenters']);
    Route::get('sync-status', [OfflineDataController::class, 'getSyncStatus']);
    Route::post('check-updates', [OfflineDataController::class, 'checkUpdates']);
});

// Test notification endpoint
Route::post('test-notification', function(Request $request) {
    try {
        $token = $request->input('token');
        $title = $request->input('title', 'Test Notification');
        $message = $request->input('message', 'This is a test notification');
        $category = $request->input('category', 'General');
        $severity = $request->input('severity', 'medium');

        if (!$token) {
            return response()->json(['error' => 'Token is required'], 400);
        }

        // Create a notification record
        $notification = new \App\Models\Notification();
        $notification->title = $title;
        $notification->message = $message;
        $notification->category = $category;
        $notification->severity = $severity;
        $notification->barangay = 'All Areas'; // Required field
        $notification->sent = true; // Use 'sent' boolean instead of 'status'
        $notification->save();

        // Send the notification using FCM service
        $fcmService = app(\App\Services\FCMService::class);
        $result = $fcmService->sendToToken($token, $notification);

        return response()->json([
            'success' => true,
            'message' => 'Test notification sent',
            'result' => $result,
            'notification_id' => $notification->id
        ]);
    } catch (\Exception $e) {
        \Log::error('Error sending test notification: ' . $e->getMessage());
        return response()->json([
            'error' => 'Failed to send notification',
            'message' => $e->getMessage()
        ], 500);
    }
});



// Simulated notification routes
Route::get('simulated-notification/check', function() {
    return response()->json([
        'success' => true,
        'message' => 'No pending notifications',
        'notification' => null
    ]);
});

Route::post('simulated-notification/create', function(\Illuminate\Http\Request $request) {
    return response()->json([
        'success' => true,
        'message' => 'Notification created successfully',
        'notification' => [
            'title' => $request->title ?? 'Test Notification',
            'body' => $request->body ?? 'This is a test notification',
            'category' => $request->category ?? 'General',
            'severity' => $request->severity ?? 'medium',
            'time' => now()->toIso8601String(),
        ]
    ]);
});

// Alert routes
Route::get('alerts', [AlertController::class, 'index']);
Route::post('alerts', [AlertController::class, 'store']);
Route::get('alerts/{id}', [AlertController::class, 'show']);
Route::put('alerts/{id}', [AlertController::class, 'update']);
Route::delete('alerts/{id}', [AlertController::class, 'destroy']);

// FCM Routes (moved to protected section below)

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // User profile
    Route::get('/user', function (Request $request) {
        return $request->user();
    });

    // Auth logout
    Route::post('auth/logout', [AuthController::class, 'logout']);

    // Device token registration
    Route::post('/device-token/register', [DeviceTokenController::class, 'register']);
    Route::post('/device-token/deactivate', [DeviceTokenController::class, 'deactivate']);

    // FCM Routes (protected)
    Route::prefix('fcm')->group(function () {
        Route::post('/send', [FCMController::class, 'sendNotification']);
        Route::post('/test', [FCMController::class, 'testNotification']);
        Route::post('/user', [FCMController::class, 'sendNotificationToUser']);
    });

    // Notification routes
    Route::prefix('notifications')->group(function () {
        Route::get('/', [NotificationController::class, 'index']);
        Route::put('/{id}/read', [NotificationController::class, 'markAsRead']);
        Route::put('/mark-all-read', [NotificationController::class, 'markAllAsRead']);
        Route::get('/unread-count', [NotificationController::class, 'getUnreadCount']);
        Route::delete('/{id}', [NotificationController::class, 'destroy']);
        Route::post('/{id}/reaction', [NotificationController::class, 'addReaction']);
        Route::get('/stats', [NotificationController::class, 'getStats']);
    });
});

Route::get('/evacuation-centers/search', function (Request $request) {
    $query = $request->get('query');
    $type = $request->get('type', 'all');
    
    $searchQuery = \App\Models\Evacuation::where('status', 'Active')
        ->where(function($q) use ($query) {
            $q->where('name', 'like', "{$query}%")
              ->orWhere('street_name', 'like', "{$query}%")
              ->orWhere('barangay', 'like', "{$query}%")
              ->orWhere('city', 'like', "{$query}%");
        });

    // Apply disaster type filter if not 'all'
    if ($type !== 'all') {
        $searchQuery->where('disaster_type', $type);
    }
    
    return $searchQuery->select('id', 'name', 'street_name', 'barangay', 'city', 'province', 'latitude', 'longitude', 'disaster_type')
        ->limit(5)
        ->get();
});

// This new route fetches all active evacuation centers
Route::get('/evacuation-centers/all', function (Request $request) {
    return response()->json([
        'success' => true,
        'data' => \App\Models\Evacuation::where('status', 'Active')->get()
    ]);
});

// Server-side geocoding endpoints
Route::get('/geocode', [EvacuationManagementController::class, 'geocode']);
Route::get('/reverse-geocode', [EvacuationManagementController::class, 'reverseGeocode']);
            
            