<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\UserManagementRequest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;

class SystemAdminRequestManagementTest extends TestCase
{
    use RefreshDatabase;

    protected $systemAdmin;
    protected $chairman;
    protected $targetUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a system administrator
        $this->systemAdmin = User::create([
            'first_name' => 'System',
            'last_name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'system_admin',
            'position' => 'Technical Administrator',
            'status' => 'Active',
        ]);

        // Create a chairman (requester)
        $this->chairman = User::create([
            'first_name' => 'Chairman',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'position' => 'Barangay Chairman',
            'city' => 'Cebu City',
            'barangay' => 'Lahug',
            'status' => 'Active',
        ]);

        // Create a target user for deactivation/deletion requests
        $this->targetUser = User::create([
            'first_name' => 'Target',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'position' => 'Officer',
            'city' => 'Cebu City',
            'barangay' => 'Lahug',
            'status' => 'Active',
        ]);
    }

    /**
     * Test request management page loads for system admin
     */
    public function test_request_management_page_loads_for_system_admin()
    {
        $response = $this->actingAs($this->systemAdmin)
            ->get('/system-admin/request-management');

        $response->assertStatus(200);
        $response->assertViewIs('components.system-admin.request-management');
        $response->assertViewHas(['requests', 'stats']);
    }

    /**
     * Test non-system admin cannot access request management
     */
    public function test_non_system_admin_cannot_access_request_management()
    {
        $response = $this->actingAs($this->chairman)
            ->get('/system-admin/request-management');

        $response->assertStatus(403);
    }

    /**
     * Test viewing request details
     */
    public function test_system_admin_can_view_request_details()
    {
        $request = UserManagementRequest::create([
            'requester_id' => $this->chairman->id,
            'target_user_id' => $this->targetUser->id,
            'action_type' => 'deactivate',
            'reason' => 'Test deactivation request',
            'status' => 'pending',
        ]);

        $response = $this->actingAs($this->systemAdmin)
            ->get("/system-admin/requests/{$request->id}/view");

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'request' => [
                'id' => $request->id,
                'action_type' => 'deactivate',
                'status' => 'pending',
                'reason' => 'Test deactivation request',
            ]
        ]);
    }

    /**
     * Test approving a deactivation request
     */
    public function test_system_admin_can_approve_deactivation_request()
    {
        $request = UserManagementRequest::create([
            'requester_id' => $this->chairman->id,
            'target_user_id' => $this->targetUser->id,
            'action_type' => 'deactivate',
            'reason' => 'Test deactivation request',
            'status' => 'pending',
        ]);

        $response = $this->actingAs($this->systemAdmin)
            ->post("/system-admin/requests/{$request->id}/review", [
                'action' => 'approve',
                'review_notes' => 'Approved for testing'
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify request was approved
        $request->refresh();
        $this->assertEquals('approved', $request->status);
        $this->assertEquals($this->systemAdmin->id, $request->reviewed_by);
        $this->assertEquals('Approved for testing', $request->review_notes);

        // Verify target user was deactivated
        $this->targetUser->refresh();
        $this->assertEquals('Inactive', $this->targetUser->status);
    }

    /**
     * Test rejecting a request
     */
    public function test_system_admin_can_reject_request()
    {
        $request = UserManagementRequest::create([
            'requester_id' => $this->chairman->id,
            'target_user_id' => $this->targetUser->id,
            'action_type' => 'deactivate',
            'reason' => 'Test deactivation request',
            'status' => 'pending',
        ]);

        $response = $this->actingAs($this->systemAdmin)
            ->post("/system-admin/requests/{$request->id}/review", [
                'action' => 'reject',
                'review_notes' => 'Rejected for testing'
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify request was rejected
        $request->refresh();
        $this->assertEquals('rejected', $request->status);
        $this->assertEquals($this->systemAdmin->id, $request->reviewed_by);
        $this->assertEquals('Rejected for testing', $request->review_notes);

        // Verify target user status unchanged
        $this->targetUser->refresh();
        $this->assertEquals('Active', $this->targetUser->status);
    }

    /**
     * Test approving a deletion request
     */
    public function test_system_admin_can_approve_deletion_request()
    {
        $request = UserManagementRequest::create([
            'requester_id' => $this->chairman->id,
            'target_user_id' => $this->targetUser->id,
            'action_type' => 'delete',
            'reason' => 'Test deletion request',
            'status' => 'pending',
        ]);

        $targetUserId = $this->targetUser->id;

        $response = $this->actingAs($this->systemAdmin)
            ->post("/system-admin/requests/{$request->id}/review", [
                'action' => 'approve',
                'review_notes' => 'Approved deletion'
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify request was approved
        $request->refresh();
        $this->assertEquals('approved', $request->status);

        // Verify target user was deleted
        $this->assertDatabaseMissing('users', ['id' => $targetUserId]);
    }

    /**
     * Test approving a registration request
     */
    public function test_system_admin_can_approve_registration_request()
    {
        Mail::fake(); // Prevent actual email sending

        $request = UserManagementRequest::create([
            'requester_id' => $this->chairman->id,
            'target_user_id' => null,
            'action_type' => 'registration',
            'reason' => 'Need new officer',
            'status' => 'pending',
            'requested_first_name' => 'New',
            'requested_last_name' => 'Officer',
            'requested_email' => '<EMAIL>',
            'requested_position' => 'BDRRMC Officer',
            'requested_barangay' => 'Lahug',
        ]);

        $response = $this->actingAs($this->systemAdmin)
            ->post("/system-admin/requests/{$request->id}/review", [
                'action' => 'approve',
                'review_notes' => 'Approved new officer registration'
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify request was approved
        $request->refresh();
        $this->assertEquals('approved', $request->status);

        // Verify new user was created
        $this->assertDatabaseHas('users', [
            'first_name' => 'New',
            'last_name' => 'Officer',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'position' => 'BDRRMC Officer',
            'barangay' => 'Lahug',
            'status' => 'Active',
        ]);
    }

    /**
     * Test request management with filters
     */
    public function test_request_management_with_filters()
    {
        // Create different types of requests
        UserManagementRequest::create([
            'requester_id' => $this->chairman->id,
            'target_user_id' => $this->targetUser->id,
            'action_type' => 'deactivate',
            'reason' => 'Test',
            'status' => 'pending',
        ]);

        UserManagementRequest::create([
            'requester_id' => $this->chairman->id,
            'target_user_id' => null,
            'action_type' => 'registration',
            'reason' => 'Test',
            'status' => 'approved',
            'requested_first_name' => 'Test',
            'requested_last_name' => 'User',
            'requested_email' => '<EMAIL>',
            'requested_position' => 'Officer',
            'requested_barangay' => 'Lahug',
        ]);

        // Test status filter
        $response = $this->actingAs($this->systemAdmin)
            ->get('/system-admin/request-management?status=pending');
        $response->assertStatus(200);

        // Test action type filter
        $response = $this->actingAs($this->systemAdmin)
            ->get('/system-admin/request-management?action_type=registration');
        $response->assertStatus(200);

        // Test barangay filter
        $response = $this->actingAs($this->systemAdmin)
            ->get('/system-admin/request-management?barangay=Lahug');
        $response->assertStatus(200);
    }

    /**
     * Test request statistics
     */
    public function test_request_statistics()
    {
        // Create requests with different statuses
        UserManagementRequest::create([
            'requester_id' => $this->chairman->id,
            'target_user_id' => $this->targetUser->id,
            'action_type' => 'deactivate',
            'reason' => 'Test',
            'status' => 'pending',
        ]);

        UserManagementRequest::create([
            'requester_id' => $this->chairman->id,
            'target_user_id' => $this->targetUser->id,
            'action_type' => 'delete',
            'reason' => 'Test',
            'status' => 'approved',
        ]);

        $response = $this->actingAs($this->systemAdmin)
            ->get('/system-admin/request-management');

        $response->assertStatus(200);
        
        $stats = $response->viewData('stats');
        $this->assertEquals(1, $stats['pending']);
        $this->assertEquals(1, $stats['approved']);
        $this->assertEquals(2, $stats['total']);
    }

    /**
     * Test handling non-existent request
     */
    public function test_handling_non_existent_request()
    {
        $response = $this->actingAs($this->systemAdmin)
            ->get('/system-admin/requests/99999/view');
        $response->assertStatus(404);

        $response = $this->actingAs($this->systemAdmin)
            ->post('/system-admin/requests/99999/review', [
                'action' => 'approve',
                'review_notes' => 'Test'
            ]);
        $response->assertStatus(404);
    }
}
