<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Barangay;
use App\Services\PSGCService;
use Illuminate\Support\Facades\DB;

class BarangaySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $this->command->info('🌐 Fetching all Cebu Province barangays from PSGC API...');

        // Disable foreign key checks to avoid issues with truncation
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        // Truncate the table to start fresh
        Barangay::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // Initialize PSGC service
        $psgcService = new PSGCService();

        // Fetch all cities in Cebu Province
        $cities = $psgcService->getCebuProvinceCities();
        $allBarangays = [];

        foreach ($cities as $city) {
            $this->command->info("📍 Fetching barangays for {$city['name']}...");
            $cityBarangays = $psgcService->getCityBarangays($city['code'], $city['name']);
            $allBarangays = array_merge($allBarangays, $cityBarangays);
        }

        $barangays = $allBarangays;

        if (empty($barangays)) {
            $this->command->error('❌ Failed to fetch barangays from PSGC API and fallback data is empty');
            return;
        }

        $this->command->info("📍 Found " . count($barangays) . " barangays for Cebu City");

        // Insert the data into the database
        $successCount = 0;
        foreach ($barangays as $barangay) {
            try {
                Barangay::create(array_merge($barangay, [
                    'created_at' => now(),
                    'updated_at' => now()
                ]));
                $successCount++;
            } catch (\Exception $e) {
                $this->command->error("Failed to create barangay: {$barangay['name']} - " . $e->getMessage());
            }
        }

        $this->command->info("✅ Successfully seeded {$successCount} barangays from PSGC API");

        if ($successCount < count($barangays)) {
            $this->command->warn("⚠️  Some barangays failed to seed. Check logs for details.");
        }
    }
} 