<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AdminEventNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The event title.
     *
     * @var string
     */
    protected $title;

    /**
     * The event message.
     *
     * @var string
     */
    protected $message;

    /**
     * The event type.
     *
     * @var string
     */
    protected $eventType;

    /**
     * The event data.
     *
     * @var array
     */
    protected $eventData;

    /**
     * Create a new notification instance.
     */
    public function __construct(string $title, string $message, string $eventType, array $eventData = [])
    {
        $this->title = $title;
        $this->message = $message;
        $this->eventType = $eventType;
        $this->eventData = $eventData;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $mailMessage = (new MailMessage)
            ->subject($this->title)
            ->greeting('Hello ' . $notifiable->first_name . '!')
            ->line($this->message);

        // Add specific content based on event type
        switch ($this->eventType) {
            case 'new_alert':
                $mailMessage->line('A new emergency alert has been issued.')
                    ->line('Please check your ALERTO dashboard for more details.')
                    ->action('View Dashboard', url('/admin/dashboard'));
                break;

            case 'system_update':
                $mailMessage->line('The ALERTO system has been updated.')
                    ->line('New features or improvements may be available.')
                    ->action('Access System', url('/admin/dashboard'));
                break;

            case 'user_registration':
                $mailMessage->line('A new user has registered in your barangay.')
                    ->line('Please review and approve their account if necessary.')
                    ->action('Manage Users', url('/admin/users'));
                break;

            case 'evacuation_center_update':
                $mailMessage->line('Evacuation center information has been updated.')
                    ->line('Please review the changes in your dashboard.')
                    ->action('View Centers', url('/admin/evacuation-centers'));
                break;

            case 'system_maintenance':
                $mailMessage->line('Scheduled system maintenance will occur.')
                    ->line('The system may be temporarily unavailable.')
                    ->action('Check Status', url('/admin/dashboard'));
                break;

            default:
                $mailMessage->action('View Details', url('/admin/dashboard'));
        }

        $mailMessage->line('Thank you for using ALERTO!')
            ->salutation('Best regards, ALERTO Team');

        return $mailMessage;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'title' => $this->title,
            'message' => $this->message,
            'event_type' => $this->eventType,
            'event_data' => $this->eventData,
            'user_id' => $notifiable->id,
            'barangay' => $notifiable->barangay,
        ];
    }
} 