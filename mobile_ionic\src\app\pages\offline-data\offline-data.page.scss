ion-content {
  --background: #f5f5f5;
}

ion-card {
  margin: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

ion-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
}

ion-card-subtitle {
  color: var(--ion-color-medium);
  font-size: 14px;
  margin-top: 4px;
}

ion-list {
  background: transparent;
}

ion-item {
  --background: transparent;
  --border-color: rgba(0, 0, 0, 0.1);
  --padding-start: 0;
  --inner-padding-end: 0;
  margin-bottom: 8px;
  border-radius: 8px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.contact-description {
  font-size: 12px;
  color: var(--ion-color-medium);
  margin-top: 2px;
}

.cache-actions {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

ion-chip {
  font-size: 12px;
  height: 24px;
}

ion-badge {
  font-size: 10px;
  margin-left: 8px;
}

// Network status styling
ion-card:first-child {
  ion-chip {
    margin: 0;
  }
}

// Emergency contacts styling
.emergency-contacts {
  ion-item {
    cursor: pointer;
    transition: background-color 0.2s ease;
    
    &:hover {
      --background: rgba(var(--ion-color-primary-rgb), 0.05);
    }
  }
}

// Cache metadata styling
.cache-metadata {
  ion-item {
    ion-label {
      h3 {
        font-weight: 500;
        text-transform: capitalize;
      }
      
      p {
        font-size: 13px;
        color: var(--ion-color-medium);
        margin: 2px 0;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  ion-card {
    margin: 12px 8px;
  }
  
  ion-card-title {
    font-size: 16px;
  }
  
  .cache-actions {
    ion-button {
      font-size: 14px;
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  ion-content {
    --background: #1a1a1a;
  }
  
  ion-card {
    --background: #2a2a2a;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  
  ion-item {
    --border-color: rgba(255, 255, 255, 0.1);
  }
}

// Loading states
.loading-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: var(--ion-color-medium);
}

// Empty states
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--ion-color-medium);
  
  ion-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  h3 {
    margin: 0 0 8px 0;
    font-weight: 500;
  }
  
  p {
    margin: 0;
    font-size: 14px;
  }
}

// Status indicators
.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  
  &.online {
    color: var(--ion-color-success);
  }
  
  &.offline {
    color: var(--ion-color-danger);
  }
}

// Priority indicators for emergency contacts
.priority-high {
  border-left: 4px solid var(--ion-color-danger);
}

.priority-medium {
  border-left: 4px solid var(--ion-color-warning);
}

.priority-low {
  border-left: 4px solid var(--ion-color-medium);
}
