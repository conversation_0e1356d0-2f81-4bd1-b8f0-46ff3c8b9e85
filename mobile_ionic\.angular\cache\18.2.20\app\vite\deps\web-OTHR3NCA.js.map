{"version": 3, "sources": ["../../../../../../node_modules/@capacitor/filesystem/dist/esm/web.js"], "sourcesContent": ["import { WebPlugin, buildRequestInit } from '@capacitor/core';\nimport { Encoding } from './definitions';\nfunction resolve(path) {\n  const posix = path.split('/').filter(item => item !== '.');\n  const newPosix = [];\n  posix.forEach(item => {\n    if (item === '..' && newPosix.length > 0 && newPosix[newPosix.length - 1] !== '..') {\n      newPosix.pop();\n    } else {\n      newPosix.push(item);\n    }\n  });\n  return newPosix.join('/');\n}\nfunction isPathParent(parent, children) {\n  parent = resolve(parent);\n  children = resolve(children);\n  const pathsA = parent.split('/');\n  const pathsB = children.split('/');\n  return parent !== children && pathsA.every((value, index) => value === pathsB[index]);\n}\nexport class FilesystemWeb extends WebPlugin {\n  constructor() {\n    super(...arguments);\n    this.DB_VERSION = 1;\n    this.DB_NAME = 'Disc';\n    this._writeCmds = ['add', 'put', 'delete'];\n    /**\n     * Function that performs a http request to a server and downloads the file to the specified destination\n     *\n     * @deprecated Use the @capacitor/file-transfer plugin instead.\n     * @param options the options for the download operation\n     * @returns a promise that resolves with the download file result\n     */\n    this.downloadFile = async options => {\n      var _a, _b;\n      const requestInit = buildRequestInit(options, options.webFetchExtra);\n      const response = await fetch(options.url, requestInit);\n      let blob;\n      if (!options.progress) blob = await response.blob();else if (!(response === null || response === void 0 ? void 0 : response.body)) blob = new Blob();else {\n        const reader = response.body.getReader();\n        let bytes = 0;\n        const chunks = [];\n        const contentType = response.headers.get('content-type');\n        const contentLength = parseInt(response.headers.get('content-length') || '0', 10);\n        while (true) {\n          const {\n            done,\n            value\n          } = await reader.read();\n          if (done) break;\n          chunks.push(value);\n          bytes += (value === null || value === void 0 ? void 0 : value.length) || 0;\n          const status = {\n            url: options.url,\n            bytes,\n            contentLength\n          };\n          this.notifyListeners('progress', status);\n        }\n        const allChunks = new Uint8Array(bytes);\n        let position = 0;\n        for (const chunk of chunks) {\n          if (typeof chunk === 'undefined') continue;\n          allChunks.set(chunk, position);\n          position += chunk.length;\n        }\n        blob = new Blob([allChunks.buffer], {\n          type: contentType || undefined\n        });\n      }\n      const result = await this.writeFile({\n        path: options.path,\n        directory: (_a = options.directory) !== null && _a !== void 0 ? _a : undefined,\n        recursive: (_b = options.recursive) !== null && _b !== void 0 ? _b : false,\n        data: blob\n      });\n      return {\n        path: result.uri,\n        blob\n      };\n    };\n  }\n  readFileInChunks(_options, _callback) {\n    throw this.unavailable('Method not implemented.');\n  }\n  async initDb() {\n    if (this._db !== undefined) {\n      return this._db;\n    }\n    if (!('indexedDB' in window)) {\n      throw this.unavailable(\"This browser doesn't support IndexedDB\");\n    }\n    return new Promise((resolve, reject) => {\n      const request = indexedDB.open(this.DB_NAME, this.DB_VERSION);\n      request.onupgradeneeded = FilesystemWeb.doUpgrade;\n      request.onsuccess = () => {\n        this._db = request.result;\n        resolve(request.result);\n      };\n      request.onerror = () => reject(request.error);\n      request.onblocked = () => {\n        console.warn('db blocked');\n      };\n    });\n  }\n  static doUpgrade(event) {\n    const eventTarget = event.target;\n    const db = eventTarget.result;\n    switch (event.oldVersion) {\n      case 0:\n      case 1:\n      default:\n        {\n          if (db.objectStoreNames.contains('FileStorage')) {\n            db.deleteObjectStore('FileStorage');\n          }\n          const store = db.createObjectStore('FileStorage', {\n            keyPath: 'path'\n          });\n          store.createIndex('by_folder', 'folder');\n        }\n    }\n  }\n  async dbRequest(cmd, args) {\n    const readFlag = this._writeCmds.indexOf(cmd) !== -1 ? 'readwrite' : 'readonly';\n    return this.initDb().then(conn => {\n      return new Promise((resolve, reject) => {\n        const tx = conn.transaction(['FileStorage'], readFlag);\n        const store = tx.objectStore('FileStorage');\n        const req = store[cmd](...args);\n        req.onsuccess = () => resolve(req.result);\n        req.onerror = () => reject(req.error);\n      });\n    });\n  }\n  async dbIndexRequest(indexName, cmd, args) {\n    const readFlag = this._writeCmds.indexOf(cmd) !== -1 ? 'readwrite' : 'readonly';\n    return this.initDb().then(conn => {\n      return new Promise((resolve, reject) => {\n        const tx = conn.transaction(['FileStorage'], readFlag);\n        const store = tx.objectStore('FileStorage');\n        const index = store.index(indexName);\n        const req = index[cmd](...args);\n        req.onsuccess = () => resolve(req.result);\n        req.onerror = () => reject(req.error);\n      });\n    });\n  }\n  getPath(directory, uriPath) {\n    const cleanedUriPath = uriPath !== undefined ? uriPath.replace(/^[/]+|[/]+$/g, '') : '';\n    let fsPath = '';\n    if (directory !== undefined) fsPath += '/' + directory;\n    if (uriPath !== '') fsPath += '/' + cleanedUriPath;\n    return fsPath;\n  }\n  async clear() {\n    const conn = await this.initDb();\n    const tx = conn.transaction(['FileStorage'], 'readwrite');\n    const store = tx.objectStore('FileStorage');\n    store.clear();\n  }\n  /**\n   * Read a file from disk\n   * @param options options for the file read\n   * @return a promise that resolves with the read file data result\n   */\n  async readFile(options) {\n    const path = this.getPath(options.directory, options.path);\n    // const encoding = options.encoding;\n    const entry = await this.dbRequest('get', [path]);\n    if (entry === undefined) throw Error('File does not exist.');\n    return {\n      data: entry.content ? entry.content : ''\n    };\n  }\n  /**\n   * Write a file to disk in the specified location on device\n   * @param options options for the file write\n   * @return a promise that resolves with the file write result\n   */\n  async writeFile(options) {\n    const path = this.getPath(options.directory, options.path);\n    let data = options.data;\n    const encoding = options.encoding;\n    const doRecursive = options.recursive;\n    const occupiedEntry = await this.dbRequest('get', [path]);\n    if (occupiedEntry && occupiedEntry.type === 'directory') throw Error('The supplied path is a directory.');\n    const parentPath = path.substr(0, path.lastIndexOf('/'));\n    const parentEntry = await this.dbRequest('get', [parentPath]);\n    if (parentEntry === undefined) {\n      const subDirIndex = parentPath.indexOf('/', 1);\n      if (subDirIndex !== -1) {\n        const parentArgPath = parentPath.substr(subDirIndex);\n        await this.mkdir({\n          path: parentArgPath,\n          directory: options.directory,\n          recursive: doRecursive\n        });\n      }\n    }\n    if (!encoding && !(data instanceof Blob)) {\n      data = data.indexOf(',') >= 0 ? data.split(',')[1] : data;\n      if (!this.isBase64String(data)) throw Error('The supplied data is not valid base64 content.');\n    }\n    const now = Date.now();\n    const pathObj = {\n      path: path,\n      folder: parentPath,\n      type: 'file',\n      size: data instanceof Blob ? data.size : data.length,\n      ctime: now,\n      mtime: now,\n      content: data\n    };\n    await this.dbRequest('put', [pathObj]);\n    return {\n      uri: pathObj.path\n    };\n  }\n  /**\n   * Append to a file on disk in the specified location on device\n   * @param options options for the file append\n   * @return a promise that resolves with the file write result\n   */\n  async appendFile(options) {\n    const path = this.getPath(options.directory, options.path);\n    let data = options.data;\n    const encoding = options.encoding;\n    const parentPath = path.substr(0, path.lastIndexOf('/'));\n    const now = Date.now();\n    let ctime = now;\n    const occupiedEntry = await this.dbRequest('get', [path]);\n    if (occupiedEntry && occupiedEntry.type === 'directory') throw Error('The supplied path is a directory.');\n    const parentEntry = await this.dbRequest('get', [parentPath]);\n    if (parentEntry === undefined) {\n      const subDirIndex = parentPath.indexOf('/', 1);\n      if (subDirIndex !== -1) {\n        const parentArgPath = parentPath.substr(subDirIndex);\n        await this.mkdir({\n          path: parentArgPath,\n          directory: options.directory,\n          recursive: true\n        });\n      }\n    }\n    if (!encoding && !this.isBase64String(data)) throw Error('The supplied data is not valid base64 content.');\n    if (occupiedEntry !== undefined) {\n      if (occupiedEntry.content instanceof Blob) {\n        throw Error('The occupied entry contains a Blob object which cannot be appended to.');\n      }\n      if (occupiedEntry.content !== undefined && !encoding) {\n        data = btoa(atob(occupiedEntry.content) + atob(data));\n      } else {\n        data = occupiedEntry.content + data;\n      }\n      ctime = occupiedEntry.ctime;\n    }\n    const pathObj = {\n      path: path,\n      folder: parentPath,\n      type: 'file',\n      size: data.length,\n      ctime: ctime,\n      mtime: now,\n      content: data\n    };\n    await this.dbRequest('put', [pathObj]);\n  }\n  /**\n   * Delete a file from disk\n   * @param options options for the file delete\n   * @return a promise that resolves with the deleted file data result\n   */\n  async deleteFile(options) {\n    const path = this.getPath(options.directory, options.path);\n    const entry = await this.dbRequest('get', [path]);\n    if (entry === undefined) throw Error('File does not exist.');\n    const entries = await this.dbIndexRequest('by_folder', 'getAllKeys', [IDBKeyRange.only(path)]);\n    if (entries.length !== 0) throw Error('Folder is not empty.');\n    await this.dbRequest('delete', [path]);\n  }\n  /**\n   * Create a directory.\n   * @param options options for the mkdir\n   * @return a promise that resolves with the mkdir result\n   */\n  async mkdir(options) {\n    const path = this.getPath(options.directory, options.path);\n    const doRecursive = options.recursive;\n    const parentPath = path.substr(0, path.lastIndexOf('/'));\n    const depth = (path.match(/\\//g) || []).length;\n    const parentEntry = await this.dbRequest('get', [parentPath]);\n    const occupiedEntry = await this.dbRequest('get', [path]);\n    if (depth === 1) throw Error('Cannot create Root directory');\n    if (occupiedEntry !== undefined) throw Error('Current directory does already exist.');\n    if (!doRecursive && depth !== 2 && parentEntry === undefined) throw Error('Parent directory must exist');\n    if (doRecursive && depth !== 2 && parentEntry === undefined) {\n      const parentArgPath = parentPath.substr(parentPath.indexOf('/', 1));\n      await this.mkdir({\n        path: parentArgPath,\n        directory: options.directory,\n        recursive: doRecursive\n      });\n    }\n    const now = Date.now();\n    const pathObj = {\n      path: path,\n      folder: parentPath,\n      type: 'directory',\n      size: 0,\n      ctime: now,\n      mtime: now\n    };\n    await this.dbRequest('put', [pathObj]);\n  }\n  /**\n   * Remove a directory\n   * @param options the options for the directory remove\n   */\n  async rmdir(options) {\n    const {\n      path,\n      directory,\n      recursive\n    } = options;\n    const fullPath = this.getPath(directory, path);\n    const entry = await this.dbRequest('get', [fullPath]);\n    if (entry === undefined) throw Error('Folder does not exist.');\n    if (entry.type !== 'directory') throw Error('Requested path is not a directory');\n    const readDirResult = await this.readdir({\n      path,\n      directory\n    });\n    if (readDirResult.files.length !== 0 && !recursive) throw Error('Folder is not empty');\n    for (const entry of readDirResult.files) {\n      const entryPath = `${path}/${entry.name}`;\n      const entryObj = await this.stat({\n        path: entryPath,\n        directory\n      });\n      if (entryObj.type === 'file') {\n        await this.deleteFile({\n          path: entryPath,\n          directory\n        });\n      } else {\n        await this.rmdir({\n          path: entryPath,\n          directory,\n          recursive\n        });\n      }\n    }\n    await this.dbRequest('delete', [fullPath]);\n  }\n  /**\n   * Return a list of files from the directory (not recursive)\n   * @param options the options for the readdir operation\n   * @return a promise that resolves with the readdir directory listing result\n   */\n  async readdir(options) {\n    const path = this.getPath(options.directory, options.path);\n    const entry = await this.dbRequest('get', [path]);\n    if (options.path !== '' && entry === undefined) throw Error('Folder does not exist.');\n    const entries = await this.dbIndexRequest('by_folder', 'getAllKeys', [IDBKeyRange.only(path)]);\n    const files = await Promise.all(entries.map(async e => {\n      let subEntry = await this.dbRequest('get', [e]);\n      if (subEntry === undefined) {\n        subEntry = await this.dbRequest('get', [e + '/']);\n      }\n      return {\n        name: e.substring(path.length + 1),\n        type: subEntry.type,\n        size: subEntry.size,\n        ctime: subEntry.ctime,\n        mtime: subEntry.mtime,\n        uri: subEntry.path\n      };\n    }));\n    return {\n      files: files\n    };\n  }\n  /**\n   * Return full File URI for a path and directory\n   * @param options the options for the stat operation\n   * @return a promise that resolves with the file stat result\n   */\n  async getUri(options) {\n    const path = this.getPath(options.directory, options.path);\n    let entry = await this.dbRequest('get', [path]);\n    if (entry === undefined) {\n      entry = await this.dbRequest('get', [path + '/']);\n    }\n    return {\n      uri: (entry === null || entry === void 0 ? void 0 : entry.path) || path\n    };\n  }\n  /**\n   * Return data about a file\n   * @param options the options for the stat operation\n   * @return a promise that resolves with the file stat result\n   */\n  async stat(options) {\n    const path = this.getPath(options.directory, options.path);\n    let entry = await this.dbRequest('get', [path]);\n    if (entry === undefined) {\n      entry = await this.dbRequest('get', [path + '/']);\n    }\n    if (entry === undefined) throw Error('Entry does not exist.');\n    return {\n      name: entry.path.substring(path.length + 1),\n      type: entry.type,\n      size: entry.size,\n      ctime: entry.ctime,\n      mtime: entry.mtime,\n      uri: entry.path\n    };\n  }\n  /**\n   * Rename a file or directory\n   * @param options the options for the rename operation\n   * @return a promise that resolves with the rename result\n   */\n  async rename(options) {\n    await this._copy(options, true);\n    return;\n  }\n  /**\n   * Copy a file or directory\n   * @param options the options for the copy operation\n   * @return a promise that resolves with the copy result\n   */\n  async copy(options) {\n    return this._copy(options, false);\n  }\n  async requestPermissions() {\n    return {\n      publicStorage: 'granted'\n    };\n  }\n  async checkPermissions() {\n    return {\n      publicStorage: 'granted'\n    };\n  }\n  /**\n   * Function that can perform a copy or a rename\n   * @param options the options for the rename operation\n   * @param doRename whether to perform a rename or copy operation\n   * @return a promise that resolves with the result\n   */\n  async _copy(options, doRename = false) {\n    let {\n      toDirectory\n    } = options;\n    const {\n      to,\n      from,\n      directory: fromDirectory\n    } = options;\n    if (!to || !from) {\n      throw Error('Both to and from must be provided');\n    }\n    // If no \"to\" directory is provided, use the \"from\" directory\n    if (!toDirectory) {\n      toDirectory = fromDirectory;\n    }\n    const fromPath = this.getPath(fromDirectory, from);\n    const toPath = this.getPath(toDirectory, to);\n    // Test that the \"to\" and \"from\" locations are different\n    if (fromPath === toPath) {\n      return {\n        uri: toPath\n      };\n    }\n    if (isPathParent(fromPath, toPath)) {\n      throw Error('To path cannot contain the from path');\n    }\n    // Check the state of the \"to\" location\n    let toObj;\n    try {\n      toObj = await this.stat({\n        path: to,\n        directory: toDirectory\n      });\n    } catch (e) {\n      // To location does not exist, ensure the directory containing \"to\" location exists and is a directory\n      const toPathComponents = to.split('/');\n      toPathComponents.pop();\n      const toPath = toPathComponents.join('/');\n      // Check the containing directory of the \"to\" location exists\n      if (toPathComponents.length > 0) {\n        const toParentDirectory = await this.stat({\n          path: toPath,\n          directory: toDirectory\n        });\n        if (toParentDirectory.type !== 'directory') {\n          throw new Error('Parent directory of the to path is a file');\n        }\n      }\n    }\n    // Cannot overwrite a directory\n    if (toObj && toObj.type === 'directory') {\n      throw new Error('Cannot overwrite a directory with a file');\n    }\n    // Ensure the \"from\" object exists\n    const fromObj = await this.stat({\n      path: from,\n      directory: fromDirectory\n    });\n    // Set the mtime/ctime of the supplied path\n    const updateTime = async (path, ctime, mtime) => {\n      const fullPath = this.getPath(toDirectory, path);\n      const entry = await this.dbRequest('get', [fullPath]);\n      entry.ctime = ctime;\n      entry.mtime = mtime;\n      await this.dbRequest('put', [entry]);\n    };\n    const ctime = fromObj.ctime ? fromObj.ctime : Date.now();\n    switch (fromObj.type) {\n      // The \"from\" object is a file\n      case 'file':\n        {\n          // Read the file\n          const file = await this.readFile({\n            path: from,\n            directory: fromDirectory\n          });\n          // Optionally remove the file\n          if (doRename) {\n            await this.deleteFile({\n              path: from,\n              directory: fromDirectory\n            });\n          }\n          let encoding;\n          if (!(file.data instanceof Blob) && !this.isBase64String(file.data)) {\n            encoding = Encoding.UTF8;\n          }\n          // Write the file to the new location\n          const writeResult = await this.writeFile({\n            path: to,\n            directory: toDirectory,\n            data: file.data,\n            encoding: encoding\n          });\n          // Copy the mtime/ctime of a renamed file\n          if (doRename) {\n            await updateTime(to, ctime, fromObj.mtime);\n          }\n          // Resolve promise\n          return writeResult;\n        }\n      case 'directory':\n        {\n          if (toObj) {\n            throw Error('Cannot move a directory over an existing object');\n          }\n          try {\n            // Create the to directory\n            await this.mkdir({\n              path: to,\n              directory: toDirectory,\n              recursive: false\n            });\n            // Copy the mtime/ctime of a renamed directory\n            if (doRename) {\n              await updateTime(to, ctime, fromObj.mtime);\n            }\n          } catch (e) {\n            // ignore\n          }\n          // Iterate over the contents of the from location\n          const contents = (await this.readdir({\n            path: from,\n            directory: fromDirectory\n          })).files;\n          for (const filename of contents) {\n            // Move item from the from directory to the to directory\n            await this._copy({\n              from: `${from}/${filename.name}`,\n              to: `${to}/${filename.name}`,\n              directory: fromDirectory,\n              toDirectory\n            }, doRename);\n          }\n          // Optionally remove the original from directory\n          if (doRename) {\n            await this.rmdir({\n              path: from,\n              directory: fromDirectory\n            });\n          }\n        }\n    }\n    return {\n      uri: toPath\n    };\n  }\n  isBase64String(str) {\n    try {\n      return btoa(atob(str)) == str;\n    } catch (err) {\n      return false;\n    }\n  }\n}\nFilesystemWeb._debug = true;\n"], "mappings": ";;;;;;;;;;;;AAEA,SAAS,QAAQ,MAAM;AACrB,QAAM,QAAQ,KAAK,MAAM,GAAG,EAAE,OAAO,UAAQ,SAAS,GAAG;AACzD,QAAM,WAAW,CAAC;AAClB,QAAM,QAAQ,UAAQ;AACpB,QAAI,SAAS,QAAQ,SAAS,SAAS,KAAK,SAAS,SAAS,SAAS,CAAC,MAAM,MAAM;AAClF,eAAS,IAAI;AAAA,IACf,OAAO;AACL,eAAS,KAAK,IAAI;AAAA,IACpB;AAAA,EACF,CAAC;AACD,SAAO,SAAS,KAAK,GAAG;AAC1B;AACA,SAAS,aAAa,QAAQ,UAAU;AACtC,WAAS,QAAQ,MAAM;AACvB,aAAW,QAAQ,QAAQ;AAC3B,QAAM,SAAS,OAAO,MAAM,GAAG;AAC/B,QAAM,SAAS,SAAS,MAAM,GAAG;AACjC,SAAO,WAAW,YAAY,OAAO,MAAM,CAAC,OAAO,UAAU,UAAU,OAAO,KAAK,CAAC;AACtF;AACO,IAAM,gBAAN,MAAM,uBAAsB,UAAU;AAAA,EAC3C,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,aAAa,CAAC,OAAO,OAAO,QAAQ;AAQzC,SAAK,eAAe,CAAM,YAAW;AACnC,UAAI,IAAI;AACR,YAAM,cAAc,iBAAiB,SAAS,QAAQ,aAAa;AACnE,YAAM,WAAW,MAAM,MAAM,QAAQ,KAAK,WAAW;AACrD,UAAI;AACJ,UAAI,CAAC,QAAQ,SAAU,QAAO,MAAM,SAAS,KAAK;AAAA,eAAW,EAAE,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,MAAO,QAAO,IAAI,KAAK;AAAA,WAAO;AACxJ,cAAM,SAAS,SAAS,KAAK,UAAU;AACvC,YAAI,QAAQ;AACZ,cAAM,SAAS,CAAC;AAChB,cAAM,cAAc,SAAS,QAAQ,IAAI,cAAc;AACvD,cAAM,gBAAgB,SAAS,SAAS,QAAQ,IAAI,gBAAgB,KAAK,KAAK,EAAE;AAChF,eAAO,MAAM;AACX,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,MAAM,OAAO,KAAK;AACtB,cAAI,KAAM;AACV,iBAAO,KAAK,KAAK;AACjB,oBAAU,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,WAAW;AACzE,gBAAM,SAAS;AAAA,YACb,KAAK,QAAQ;AAAA,YACb;AAAA,YACA;AAAA,UACF;AACA,eAAK,gBAAgB,YAAY,MAAM;AAAA,QACzC;AACA,cAAM,YAAY,IAAI,WAAW,KAAK;AACtC,YAAI,WAAW;AACf,mBAAW,SAAS,QAAQ;AAC1B,cAAI,OAAO,UAAU,YAAa;AAClC,oBAAU,IAAI,OAAO,QAAQ;AAC7B,sBAAY,MAAM;AAAA,QACpB;AACA,eAAO,IAAI,KAAK,CAAC,UAAU,MAAM,GAAG;AAAA,UAClC,MAAM,eAAe;AAAA,QACvB,CAAC;AAAA,MACH;AACA,YAAM,SAAS,MAAM,KAAK,UAAU;AAAA,QAClC,MAAM,QAAQ;AAAA,QACd,YAAY,KAAK,QAAQ,eAAe,QAAQ,OAAO,SAAS,KAAK;AAAA,QACrE,YAAY,KAAK,QAAQ,eAAe,QAAQ,OAAO,SAAS,KAAK;AAAA,QACrE,MAAM;AAAA,MACR,CAAC;AACD,aAAO;AAAA,QACL,MAAM,OAAO;AAAA,QACb;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB,UAAU,WAAW;AACpC,UAAM,KAAK,YAAY,yBAAyB;AAAA,EAClD;AAAA,EACM,SAAS;AAAA;AACb,UAAI,KAAK,QAAQ,QAAW;AAC1B,eAAO,KAAK;AAAA,MACd;AACA,UAAI,EAAE,eAAe,SAAS;AAC5B,cAAM,KAAK,YAAY,wCAAwC;AAAA,MACjE;AACA,aAAO,IAAI,QAAQ,CAACA,UAAS,WAAW;AACtC,cAAM,UAAU,UAAU,KAAK,KAAK,SAAS,KAAK,UAAU;AAC5D,gBAAQ,kBAAkB,eAAc;AACxC,gBAAQ,YAAY,MAAM;AACxB,eAAK,MAAM,QAAQ;AACnB,UAAAA,SAAQ,QAAQ,MAAM;AAAA,QACxB;AACA,gBAAQ,UAAU,MAAM,OAAO,QAAQ,KAAK;AAC5C,gBAAQ,YAAY,MAAM;AACxB,kBAAQ,KAAK,YAAY;AAAA,QAC3B;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA,EACA,OAAO,UAAU,OAAO;AACtB,UAAM,cAAc,MAAM;AAC1B,UAAM,KAAK,YAAY;AACvB,YAAQ,MAAM,YAAY;AAAA,MACxB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,SACE;AACE,YAAI,GAAG,iBAAiB,SAAS,aAAa,GAAG;AAC/C,aAAG,kBAAkB,aAAa;AAAA,QACpC;AACA,cAAM,QAAQ,GAAG,kBAAkB,eAAe;AAAA,UAChD,SAAS;AAAA,QACX,CAAC;AACD,cAAM,YAAY,aAAa,QAAQ;AAAA,MACzC;AAAA,IACJ;AAAA,EACF;AAAA,EACM,UAAU,KAAK,MAAM;AAAA;AACzB,YAAM,WAAW,KAAK,WAAW,QAAQ,GAAG,MAAM,KAAK,cAAc;AACrE,aAAO,KAAK,OAAO,EAAE,KAAK,UAAQ;AAChC,eAAO,IAAI,QAAQ,CAACA,UAAS,WAAW;AACtC,gBAAM,KAAK,KAAK,YAAY,CAAC,aAAa,GAAG,QAAQ;AACrD,gBAAM,QAAQ,GAAG,YAAY,aAAa;AAC1C,gBAAM,MAAM,MAAM,GAAG,EAAE,GAAG,IAAI;AAC9B,cAAI,YAAY,MAAMA,SAAQ,IAAI,MAAM;AACxC,cAAI,UAAU,MAAM,OAAO,IAAI,KAAK;AAAA,QACtC,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA;AAAA,EACM,eAAe,WAAW,KAAK,MAAM;AAAA;AACzC,YAAM,WAAW,KAAK,WAAW,QAAQ,GAAG,MAAM,KAAK,cAAc;AACrE,aAAO,KAAK,OAAO,EAAE,KAAK,UAAQ;AAChC,eAAO,IAAI,QAAQ,CAACA,UAAS,WAAW;AACtC,gBAAM,KAAK,KAAK,YAAY,CAAC,aAAa,GAAG,QAAQ;AACrD,gBAAM,QAAQ,GAAG,YAAY,aAAa;AAC1C,gBAAM,QAAQ,MAAM,MAAM,SAAS;AACnC,gBAAM,MAAM,MAAM,GAAG,EAAE,GAAG,IAAI;AAC9B,cAAI,YAAY,MAAMA,SAAQ,IAAI,MAAM;AACxC,cAAI,UAAU,MAAM,OAAO,IAAI,KAAK;AAAA,QACtC,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA;AAAA,EACA,QAAQ,WAAW,SAAS;AAC1B,UAAM,iBAAiB,YAAY,SAAY,QAAQ,QAAQ,gBAAgB,EAAE,IAAI;AACrF,QAAI,SAAS;AACb,QAAI,cAAc,OAAW,WAAU,MAAM;AAC7C,QAAI,YAAY,GAAI,WAAU,MAAM;AACpC,WAAO;AAAA,EACT;AAAA,EACM,QAAQ;AAAA;AACZ,YAAM,OAAO,MAAM,KAAK,OAAO;AAC/B,YAAM,KAAK,KAAK,YAAY,CAAC,aAAa,GAAG,WAAW;AACxD,YAAM,QAAQ,GAAG,YAAY,aAAa;AAC1C,YAAM,MAAM;AAAA,IACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,SAAS,SAAS;AAAA;AACtB,YAAM,OAAO,KAAK,QAAQ,QAAQ,WAAW,QAAQ,IAAI;AAEzD,YAAM,QAAQ,MAAM,KAAK,UAAU,OAAO,CAAC,IAAI,CAAC;AAChD,UAAI,UAAU,OAAW,OAAM,MAAM,sBAAsB;AAC3D,aAAO;AAAA,QACL,MAAM,MAAM,UAAU,MAAM,UAAU;AAAA,MACxC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,UAAU,SAAS;AAAA;AACvB,YAAM,OAAO,KAAK,QAAQ,QAAQ,WAAW,QAAQ,IAAI;AACzD,UAAI,OAAO,QAAQ;AACnB,YAAM,WAAW,QAAQ;AACzB,YAAM,cAAc,QAAQ;AAC5B,YAAM,gBAAgB,MAAM,KAAK,UAAU,OAAO,CAAC,IAAI,CAAC;AACxD,UAAI,iBAAiB,cAAc,SAAS,YAAa,OAAM,MAAM,mCAAmC;AACxG,YAAM,aAAa,KAAK,OAAO,GAAG,KAAK,YAAY,GAAG,CAAC;AACvD,YAAM,cAAc,MAAM,KAAK,UAAU,OAAO,CAAC,UAAU,CAAC;AAC5D,UAAI,gBAAgB,QAAW;AAC7B,cAAM,cAAc,WAAW,QAAQ,KAAK,CAAC;AAC7C,YAAI,gBAAgB,IAAI;AACtB,gBAAM,gBAAgB,WAAW,OAAO,WAAW;AACnD,gBAAM,KAAK,MAAM;AAAA,YACf,MAAM;AAAA,YACN,WAAW,QAAQ;AAAA,YACnB,WAAW;AAAA,UACb,CAAC;AAAA,QACH;AAAA,MACF;AACA,UAAI,CAAC,YAAY,EAAE,gBAAgB,OAAO;AACxC,eAAO,KAAK,QAAQ,GAAG,KAAK,IAAI,KAAK,MAAM,GAAG,EAAE,CAAC,IAAI;AACrD,YAAI,CAAC,KAAK,eAAe,IAAI,EAAG,OAAM,MAAM,gDAAgD;AAAA,MAC9F;AACA,YAAM,MAAM,KAAK,IAAI;AACrB,YAAM,UAAU;AAAA,QACd;AAAA,QACA,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM,gBAAgB,OAAO,KAAK,OAAO,KAAK;AAAA,QAC9C,OAAO;AAAA,QACP,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AACA,YAAM,KAAK,UAAU,OAAO,CAAC,OAAO,CAAC;AACrC,aAAO;AAAA,QACL,KAAK,QAAQ;AAAA,MACf;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,WAAW,SAAS;AAAA;AACxB,YAAM,OAAO,KAAK,QAAQ,QAAQ,WAAW,QAAQ,IAAI;AACzD,UAAI,OAAO,QAAQ;AACnB,YAAM,WAAW,QAAQ;AACzB,YAAM,aAAa,KAAK,OAAO,GAAG,KAAK,YAAY,GAAG,CAAC;AACvD,YAAM,MAAM,KAAK,IAAI;AACrB,UAAI,QAAQ;AACZ,YAAM,gBAAgB,MAAM,KAAK,UAAU,OAAO,CAAC,IAAI,CAAC;AACxD,UAAI,iBAAiB,cAAc,SAAS,YAAa,OAAM,MAAM,mCAAmC;AACxG,YAAM,cAAc,MAAM,KAAK,UAAU,OAAO,CAAC,UAAU,CAAC;AAC5D,UAAI,gBAAgB,QAAW;AAC7B,cAAM,cAAc,WAAW,QAAQ,KAAK,CAAC;AAC7C,YAAI,gBAAgB,IAAI;AACtB,gBAAM,gBAAgB,WAAW,OAAO,WAAW;AACnD,gBAAM,KAAK,MAAM;AAAA,YACf,MAAM;AAAA,YACN,WAAW,QAAQ;AAAA,YACnB,WAAW;AAAA,UACb,CAAC;AAAA,QACH;AAAA,MACF;AACA,UAAI,CAAC,YAAY,CAAC,KAAK,eAAe,IAAI,EAAG,OAAM,MAAM,gDAAgD;AACzG,UAAI,kBAAkB,QAAW;AAC/B,YAAI,cAAc,mBAAmB,MAAM;AACzC,gBAAM,MAAM,wEAAwE;AAAA,QACtF;AACA,YAAI,cAAc,YAAY,UAAa,CAAC,UAAU;AACpD,iBAAO,KAAK,KAAK,cAAc,OAAO,IAAI,KAAK,IAAI,CAAC;AAAA,QACtD,OAAO;AACL,iBAAO,cAAc,UAAU;AAAA,QACjC;AACA,gBAAQ,cAAc;AAAA,MACxB;AACA,YAAM,UAAU;AAAA,QACd;AAAA,QACA,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM,KAAK;AAAA,QACX;AAAA,QACA,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AACA,YAAM,KAAK,UAAU,OAAO,CAAC,OAAO,CAAC;AAAA,IACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,WAAW,SAAS;AAAA;AACxB,YAAM,OAAO,KAAK,QAAQ,QAAQ,WAAW,QAAQ,IAAI;AACzD,YAAM,QAAQ,MAAM,KAAK,UAAU,OAAO,CAAC,IAAI,CAAC;AAChD,UAAI,UAAU,OAAW,OAAM,MAAM,sBAAsB;AAC3D,YAAM,UAAU,MAAM,KAAK,eAAe,aAAa,cAAc,CAAC,YAAY,KAAK,IAAI,CAAC,CAAC;AAC7F,UAAI,QAAQ,WAAW,EAAG,OAAM,MAAM,sBAAsB;AAC5D,YAAM,KAAK,UAAU,UAAU,CAAC,IAAI,CAAC;AAAA,IACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,MAAM,SAAS;AAAA;AACnB,YAAM,OAAO,KAAK,QAAQ,QAAQ,WAAW,QAAQ,IAAI;AACzD,YAAM,cAAc,QAAQ;AAC5B,YAAM,aAAa,KAAK,OAAO,GAAG,KAAK,YAAY,GAAG,CAAC;AACvD,YAAM,SAAS,KAAK,MAAM,KAAK,KAAK,CAAC,GAAG;AACxC,YAAM,cAAc,MAAM,KAAK,UAAU,OAAO,CAAC,UAAU,CAAC;AAC5D,YAAM,gBAAgB,MAAM,KAAK,UAAU,OAAO,CAAC,IAAI,CAAC;AACxD,UAAI,UAAU,EAAG,OAAM,MAAM,8BAA8B;AAC3D,UAAI,kBAAkB,OAAW,OAAM,MAAM,uCAAuC;AACpF,UAAI,CAAC,eAAe,UAAU,KAAK,gBAAgB,OAAW,OAAM,MAAM,6BAA6B;AACvG,UAAI,eAAe,UAAU,KAAK,gBAAgB,QAAW;AAC3D,cAAM,gBAAgB,WAAW,OAAO,WAAW,QAAQ,KAAK,CAAC,CAAC;AAClE,cAAM,KAAK,MAAM;AAAA,UACf,MAAM;AAAA,UACN,WAAW,QAAQ;AAAA,UACnB,WAAW;AAAA,QACb,CAAC;AAAA,MACH;AACA,YAAM,MAAM,KAAK,IAAI;AACrB,YAAM,UAAU;AAAA,QACd;AAAA,QACA,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AACA,YAAM,KAAK,UAAU,OAAO,CAAC,OAAO,CAAC;AAAA,IACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,MAAM,SAAS;AAAA;AACnB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,WAAW,KAAK,QAAQ,WAAW,IAAI;AAC7C,YAAM,QAAQ,MAAM,KAAK,UAAU,OAAO,CAAC,QAAQ,CAAC;AACpD,UAAI,UAAU,OAAW,OAAM,MAAM,wBAAwB;AAC7D,UAAI,MAAM,SAAS,YAAa,OAAM,MAAM,mCAAmC;AAC/E,YAAM,gBAAgB,MAAM,KAAK,QAAQ;AAAA,QACvC;AAAA,QACA;AAAA,MACF,CAAC;AACD,UAAI,cAAc,MAAM,WAAW,KAAK,CAAC,UAAW,OAAM,MAAM,qBAAqB;AACrF,iBAAWC,UAAS,cAAc,OAAO;AACvC,cAAM,YAAY,GAAG,IAAI,IAAIA,OAAM,IAAI;AACvC,cAAM,WAAW,MAAM,KAAK,KAAK;AAAA,UAC/B,MAAM;AAAA,UACN;AAAA,QACF,CAAC;AACD,YAAI,SAAS,SAAS,QAAQ;AAC5B,gBAAM,KAAK,WAAW;AAAA,YACpB,MAAM;AAAA,YACN;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,gBAAM,KAAK,MAAM;AAAA,YACf,MAAM;AAAA,YACN;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AACA,YAAM,KAAK,UAAU,UAAU,CAAC,QAAQ,CAAC;AAAA,IAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,QAAQ,SAAS;AAAA;AACrB,YAAM,OAAO,KAAK,QAAQ,QAAQ,WAAW,QAAQ,IAAI;AACzD,YAAM,QAAQ,MAAM,KAAK,UAAU,OAAO,CAAC,IAAI,CAAC;AAChD,UAAI,QAAQ,SAAS,MAAM,UAAU,OAAW,OAAM,MAAM,wBAAwB;AACpF,YAAM,UAAU,MAAM,KAAK,eAAe,aAAa,cAAc,CAAC,YAAY,KAAK,IAAI,CAAC,CAAC;AAC7F,YAAM,QAAQ,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAM,MAAK;AACrD,YAAI,WAAW,MAAM,KAAK,UAAU,OAAO,CAAC,CAAC,CAAC;AAC9C,YAAI,aAAa,QAAW;AAC1B,qBAAW,MAAM,KAAK,UAAU,OAAO,CAAC,IAAI,GAAG,CAAC;AAAA,QAClD;AACA,eAAO;AAAA,UACL,MAAM,EAAE,UAAU,KAAK,SAAS,CAAC;AAAA,UACjC,MAAM,SAAS;AAAA,UACf,MAAM,SAAS;AAAA,UACf,OAAO,SAAS;AAAA,UAChB,OAAO,SAAS;AAAA,UAChB,KAAK,SAAS;AAAA,QAChB;AAAA,MACF,EAAC,CAAC;AACF,aAAO;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,OAAO,SAAS;AAAA;AACpB,YAAM,OAAO,KAAK,QAAQ,QAAQ,WAAW,QAAQ,IAAI;AACzD,UAAI,QAAQ,MAAM,KAAK,UAAU,OAAO,CAAC,IAAI,CAAC;AAC9C,UAAI,UAAU,QAAW;AACvB,gBAAQ,MAAM,KAAK,UAAU,OAAO,CAAC,OAAO,GAAG,CAAC;AAAA,MAClD;AACA,aAAO;AAAA,QACL,MAAM,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,SAAS;AAAA,MACrE;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,KAAK,SAAS;AAAA;AAClB,YAAM,OAAO,KAAK,QAAQ,QAAQ,WAAW,QAAQ,IAAI;AACzD,UAAI,QAAQ,MAAM,KAAK,UAAU,OAAO,CAAC,IAAI,CAAC;AAC9C,UAAI,UAAU,QAAW;AACvB,gBAAQ,MAAM,KAAK,UAAU,OAAO,CAAC,OAAO,GAAG,CAAC;AAAA,MAClD;AACA,UAAI,UAAU,OAAW,OAAM,MAAM,uBAAuB;AAC5D,aAAO;AAAA,QACL,MAAM,MAAM,KAAK,UAAU,KAAK,SAAS,CAAC;AAAA,QAC1C,MAAM,MAAM;AAAA,QACZ,MAAM,MAAM;AAAA,QACZ,OAAO,MAAM;AAAA,QACb,OAAO,MAAM;AAAA,QACb,KAAK,MAAM;AAAA,MACb;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,OAAO,SAAS;AAAA;AACpB,YAAM,KAAK,MAAM,SAAS,IAAI;AAC9B;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,KAAK,SAAS;AAAA;AAClB,aAAO,KAAK,MAAM,SAAS,KAAK;AAAA,IAClC;AAAA;AAAA,EACM,qBAAqB;AAAA;AACzB,aAAO;AAAA,QACL,eAAe;AAAA,MACjB;AAAA,IACF;AAAA;AAAA,EACM,mBAAmB;AAAA;AACvB,aAAO;AAAA,QACL,eAAe;AAAA,MACjB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOM,MAAM,SAAS,WAAW,OAAO;AAAA;AACrC,UAAI;AAAA,QACF;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA,WAAW;AAAA,MACb,IAAI;AACJ,UAAI,CAAC,MAAM,CAAC,MAAM;AAChB,cAAM,MAAM,mCAAmC;AAAA,MACjD;AAEA,UAAI,CAAC,aAAa;AAChB,sBAAc;AAAA,MAChB;AACA,YAAM,WAAW,KAAK,QAAQ,eAAe,IAAI;AACjD,YAAM,SAAS,KAAK,QAAQ,aAAa,EAAE;AAE3C,UAAI,aAAa,QAAQ;AACvB,eAAO;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AACA,UAAI,aAAa,UAAU,MAAM,GAAG;AAClC,cAAM,MAAM,sCAAsC;AAAA,MACpD;AAEA,UAAI;AACJ,UAAI;AACF,gBAAQ,MAAM,KAAK,KAAK;AAAA,UACtB,MAAM;AAAA,UACN,WAAW;AAAA,QACb,CAAC;AAAA,MACH,SAAS,GAAG;AAEV,cAAM,mBAAmB,GAAG,MAAM,GAAG;AACrC,yBAAiB,IAAI;AACrB,cAAMC,UAAS,iBAAiB,KAAK,GAAG;AAExC,YAAI,iBAAiB,SAAS,GAAG;AAC/B,gBAAM,oBAAoB,MAAM,KAAK,KAAK;AAAA,YACxC,MAAMA;AAAA,YACN,WAAW;AAAA,UACb,CAAC;AACD,cAAI,kBAAkB,SAAS,aAAa;AAC1C,kBAAM,IAAI,MAAM,2CAA2C;AAAA,UAC7D;AAAA,QACF;AAAA,MACF;AAEA,UAAI,SAAS,MAAM,SAAS,aAAa;AACvC,cAAM,IAAI,MAAM,0CAA0C;AAAA,MAC5D;AAEA,YAAM,UAAU,MAAM,KAAK,KAAK;AAAA,QAC9B,MAAM;AAAA,QACN,WAAW;AAAA,MACb,CAAC;AAED,YAAM,aAAa,CAAO,MAAMC,QAAO,UAAU;AAC/C,cAAM,WAAW,KAAK,QAAQ,aAAa,IAAI;AAC/C,cAAM,QAAQ,MAAM,KAAK,UAAU,OAAO,CAAC,QAAQ,CAAC;AACpD,cAAM,QAAQA;AACd,cAAM,QAAQ;AACd,cAAM,KAAK,UAAU,OAAO,CAAC,KAAK,CAAC;AAAA,MACrC;AACA,YAAM,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,KAAK,IAAI;AACvD,cAAQ,QAAQ,MAAM;AAAA,QAEpB,KAAK,QACH;AAEE,gBAAM,OAAO,MAAM,KAAK,SAAS;AAAA,YAC/B,MAAM;AAAA,YACN,WAAW;AAAA,UACb,CAAC;AAED,cAAI,UAAU;AACZ,kBAAM,KAAK,WAAW;AAAA,cACpB,MAAM;AAAA,cACN,WAAW;AAAA,YACb,CAAC;AAAA,UACH;AACA,cAAI;AACJ,cAAI,EAAE,KAAK,gBAAgB,SAAS,CAAC,KAAK,eAAe,KAAK,IAAI,GAAG;AACnE,uBAAW,SAAS;AAAA,UACtB;AAEA,gBAAM,cAAc,MAAM,KAAK,UAAU;AAAA,YACvC,MAAM;AAAA,YACN,WAAW;AAAA,YACX,MAAM,KAAK;AAAA,YACX;AAAA,UACF,CAAC;AAED,cAAI,UAAU;AACZ,kBAAM,WAAW,IAAI,OAAO,QAAQ,KAAK;AAAA,UAC3C;AAEA,iBAAO;AAAA,QACT;AAAA,QACF,KAAK,aACH;AACE,cAAI,OAAO;AACT,kBAAM,MAAM,iDAAiD;AAAA,UAC/D;AACA,cAAI;AAEF,kBAAM,KAAK,MAAM;AAAA,cACf,MAAM;AAAA,cACN,WAAW;AAAA,cACX,WAAW;AAAA,YACb,CAAC;AAED,gBAAI,UAAU;AACZ,oBAAM,WAAW,IAAI,OAAO,QAAQ,KAAK;AAAA,YAC3C;AAAA,UACF,SAAS,GAAG;AAAA,UAEZ;AAEA,gBAAM,YAAY,MAAM,KAAK,QAAQ;AAAA,YACnC,MAAM;AAAA,YACN,WAAW;AAAA,UACb,CAAC,GAAG;AACJ,qBAAW,YAAY,UAAU;AAE/B,kBAAM,KAAK,MAAM;AAAA,cACf,MAAM,GAAG,IAAI,IAAI,SAAS,IAAI;AAAA,cAC9B,IAAI,GAAG,EAAE,IAAI,SAAS,IAAI;AAAA,cAC1B,WAAW;AAAA,cACX;AAAA,YACF,GAAG,QAAQ;AAAA,UACb;AAEA,cAAI,UAAU;AACZ,kBAAM,KAAK,MAAM;AAAA,cACf,MAAM;AAAA,cACN,WAAW;AAAA,YACb,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACJ;AACA,aAAO;AAAA,QACL,KAAK;AAAA,MACP;AAAA,IACF;AAAA;AAAA,EACA,eAAe,KAAK;AAClB,QAAI;AACF,aAAO,KAAK,KAAK,GAAG,CAAC,KAAK;AAAA,IAC5B,SAAS,KAAK;AACZ,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,cAAc,SAAS;", "names": ["resolve", "entry", "to<PERSON><PERSON>", "ctime"]}