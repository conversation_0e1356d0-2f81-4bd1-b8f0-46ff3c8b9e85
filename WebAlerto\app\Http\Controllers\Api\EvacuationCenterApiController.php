<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\EvacuationCenter; // Make sure this model exists

class EvacuationCenterApiController extends Controller
{
    // Return all evacuation centers
    public function index()
    {
        return response()->json(EvacuationCenter::all());
    }

    // Return a single evacuation center by ID
    public function show($id)
    {
        $center = EvacuationCenter::find($id);
        if (!$center) {
            return response()->json(['message' => 'Evacuation Center not found'], 404);
        }
        return response()->json($center);
    }

    // Update an evacuation center by ID
    public function update(Request $request, $id)
    {
        $center = EvacuationCenter::find($id);
        if (!$center) {
            return response()->json(['message' => 'Evacuation Center not found'], 404);
        }

        $validated = $request->validate([
            'name' => 'required|string',
            'street_name' => 'nullable|string',
            'barangay' => 'nullable|string',
            'city' => 'nullable|string',
            'province' => 'nullable|string',
            'postal_code' => 'nullable|string',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'capacity' => 'required|integer|min:1',
            'status' => 'required|in:active,inactive',
            'contact_number' => 'required|string',
            'email' => 'required|email',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'amenities' => 'nullable|array',
            'amenities.*' => 'string'
        ]);

        // If disaster_type is array, encode as JSON
        if (is_array($validated['disaster_type'])) {
            $validated['disaster_type'] = json_encode($validated['disaster_type']);
        }

        $center->update($validated);
        return response()->json(['success' => true, 'message' => 'Evacuation Center updated successfully', 'data' => $center]);
    }
}
