

<ion-content class="login-bg">
  <div class="login-wrapper">
    <!-- Logo -->
    <div class="logo-container">
      <img src="assets/ALERTO.png" />
    </div>

    <form (ngSubmit)="onLogin()" class="login-form">
      <div class="input-group">
        <ion-input
          type="email"
          [(ngModel)]="credentials.email"
          name="email"
          placeholder="Username"
          class="modern-input"
          required>
        </ion-input>
      </div>

      <div class="input-group">
        <ion-input
          type="password"
          [(ngModel)]="credentials.password"
          name="password"
          placeholder="Password"
          class="modern-input"
          required>
        </ion-input>
      </div>

      <ion-button expand="block" type="submit" class="modern-btn">
        Sign In
      </ion-button>
    </form>

    <div class="register-link">
      Don't have an account?
      <a (click)="goToRegister()"><strong><u>Sign Up</u></strong></a>
    </div>
  </div>
</ion-content>