<?php

echo "Downloading CA certificate...\n";

// Create certs directory if it doesn't exist
$certDir = __DIR__ . '/storage/certs';
if (!is_dir($certDir)) {
    mkdir($certDir, 0755, true);
    echo "Created directory: $certDir\n";
}

$certPath = $certDir . '/cacert.pem';

// Download with SSL verification disabled for this specific download
$context = stream_context_create([
    'ssl' => [
        'verify_peer' => false,
        'verify_peer_name' => false
    ],
    'http' => [
        'timeout' => 30,
        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ]
]);

$certContent = file_get_contents('https://curl.se/ca/cacert.pem', false, $context);

if ($certContent !== false) {
    file_put_contents($certPath, $certContent);
    echo "CA certificate downloaded successfully to: $certPath\n";
    echo "Certificate size: " . strlen($certContent) . " bytes\n";
    
    // Verify the certificate file
    if (file_exists($certPath) && filesize($certPath) > 1000) {
        echo "Certificate file verified successfully!\n";
    } else {
        echo "Warning: Certificate file seems too small or doesn't exist\n";
    }
} else {
    echo "Failed to download CA certificate\n";
    exit(1);
}

echo "Done!\n";
