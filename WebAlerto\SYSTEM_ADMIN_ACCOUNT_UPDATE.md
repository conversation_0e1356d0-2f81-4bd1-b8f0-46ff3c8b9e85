# System Administrator Account Update

## 🔄 **Changes Made**

The default system administrator account has been updated to properly reflect the new RBAC hierarchy:

### **Old Account (Removed)**
- **Email**: `<EMAIL>`
- **Role**: `super_admin` (incorrect - this was city-wide access)
- **Password**: `Admin@2025`

### **New Account (Current)**
- **Email**: `<EMAIL>`
- **Role**: `system_admin` (correct - province-wide access)
- **Password**: `SysAdmin@2025`
- **Position**: Technical Administrator
- **Access Level**: All cities/municipalities in Cebu Province

## 🔧 **Environment Configuration**

Update your `.env` file with the new system administrator credentials:

```env
# System Administrator Account (for initial setup)
SYSTEM_ADMIN_EMAIL=<EMAIL>
SYSTEM_ADMIN_PASSWORD=SysAdmin@2025
```

**Remove these old variables if they exist:**
```env
# Remove these old variables
SUPER_ADMIN_EMAIL=<EMAIL>
SUPER_ADMIN_PASSWORD=Admin@2025
```

## 🎯 **RBAC Hierarchy Clarification**

### **1. System Administrator** (`system_admin`)
- **Email**: `<EMAIL>`
- **Access**: All cities/municipalities in Cebu Province
- **Responsibilities**: 
  - User account management
  - System configuration
  - Technical maintenance
  - Creating CDRRMC and BDRRMC accounts

### **2. CDRRMC** (`super_admin`)
- **Access**: City/municipality-wide
- **Example**: Dalaguete CDRRMC sees only Dalaguete barangays
- **Responsibilities**:
  - City-wide disaster monitoring
  - Oversight of BDRRMC units
  - City-level emergency coordination

### **3. BDRRMC** (`admin`)
- **Access**: Barangay-specific
- **Example**: Mantalongon BDRRMC sees only Mantalongon data
- **Responsibilities**:
  - Barangay disaster response
  - Local evacuation management
  - Community notifications

## 🚀 **Database Update Instructions**

### **Option 1: Fresh Installation**
```bash
# Run the updated seeder
php artisan db:seed --class=UserSeeder
```

### **Option 2: Update Existing Database**
```sql
-- <NAME_EMAIL> account
UPDATE users 
SET 
    email = '<EMAIL>',
    role = 'system_admin',
    position = 'Technical Administrator',
    city = NULL,
    barangay = NULL,
    password = '$2y$12$...' -- Hash of 'SysAdmin@2025'
WHERE email = '<EMAIL>';
```

### **Option 3: Manual Database Update**
1. Delete the old `<EMAIL>` account
2. Run the UserSeeder to create the new system admin account

## 🔐 **Login Credentials**

**New System Administrator Login:**
- **URL**: `/login`
- **Email**: `<EMAIL>`
- **Password**: `SysAdmin@2025`

## 📋 **Testing Checklist**

- [ ] Update `.env` file with new credentials
- [ ] Run database seeder or update existing account
- [ ] Test login with new credentials
- [ ] Verify system-wide access (all cities/municipalities)
- [ ] Test user creation functionality
- [ ] Verify RBAC permissions work correctly

## 🔄 **Migration Path**

If you have existing data and users:

1. **Backup your database** before making changes
2. **Update environment variables** in `.env`
3. **Update the existing admin account** using SQL or recreate it
4. **Test the new login credentials**
5. **Verify all existing CDRRMC and BDRRMC accounts** still work correctly

## 📝 **Notes**

- The old `<EMAIL>` account had incorrect `super_admin` role
- The new `<EMAIL>` account has proper `system_admin` role
- This change ensures proper RBAC hierarchy and data isolation
- All documentation and test files have been updated accordingly
