<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\AppNotification;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class NotificationController extends Controller
{
    /**
     * Get notifications for the authenticated user
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }

            $perPage = $request->get('per_page', 20);
            $page = $request->get('page', 1);
            $type = $request->get('type'); // Filter by type if provided

            $query = AppNotification::forUser($user->id)
                ->orderBy('created_at', 'desc');

            if ($type) {
                $query->ofType($type);
            }

            $notifications = $query->paginate($perPage, ['*'], 'page', $page);

            // Get unread count
            $unreadCount = AppNotification::forUser($user->id)
                ->unread()
                ->count();

            return response()->json([
                'notifications' => $notifications->items(),
                'unread_count' => $unreadCount,
                'has_more' => $notifications->hasMorePages(),
                'current_page' => $notifications->currentPage(),
                'total' => $notifications->total()
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching notifications: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch notifications'], 500);
        }
    }

    /**
     * Mark a specific notification as read
     */
    public function markAsRead(Request $request, $id): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }

            $notification = AppNotification::forUser($user->id)
                ->where('id', $id)
                ->first();

            if (!$notification) {
                return response()->json(['error' => 'Notification not found'], 404);
            }

            $notification->markAsRead();

            return response()->json([
                'message' => 'Notification marked as read',
                'notification' => $notification
            ]);

        } catch (\Exception $e) {
            Log::error('Error marking notification as read: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to mark notification as read'], 500);
        }
    }

    /**
     * Mark all notifications as read for the authenticated user
     */
    public function markAllAsRead(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }

            $updatedCount = AppNotification::forUser($user->id)
                ->unread()
                ->update(['read' => true]);

            return response()->json([
                'message' => 'All notifications marked as read',
                'updated_count' => $updatedCount
            ]);

        } catch (\Exception $e) {
            Log::error('Error marking all notifications as read: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to mark all notifications as read'], 500);
        }
    }

    /**
     * Get unread notification count
     */
    public function getUnreadCount(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }

            $unreadCount = AppNotification::forUser($user->id)
                ->unread()
                ->count();

            return response()->json([
                'unread_count' => $unreadCount
            ]);

        } catch (\Exception $e) {
            Log::error('Error getting unread count: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get unread count'], 500);
        }
    }

    /**
     * Delete a notification
     */
    public function destroy(Request $request, $id): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }

            $notification = AppNotification::forUser($user->id)
                ->where('id', $id)
                ->first();

            if (!$notification) {
                return response()->json(['error' => 'Notification not found'], 404);
            }

            $notification->delete();

            return response()->json([
                'message' => 'Notification deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error deleting notification: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete notification'], 500);
        }
    }

    /**
     * Add reaction to a notification
     */
    public function addReaction(Request $request, $id): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }

            $notification = AppNotification::forUser($user->id)
                ->where('id', $id)
                ->first();

            if (!$notification) {
                return response()->json(['error' => 'Notification not found'], 404);
            }

            $notification->addReaction();

            return response()->json([
                'message' => 'Reaction added',
                'notification' => $notification
            ]);

        } catch (\Exception $e) {
            Log::error('Error adding reaction: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to add reaction'], 500);
        }
    }

    /**
     * Get notification statistics
     */
    public function getStats(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }

            $stats = [
                'total' => AppNotification::forUser($user->id)->count(),
                'unread' => AppNotification::forUser($user->id)->unread()->count(),
                'read' => AppNotification::forUser($user->id)->read()->count(),
                'by_type' => [
                    'evacuation_center_added' => AppNotification::forUser($user->id)->ofType('evacuation_center_added')->count(),
                    'emergency_alert' => AppNotification::forUser($user->id)->ofType('emergency_alert')->count(),
                    'system_update' => AppNotification::forUser($user->id)->ofType('system_update')->count(),
                    'general' => AppNotification::forUser($user->id)->ofType('general')->count(),
                ],
                'recent' => AppNotification::forUser($user->id)
                    ->where('created_at', '>=', now()->subDay())
                    ->count()
            ];

            return response()->json($stats);

        } catch (\Exception $e) {
            Log::error('Error getting notification stats: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get notification stats'], 500);
        }
    }
}