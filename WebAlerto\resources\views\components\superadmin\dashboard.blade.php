@extends('layout.app')

@section('title', 'Super Administrator Dashboard')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-sky-50 to-blue-100 py-8">
    <div class="max-w-[1920px] mx-auto px-6 sm:px-8 lg:px-12">
        
        <!-- Header Section -->
        <div class="bg-white/90 backdrop-blur-sm rounded-xl shadow-xl border border-blue-200 p-6 mb-6">
            <div class="flex items-center gap-3 mb-4">
                <div class="p-3 bg-gradient-to-br from-blue-600 to-sky-700 rounded-lg shadow-lg">
                    <i class="fas fa-city text-white text-xl"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">CDRRMC Dashboard</h1>
                    <p class="text-sm text-gray-600 mt-1">City-wide disaster monitoring and oversight across all barangays</p>
                </div>
            </div>
        </div>

        <!-- City-wide Overview Stats -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Users -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-blue-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Users</p>
                        <p class="text-3xl font-bold text-gray-900">{{ number_format($stats['total_users']) }}</p>
                        <p class="text-xs text-gray-500 mt-1">City-wide</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Active Users -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-blue-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Active Users</p>
                        <p class="text-3xl font-bold text-green-600">{{ number_format($stats['active_users']) }}</p>
                        <p class="text-xs text-gray-500 mt-1">Currently active</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                        <i class="fas fa-user-check text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Total Barangays -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-blue-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Barangays</p>
                        <p class="text-3xl font-bold text-purple-600">{{ count($stats['barangay_stats']) }}</p>
                        <p class="text-xs text-gray-500 mt-1">Under management</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
                        <i class="fas fa-map-marked-alt text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Total Notifications -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-blue-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Notifications</p>
                        <p class="text-3xl font-bold text-orange-600">
                            {{ number_format(collect($stats['barangay_stats'])->sum('total_notifications')) }}
                        </p>
                        <p class="text-xs text-gray-500 mt-1">City-wide alerts</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-lg">
                        <i class="fas fa-bell text-white text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- City Management Tools -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-blue-200 p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h3 class="text-lg font-bold text-gray-900">CDRRMC Management Tools</h3>
                    <p class="text-sm text-gray-600">City-wide disaster monitoring and oversight functions</p>
                </div>
                <div class="p-3 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl shadow-lg">
                    <i class="fas fa-tools text-white text-xl"></i>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="{{ route('superadmin.admin-users') }}" class="flex items-center gap-3 p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200 hover:from-blue-100 hover:to-blue-200 transition-all duration-200">
                    <div class="p-2 bg-blue-500 rounded-lg">
                        <i class="fas fa-user-shield text-white"></i>
                    </div>
                    <div>
                        <p class="font-medium text-blue-900">Admin Users</p>
                        <p class="text-xs text-blue-700">Manage barangay administrators</p>
                    </div>
                </a>
                

                
                <a href="{{ route('admin.barangay-statistics') }}" class="flex items-center gap-3 p-4 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg border border-purple-200 hover:from-purple-100 hover:to-purple-200 transition-all duration-200">
                    <div class="p-2 bg-purple-500 rounded-lg">
                        <i class="fas fa-chart-bar text-white"></i>
                    </div>
                    <div>
                        <p class="font-medium text-purple-900">Barangay Statistics</p>
                        <p class="text-xs text-purple-700">View detailed analytics</p>
                    </div>
                </a>
            </div>
        </div>

        <!-- Barangay Overview -->
        @if(isset($stats['barangay_stats']) && count($stats['barangay_stats']) > 0)
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-blue-200 p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h3 class="text-lg font-bold text-gray-900">Barangay Overview</h3>
                    <p class="text-sm text-gray-600">Summary of all barangays under city management</p>
                </div>
                <div class="p-3 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-xl shadow-lg">
                    <i class="fas fa-list text-white text-xl"></i>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="border-b border-gray-200">
                            <th class="text-left py-3 px-4 font-medium text-gray-700">Barangay</th>
                            <th class="text-center py-3 px-4 font-medium text-gray-700">Total Users</th>
                            <th class="text-center py-3 px-4 font-medium text-gray-700">Active Users</th>
                            <th class="text-center py-3 px-4 font-medium text-gray-700">Notifications</th>
                            <th class="text-center py-3 px-4 font-medium text-gray-700">Active Centers</th>
                            <th class="text-center py-3 px-4 font-medium text-gray-700">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($stats['barangay_stats'] as $barangay => $barangayData)
                        <tr class="border-b border-gray-100 hover:bg-gray-50">
                            <td class="py-3 px-4">
                                <div class="font-medium text-gray-900">{{ $barangay }}</div>
                            </td>
                            <td class="py-3 px-4 text-center">
                                <span class="text-lg font-bold text-gray-900">{{ $barangayData['total_users'] }}</span>
                            </td>
                            <td class="py-3 px-4 text-center">
                                <span class="text-lg font-bold text-green-600">{{ $barangayData['active_users'] }}</span>
                            </td>
                            <td class="py-3 px-4 text-center">
                                <span class="text-lg font-bold text-orange-600">{{ $barangayData['total_notifications'] }}</span>
                            </td>
                            <td class="py-3 px-4 text-center">
                                <span class="text-lg font-bold text-purple-600">{{ $barangayData['active_centers'] }}</span>
                            </td>
                            <td class="py-3 px-4 text-center">
                                @if($barangayData['active_users'] > 0)
                                    <span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                @else
                                    <span class="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Inactive
                                    </span>
                                @endif
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
        @endif

        <!-- Recent Activity Summary -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Top Active Barangays -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-blue-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-bold text-gray-900">Most Active Barangays</h3>
                        <p class="text-sm text-gray-600">Based on recent notifications</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl shadow-lg">
                        <i class="fas fa-trophy text-white text-xl"></i>
                    </div>
                </div>
                
                @if(isset($stats['barangay_stats']))
                @php
                    $sortedBarangays = collect($stats['barangay_stats'])
                        ->sortByDesc('total_notifications')
                        ->take(5);
                @endphp
                
                <div class="space-y-3">
                    @foreach($sortedBarangays as $barangay => $data)
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <p class="font-medium text-gray-900">{{ $barangay }}</p>
                            <p class="text-sm text-gray-600">{{ $data['active_users'] }} active users</p>
                        </div>
                        <div class="text-right">
                            <p class="text-lg font-bold text-orange-600">{{ $data['total_notifications'] }}</p>
                            <p class="text-xs text-gray-500">notifications</p>
                        </div>
                    </div>
                    @endforeach
                </div>
                @endif
            </div>

            <!-- System Summary -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-blue-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-bold text-gray-900">City Summary</h3>
                        <p class="text-sm text-gray-600">Overall city statistics</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-teal-500 to-teal-600 rounded-xl shadow-lg">
                        <i class="fas fa-chart-line text-white text-xl"></i>
                    </div>
                </div>
                
                @if(isset($stats['barangay_stats']))
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Total Evacuation Centers</span>
                        <span class="text-lg font-bold text-purple-600">
                            {{ collect($stats['barangay_stats'])->sum('active_centers') }}
                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Average Users per Barangay</span>
                        <span class="text-lg font-bold text-blue-600">
                            {{ count($stats['barangay_stats']) > 0 ? round(collect($stats['barangay_stats'])->avg('total_users'), 1) : 0 }}
                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Most Active Barangay</span>
                        <span class="text-sm font-medium text-gray-900">
                            @php
                                $mostActive = collect($stats['barangay_stats'])
                                    ->sortByDesc('total_notifications')
                                    ->keys()
                                    ->first();
                            @endphp
                            {{ $mostActive ?? 'N/A' }}
                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">City Coverage</span>
                        <span class="text-lg font-bold text-green-600">
                            {{ count($stats['barangay_stats']) }} barangays
                        </span>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
