import{c as s}from"./chunk-ICWJVXBH.js";import{h as e}from"./chunk-B7O3QC5Z.js";var a=class extends s{constructor(){super(...arguments),this.pending=[],this.deliveredNotifications=[],this.hasNotificationSupport=()=>{if(!("Notification"in window)||!Notification.requestPermission)return!1;if(Notification.permission!=="granted")try{new Notification("")}catch(i){if(i.name=="TypeError")return!1}return!0}}getDeliveredNotifications(){return e(this,null,function*(){let i=[];for(let t of this.deliveredNotifications){let n={title:t.title,id:parseInt(t.tag),body:t.body};i.push(n)}return{notifications:i}})}removeDeliveredNotifications(i){return e(this,null,function*(){for(let t of i.notifications){let n=this.deliveredNotifications.find(o=>o.tag===String(t.id));n?.close(),this.deliveredNotifications=this.deliveredNotifications.filter(()=>!n)}})}removeAllDeliveredNotifications(){return e(this,null,function*(){for(let i of this.deliveredNotifications)i.close();this.deliveredNotifications=[]})}createChannel(){return e(this,null,function*(){throw this.unimplemented("Not implemented on web.")})}deleteChannel(){return e(this,null,function*(){throw this.unimplemented("Not implemented on web.")})}listChannels(){return e(this,null,function*(){throw this.unimplemented("Not implemented on web.")})}schedule(i){return e(this,null,function*(){if(!this.hasNotificationSupport())throw this.unavailable("Notifications not supported in this browser.");for(let t of i.notifications)this.sendNotification(t);return{notifications:i.notifications.map(t=>({id:t.id}))}})}getPending(){return e(this,null,function*(){return{notifications:this.pending}})}registerActionTypes(){return e(this,null,function*(){throw this.unimplemented("Not implemented on web.")})}cancel(i){return e(this,null,function*(){this.pending=this.pending.filter(t=>!i.notifications.find(n=>n.id===t.id))})}areEnabled(){return e(this,null,function*(){let{display:i}=yield this.checkPermissions();return{value:i==="granted"}})}changeExactNotificationSetting(){return e(this,null,function*(){throw this.unimplemented("Not implemented on web.")})}checkExactNotificationSetting(){return e(this,null,function*(){throw this.unimplemented("Not implemented on web.")})}requestPermissions(){return e(this,null,function*(){if(!this.hasNotificationSupport())throw this.unavailable("Notifications not supported in this browser.");return{display:this.transformNotificationPermission(yield Notification.requestPermission())}})}checkPermissions(){return e(this,null,function*(){if(!this.hasNotificationSupport())throw this.unavailable("Notifications not supported in this browser.");return{display:this.transformNotificationPermission(Notification.permission)}})}transformNotificationPermission(i){switch(i){case"granted":return"granted";case"denied":return"denied";default:return"prompt"}}sendPending(){var i;let t=[],n=new Date().getTime();for(let o of this.pending)!((i=o.schedule)===null||i===void 0)&&i.at&&o.schedule.at.getTime()<=n&&(this.buildNotification(o),t.push(o));this.pending=this.pending.filter(o=>!t.find(r=>r===o))}sendNotification(i){var t;if(!((t=i.schedule)===null||t===void 0)&&t.at){let n=i.schedule.at.getTime()-new Date().getTime();this.pending.push(i),setTimeout(()=>{this.sendPending()},n);return}this.buildNotification(i)}buildNotification(i){let t=new Notification(i.title,{body:i.body,tag:String(i.id)});return t.addEventListener("click",this.onClick.bind(this,i),!1),t.addEventListener("show",this.onShow.bind(this,i),!1),t.addEventListener("close",()=>{this.deliveredNotifications=this.deliveredNotifications.filter(()=>!this)},!1),this.deliveredNotifications.push(t),t}onClick(i){let t={actionId:"tap",notification:i};this.notifyListeners("localNotificationActionPerformed",t)}onShow(i){this.notifyListeners("localNotificationReceived",i)}};export{a as LocalNotificationsWeb};
