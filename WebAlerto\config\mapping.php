<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Map Provider
    |--------------------------------------------------------------------------
    |
    | This option controls the default map provider used throughout the
    | application. Currently supported: "mapbox"
    |
    */

    'default' => env('MAP_PROVIDER', 'mapbox'),

    /*
    |--------------------------------------------------------------------------
    | Map Providers
    |--------------------------------------------------------------------------
    |
    | Here you may configure the map providers for your application.
    |
    */

    'providers' => [

        'mapbox' => [
            'access_token' => env('MAPBOX_ACCESS_TOKEN'),
            'style' => env('MAPBOX_STYLE', 'mapbox://styles/mapbox/streets-v12'),
            'api_url' => 'https://api.mapbox.com',
            'geocoding_url' => 'https://api.mapbox.com/geocoding/v5/mapbox.places',
            'directions_url' => 'https://api.mapbox.com/directions/v5/mapbox',
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Default Map Settings
    |--------------------------------------------------------------------------
    |
    | Default settings for map initialization
    |
    */

    'defaults' => [
        'center' => [
            'lat' => env('MAP_DEFAULT_LAT', 10.3157),
            'lng' => env('MAP_DEFAULT_LNG', 123.8854),
        ],
        'zoom' => env('MAP_DEFAULT_ZOOM', 13),
        'max_zoom' => env('MAP_MAX_ZOOM', 19),
        'min_zoom' => env('MAP_MIN_ZOOM', 1),
    ],

];
