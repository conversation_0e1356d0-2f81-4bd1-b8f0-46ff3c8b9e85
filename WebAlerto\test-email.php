<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing email configuration...\n";

// Check mail configuration
echo "Mail configuration:\n";
echo "MAIL_MAILER: " . config('mail.default') . "\n";
echo "MAIL_HOST: " . config('mail.mailers.smtp.host') . "\n";
echo "MAIL_PORT: " . config('mail.mailers.smtp.port') . "\n";
echo "MAIL_USERNAME: " . config('mail.mailers.smtp.username') . "\n";
echo "MAIL_FROM_ADDRESS: " . config('mail.from.address') . "\n";
echo "MAIL_FROM_NAME: " . config('mail.from.name') . "\n";

// Test sending a simple email
try {
    echo "\nAttempting to send test email...\n";
    
    \Illuminate\Support\Facades\Mail::raw('This is a test email from ALERTO system', function($message) {
        $message->to('<EMAIL>')
                ->subject('ALERTO Test Email');
    });
    
    echo "Test email sent successfully!\n";
} catch (\Exception $e) {
    echo "Failed to send test email: " . $e->getMessage() . "\n";
    echo "Error details: " . $e->getTraceAsString() . "\n";
}

// Test the notification system
try {
    echo "\nTesting notification system...\n";
    
    $invitationData = [
        'email' => '<EMAIL>',
        'title' => 'Mr.',
        'first_name' => 'Test',
        'last_name' => 'User',
        'position' => 'Test Position',
        'barangay' => 'Test Barangay',
        'role' => 'admin',
        'user_id' => null
    ];
    
    $emailService = new \App\Services\EmailNotificationService();
    $invitation = $emailService->sendAdminInvitation($invitationData, \App\Models\User::where('role', 'super_admin')->first());
    
    if ($invitation) {
        echo "Invitation sent successfully! Invitation ID: " . $invitation->id . "\n";
    } else {
        echo "Failed to send invitation\n";
    }
    
} catch (\Exception $e) {
    echo "Failed to send invitation: " . $e->getMessage() . "\n";
    echo "Error details: " . $e->getTraceAsString() . "\n";
} 