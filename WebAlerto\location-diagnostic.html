<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Location & Routing Diagnostic Tool</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        #map { height: 300px; }
        .log-entry { 
            font-family: 'Courier New', monospace; 
            font-size: 12px;
        }
        .log-success { color: #10b981; }
        .log-error { color: #ef4444; }
        .log-warning { color: #f59e0b; }
        .log-info { color: #3b82f6; }
    </style>
</head>
<body class="p-4">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-2xl shadow-2xl overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
                <h1 class="text-2xl font-bold flex items-center">
                    <i class="fas fa-stethoscope mr-3"></i>
                    Location & Routing Diagnostic Tool
                </h1>
                <p class="mt-2 opacity-90">Comprehensive testing for geolocation and routing issues</p>
            </div>

            <!-- Test Controls -->
            <div class="p-6 border-b">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onclick="testBrowserSupport()" class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200">
                        <i class="fas fa-browser mr-2"></i>
                        Test Browser Support
                    </button>
                    
                    <button onclick="testPermissions()" class="bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200">
                        <i class="fas fa-shield-alt mr-2"></i>
                        Test Permissions
                    </button>
                    
                    <button onclick="testLocationAccuracy()" class="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200">
                        <i class="fas fa-crosshairs mr-2"></i>
                        Test Location Accuracy
                    </button>
                    
                    <button onclick="testNetworkConnectivity()" class="bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200">
                        <i class="fas fa-wifi mr-2"></i>
                        Test Network
                    </button>
                    
                    <button onclick="testRoutingService()" class="bg-red-500 hover:bg-red-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200">
                        <i class="fas fa-route mr-2"></i>
                        Test Routing
                    </button>
                    
                    <button onclick="runFullDiagnostic()" class="bg-gray-800 hover:bg-gray-900 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200">
                        <i class="fas fa-play mr-2"></i>
                        Run Full Test
                    </button>
                </div>
            </div>

            <!-- Map -->
            <div class="p-6 border-b">
                <h3 class="text-lg font-semibold mb-3">Live Map Test</h3>
                <div id="map" class="rounded-lg border"></div>
            </div>

            <!-- Results -->
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold">Diagnostic Results</h3>
                    <button onclick="clearLogs()" class="text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-lg">
                        Clear Logs
                    </button>
                </div>
                <div id="logs" class="bg-gray-50 rounded-lg p-4 h-64 overflow-y-auto border">
                    <div class="text-gray-500 text-sm">Click any test button to start diagnostics...</div>
                </div>
            </div>

            <!-- System Info -->
            <div class="p-6 bg-gray-50">
                <h3 class="text-lg font-semibold mb-3">System Information</h3>
                <div id="systemInfo" class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <script>
        let map;
        let userMarker;
        let testMarker;
        let routingControl;
        let userLocation = null;
        
        // Test destination (Cebu City Hall)
        const testDestination = { lat: 10.3157, lng: 123.8854, name: "Cebu City Hall" };

        // Initialize map
        function initMap() {
            map = L.map('map').setView([10.3157, 123.8854], 13);
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);
            
            // Add test destination marker
            testMarker = L.marker([testDestination.lat, testDestination.lng])
                .addTo(map)
                .bindPopup('<strong>Test Destination</strong><br>Cebu City Hall');
        }

        // Logging functions
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type} mb-1`;
            logEntry.innerHTML = `<span class="text-gray-500">[${timestamp}]</span> ${message}`;
            
            const logsContainer = document.getElementById('logs');
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '<div class="text-gray-500 text-sm">Logs cleared...</div>';
        }

        // Test 1: Browser Support
        function testBrowserSupport() {
            log('🔍 Testing browser support...', 'info');
            
            // Check geolocation support
            if (navigator.geolocation) {
                log('✅ Geolocation API is supported', 'success');
            } else {
                log('❌ Geolocation API is NOT supported', 'error');
                return;
            }
            
            // Check HTTPS
            if (location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1') {
                log('✅ Secure context detected (HTTPS/localhost)', 'success');
            } else {
                log('⚠️ Insecure context - geolocation may be blocked', 'warning');
            }
            
            // Check permissions API
            if (navigator.permissions) {
                log('✅ Permissions API is supported', 'success');
                navigator.permissions.query({name: 'geolocation'}).then(result => {
                    log(`📍 Geolocation permission state: ${result.state}`, result.state === 'granted' ? 'success' : 'warning');
                });
            } else {
                log('⚠️ Permissions API not supported', 'warning');
            }
        }

        // Test 2: Permissions
        async function testPermissions() {
            log('🔐 Testing location permissions...', 'info');
            
            if (!navigator.geolocation) {
                log('❌ Geolocation not supported', 'error');
                return;
            }
            
            try {
                const position = await new Promise((resolve, reject) => {
                    navigator.geolocation.getCurrentPosition(resolve, reject, {
                        enableHighAccuracy: false,
                        timeout: 5000,
                        maximumAge: 0
                    });
                });
                
                log('✅ Location permission granted', 'success');
                log(`📍 Basic location: ${position.coords.latitude.toFixed(6)}, ${position.coords.longitude.toFixed(6)}`, 'success');
                
            } catch (error) {
                log(`❌ Permission error: ${error.message} (Code: ${error.code})`, 'error');
                
                switch(error.code) {
                    case 1:
                        log('💡 User denied location access. Check browser settings.', 'warning');
                        break;
                    case 2:
                        log('💡 Location unavailable. Check device GPS settings.', 'warning');
                        break;
                    case 3:
                        log('💡 Location request timed out. Try again.', 'warning');
                        break;
                }
            }
        }

        // Test 3: Location Accuracy
        async function testLocationAccuracy() {
            log('🎯 Testing location accuracy...', 'info');
            
            if (!navigator.geolocation) {
                log('❌ Geolocation not supported', 'error');
                return;
            }
            
            try {
                // Test with high accuracy
                log('📡 Requesting high accuracy location...', 'info');
                const highAccuracyPosition = await new Promise((resolve, reject) => {
                    navigator.geolocation.getCurrentPosition(resolve, reject, {
                        enableHighAccuracy: true,
                        timeout: 15000,
                        maximumAge: 0
                    });
                });
                
                userLocation = {
                    lat: highAccuracyPosition.coords.latitude,
                    lng: highAccuracyPosition.coords.longitude
                };
                
                log(`✅ High accuracy location: ${userLocation.lat.toFixed(6)}, ${userLocation.lng.toFixed(6)}`, 'success');
                log(`📏 Accuracy: ±${highAccuracyPosition.coords.accuracy.toFixed(1)} meters`, 'info');
                
                // Add user marker to map
                if (userMarker) {
                    map.removeLayer(userMarker);
                }
                
                userMarker = L.marker([userLocation.lat, userLocation.lng], {
                    icon: L.divIcon({
                        className: 'user-location-marker',
                        html: '<div style="width: 20px; height: 20px; background: #007bff; border: 3px solid white; border-radius: 50%; box-shadow: 0 2px 6px rgba(0,0,0,0.3);"></div>',
                        iconSize: [20, 20],
                        iconAnchor: [10, 10]
                    })
                }).addTo(map).bindPopup('<strong>Your Location</strong>');
                
                // Center map to show both locations
                const group = new L.featureGroup([userMarker, testMarker]);
                map.fitBounds(group.getBounds().pad(0.1));
                
            } catch (error) {
                log(`❌ High accuracy location failed: ${error.message}`, 'error');
            }
        }

        // Test 4: Network Connectivity
        async function testNetworkConnectivity() {
            log('🌐 Testing network connectivity...', 'info');
            
            // Test online status
            if (navigator.onLine) {
                log('✅ Browser reports online status', 'success');
            } else {
                log('❌ Browser reports offline status', 'error');
                return;
            }
            
            // Test OpenStreetMap tiles
            try {
                const response = await fetch('https://tile.openstreetmap.org/1/0/0.png', { 
                    method: 'HEAD',
                    mode: 'no-cors'
                });
                log('✅ OpenStreetMap tiles accessible', 'success');
            } catch (error) {
                log('❌ OpenStreetMap tiles not accessible', 'error');
            }
            
            // Test routing service
            try {
                const testUrl = 'https://router.project-osrm.org/route/v1/driving/123.8854,10.3157;123.8900,10.3200?overview=false&steps=false';
                const response = await fetch(testUrl);
                if (response.ok) {
                    log('✅ OSRM routing service accessible', 'success');
                } else {
                    log('❌ OSRM routing service returned error', 'error');
                }
            } catch (error) {
                log('❌ OSRM routing service not accessible', 'error');
            }
        }

        // Test 5: Routing Service
        async function testRoutingService() {
            log('🗺️ Testing routing functionality...', 'info');
            
            if (!userLocation) {
                log('❌ User location not available. Run location test first.', 'error');
                return;
            }
            
            try {
                // Clear existing route
                if (routingControl) {
                    map.removeControl(routingControl);
                }
                
                log('🔄 Creating route from your location to test destination...', 'info');
                
                routingControl = L.Routing.control({
                    waypoints: [
                        L.latLng(userLocation.lat, userLocation.lng),
                        L.latLng(testDestination.lat, testDestination.lng)
                    ],
                    routeWhileDragging: false,
                    showAlternatives: false,
                    lineOptions: {
                        styles: [{
                            color: '#007bff',
                            opacity: 0.8,
                            weight: 5
                        }]
                    },
                    createMarker: function() { return null; }
                }).addTo(map);
                
                routingControl.on('routesfound', function(e) {
                    const route = e.routes[0];
                    const distance = (route.summary.totalDistance / 1000).toFixed(2);
                    const time = Math.round(route.summary.totalTime / 60);
                    
                    log(`✅ Route found! Distance: ${distance} km, Time: ${time} minutes`, 'success');
                });
                
                routingControl.on('routingerror', function(e) {
                    log(`❌ Routing error: ${e.error.message}`, 'error');
                });
                
            } catch (error) {
                log(`❌ Routing test failed: ${error.message}`, 'error');
            }
        }

        // Run full diagnostic
        async function runFullDiagnostic() {
            clearLogs();
            log('🚀 Starting full diagnostic...', 'info');
            
            await testBrowserSupport();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testPermissions();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testLocationAccuracy();
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            await testNetworkConnectivity();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testRoutingService();
            
            log('✅ Full diagnostic completed!', 'success');
        }

        // Initialize system info
        function initSystemInfo() {
            const systemInfo = document.getElementById('systemInfo');
            systemInfo.innerHTML = `
                <div><strong>Browser:</strong> ${navigator.userAgent}</div>
                <div><strong>Platform:</strong> ${navigator.platform}</div>
                <div><strong>Language:</strong> ${navigator.language}</div>
                <div><strong>Online:</strong> ${navigator.onLine ? 'Yes' : 'No'}</div>
                <div><strong>Protocol:</strong> ${location.protocol}</div>
                <div><strong>Host:</strong> ${location.host}</div>
                <div><strong>Geolocation:</strong> ${navigator.geolocation ? 'Supported' : 'Not Supported'}</div>
                <div><strong>Permissions API:</strong> ${navigator.permissions ? 'Supported' : 'Not Supported'}</div>
            `;
        }

        // Initialize everything when page loads
        window.onload = function() {
            initMap();
            initSystemInfo();
            log('🔧 Diagnostic tool ready. Click any test button to begin.', 'info');
        };
    </script>
</body>
</html>
