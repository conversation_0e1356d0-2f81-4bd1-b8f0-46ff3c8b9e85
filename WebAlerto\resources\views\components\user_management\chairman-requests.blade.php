@extends('layout.app')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-sky-100 py-8">
    <!-- Main Container -->
    <div class="max-w-[1920px] mx-auto px-6 sm:px-8 lg:px-12">
        <!-- Header Section -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6 mb-8">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl shadow-lg">
                        <i class="fas fa-crown text-white text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Chairman User Management Requests</h1>
                        <p class="text-gray-600 mt-1">Manage your user management requests to SuperAdmin</p>
                        <div class="mt-2 flex items-center gap-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <i class="fas fa-crown mr-1"></i>
                                Chairman Authority
                            </span>
                            <span class="text-xs text-gray-500">Enhanced user management capabilities</span>
                        </div>
                    </div>
                </div>
                <a href="{{ route('components.user-management') }}" 
                   class="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white rounded-lg font-medium shadow-lg transition-all duration-200">
                    <i class="fas fa-arrow-left"></i>
                    Back to User Management
                </a>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="bg-white/90 backdrop-blur-sm rounded-xl shadow-xl border border-sky-200 p-6 mb-6">
            <form action="{{ route('user-management.chairman-requests') }}" method="GET" class="flex items-center gap-4">
                <div class="flex-1">
                    <label for="status" class="block text-sm font-semibold text-gray-700 mb-2">Filter by Status</label>
                    <select name="status" id="status" onchange="this.form.submit()"
                            class="w-full rounded-lg border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 bg-white/90 py-3 px-4 text-base text-gray-700 font-medium transition-all duration-200">
                        <option value="all" {{ $status === 'all' ? 'selected' : '' }}>All Requests</option>
                        <option value="pending" {{ $status === 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="approved" {{ $status === 'approved' ? 'selected' : '' }}>Approved</option>
                        <option value="rejected" {{ $status === 'rejected' ? 'selected' : '' }}>Rejected</option>
                    </select>
                </div>
            </form>
        </div>

        <!-- Requests Table -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 overflow-hidden">
            <!-- Section Header -->
            <div class="bg-gradient-to-r from-yellow-500 to-orange-500 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <i class="fas fa-clipboard-list text-white text-xl"></i>
                        <h2 class="text-xl font-bold text-white">My Management Requests</h2>
                    </div>
                    <div class="text-white text-sm">
                        <span class="font-medium">Total:</span> {{ $requests->total() }} requests
                    </div>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50/80">
                        <tr>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Target User
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Action
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Reason
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Submitted
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white/60 divide-y divide-gray-200">
                        @forelse($requests as $request)
                        <tr class="hover:bg-yellow-50/80 transition-all duration-200">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        @if($request->isRegistrationRequest())
                                            <div class="h-10 w-10 rounded-full bg-gradient-to-br from-green-400 to-emerald-500 flex items-center justify-center">
                                                <span class="text-white font-bold text-sm">
                                                    {{ strtoupper(substr($request->requested_first_name, 0, 1) . substr($request->requested_last_name, 0, 1)) }}
                                                </span>
                                            </div>
                                        @else
                                            <div class="h-10 w-10 rounded-full bg-gradient-to-br from-sky-400 to-blue-500 flex items-center justify-center">
                                                <span class="text-white font-bold text-sm">
                                                    {{ strtoupper(substr($request->targetUser->first_name, 0, 1) . substr($request->targetUser->last_name, 0, 1)) }}
                                                </span>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="ml-4">
                                        @if($request->isRegistrationRequest())
                                            <div class="text-sm font-semibold text-gray-900">
                                                {{ $request->requested_first_name }} {{ $request->requested_last_name }}
                                                <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 ml-2">
                                                    <i class="fas fa-user-plus mr-1"></i>New User
                                                </span>
                                            </div>
                                            <div class="text-sm text-gray-600">{{ $request->requested_email }}</div>
                                            <div class="text-xs text-gray-500">Position: {{ $request->requested_position }}</div>
                                        @else
                                            <div class="text-sm font-semibold text-gray-900">
                                                {{ $request->targetUser->first_name }} {{ $request->targetUser->last_name }}
                                            </div>
                                            <div class="text-sm text-gray-600">{{ $request->targetUser->email }}</div>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                @if($request->action_type === 'registration')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-user-plus mr-1"></i>
                                        Registration
                                    </span>
                                @elseif($request->action_type === 'deactivate')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-pause mr-1"></i>
                                        Deactivate
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-trash mr-1"></i>
                                        Delete
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900 max-w-xs truncate" title="{{ $request->reason }}">
                                    {{ $request->reason }}
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    @if($request->status === 'pending') bg-blue-100 text-blue-800
                                    @elseif($request->status === 'approved') bg-green-100 text-green-800
                                    @else bg-red-100 text-red-800 @endif">
                                    <i class="fas 
                                        @if($request->status === 'pending') fa-clock
                                        @elseif($request->status === 'approved') fa-check
                                        @else fa-times @endif mr-1"></i>
                                    {{ ucfirst($request->status) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-600">
                                {{ $request->created_at->format('M d, Y H:i') }}
                            </td>
                            <td class="px-6 py-4">
                                @if($request->status === 'pending')
                                    <form action="{{ route('user-management.chairman-requests.cancel', $request->id) }}" method="POST" class="inline">
                                        @csrf
                                        @method('PATCH')
                                        <button type="submit" 
                                                class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded-lg text-xs font-medium transition-colors duration-200"
                                                onclick="return confirm('Are you sure you want to cancel this request?')">
                                            <i class="fas fa-times mr-1"></i>
                                            Cancel
                                        </button>
                                    </form>
                                @else
                                    <span class="text-xs text-gray-500 italic">{{ ucfirst($request->status) }}</span>
                                @endif
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center">
                                    <div class="text-gray-400 text-4xl mb-3">📋</div>
                                    <p class="text-gray-500 font-medium">No management requests found</p>
                                    <p class="text-gray-400 text-sm mt-1">Go to User Management to create your first request</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($requests->hasPages())
            <div class="bg-gray-50/80 px-6 py-4 border-t border-gray-200">
                {{ $requests->links() }}
            </div>
            @endif
        </div>

        <!-- Quick Actions for Manageable Users -->
        @if($manageableUsers->count() > 0)
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6 mt-8">
            <h3 class="text-lg font-bold text-gray-900 mb-4">
                <i class="fas fa-users text-yellow-500 mr-2"></i>
                Quick Actions - Manageable Users
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                @foreach($manageableUsers as $user)
                <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-semibold text-gray-900">{{ $user->first_name }} {{ $user->last_name }}</h4>
                            <p class="text-sm text-gray-600">{{ $user->email }}</p>
                            <p class="text-xs text-gray-500">{{ $user->position }}</p>
                        </div>
                        <div class="flex gap-1">
                            <button onclick="quickRequest({{ $user->id }}, '{{ $user->first_name }} {{ $user->last_name }}', 'deactivate')"
                                    class="p-2 bg-yellow-500 hover:bg-yellow-600 text-white rounded text-xs">
                                <i class="fas fa-pause"></i>
                            </button>
                            <button onclick="quickRequest({{ $user->id }}, '{{ $user->first_name }} {{ $user->last_name }}', 'delete')"
                                    class="p-2 bg-red-500 hover:bg-red-600 text-white rounded text-xs">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @endif
    </div>
</div>

<!-- Quick Request Modal -->
<div id="quickRequestModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center p-4">
    <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full">
        <div class="bg-gradient-to-r from-yellow-500 to-orange-500 px-6 py-4">
            <h3 class="text-xl font-bold text-white">Quick Request</h3>
        </div>
        <div class="p-6">
            <form id="quickRequestForm" action="{{ route('user-management.chairman-requests.store') }}" method="POST">
                @csrf
                <input type="hidden" id="quickUserId" name="target_user_id">
                <input type="hidden" id="quickAction" name="action_type">
                
                <p class="mb-4">Request to <span id="quickActionText"></span> user: <strong id="quickUserName"></strong></p>
                
                <div class="mb-6">
                    <label for="quickReason" class="block text-sm font-medium text-gray-700 mb-2">Reason *</label>
                    <textarea id="quickReason" name="reason" rows="3" required
                              class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-yellow-500"
                              placeholder="Provide reason for this request..."></textarea>
                </div>
                
                <div class="flex gap-3">
                    <button type="button" onclick="closeQuickModal()" class="flex-1 px-4 py-2 border border-gray-300 rounded-lg">Cancel</button>
                    <button type="submit" class="flex-1 px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-white rounded-lg">Submit</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function quickRequest(userId, userName, action) {
    document.getElementById('quickUserId').value = userId;
    document.getElementById('quickAction').value = action;
    document.getElementById('quickUserName').textContent = userName;
    document.getElementById('quickActionText').textContent = action;
    document.getElementById('quickRequestModal').classList.remove('hidden');
    document.getElementById('quickRequestModal').classList.add('flex');
}

function closeQuickModal() {
    document.getElementById('quickRequestModal').classList.add('hidden');
    document.getElementById('quickRequestModal').classList.remove('flex');
    document.getElementById('quickRequestForm').reset();
}
</script>
@endsection
