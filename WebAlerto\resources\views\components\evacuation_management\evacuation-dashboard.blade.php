@extends('layout.app')

@section('title', 'Evacuation Management Dashboard')

@push('styles')
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
      integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
      crossorigin=""/>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
<style>
    /* Delete Modal Styles */
    #deleteModal {
        display: none;
    }
    
    #deleteModal.show {
        display: flex;
    }
    
    #deleteModal .bg-white {
        background-color: white;
        border-radius: 16px;
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    }
    
    #deleteButton {
        background-image: linear-gradient(to right, #dc2626, #e11d48);
    }
    
    #deleteButton:hover {
        background-image: linear-gradient(to right, #b91c1c, #be123c);
    }
    
    #deleteButton:disabled {
        opacity: 0.7;
        cursor: not-allowed;
    }
</style>
@endpush

@section('content')
<div class="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-sky-100 py-8">
    <div class="max-w-[1920px] mx-auto px-6 sm:px-8 lg:px-12">
        <!-- Header Section -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-4 sm:p-6 mb-8">
            <div class="flex flex-col justify-center">
                <h1 class="text-base sm:text-2xl font-bold text-black-600">Evacuation Center Management Dashboard</h1>
                <p class="text-xs sm:text-base text-gray-600 mt-1">Manage and monitor evacuation centers</p>
            </div>
        </div>

        <!-- Stats Grid - Make responsive -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-6 mb-4 md:mb-6">
 
            <!-- Total Centers -->
            <a href="{{ route('components.evacuation_management.centers-list', ['type' => 'all']) }}" class="bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border border-sky-200 p-3 md:p-6 transform transition-all hover:scale-105">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-xs md:text-sm font-medium text-gray-600">Total Centers</p>
                        <h3 class="text-xl md:text-3xl font-bold text-gray-900 mt-1 md:mt-2">{{ $totalCenters }}</h3>
                    </div>
                    <div class="p-2 md:p-4 bg-gradient-to-br from-sky-500 to-blue-600 rounded-lg shadow-lg">
                        <i class="fas fa-building text-white text-lg md:text-2xl"></i>
                    </div>
                </div>
            </a>
 
            <!-- Active Centers -->
            <a href="{{ route('components.evacuation_management.centers-list', ['type' => 'active']) }}" class="bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border border-green-200 p-3 md:p-6 transform transition-all hover:scale-105">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-xs md:text-sm font-medium text-gray-600">Active Centers</p>
                        <h3 class="text-xl md:text-3xl font-bold text-green-600 mt-1 md:mt-2">{{ $activeCenters }}</h3>
                    </div>
                    <div class="p-2 md:p-4 bg-gradient-to-br from-emerald-500 to-green-600 rounded-lg shadow-lg">
                        <i class="fas fa-check-circle text-white text-lg md:text-2xl"></i>
                    </div>
                </div>
            </a>
 
            <!-- Inactive Centers -->
            <a href="{{ route('components.evacuation_management.centers-list', ['type' => 'inactive']) }}" class="bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border border-red-200 p-3 md:p-6 transform transition-all hover:scale-105">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-xs md:text-sm font-medium text-gray-600">Inactive Centers</p>
                        <h3 class="text-xl md:text-3xl font-bold text-red-600 mt-1 md:mt-2">{{ $inactiveCenters }}</h3>
                    </div>
                    <div class="p-2 md:p-4 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg shadow-lg">
                        <i class="fas fa-times-circle text-white text-lg md:text-2xl"></i>
                    </div>
                </div>
            </a>
 
            <!-- Under Maintenance -->
            <a href="{{ route('components.evacuation_management.centers-list', ['type' => 'maintenance']) }}" class="bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border border-yellow-200 p-3 md:p-6 transform transition-all hover:scale-105">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-xs md:text-sm font-medium text-gray-600">Under Maintenance</p>
                        <h3 class="text-xl md:text-3xl font-bold text-yellow-600 mt-1 md:mt-2">{{ $maintenanceCenters }}</h3>
                    </div>
                    <div class="p-2 md:p-4 bg-gradient-to-br from-amber-500 to-yellow-600 rounded-lg shadow-lg">
                        <i class="fas fa-wrench text-white text-lg md:text-2xl"></i>
                    </div>
                </div>
            </a>
         </div>

        <!-- Search and Filter Section -->
        <div class="bg-white/90 backdrop-blur-sm rounded-xl shadow-xl border border-sky-200 p-6 mb-6">
            <!-- Header with Add Button -->
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
                <div class="flex items-center gap-3">
                    <div class="p-3 bg-gradient-to-br from-sky-500 to-blue-600 rounded-lg shadow-lg">
                        <i class="fas fa-search text-white text-xl"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-bold text-gray-900">Find Evacuation Centers</h2>
                        <p class="text-sm text-gray-600 mt-1">Begin your search below</p>
                    </div>
                </div>

                @if(!auth()->user()->hasRole('super_admin') && !auth()->user()->hasRole('system_admin'))

                    <a href="{{ route('components.evacuation_management.add-evacuation-center') }}"
                       class="inline-flex items-center justify-center gap-2 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white px-6 py-3 rounded-lg font-semibold shadow-lg transition-all duration-200 transform hover:scale-105">
                        <i class="fas fa-plus"></i>
                        <span>Add New Center</span>
                    </a>
                @endif
            </div>

            <!-- Search Bar - Top, Full Width -->
            <div class="mb-6">
                <div class="relative">
                    <input type="text" id="searchInput" 
                           class="w-full rounded-lg border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 bg-white/90 py-4 px-4 text-base text-gray-700 font-medium transition-all duration-200 hover:border-blue-400 pl-12"
                           placeholder="Search by center name, address, or barangay...">
                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            </div>

            <!-- Filter Controls -->
            <form action="{{ route('components.evacuation_management.evacuation-dashboard') }}" method="GET">
                @if(auth()->user()->hasRole('system_admin'))
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <!-- City Filter (Only for System Admin) -->
                    <div class="relative">
                        <label for="city" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-city mr-1"></i>Filter by City
                        </label>
                        <select name="city" id="city" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" onchange="this.form.submit()">
                            <option value="">All Cities</option>
                            @foreach($cities as $city)
                                <option value="{{ $city }}" {{ $selectedCity == $city ? 'selected' : '' }}>
                                    {{ $city }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Barangay Filter -->
                    <div class="relative">
                        <label for="barangay" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-map-marker-alt mr-1"></i>Filter by Barangay
                        </label>
                        <select name="barangay" id="barangay" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" {{ !$selectedCity ? 'disabled' : '' }} onchange="this.form.submit()">
                            <option value="">All Barangays</option>
                            @foreach($barangays as $barangay)
                                <option value="{{ $barangay }}" {{ $selectedBarangay == $barangay ? 'selected' : '' }}>
                                    {{ $barangay }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Status Filter -->
                    <div class="relative">
                        <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-info-circle mr-1"></i>Filter by Status
                        </label>
                        <select name="status" id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" onchange="this.form.submit()">
                            <option value="">All Status</option>
                            <option value="Active" {{ request('status') == 'Active' ? 'selected' : '' }}>Active</option>
                            <option value="Inactive" {{ request('status') == 'Inactive' ? 'selected' : '' }}>Inactive</option>
                            <option value="Under Maintenance" {{ request('status') == 'Under Maintenance' ? 'selected' : '' }}>Under Maintenance</option>
                        </select>
                    </div>
                </div>


                @elseif(auth()->user()->hasRole('super_admin'))
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <!-- Barangay Filter (Only for CDRRMC) -->
                    <div class="relative">
                        <label for="barangay" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-map-marker-alt mr-1"></i>Filter by Barangay
                        </label>
                        <select name="barangay" id="barangay" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" onchange="this.form.submit()">
                            <option value="">All Barangays</option>
                            @foreach($barangays as $barangay)
                                <option value="{{ $barangay }}" {{ $selectedBarangay == $barangay ? 'selected' : '' }}>
                                    {{ $barangay }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Status Filter -->
                    <div class="relative">
                        <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-info-circle mr-1"></i>Filter by Status
                        </label>
                        <select name="status" id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" onchange="this.form.submit()">
                            <option value="">All Status</option>
                            <option value="Active" {{ request('status') == 'Active' ? 'selected' : '' }}>Active</option>
                            <option value="Inactive" {{ request('status') == 'Inactive' ? 'selected' : '' }}>Inactive</option>
                            <option value="Under Maintenance" {{ request('status') == 'Under Maintenance' ? 'selected' : '' }}>Under Maintenance</option>
                        </select>
                    </div>
                </div>
                @else
                <!-- BDRRMC users see only their barangay data - no filter needed -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-map-marker-alt text-blue-600 mr-2"></i>
                        <span class="text-sm font-medium text-blue-800">
                            Showing centers for: <strong>{{ auth()->user()->barangay }}</strong>
                        </span>
                    </div>
                </div>
                @endif
            </form>
        </div>

        <!-- Table Section -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border border-sky-200 overflow-hidden">
            <!-- Mobile Card View (visible on small screens) -->
            <div class="md:hidden">
                <div class="bg-blue-500 text-white p-4 grid grid-cols-3">
                    <div class="font-semibold">Center Name</div>
                    <div class="font-semibold text-center">Status</div>
                    <div class="font-semibold text-center">Actions</div>
                </div>
                
                <div class="divide-y divide-sky-100">
                    @forelse($centers as $center)
                        <div class="p-4 evacuation-row" 
                            data-name="{{ strtolower($center->name) }}"
                            data-address="{{ strtolower($center->street_name . ' ' . $center->barangay . ' ' . $center->city . ' ' . $center->province) }}">
                            
                            <div class="grid grid-cols-1 gap-2 mb-2">
                                <!-- Center name and status in one row -->
                                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
                                    <div class="w-full sm:w-auto">
                                        <span class="font-medium text-gray-900 text-sm sm:text-base line-clamp-2">{{ $center->name }}</span>
                                    </div>
                                    <div class="w-full sm:w-auto flex justify-between sm:justify-end items-center gap-2">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                            @if($center->status === 'Active')
                                                bg-green-100 text-green-800
                                            @elseif($center->status === 'Under Maintenance')
                                                bg-yellow-100 text-yellow-800
                                            @else
                                                bg-red-100 text-red-800
                                            @endif
                                        ">
                                            {{ $center->status }}
                                        </span>
                                        <div class="flex justify-end gap-1">
                                            <button onclick="openViewModal({{ $center->id }})"
                                                    class="bg-blue-500 text-white p-1 rounded-lg">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            @if(!auth()->user()->hasRole('super_admin'))
                                                <a href="{{ route('components.evacuation_management.edit-evacuation-center', $center->id) }}"
                                                   class="bg-yellow-500 text-white p-1 rounded-lg">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button onclick="openDeleteModal({{ $center->id }}, '{{ $center->name }}')"
                                                        class="bg-red-500 text-white p-1 rounded-lg">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Mobile details -->
                            <div class="text-sm text-gray-600 space-y-1 border-l-2 border-sky-200 pl-2">
                                <div>
                                    <span class="font-medium">Address:</span> 
                                    @php
                                        $addressParts = [
                                            $center->building_name,
                                            $center->street_name,
                                            $center->barangay,
                                            $center->city,
                                            $center->province,
                                        ];
                                        
                                        $address = implode(', ', array_filter($addressParts));
                                        $address = trim(str_ireplace('Philippines', '', $address), ' ,');
                                    @endphp
                                    {{ $address }}
                                </div>
                                <div>
                                    <span class="font-medium">Capacity:</span> {{ $center->capacity }}
                                </div>
                                <div>
                                    <span class="font-medium">Contact:</span>
                                    @if(is_array($center->contact))
                                        @foreach($center->contact as $contact)
                                            <div class="ml-2">
                                                <span class="text-sm">{{ $contact['number'] }}</span>
                                                <span class="text-xs text-gray-500">({{ $contact['network'] }})</span>
                                            </div>
                                        @endforeach
                                    @else
                                        {{ $center->contact }}
                                    @endif
                                </div>
                                <div>
                                    <span class="font-medium">Disaster Types:</span>
                                    <div class="flex flex-wrap gap-1 mt-1">
                                        @if(is_array($center->disaster_type))
                                            @foreach($center->disaster_type as $type)
                                                @php
                                                    $badgeClass = 'bg-gray-100 text-gray-800';
                                                    $displayText = $type;
                                                    
                                                    if (strpos($type, 'Others:') === 0) {
                                                        $badgeClass = 'bg-purple-100 text-purple-800';
                                                        $displayText = trim(str_replace('Others:', '', $type));
                                                    } 
                                                    else if ($type === 'Typhoon') {
                                                        $badgeClass = 'bg-blue-100 text-blue-800';
                                                    }
                                                    else if ($type === 'Flood') {
                                                        $badgeClass = 'bg-cyan-100 text-cyan-800';
                                                    }
                                                    else if ($type === 'Fire') {
                                                        $badgeClass = 'bg-red-100 text-red-800';
                                                    }
                                                    else if ($type === 'Earthquake') {
                                                        $badgeClass = 'bg-amber-100 text-amber-800';
                                                    }
                                                    else if ($type === 'Landslide') {
                                                        $badgeClass = 'bg-yellow-100 text-yellow-800';
                                                    }
                                                @endphp
                                                <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium {{ $badgeClass }}">
                                                    {{ $displayText }}
                                                </span>
                                            @endforeach
                                        @else
                                            <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                {{ $center->disaster_type }}
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="p-8 text-center text-gray-500">
                            No evacuation centers found
                        </div>
                    @endforelse
                    
                    <!-- No results message for mobile -->
                    <div id="noResultsMessageMobile" class="p-8 text-center text-gray-500 hidden">
                        No evacuation centers found matching "<span class="searchTermDisplayMobile"></span>"
                    </div>
                </div>
            </div>

            <!-- Desktop Table View (hidden on small screens) -->
            <div class="hidden md:block overflow-x-auto">
                <table class="min-w-full">
                    <thead>
                        <tr class="bg-blue-500 text-white text-sm leading-normal">
                            <th class="py-3 px-6 text-left font-semibold">Center Name</th>
                            <th class="py-3 px-6 text-left font-semibold">Address</th>
                            <th class="py-3 px-6 text-center font-semibold">Capacity</th>
                            <th class="py-3 px-6 text-center font-semibold">Contact</th>
                            <th class="py-3 px-6 text-center font-semibold">Disaster Type</th>
                            <th class="py-3 px-6 text-center font-semibold">Status</th>
                            <th class="py-3 px-6 text-center font-semibold">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="text-gray-600 text-sm" id="evacuationTableBody">
                        @forelse($centers as $center)
                            <tr class="evacuation-row border-b border-sky-100 hover:bg-sky-50/50 transition-colors" 
                                data-name="{{ strtolower($center->name) }}"
                                data-address="{{ strtolower($center->street_name . ' ' . $center->barangay . ' ' . $center->city . ' ' . $center->province) }}">
                                <td class="py-3 px-6">
                                    <span class="font-medium text-gray-900">{{ $center->name }}</span>
                                </td>
                                <td class="py-3 px-6 text-left">
                                    @php
                                        $addressParts = [
                                            $center->building_name,
                                            $center->street_name,
                                            $center->barangay,
                                            $center->city,
                                            $center->province,
                                        ];
                                        
                                        $address = implode(', ', array_filter($addressParts));
                                        
                                        // Ensure "Philippines" is at the end, but only once.
                                        $address = trim(str_ireplace('Philippines', '', $address), ' ,');
                                        $address .= ', Philippines';
                                    @endphp
                                    {{ $address }}
                                </td>
                                <td class="py-3 px-6 text-center">{{ $center->capacity }}</td>
                                <td class="py-3 px-6 text-center">
                                    @if(is_array($center->contact))
                                        @foreach($center->contact as $contact)
                                            <div class="text-sm">
                                                {{ $contact['number'] }}
                                                <span class="text-xs text-gray-500">({{ $contact['network'] }})</span>
                                            </div>
                                        @endforeach
                                    @else
                                        {{ $center->contact }}
                                    @endif
                                </td>
                                <td class="py-3 px-6 text-center">
                                    <div class="flex flex-wrap justify-center gap-1">
                                        @if(is_array($center->disaster_type))
                                            @foreach($center->disaster_type as $type)
                                                @php
                                                    $badgeClass = 'bg-gray-100 text-gray-800';
                                                    $displayText = $type;
                                                    
                                                    if (strpos($type, 'Others:') === 0) {
                                                        $badgeClass = 'bg-purple-100 text-purple-800';
                                                        $displayText = trim(str_replace('Others:', '', $type));
                                                    } 
                                                    else if ($type === 'Typhoon') {
                                                        $badgeClass = 'bg-green-100 text-green-800';
                                                    }
                                                    else if ($type === 'Flood') {
                                                        $badgeClass = 'bg-cyan-100 text-cyan-800';
                                                    }
                                                    else if ($type === 'Fire') {
                                                        $badgeClass = 'bg-red-100 text-red-800';
                                                    }
                                                    else if ($type === 'Earthquake') {
                                                        $badgeClass = 'bg-amber-100 text-amber-800';
                                                    }
                                                    else if ($type === 'Landslide') {
                                                        $badgeClass = 'bg-yellow-100 text-yellow-800';
                                                    }
                                                @endphp
                                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium {{ $badgeClass }}">
                                                    {{ $displayText }}
                                                </span>
                                            @endforeach
                                        @else
                                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                {{ $center->disaster_type }}
                                            </span>
                                        @endif
                                    </div>
                                </td>
                                <td class="py-3 px-6 text-center">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        @if($center->status === 'Active')
                                            bg-green-100 text-green-800
                                        @elseif($center->status === 'Under Maintenance')
                                            bg-yellow-100 text-yellow-800
                                        @else
                                            bg-red-100 text-red-800
                                        @endif
                                    ">
                                        {{ $center->status }}
                                    </span>
                                </td>
                                <td class="py-3 px-6 text-center">
                                    <div class="flex justify-center gap-2">
                                        <button onclick="openViewModal({{ $center->id }})"
                                                class="inline-flex items-center justify-center gap-1 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white px-3 py-1.5 rounded-lg text-xs font-medium shadow-lg transition-all duration-200">
                                            <i class="fas fa-eye"></i>
                                            <span>View</span>
                                        </button>
                                        @if(!auth()->user()->hasRole('super_admin'))
                                            <a href="{{ route('components.evacuation_management.edit-evacuation-center', $center->id) }}"
                                               class="inline-flex items-center justify-center gap-1 bg-gradient-to-r from-amber-500 to-yellow-600 hover:from-amber-600 hover:to-yellow-700 text-white px-3 py-1.5 rounded-lg text-xs font-medium shadow-lg transition-all duration-200">
                                                <i class="fas fa-edit"></i>
                                                <span>Edit</span>
                                            </a>
                                            <button onclick="openDeleteModal({{ $center->id }}, '{{ $center->name }}')"
                                                    class="inline-flex items-center justify-center gap-1 bg-gradient-to-r from-red-600 to-rose-600 hover:from-red-700 hover:to-rose-700 text-white px-3 py-1.5 rounded-lg text-xs font-medium shadow-lg transition-all duration-200">
                                                <i class="fas fa-trash"></i>
                                                <span>Delete</span>
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="py-8 text-center text-gray-500">
                                    No evacuation centers found
                                </td>
                            </tr>
                        @endforelse
                        <!-- Add no results message -->
                        <tr id="noResultsMessage" class="hidden">
                            <td colspan="7" class="py-8 text-center text-gray-500">
                                No evacuation centers found matching "<span id="searchTermDisplay"></span>"
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($centers->hasPages())
                <div class="px-6 py-4 bg-gradient-to-r from-sky-50 to-blue-50 border-t border-sky-100">
                    {{ $centers->links() }}
                </div>
            @endif
        </div>
    </div>
</div>

<!-- View Modal -->
<div id="viewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 backdrop-blur-sm z-50 flex items-center justify-center p-4 hidden overflow-y-auto" onclick="closeViewModal()">
    <div class="relative mx-auto w-full max-w-sm md:max-w-4xl bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border border-sky-200 overflow-hidden" onclick="event.stopPropagation()">
        <!-- Header -->
        <div class="bg-gradient-to-r from-sky-600 to-blue-600 px-4 md:px-6 py-3 md:py-4">
            <div class="flex items-center justify-between">
                <h3 class="text-lg md:text-xl font-semibold text-white">Evacuation Center Details</h3>
                <button onclick="closeViewModal()" class="text-white hover:text-sky-100 transition-colors">
                    <i class="fas fa-times text-lg md:text-xl"></i>
                </button>
            </div>
        </div>
        <!-- Content -->
        <div id="viewModalContent" class="p-4 md:p-6 max-h-[calc(100vh-150px)] overflow-y-auto">
            <!-- Content will be loaded dynamically -->
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50 flex items-center justify-center p-6">
    <div class="relative mx-auto p-8 border w-[480px] shadow-lg rounded-2xl bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-bold text-gray-900 mb-2">Delete Confirmation</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-gray-600">
                    Are you sure you want to delete <span id="centerNameToDelete" class="font-medium"></span>?
                </p>
                <p class="text-gray-500 text-sm mt-1">
                    This action cannot be undone.
                </p>
            </div>
            <div class="flex justify-center gap-4 mt-4">
                <button id="deleteButton" 
                        class="px-4 py-2 bg-gradient-to-r from-red-600 to-rose-600 hover:from-red-700 hover:to-rose-700 text-white text-sm font-medium rounded-lg shadow-lg transition-all duration-200">
                    Delete
                </button>
                <button onclick="closeDeleteModal()" 
                        class="px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-lg shadow-sm hover:bg-gray-200 transition-all duration-200">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
        integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
        crossorigin=""></script>
<script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
<script>
const disasterTypeColors = {
    'Typhoon': '#22c55e',
    'Flood': '#3b82f6',
    'Fire': '#ef4444',
    'Earthquake': '#f59e42',
    'Landslide': '#a16207',
    'Others': '#9333ea',
    'Multi-disaster': '#6b7280'
};

// Search functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    
    if (searchInput) {
        searchInput.addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase().trim();
            const rows = document.querySelectorAll('.evacuation-row');
            let visibleCount = 0;
            
            rows.forEach(row => {
                const name = row.dataset.name || '';
                const address = row.dataset.address || '';
                
                if (name.includes(searchTerm) || address.includes(searchTerm)) {
                    row.classList.remove('hidden');
                    visibleCount++;
                } else {
                    row.classList.add('hidden');
                }
            });
            
            // Handle no results message for desktop
            const noResultsMessage = document.getElementById('noResultsMessage');
            if (noResultsMessage) {
                if (visibleCount === 0 && searchTerm !== '') {
                    noResultsMessage.classList.remove('hidden');
                    const searchTermDisplay = document.getElementById('searchTermDisplay');
                    if (searchTermDisplay) {
                        searchTermDisplay.textContent = searchTerm;
                    }
                } else {
                    noResultsMessage.classList.add('hidden');
                }
            }
            
            // Handle no results message for mobile
            const noResultsMessageMobile = document.getElementById('noResultsMessageMobile');
            if (noResultsMessageMobile) {
                if (visibleCount === 0 && searchTerm !== '') {
                    noResultsMessageMobile.classList.remove('hidden');
                    const searchTermDisplayMobile = document.querySelector('.searchTermDisplayMobile');
                    if (searchTermDisplayMobile) {
                        searchTermDisplayMobile.textContent = searchTerm;
                    }
                } else {
                    noResultsMessageMobile.classList.add('hidden');
                }
            }
        });
    }
});

function openDeleteModal(id, name) {
    // Set the center name in the modal
    document.getElementById('centerNameToDelete').textContent = name;
    
    // Show the modal
    const modal = document.getElementById('deleteModal');
    modal.classList.remove('hidden');
    modal.classList.add('show');
    
    // Set up the delete button click handler
    const deleteButton = document.getElementById('deleteButton');
    
    // Remove any existing event listeners
    const newDeleteButton = deleteButton.cloneNode(true);
    deleteButton.parentNode.replaceChild(newDeleteButton, deleteButton);
    
    // Add new event listener
    newDeleteButton.addEventListener('click', function() {
        // Show loading state with spinner
        this.disabled = true;
        this.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Deleting...';
        
        // Send delete request
        fetch(`/evacuation/${id}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Refresh the page after successful deletion
                window.location.reload();
            } else {
                // Re-enable button if error
                this.disabled = false;
                this.innerHTML = 'Delete';
                
                // Show error message using Toastify if available
                if (typeof Toastify === 'function') {
                    Toastify({
                        text: data.message || 'Failed to delete evacuation center',
                        duration: 3000,
                        close: true,
                        gravity: "top",
                        position: "right",
                        backgroundColor: "#ef4444",
                    }).showToast();
                } else {
                    alert(data.message || 'Failed to delete evacuation center');
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            this.disabled = false;
            this.innerHTML = 'Delete';
            
            // Show error message using Toastify if available
            if (typeof Toastify === 'function') {
                Toastify({
                    text: 'An unexpected error occurred',
                    duration: 3000,
                    close: true,
                    gravity: "top",
                    position: "right",
                    backgroundColor: "#ef4444",
                }).showToast();
            } else {
                alert('An unexpected error occurred');
            }
        });
    });
}

function closeDeleteModal() {
    // Hide the modal
    const modal = document.getElementById('deleteModal');
    modal.classList.add('hidden');
    modal.classList.remove('show');
}

// Replace the showNotification function to avoid alerts
function showNotification(title, message, type) {
    // Only use Toastify if available, otherwise do nothing (no alert fallback)
    if (typeof Toastify === 'function') {
        Toastify({
            text: message,
            duration: 3000,
            close: true,
            gravity: "top",
            position: "right",
            backgroundColor: type === 'success' ? "#10b981" : "#ef4444",
        }).showToast();
    }
}

function openViewModal(id) {
    // Show modal immediately with loading spinner
    const viewModal = document.getElementById('viewModal');
    const modalContent = document.getElementById('viewModalContent');
    
    viewModal.classList.remove('hidden');
    
    // Add loading indicator
    modalContent.innerHTML = `
        <div class="flex items-center justify-center py-8">
            <div class="animate-spin rounded-full h-12 w-12 border-4 border-sky-500 border-t-transparent"></div>
        </div>
    `;
    
    // Fetch center details
    fetch(`/api/evacuation-centers/${id}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            // Process disaster types for display
            function getDisasterTypeBadges(disasterTypes) {
                if (!disasterTypes) return '';
                
                let types = Array.isArray(disasterTypes) ? disasterTypes : [disasterTypes];
                return types.map(type => {
                    let badgeClass = 'bg-gray-100 text-gray-800';
                    let displayText = type;
                    
                    if (type.startsWith('Others:')) {
                        badgeClass = 'bg-purple-100 text-purple-800';
                        displayText = type.replace('Others:', '').trim();
                    } else if (type === 'Typhoon') {
                        badgeClass = 'bg-green-100 text-green-800';
                    } else if (type === 'Flood') {
                        badgeClass = 'bg-cyan-100 text-cyan-800';
                    } else if (type === 'Fire') {
                        badgeClass = 'bg-red-100 text-red-800';
                    } else if (type === 'Earthquake') {
                        badgeClass = 'bg-amber-100 text-amber-800';
                    } else if (type === 'Landslide') {
                        badgeClass = 'bg-yellow-100 text-yellow-800';
                    }
                    
                    return `<span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${badgeClass}">${displayText}</span>`;
                }).join('');
            }
            
            // Determine marker color based on disaster type
            let markerColor = '#3b82f6'; // Default blue
            if (data.disaster_type) {
                const types = Array.isArray(data.disaster_type) ? data.disaster_type : [data.disaster_type];
                const primaryType = types[0];
                
                if (primaryType === 'Typhoon') markerColor = '#22c55e';
                else if (primaryType === 'Flood') markerColor = '#3b82f6';
                else if (primaryType === 'Fire') markerColor = '#ef4444';
                else if (primaryType === 'Earthquake') markerColor = '#f59e42';
                else if (primaryType === 'Landslide') markerColor = '#a16207';
                else if (primaryType.startsWith('Others:')) markerColor = '#9333ea';
            }
            
            // Update modal content with center details
            modalContent.innerHTML = `
                <div class="p-6 space-y-4">
                    <!-- Center Name -->
                    <div>
                        <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">CENTER NAME</h4>
                        <p class="text-lg font-bold text-gray-900">${data.name}</p>
                    </div>

                    <!-- Capacity and Contact Info Row -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">CAPACITY</h4>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-users text-sky-500"></i>
                                <p class="text-gray-900 font-medium">${data.capacity}</p>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">CONTACT INFO</h4>
                            <div class="space-y-1">
                                ${Array.isArray(data.contact) ?
                                    data.contact.map(contact => `
                                        <div class="flex items-center gap-2">
                                            <i class="fas fa-phone text-sky-500"></i>
                                            <p class="text-gray-900">${contact.number}</p>
                                            <span class="text-xs text-gray-500">(${contact.network})</span>
                                        </div>
                                    `).join('') :
                                    `<div class="flex items-center gap-2">
                                        <i class="fas fa-phone text-sky-500"></i>
                                        <p class="text-gray-900">${data.contact}</p>
                                    </div>`
                                }
                            </div>
                        </div>
                    </div>

                    <!-- Status and Disaster Type Row -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">STATUS</h4>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                                data.status === 'Active' ? 'bg-green-100 text-green-800' :
                                data.status === 'Under Maintenance' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-red-100 text-red-800'
                            }">
                                <i class="fas ${
                                    data.status === 'Active' ? 'fa-circle' :
                                    data.status === 'Under Maintenance' ? 'fa-circle' :
                                    'fa-circle'
                                } mr-2 text-xs"></i>
                                ${data.status}
                            </span>
                        </div>
                        <div>
                            <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">DISASTER TYPE</h4>
                            <div class="flex flex-wrap gap-2">
                                ${getDisasterTypeBadges(data.disaster_type)}
                            </div>
                        </div>
                    </div>

                    <!-- Address -->
                    <div>
                        <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">ADDRESS</h4>
                        <div class="flex items-start gap-2">
                            <i class="fas fa-map-marker-alt text-sky-500 mt-1"></i>
                            <p class="text-gray-900">${data.building_name ? data.building_name + ', ' : ''}${data.street_name}, ${data.barangay}, ${data.city}, ${data.province}, Philippines</p>
                        </div>
                    </div>

                    <!-- Location Map -->
                    <div>
                        <h4 class="text-sm font-semibold text-sky-600 uppercase tracking-wider mb-2">LOCATION</h4>
                        <div id="viewMap" class="h-80 w-full rounded-lg border border-gray-300 shadow-sm bg-gray-50"></div>
                    </div>
                </div>
            `;

            // Initialize map if coordinates are available
            if (data.latitude && data.longitude) {
                setTimeout(() => {
                    const map = L.map('viewMap', {
                        zoomControl: true,
                        maxZoom: 19,
                        minZoom: 1,
                        scrollWheelZoom: true
                    }).setView([data.latitude, data.longitude], 15);
                    
                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '© <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                        maxZoom: 19
                    }).addTo(map);

                    // Determine correct pin based on disaster types
                    const disasterTypePins = {
                        'Typhoon': '/image/pins/forTyphoon.png',
                        'Flood': '/image/pins/forFlood.png',
                        'Fire': '/image/pins/forFire.png',
                        'Earthquake': '/image/pins/forEarthquake.png',
                        'Landslide': '/image/pins/forLandslide.png',
                        'Others': '/image/pins/forOthers.png',
                        'Multi-disaster': '/image/pins/forMultiple.png'
                    };

                    let disasterTypes = [];
                    try {
                        disasterTypes = typeof data.disaster_type === 'string' ? JSON.parse(data.disaster_type) : data.disaster_type;
                    } catch (e) {
                        disasterTypes = data.disaster_type ? [data.disaster_type] : [];
                    }

                    let pinUrl;
                    if (!disasterTypes || disasterTypes.length === 0) {
                        pinUrl = disasterTypePins['Others'];
                    } else if (disasterTypes.length > 1) {
                        pinUrl = disasterTypePins['Multi-disaster'];
                    } else {
                        const type = disasterTypes[0];
                        pinUrl = disasterTypePins[type] || disasterTypePins['Others'];
                    }

                    const markerIcon = L.icon({
                        iconUrl: pinUrl,
                        iconSize: [32, 32],
                        iconAnchor: [16, 32],
                        popupAnchor: [0, -32]
                    });

                    L.marker([data.latitude, data.longitude], { icon: markerIcon }).addTo(map);
                }, 100); // Small delay to ensure the container is ready
            }
        })
        .catch(error => {
            console.error('Error fetching center details:', error);
            modalContent.innerHTML = `
                <div class="text-center py-8">
                    <div class="text-red-500 text-5xl mb-4">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Failed to Load Data</h3>
                    <p class="text-gray-500">There was an error loading the center details. Please try again.</p>
                    <button onclick="closeViewModal()" class="mt-4 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                        Close
                    </button>
                </div>
            `;
        });
}

function closeViewModal() {
    document.getElementById('viewModal').classList.add('hidden');
}

function confirmDelete() {
    const form = document.getElementById('deleteForm');
    const id = form.dataset.id;
    
    // Create a form to submit
    const submitForm = document.createElement('form');
    submitForm.method = 'POST';
    submitForm.action = `/evacuation-centers/${id}`;
    submitForm.style.display = 'none';
    
    // Add CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = '_token';
    csrfInput.value = csrfToken;
    submitForm.appendChild(csrfInput);
    
    // Add method field
    const methodInput = document.createElement('input');
    methodInput.type = 'hidden';
    methodInput.name = '_method';
    methodInput.value = 'DELETE';
    submitForm.appendChild(methodInput);
    
    // Append to body and submit
    document.body.appendChild(submitForm);
    submitForm.submit();
}

// City-Barangay filter interaction for System Admin
@if(auth()->user()->hasRole('system_admin'))
document.addEventListener('DOMContentLoaded', function() {
    const citySelect = document.getElementById('city');
    const barangaySelect = document.getElementById('barangay');

    if (citySelect && barangaySelect) {
        citySelect.addEventListener('change', function() {
            loadBarangaysForEvacuation();
        });
    }
});

async function loadBarangaysForEvacuation() {
    const citySelect = document.getElementById('city');
    const barangaySelect = document.getElementById('barangay');

    // Clear barangay options
    barangaySelect.innerHTML = '<option value="">All Barangays</option>';
    barangaySelect.disabled = true;

    if (!citySelect.value) {
        return;
    }

    try {
        const response = await fetch(`{{ route('system-admin.get-barangays-by-city') }}?city=${encodeURIComponent(citySelect.value)}`, {
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Accept': 'application/json'
            }
        });

        const result = await response.json();

        if (result.barangays && Array.isArray(result.barangays)) {
            result.barangays.forEach(barangay => {
                const option = document.createElement('option');
                option.value = barangay;
                option.textContent = barangay;
                barangaySelect.appendChild(option);
            });
            barangaySelect.disabled = false;
        }
    } catch (error) {
        console.error('Failed to load barangays:', error);
    }
}
@endif
</script>
@endpush



























