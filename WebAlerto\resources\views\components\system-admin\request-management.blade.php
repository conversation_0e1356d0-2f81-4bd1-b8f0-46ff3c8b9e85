@extends('layout.app')

@section('title', 'Request Management - Technical Administrator')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-gray-50 to-slate-100 py-8">
    <div class="max-w-[1920px] mx-auto px-6 sm:px-8 lg:px-12">
        
        <!-- Header Section -->
        <div class="bg-white/90 backdrop-blur-sm rounded-xl shadow-xl border border-slate-200 p-6 mb-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div class="p-3 bg-gradient-to-br from-purple-600 to-indigo-700 rounded-lg shadow-lg">
                        <i class="fas fa-tasks text-white text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Request Management</h1>
                        <p class="text-sm text-gray-600 mt-1">Review and process user management requests from CDRRMC Chairmen</p>
                    </div>
                </div>
                
                <button onclick="refreshRequests()" class="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-sync-alt"></i>
                    <span>Refresh</span>
                </button>
            </div>
            
            @if(isset($error))
                <div class="mt-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    <i class="fas fa-exclamation-triangle mr-2"></i>{{ $error }}
                </div>
            @endif
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-orange-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Pending Requests</p>
                        <p class="text-3xl font-bold text-orange-600">{{ number_format($stats['pending']) }}</p>
                        <p class="text-xs text-gray-500 mt-1">Awaiting review</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-lg">
                        <i class="fas fa-clock text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-green-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Approved</p>
                        <p class="text-3xl font-bold text-green-600">{{ number_format($stats['approved']) }}</p>
                        <p class="text-xs text-gray-500 mt-1">Successfully processed</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                        <i class="fas fa-check-circle text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-red-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Rejected</p>
                        <p class="text-3xl font-bold text-red-600">{{ number_format($stats['rejected']) }}</p>
                        <p class="text-xs text-gray-500 mt-1">Declined requests</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-red-500 to-red-600 rounded-xl shadow-lg">
                        <i class="fas fa-times-circle text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-blue-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Requests</p>
                        <p class="text-3xl font-bold text-blue-600">{{ number_format($stats['total']) }}</p>
                        <p class="text-xs text-gray-500 mt-1">All time</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
                        <i class="fas fa-list text-white text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Type Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-purple-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Registration Requests</p>
                        <p class="text-2xl font-bold text-purple-600">{{ number_format($stats['registration']) }}</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
                        <i class="fas fa-user-plus text-white text-lg"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-yellow-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Deactivation Requests</p>
                        <p class="text-2xl font-bold text-yellow-600">{{ number_format($stats['deactivate']) }}</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl shadow-lg">
                        <i class="fas fa-user-slash text-white text-lg"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-red-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Deletion Requests</p>
                        <p class="text-2xl font-bold text-red-600">{{ number_format($stats['delete']) }}</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-red-500 to-red-600 rounded-xl shadow-lg">
                        <i class="fas fa-user-times text-white text-lg"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200 p-6 mb-6">
            <form method="GET" action="{{ route('system-admin.request-management') }}" class="flex flex-wrap items-center gap-4">
                <div class="min-w-[150px]">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="all" {{ $status === 'all' ? 'selected' : '' }}>All Status</option>
                        <option value="pending" {{ $status === 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="approved" {{ $status === 'approved' ? 'selected' : '' }}>Approved</option>
                        <option value="rejected" {{ $status === 'rejected' ? 'selected' : '' }}>Rejected</option>
                    </select>
                </div>
                
                <div class="min-w-[150px]">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Action Type</label>
                    <select name="action_type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="all" {{ $actionType === 'all' ? 'selected' : '' }}>All Types</option>
                        <option value="registration" {{ $actionType === 'registration' ? 'selected' : '' }}>Registration</option>
                        <option value="deactivate" {{ $actionType === 'deactivate' ? 'selected' : '' }}>Deactivation</option>
                        <option value="delete" {{ $actionType === 'delete' ? 'selected' : '' }}>Deletion</option>
                    </select>
                </div>
                
                <div class="min-w-[150px]">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Barangay</label>
                    <select name="barangay" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">All Barangays</option>
                        @foreach($barangays as $brgy)
                            <option value="{{ $brgy }}" {{ $barangay === $brgy ? 'selected' : '' }}>{{ $brgy }}</option>
                        @endforeach
                    </select>
                </div>
                
                <div class="flex items-end gap-2">
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-filter mr-2"></i>Filter
                    </button>
                    <a href="{{ route('system-admin.request-management') }}" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fas fa-times mr-2"></i>Clear
                    </a>
                </div>
            </form>
        </div>

        <!-- Requests Table -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200 p-6">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h3 class="text-lg font-bold text-gray-900">User Management Requests</h3>
                    <p class="text-sm text-gray-600">
                        Showing {{ $requests->count() }} of {{ number_format($requests->total()) }} requests
                    </p>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                @if($requests->count() > 0)
                <table class="w-full">
                    <thead>
                        <tr class="border-b border-gray-200">
                            <th class="text-left py-3 px-4 font-medium text-gray-700">Request</th>
                            <th class="text-left py-3 px-4 font-medium text-gray-700">Requester</th>
                            <th class="text-left py-3 px-4 font-medium text-gray-700">Target/Details</th>
                            <th class="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                            <th class="text-left py-3 px-4 font-medium text-gray-700">Date</th>
                            <th class="text-left py-3 px-4 font-medium text-gray-700">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($requests as $request)
                        <tr class="border-b border-gray-100 hover:bg-gray-50">
                            <td class="py-3 px-4">
                                <div class="flex items-center gap-3">
                                    <div class="p-2 rounded-lg
                                        @if($request->action_type === 'registration') bg-purple-100 text-purple-600
                                        @elseif($request->action_type === 'deactivate') bg-yellow-100 text-yellow-600
                                        @else bg-red-100 text-red-600 @endif">
                                        <i class="fas fa-{{ $request->action_type === 'registration' ? 'user-plus' : ($request->action_type === 'deactivate' ? 'user-slash' : 'user-times') }}"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900">{{ ucfirst($request->action_type) }}</p>
                                        <p class="text-sm text-gray-600">{{ Str::limit($request->reason, 50) }}</p>
                                    </div>
                                </div>
                            </td>
                            <td class="py-3 px-4">
                                @if($request->requester)
                                <div>
                                    <p class="font-medium text-gray-900">{{ $request->requester->first_name }} {{ $request->requester->last_name }}</p>
                                    <p class="text-sm text-gray-600">{{ $request->requester->barangay }}</p>
                                </div>
                                @else
                                <span class="text-gray-400">N/A</span>
                                @endif
                            </td>
                            <td class="py-3 px-4">
                                @if($request->action_type === 'registration')
                                <div>
                                    <p class="font-medium text-gray-900">{{ $request->requested_first_name }} {{ $request->requested_last_name }}</p>
                                    <p class="text-sm text-gray-600">{{ $request->requested_email }}</p>
                                </div>
                                @elseif($request->targetUser)
                                <div>
                                    <p class="font-medium text-gray-900">{{ $request->targetUser->first_name }} {{ $request->targetUser->last_name }}</p>
                                    <p class="text-sm text-gray-600">{{ $request->targetUser->email }}</p>
                                </div>
                                @else
                                <span class="text-gray-400">N/A</span>
                                @endif
                            </td>
                            <td class="py-3 px-4">
                                <span class="px-2 py-1 rounded-full text-xs font-medium
                                    @if($request->status === 'pending') bg-orange-100 text-orange-800
                                    @elseif($request->status === 'approved') bg-green-100 text-green-800
                                    @else bg-red-100 text-red-800 @endif">
                                    {{ ucfirst($request->status) }}
                                </span>
                            </td>
                            <td class="py-3 px-4 text-sm text-gray-600">{{ $request->created_at->format('M d, Y') }}</td>
                            <td class="py-3 px-4">
                                <div class="flex items-center gap-2">
                                    <button onclick="viewRequest({{ $request->id }})" class="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    @if($request->status === 'pending')
                                    <button onclick="reviewRequest({{ $request->id }}, 'approve')" class="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors" title="Approve Request">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button onclick="reviewRequest({{ $request->id }}, 'reject')" class="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors" title="Reject Request">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
                @else
                <div class="text-center py-12">
                    <i class="fas fa-tasks text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-500">No requests found</p>
                    @if($status !== 'all' || $actionType !== 'all' || $barangay)
                    <p class="text-sm text-gray-400 mt-2">Try adjusting your filters</p>
                    @endif
                </div>
                @endif
            </div>
            
            <!-- Pagination -->
            @if($requests->hasPages())
            <div class="mt-6 flex justify-center">
                {{ $requests->appends(request()->query())->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Request Details Modal -->
<div id="requestDetailsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-white rounded-xl shadow-2xl max-w-3xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div class="p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-gray-900">Request Details</h3>
                <button onclick="closeRequestDetailsModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <div id="requestDetailsContent">
                <!-- Request details will be populated here -->
            </div>
        </div>
    </div>
</div>

<!-- Review Request Modal -->
<div id="reviewRequestModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-white rounded-xl shadow-2xl max-w-md w-full mx-4">
        <div class="p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-gray-900" id="reviewModalTitle">Review Request</h3>
                <button onclick="closeReviewRequestModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <form id="reviewRequestForm" onsubmit="submitReview(event)">
                <input type="hidden" id="reviewRequestId" name="request_id">
                <input type="hidden" id="reviewAction" name="action">

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Review Notes</label>
                    <textarea name="review_notes" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Add notes about your decision (optional)"></textarea>
                </div>

                <div class="flex gap-3">
                    <button type="button" onclick="closeReviewRequestModal()" class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        Cancel
                    </button>
                    <button type="submit" id="reviewSubmitBtn" class="flex-1 px-4 py-2 text-white rounded-lg transition-colors">
                        Confirm
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function refreshRequests() {
    window.location.reload();
}

async function viewRequest(requestId) {
    try {
        const response = await fetch(`{{ url('system-admin/requests') }}/${requestId}/view`, {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Accept': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            showRequestDetailsModal(result.request);
        } else {
            alert('Error: ' + result.message);
        }
    } catch (error) {
        alert('An error occurred while loading request details.');
        console.error(error);
    }
}

async function reviewRequest(requestId, action) {
    const modal = document.getElementById('reviewRequestModal');
    const title = document.getElementById('reviewModalTitle');
    const submitBtn = document.getElementById('reviewSubmitBtn');
    const requestIdInput = document.getElementById('reviewRequestId');
    const actionInput = document.getElementById('reviewAction');

    // Set modal content based on action
    if (action === 'approve') {
        title.textContent = 'Approve Request';
        submitBtn.textContent = 'Approve';
        submitBtn.className = 'flex-1 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors';
    } else {
        title.textContent = 'Reject Request';
        submitBtn.textContent = 'Reject';
        submitBtn.className = 'flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors';
    }

    requestIdInput.value = requestId;
    actionInput.value = action;

    modal.classList.remove('hidden');
}

async function submitReview(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const requestId = formData.get('request_id');
    const action = formData.get('action');
    const reviewNotes = formData.get('review_notes');

    try {
        const response = await fetch(`{{ url('system-admin/requests') }}/${requestId}/review`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                action: action,
                review_notes: reviewNotes
            })
        });

        const result = await response.json();

        if (result.success) {
            alert(result.message);
            closeReviewRequestModal();
            location.reload();
        } else {
            alert('Error: ' + result.message);
        }
    } catch (error) {
        alert('An error occurred while processing the request.');
        console.error(error);
    }
}

function showRequestDetailsModal(request) {
    const modal = document.getElementById('requestDetailsModal');
    const content = document.getElementById('requestDetailsContent');

    content.innerHTML = `
        <div class="space-y-6">
            <!-- Request Information -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-lg font-semibold text-gray-900 mb-3">Request Information</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Action Type</label>
                        <span class="px-2 py-1 rounded-full text-xs font-medium ${getActionTypeColorClass(request.action_type)}">${request.action_type_display}</span>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Status</label>
                        <span class="px-2 py-1 rounded-full text-xs font-medium ${getStatusColorClass(request.status)}">${request.status_display}</span>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Submitted</label>
                        <p class="text-gray-900">${request.created_at}</p>
                    </div>
                    ${request.reviewed_at ? `
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Reviewed</label>
                        <p class="text-gray-900">${request.reviewed_at}</p>
                    </div>
                    ` : ''}
                </div>
            </div>

            <!-- Requester Information -->
            ${request.requester ? `
            <div class="bg-blue-50 rounded-lg p-4">
                <h4 class="text-lg font-semibold text-gray-900 mb-3">Requester Information</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Name</label>
                        <p class="text-gray-900">${request.requester.name}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Email</label>
                        <p class="text-gray-900">${request.requester.email}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Position</label>
                        <p class="text-gray-900">${request.requester.position}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Barangay</label>
                        <p class="text-gray-900">${request.requester.barangay}</p>
                    </div>
                </div>
            </div>
            ` : ''}

            <!-- Target User or Registration Details -->
            ${request.action_type === 'registration' ? `
            <div class="bg-purple-50 rounded-lg p-4">
                <h4 class="text-lg font-semibold text-gray-900 mb-3">Registration Details</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-600">First Name</label>
                        <p class="text-gray-900">${request.requested_first_name}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Last Name</label>
                        <p class="text-gray-900">${request.requested_last_name}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Email</label>
                        <p class="text-gray-900">${request.requested_email}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Position</label>
                        <p class="text-gray-900">${request.requested_position}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Barangay</label>
                        <p class="text-gray-900">${request.requested_barangay}</p>
                    </div>
                </div>
            </div>
            ` : request.target_user ? `
            <div class="bg-yellow-50 rounded-lg p-4">
                <h4 class="text-lg font-semibold text-gray-900 mb-3">Target User</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Name</label>
                        <p class="text-gray-900">${request.target_user.name}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Email</label>
                        <p class="text-gray-900">${request.target_user.email}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Position</label>
                        <p class="text-gray-900">${request.target_user.position}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Role</label>
                        <p class="text-gray-900">${request.target_user.role}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Status</label>
                        <span class="px-2 py-1 rounded-full text-xs font-medium ${request.target_user.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">${request.target_user.status}</span>
                    </div>
                </div>
            </div>
            ` : ''}

            <!-- Reason -->
            <div class="bg-orange-50 rounded-lg p-4">
                <h4 class="text-lg font-semibold text-gray-900 mb-3">Reason</h4>
                <p class="text-gray-700">${request.reason}</p>
            </div>

            <!-- Review Information -->
            ${request.reviewer || request.review_notes ? `
            <div class="bg-green-50 rounded-lg p-4">
                <h4 class="text-lg font-semibold text-gray-900 mb-3">Review Information</h4>
                ${request.reviewer ? `
                <div class="mb-3">
                    <label class="block text-sm font-medium text-gray-600">Reviewed By</label>
                    <p class="text-gray-900">${request.reviewer.name} (${request.reviewer.email})</p>
                </div>
                ` : ''}
                ${request.review_notes ? `
                <div>
                    <label class="block text-sm font-medium text-gray-600">Review Notes</label>
                    <p class="text-gray-700">${request.review_notes}</p>
                </div>
                ` : ''}
            </div>
            ` : ''}
        </div>
    `;

    modal.classList.remove('hidden');
}

function closeRequestDetailsModal() {
    document.getElementById('requestDetailsModal').classList.add('hidden');
}

function closeReviewRequestModal() {
    document.getElementById('reviewRequestModal').classList.add('hidden');
    document.getElementById('reviewRequestForm').reset();
}

function getActionTypeColorClass(actionType) {
    switch(actionType) {
        case 'registration':
            return 'bg-purple-100 text-purple-800';
        case 'deactivate':
            return 'bg-yellow-100 text-yellow-800';
        case 'delete':
            return 'bg-red-100 text-red-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
}

function getStatusColorClass(status) {
    switch(status) {
        case 'pending':
            return 'bg-orange-100 text-orange-800';
        case 'approved':
            return 'bg-green-100 text-green-800';
        case 'rejected':
            return 'bg-red-100 text-red-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
}
</script>
@endsection
