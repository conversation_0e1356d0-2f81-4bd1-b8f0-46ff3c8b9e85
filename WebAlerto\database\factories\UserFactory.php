<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Barangay;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class UserFactory extends Factory
{
    protected $model = User::class;

    public function definition()
    {
        // Get barangays from PSGC-connected database
        try {
            $barangays = Barangay::where('status', true)->pluck('name')->toArray();
        } catch (\Exception $e) {
            // Use PSGC service as fallback if database query fails
            try {
                $psgcService = new \App\Services\PSGCService();
                $psgcBarangays = $psgcService->getCebuCityBarangays();
                $barangays = collect($psgcBarangays)->pluck('name')->toArray();
            } catch (\Exception $psgcError) {
                // Final fallback to ensure factory works
                $barangays = ['Lahug', 'Mabolo', 'Banilad', 'Guadalupe', 'Talamban'];
            }
        }

        // If still empty, use a minimal fallback
        if (empty($barangays)) {
            $barangays = ['Lahug', 'Mabolo', 'Banilad', 'Guadalupe', 'Talamban'];
        }

        $positions = [
            'Chairman',
            'Disaster Risk Reduction Officer',
            'Emergency Response Coordinator',
            'Community Resilience Officer',
            'Disaster Management Specialist',
        ];

        $firstName = $this->faker->firstName();
        $lastName = $this->faker->lastName();

        return [
            'title' => null,
            'first_name' => $firstName,
            'middle_name' => null,
            'last_name' => $lastName,
            'suffix' => null,
            'email' => $this->faker->unique()->safeEmail(),
            'password' => bcrypt('password'),
            'position' => $this->faker->randomElement($positions),
            'barangay' => $this->faker->randomElement($barangays),
            'role' => 'officer',
            'status' => 'Active',
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    public function chairman()
    {
        return $this->state(function (array $attributes) {
            return [
                'position' => 'Chairman',
            ];
        });
    }
}