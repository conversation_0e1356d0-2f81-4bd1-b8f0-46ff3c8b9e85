.status-text {
  margin-left: 8px;
}

.home-content {
  padding: 20px 16px;
  height: calc(100vh - 120px);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;

  // Add subtle pattern overlay
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
                      radial-gradient(circle at 75% 75%, rgba(34, 197, 94, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
  }
}

ion-header {
  background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);

  ion-toolbar {
    --background: transparent;
    --color: white;
  }
}

ion-title {
  text-align: center;
  font-family: 'Pop<PERSON>s', Aria<PERSON>, sans-serif;
  font-size: 1.8rem;
  font-weight: 800;
  letter-spacing: 1.5px;
  color: white !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

  &::after {
    content: '⚠️';
    margin-left: 8px;
    font-size: 1.2rem;
  }
}

.disaster-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin: 0;
  height: 100%;
  position: relative;
  z-index: 1;
}

// Welcome Section
.welcome-section {
  text-align: center;
  margin-bottom: 16px;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

.welcome-title {
  font-size: 1.4rem;
  font-weight: 800;
  color: #1e293b;
  margin: 0 0 8px 0;
  letter-spacing: 0.5px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-subtitle {
  font-size: 0.9rem;
  color: #64748b;
  margin: 0;
  font-weight: 500;
  opacity: 0.8;
}

.disaster {
  margin: 0;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
  height: 130px;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;

  &:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(-2px) scale(1.01);
    transition: all 0.1s ease;
  }

  ion-card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 16px 12px;
    height: 100%;
    position: relative;
    z-index: 2;
  }

  .card-icon-wrapper {
    position: relative;
    margin-bottom: 8px;

    img {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
      border: 2px solid rgba(255, 255, 255, 0.3);
    }
  }

  ion-text {
    font-size: 0.95rem;
    font-weight: 700;
    letter-spacing: 0.3px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-top: 4px;
    position: relative;
    z-index: 2;
  }

  .card-indicator {
    position: absolute;
    bottom: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
  }

  // Add subtle gradient overlay for depth
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%);
    z-index: 1;
    pointer-events: none;
  }

  &:hover {
    .card-icon-wrapper img {
      transform: scale(1.1) rotate(5deg);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    }

    .card-indicator {
      transform: scale(1.3);
      background: rgba(255, 255, 255, 1);
    }
  }
}

.earthquake {
  background: linear-gradient(135deg, #FF8C00 0%, #FFA500 50%, #FFB84D 100%);
  border: 2px solid rgba(255, 140, 0, 0.3);

  ion-text {
    color: white !important;
  }

  &:hover {
    box-shadow: 0 8px 25px rgba(255, 140, 0, 0.3);
  }
}

.typhoon {
  background: linear-gradient(135deg, #22C55E 0%, #16A34A 50%, #15803D 100%);
  border: 2px solid rgba(34, 197, 94, 0.3);

  ion-text {
    color: white !important;
  }

  &:hover {
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
  }
}

.flood {
  background: linear-gradient(135deg, #3B82F6 0%, #2563EB 50%, #1D4ED8 100%);
  border: 2px solid rgba(59, 130, 246, 0.3);

  ion-text {
    color: white !important;
  }

  &:hover {
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  }
}

.fire {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 50%, #B91C1C 100%);
  border: 2px solid rgba(239, 68, 68, 0.3);

  ion-text {
    color: white !important;
  }

  &:hover {
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
  }
}

.landslide {
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 50%, #B45309 100%);
  border: 2px solid rgba(245, 158, 11, 0.3);

  ion-text {
    color: white !important;
  }

  &:hover {
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
  }
}



.view-map {
  margin-top: 24px;
  --background: #00bfff;

  &:hover {
    --background: #0090cc;
  }

  &[disabled] {
    --background: #999;
  }
}

.disaster-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: repeat(3, 1fr);
  gap: 16px;
  padding: 0 20px;
  max-width: 420px;
  margin: 0 auto;
  width: 100%;
  position: relative;

  // Add subtle background for the grid
  &::before {
    content: '';
    position: absolute;
    top: -12px;
    left: 8px;
    right: 8px;
    bottom: -12px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    z-index: -1;
  }
}

/* Style for the landslide card to span both columns in the last row */
.disaster.landslide {
  grid-column: 1 / 3;
  max-width: 200px;
  margin: 0 auto;
}

/* See whole map button container */
.map-button-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

/* See whole map button styling */
.see-map-btn {
  background: linear-gradient(135deg, #6366F1 0%, #4F46E5 50%, #4338CA 100%);
  color: white;
  border-radius: 30px;
  font-weight: 700;
  font-size: 1.1rem;
  height: 56px;
  max-width: 320px;
  text-transform: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;

  // Add shine effect
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 10px 30px rgba(99, 102, 241, 0.4);
    background: linear-gradient(135deg, #7C3AED 0%, #6366F1 50%, #4F46E5 100%);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(-1px) scale(1.01);
    box-shadow: 0 6px 20px rgba(99, 102, 241, 0.3);
    transition: all 0.1s ease;
  }

  &[disabled] {
    background: linear-gradient(135deg, #9CA3AF 0%, #6B7280 100%);
    color: #D1D5DB;
    cursor: not-allowed;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    &:hover {
      transform: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &::before {
      display: none;
    }
  }

  // Add icon before text
  &::after {
    content: '🗺️';
    margin-left: 8px;
    font-size: 1.2rem;
  }
}

.notifications-section {
  margin-top: 20px;
  border-top: 1px solid var(--ion-color-light);
  padding-top: 10px;
}

ion-item-divider {
  --background: transparent;
  --color: var(--ion-color-primary);
  font-weight: bold;
  font-size: 1.1rem;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

// Notification button styles
.notification-button {
  position: relative;
  --color: white;
  --background: rgba(255, 255, 255, 0.1);
  --border-radius: 12px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);

  &:hover {
    --background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
  }

  ion-icon {
    font-size: 1.4rem;
    color: white;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  }
}

.notification-badge {
  position: absolute;
  top: 6px;
  right: 6px;
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
  color: white;
  font-size: 10px;
  font-weight: 700;
  min-width: 18px;
  height: 18px;
  border-radius: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
  border: 2px solid white;
  animation: pulse-notification 2s infinite;
}

@keyframes pulse-notification {
  0% {
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.6);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
  }
}