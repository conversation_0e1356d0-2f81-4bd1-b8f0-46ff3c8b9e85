<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>Disaster Filtering Test</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="loadAndTestData()" [disabled]="isLoading">
        <ion-icon name="refresh"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Disaster Test</ion-title>
    </ion-toolbar>
  </ion-header>

  <div class="test-container">
    
    <!-- Loading State -->
    <div *ngIf="isLoading" class="loading-container">
      <ion-spinner></ion-spinner>
      <p>Loading evacuation centers...</p>
    </div>

    <!-- Test Results Summary -->
    <ion-card *ngIf="!isLoading">
      <ion-card-header>
        <ion-card-title>Test Results Summary</ion-card-title>
        <ion-card-subtitle>Disaster-specific filtering analysis</ion-card-subtitle>
      </ion-card-header>
      <ion-card-content>
        
        <ion-item>
          <ion-icon name="list" slot="start" color="medium"></ion-icon>
          <ion-label>
            <h3>Total Centers</h3>
            <p>{{ testResults.totalCenters }} evacuation centers loaded</p>
          </ion-label>
          <ion-badge slot="end" color="medium">{{ testResults.totalCenters }}</ion-badge>
        </ion-item>

        <ion-item>
          <ion-icon name="pulse" slot="start" color="warning"></ion-icon>
          <ion-label>
            <h3>Earthquake Centers</h3>
            <p>Centers designated for earthquakes</p>
          </ion-label>
          <ion-badge slot="end" color="warning">{{ testResults.earthquakeCount }}</ion-badge>
        </ion-item>

        <ion-item>
          <ion-icon name="cloudy" slot="start" color="success"></ion-icon>
          <ion-label>
            <h3>Typhoon Centers</h3>
            <p>Centers designated for typhoons</p>
          </ion-label>
          <ion-badge slot="end" color="success">{{ testResults.typhoonCount }}</ion-badge>
        </ion-item>

        <ion-item>
          <ion-icon name="water" slot="start" color="primary"></ion-icon>
          <ion-label>
            <h3>Flood Centers</h3>
            <p>Centers designated for floods</p>
          </ion-label>
          <ion-badge slot="end" color="primary">{{ testResults.floodCount }}</ion-badge>
        </ion-item>

        <ion-item *ngIf="testResults.unknownCount > 0">
          <ion-icon name="help-circle" slot="start" color="danger"></ion-icon>
          <ion-label>
            <h3>Unknown/Invalid Types</h3>
            <p>Centers with invalid disaster types</p>
          </ion-label>
          <ion-badge slot="end" color="danger">{{ testResults.unknownCount }}</ion-badge>
        </ion-item>

      </ion-card-content>
    </ion-card>

    <!-- Navigation Test Buttons -->
    <ion-card *ngIf="!isLoading">
      <ion-card-header>
        <ion-card-title>Test Navigation</ion-card-title>
        <ion-card-subtitle>Simulate home page navigation</ion-card-subtitle>
      </ion-card-header>
      <ion-card-content>
        
        <ion-button 
          expand="block" 
          color="warning" 
          (click)="testNavigation('Earthquake')"
          [disabled]="testResults.earthquakeCount === 0">
          <ion-icon name="pulse" slot="start"></ion-icon>
          Test Earthquake Map ({{ testResults.earthquakeCount }} centers)
        </ion-button>

        <ion-button 
          expand="block" 
          color="success" 
          (click)="testNavigation('Typhoon')"
          [disabled]="testResults.typhoonCount === 0">
          <ion-icon name="cloudy" slot="start"></ion-icon>
          Test Typhoon Map ({{ testResults.typhoonCount }} centers)
        </ion-button>

        <ion-button 
          expand="block" 
          color="primary" 
          (click)="testNavigation('Flood')"
          [disabled]="testResults.floodCount === 0">
          <ion-icon name="water" slot="start"></ion-icon>
          Test Flood Map ({{ testResults.floodCount }} centers)
        </ion-button>

      </ion-card-content>
    </ion-card>

    <!-- Detailed Center Lists -->
    <ion-card *ngIf="!isLoading && testResults.totalCenters > 0">
      <ion-card-header>
        <ion-card-title>Center Details</ion-card-title>
        <ion-card-subtitle>Breakdown by disaster type</ion-card-subtitle>
      </ion-card-header>
      <ion-card-content>
        
        <!-- Earthquake Centers -->
        <div *ngIf="earthquakeCenters.length > 0">
          <h4>🔶 Earthquake Centers ({{ earthquakeCenters.length }})</h4>
          <ion-item *ngFor="let center of earthquakeCenters">
            <ion-icon name="pulse" slot="start" color="warning"></ion-icon>
            <ion-label>
              <h3>{{ center.name }}</h3>
              <p>Type: {{ center.disaster_type }} | Status: {{ center.status }}</p>
            </ion-label>
          </ion-item>
        </div>

        <!-- Typhoon Centers -->
        <div *ngIf="typhoonCenters.length > 0">
          <h4>🟢 Typhoon Centers ({{ typhoonCenters.length }})</h4>
          <ion-item *ngFor="let center of typhoonCenters">
            <ion-icon name="cloudy" slot="start" color="success"></ion-icon>
            <ion-label>
              <h3>{{ center.name }}</h3>
              <p>Type: {{ center.disaster_type }} | Status: {{ center.status }}</p>
            </ion-label>
          </ion-item>
        </div>

        <!-- Flood Centers -->
        <div *ngIf="floodCenters.length > 0">
          <h4>🔵 Flood Centers ({{ floodCenters.length }})</h4>
          <ion-item *ngFor="let center of floodCenters">
            <ion-icon name="water" slot="start" color="primary"></ion-icon>
            <ion-label>
              <h3>{{ center.name }}</h3>
              <p>Type: {{ center.disaster_type }} | Status: {{ center.status }}</p>
            </ion-label>
          </ion-item>
        </div>

        <!-- No Centers Message -->
        <div *ngIf="testResults.totalCenters === 0">
          <ion-item>
            <ion-icon name="warning" slot="start" color="warning"></ion-icon>
            <ion-label>
              <h3>No Centers Found</h3>
              <p>No evacuation centers are available in the database</p>
            </ion-label>
          </ion-item>
        </div>

      </ion-card-content>
    </ion-card>

  </div>
</ion-content>

<style>
.test-container {
  padding: 16px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

ion-card {
  margin-bottom: 16px;
}

ion-button {
  margin-bottom: 8px;
}

h4 {
  margin: 16px 0 8px 0;
  font-weight: bold;
}

ion-item {
  --padding-start: 0;
  --inner-padding-end: 0;
}
</style>
