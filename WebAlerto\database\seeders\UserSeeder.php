<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::table('users')->truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // Create System Administrator (only account for initial setup and system management)
        User::create([
            'email' => env('SYSTEM_ADMIN_EMAIL', '<EMAIL>'),
            'password' => Hash::make(env('SYSTEM_ADMIN_PASSWORD', 'SysAdmin@2025')),

            'title' => null,

            'first_name' => 'Web',
            'middle_name' => null,
            'last_name' => 'Alerto',
            'suffix' => null,
            'position' => 'Technical Administrator',
            'city' => null, // System admin has no specific city - province-wide access
            'barangay' => null, // System admin has no specific barangay - all barangays access
            'role' => 'system_admin',
            'status' => 'Active'
        ]);

        // Create CDRRMC user for Dalaguete for testing purposes
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'CDRRMC',
                'middle_name' => '',
                'last_name' => 'Dalaguete',
                'password' => Hash::make('password123'),
                'role' => 'super_admin',
                'position' => 'CDRRMC Chairperson',
                'city' => '*********', // Dalaguete PSGC code
                'barangay' => null, // CDRRMC users don't have specific barangay
                'status' => 'Active',
                'email_verified_at' => now()
            ]
        );

        // Note: All other accounts (CDRRMC and BDRRMC) must be created through the System Administrator
        // This ensures proper RBAC implementation and invitation flow
        //
        // Updated RBAC Hierarchy:
        // - system_admin: Technical Administrator (province-wide access, user management, system config)
        // - super_admin: CDRRMC (city/municipality-wide monitoring and oversight)
        // - admin: BDRRMC (barangay-specific disaster response and management)
        //
        // Access Levels:
        // - System Admin: All cities/municipalities in Cebu Province
        // - CDRRMC: All barangays within their specific city/municipality
        // - BDRRMC: Only their specific barangay
    }
}
