<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Barangay;

class CheckBarangayUsers extends Command
{
    protected $signature = 'fix:barangay-users';
    protected $description = 'Fix user barangay fields to match barangay names in the barangays table';

    public function handle()
    {
        $barangayNames = Barangay::pluck('name')->map(function($name) {
            return strtolower(trim($name));
        })->toArray();

        $users = User::all();
        $fixed = 0;
        $unmatched = [];

        foreach ($users as $user) {
            $userBarangay = strtolower(trim($user->barangay));
            if (in_array($userBarangay, $barangayNames)) {
                // Already matches
                continue;
            }
            // Try to find a close match (case-insensitive, ignore spaces)
            $match = null;
            foreach ($barangayNames as $name) {
                if (levenshtein($userBarangay, $name) <= 2) {
                    $match = $name;
                    break;
                }
            }
            if ($match) {
                $original = $user->barangay;
                $user->barangay = ucwords($match);
                $user->save();
                $this->info("Fixed user {$user->email}: '{$original}' => '{$user->barangay}'");
                $fixed++;
            } else {
                $unmatched[] = $user->email . ' (' . $user->barangay . ')';
            }
        }
        $this->info("Total users fixed: $fixed");
        if (!empty($unmatched)) {
            $this->warn("Users with unmatched barangay:");
            foreach ($unmatched as $u) {
                $this->warn($u);
            }
        }
    }
} 