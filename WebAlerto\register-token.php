<?php

/**
 * This script registers a test FCM token in the database
 * Run it with: php register-token.php <fcm_token>
 */

// Load the Laravel application
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// Get the FCM token from the command line
$token = $argv[1] ?? null;

if (!$token) {
    echo "Usage: php register-token.php <fcm_token>\n";
    exit(1);
}

echo "Registering token: " . substr($token, 0, 10) . "...\n";

// Check if the token already exists
$existingToken = App\Models\DeviceToken::where('token', $token)->first();

if ($existingToken) {
    echo "Token already exists in the database (ID: {$existingToken->id}).\n";
    
    // Make sure it's active
    if (!$existingToken->is_active) {
        $existingToken->is_active = true;
        $existingToken->save();
        echo "Token was inactive. Activated successfully.\n";
    } else {
        echo "Token is already active.\n";
    }
    
    exit(0);
}

// Create a new device token
try {
    $deviceToken = new App\Models\DeviceToken([
        'token' => $token,
        'device_type' => 'android',
        'is_active' => true,
        'project_id' => 'last-5acaf'
    ]);
    
    // Associate with a user if available
    $user = App\Models\User::first();
    if ($user) {
        $deviceToken->user_id = $user->id;
        echo "Associating token with user ID: {$user->id}\n";
    }
    
    $deviceToken->save();
    
    echo "Token registered successfully with ID: {$deviceToken->id}\n";
} catch (Exception $e) {
    echo "Error registering token: " . $e->getMessage() . "\n";
    exit(1);
}