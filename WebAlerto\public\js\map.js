// Map JavaScript functionality

// Disaster type color mapping
const disasterTypeColors = {
    'Typhoon': '#22c55e',         // Green
    'Flood': '#3b82f6',             // Blue
    'Fire': '#ef4444',               // Red
    'Earthquake': '#f59e42',   // Orange
    'Landslide': '#a16207',     // Amber
    'Others': '#9333ea',           // Purple
    'Multi-disaster': '#6b7280'   // Gray
};

// Disaster type to pin mapping
const disasterTypePins = {
    'Typhoon': '/image/pins/forTyphoon.png',
    'Flood': '/image/pins/forFlood.png',
    'Fire': '/image/pins/forFire.png',
    'Earthquake': '/image/pins/forEarthquake.png',
    'Landslide': '/image/pins/forLandslide.png',
    'Others': '/image/pins/forOthers.png',
    'Multi-disaster': '/image/pins/forMultiple.png'
};

// Global variables
let map;
let centers = [];
let markers = [];
let userLocation = null;
let userMarker = null;
let currentTransportMode = 'driving'; // Default transport mode
let currentRoute = null;
let currentCenterId = null;

// Route colors for different transport modes
const routeColors = {
    'walking': 'red',
    'driving': 'blue',
    'cycling': 'green'
};

// Initialize map function (for dashboard compatibility)
function initializeMap(centersData, isAdmin) {
    centers = centersData || [];
    window.isAdmin = isAdmin;

    map = L.map('map', {
        center: [10.3157, 123.8854],
        zoom: 13,
        zoomControl: true,
        scrollWheelZoom: true,
        dragging: true,
        touchZoom: true,
        doubleClickZoom: true,
        boxZoom: true,
        keyboard: true,
        attributionControl: true,
    });

    // Inject custom CSS for routing instructions and directions panel
    const style = document.createElement('style');
    style.textContent = `
        .leaflet-routing-container {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
            border: 1px solid #e2e8f0 !important;
            border-radius: 20px !important;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08), 0 4px 10px rgba(0, 0, 0, 0.03) !important;
            padding: 20px 24px !important;
            font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif !important;
            font-size: 14px !important;
            line-height: 1.5 !important;
            max-height: 520px !important;
            overflow-y: auto !important;
            backdrop-filter: blur(10px) !important;
            border-left: 4px solid #3b82f6 !important;
            position: relative !important;
            margin-bottom: 60px !important;
            width: 320px !important;
            min-width: 280px !important;
        }

        .leaflet-routing-container::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            height: 3px !important;
            background: linear-gradient(90deg, #3b82f6, #06b6d4, #10b981) !important;
            border-radius: 20px 20px 0 0 !important;
        }

        .leaflet-routing-container h2 {
            font-size: 18px !important;
            font-weight: 700 !important;
            margin: 0 0 16px 0 !important;
            color: #1e293b !important;
            display: flex !important;
            align-items: center !important;
            gap: 8px !important;
        }

        .leaflet-routing-container h2::before {
            content: '🗺️' !important;
            font-size: 16px !important;
        }

        .leaflet-routing-container table {
            width: 100% !important;
            border-collapse: separate !important;
            border-spacing: 0 4px !important;
            background: transparent !important;
            margin: 0 !important;
        }

        .leaflet-routing-container tr {
            background: rgba(248, 250, 252, 0.6) !important;
            border-radius: 12px !important;
            transition: all 0.2s ease !important;
        }

        .leaflet-routing-container tr:hover {
            background: rgba(239, 246, 255, 0.8) !important;
            transform: translateX(2px) !important;
        }

        .leaflet-routing-container td {
            background: transparent !important;
            padding: 12px 8px !important;
            vertical-align: top !important;
            border: none !important;
        }

        .leaflet-routing-container td:first-child {
            width: 30px !important;
            text-align: center !important;
            color: #3b82f6 !important;
            font-weight: 600 !important;
            font-size: 13px !important;
        }

        .leaflet-routing-container td:last-child {
            color: #475569 !important;
            font-size: 13px !important;
            line-height: 1.4 !important;
        }

        .leaflet-routing-alt {
            margin-top: 20px !important;
            padding-top: 16px !important;
            border-top: 1px solid #e2e8f0 !important;
        }

        .leaflet-routing-container .leaflet-routing-collapse-btn {
            top: 12px !important;
            right: 12px !important;
            background: #f1f5f9 !important;
            border: 1px solid #e2e8f0 !important;
            border-radius: 8px !important;
            width: 28px !important;
            height: 28px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            color: #64748b !important;
            font-size: 12px !important;
            transition: all 0.2s ease !important;
        }

        .leaflet-routing-container .leaflet-routing-collapse-btn:hover {
            background: #e2e8f0 !important;
            color: #475569 !important;
            transform: scale(1.05) !important;
        }

        /* Enhanced scrollbar styling */
        .leaflet-routing-container::-webkit-scrollbar {
            width: 8px !important;
        }

        .leaflet-routing-container::-webkit-scrollbar-track {
            background: rgba(241, 245, 249, 0.5) !important;
            border-radius: 10px !important;
            margin: 8px 0 !important;
        }

        .leaflet-routing-container::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, #cbd5e1, #94a3b8) !important;
            border-radius: 10px !important;
            border: 2px solid transparent !important;
            background-clip: content-box !important;
        }

        .leaflet-routing-container::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, #94a3b8, #64748b) !important;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .leaflet-routing-container {
                max-height: 420px !important;
                padding: 16px 20px !important;
                font-size: 13px !important;
                margin-bottom: 50px !important;
                width: 280px !important;
                min-width: 250px !important;
            }

            .leaflet-routing-container h2 {
                font-size: 16px !important;
                margin-bottom: 12px !important;
            }

            .leaflet-routing-container td {
                padding: 10px 6px !important;
            }
        }

        /* Animation for smooth appearance */
        .leaflet-routing-container {
            animation: slideInUp 0.4s cubic-bezier(0.16, 1, 0.3, 1) !important;
        }

        @keyframes slideInUp {
            from {
                transform: translateY(20px) !important;
                opacity: 0 !important;
            }
            to {
                transform: translateY(0) !important;
                opacity: 1 !important;
            }
        }

        /* Distance and time summary styling */
        .leaflet-routing-container h3 {
            font-size: 14px !important;
            font-weight: 600 !important;
            color: #059669 !important;
            margin: 8px 0 12px 0 !important;
            padding: 8px 12px !important;
            background: rgba(16, 185, 129, 0.1) !important;
            border-radius: 8px !important;
            border-left: 3px solid #10b981 !important;
        }
    `;
    document.head.appendChild(style);

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        maxZoom: 19,
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);

    const resizeObserver = new ResizeObserver(entries => {
        for (const entry of entries) {
            if (entry.target.id === 'map') {
                map.invalidateSize();
            }
        }
    });

    const mapContainer = document.getElementById('map');
    if (mapContainer) {
        resizeObserver.observe(mapContainer);
    }

    window.addEventListener('resize', () => {
        map.invalidateSize();
    });

    setTimeout(() => {
        map.invalidateSize();
        map.setView([10.3157, 123.8854], 13);
    }, 100);

    // Get user location immediately after map initialization
    getUserLocation().then(location => {
        console.log('User location obtained:', location);
        map.setView([location.lat, location.lng], 15);
    }).catch(error => {
        console.error('Failed to get initial user location:', error);
    });

    addMarkers();
    updateStats();
    initializeMapFilters();
    initializeRoutingControls();
}

function initializeLocationSearch() {
    console.log('Location search initialized');
}

function initializeBarangayFilter() {
    console.log('Barangay filter initialized');
}

// Initialize routing controls
function initializeRoutingControls() {
    const hideLocationInfoBtn = document.getElementById('hide-location-info');
    const resetMapBtn = document.getElementById('reset-map');

    if (hideLocationInfoBtn) {
        hideLocationInfoBtn.addEventListener('click', hideLocationInfo);
    }

    if (resetMapBtn) {
        resetMapBtn.addEventListener('click', resetMap);
    }
}

// Get user location
function getUserLocation() {
    return new Promise((resolve, reject) => {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                function(position) {
                    const lat = position.coords.latitude;
                    const lng = position.coords.longitude;
                    userLocation = { lat, lng };

                    // Add or update user marker with more visible styling
                    if (userMarker) {
                        userMarker.setLatLng([lat, lng]);
                    } else {
                        addUserMarker();
                    }

                    // Center map on user location
                    map.setView([lat, lng], 15);

                    // Enable all route buttons
                    document.querySelectorAll('[id^="show-route-"]').forEach(btn => {
                        btn.disabled = false;
                    });

                    resolve(userLocation);
                },
                function(error) {
                    console.error('Error getting location:', error);
                    showLocationPermissionModal(error);
                    reject(error);
                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 300000 // 5 minutes
                }
            );
        } else {
            showLocationPermissionModal({ code: 0, message: 'Geolocation is not supported by this browser.' });
            reject(new Error('Geolocation not supported'));
        }
    });
}

// Add user location marker
function addUserMarker() {
    if (userMarker) {
        map.removeLayer(userMarker);
    }

    const userIcon = L.divIcon({
        className: 'user-location-marker',
        html: `<div class="w-8 h-8 bg-blue-500 rounded-full border-3 border-white shadow-lg flex items-center justify-center animate-pulse">
                <i class="fas fa-user text-white text-sm"></i>
               </div>`,
        iconSize: [32, 32],
        iconAnchor: [16, 32]
    });

    userMarker = L.marker([userLocation.lat, userLocation.lng], { icon: userIcon })
        .addTo(map)
        .bindPopup('<div><strong>Your Location</strong><br>Click "Show Route" to see directions to evacuation centers</div>');
}

// Helper to generate center info popup content
function getCenterInfoPopup(center) {
    const disasterTypeStr = formatDisasterType(center.disaster_type);
    const showRouteButton = (typeof window.isDashboardMap === 'undefined' || !window.isDashboardMap)
        ? `<button onclick="showRouteToCenter(${center.id})" id="show-route-${center.id}" style="width: 100%; padding: 12px 16px; background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; border: none; border-radius: 10px; font-size: 14px; font-weight: 600; cursor: pointer; transition: all 0.2s ease; box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3); display: flex; align-items: center; justify-content: center; gap: 8px;" onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 6px 16px rgba(59, 130, 246, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(59, 130, 246, 0.3)'">
            <i class="fas fa-route"></i>Show Route
        </button>`
        : '';
    const separator = showRouteButton ? '<div style="padding-top: 12px; border-top: 1px solid #e2e8f0;">' : '';
    const separatorClose = showRouteButton ? '</div>' : '';

    return `
        <div id="center-info-content-${center.id}" style="font-family: 'Inter', 'Segoe UI', sans-serif; min-width: 280px; max-width: 320px;">
            <!-- Header with center name -->
            <div style="background: #3b82f6; color: white; padding: 12px 16px; margin: -10px -10px 12px -10px; border-radius: 8px 8px 0 0;">
                <h3 style="margin: 0; font-size: 16px; font-weight: 600; line-height: 1.3;">
                    <i class="fas fa-home mr-2" style="color: #dbeafe;"></i>${center.name}
                </h3>
            </div>

            <!-- Content section -->
            <div style="padding: 0 4px;">
                <p><strong>Address:</strong> ${center.address}</p>
                <p><strong>Capacity:</strong> ${center.capacity}</p>
                <p><strong>Status:</strong> ${center.status}</p>
                <p><strong>Disaster Type:</strong> ${disasterTypeStr}</p>
                <p><strong>Contact:</strong> ${center.contact}</p>
            </div>

            ${showRouteButton ? `<div style=\"padding-top: 12px; border-top: 1px solid #e2e8f0;\">${showRouteButton}</div>` : ''}
        </div>
    `;
}

// Hide location info
function hideLocationInfo() {
    const locationInfo = document.getElementById('location-info');
    if (locationInfo) {
        locationInfo.classList.add('hidden');
    }
}

// Get currently visible centers based on filters
function getVisibleCenters() {
    const safe = (v) => (v === undefined || v === null || v === '') ? 'All' : v;

    const status = safe(document.getElementById('status-filter')?.value);
    const disaster = safe(document.getElementById('disaster-filter')?.value);
    const barangay = safe(document.getElementById('barangay')?.value);
    const search = (document.getElementById('search')?.value || '').toLowerCase();

    return centers.filter(center => {
        const matchesStatus = status === 'All' || center.status === status;
        const matchesDisaster = matchesDisasterFilter(center, disaster);
        const matchesSearch = center.name.toLowerCase().includes(search) || center.address.toLowerCase().includes(search);
        const matchesBarangay = barangay === 'All' || center.barangay === barangay;
        return matchesStatus && matchesDisaster && matchesSearch && matchesBarangay;
    });
}

function getMarkerColor(disasterType) {
    if (Array.isArray(disasterType)) {
        if (disasterType.length === 0) return disasterTypeColors['Multi-disaster'];
        if (disasterType.length === 1) {
            const type = disasterType[0];
            return type.startsWith('Others:') ? disasterTypeColors['Others'] : disasterTypeColors[type] || disasterTypeColors['Multi-disaster'];
        }
        return disasterTypeColors['Multi-disaster'];
    }
    if (typeof disasterType === 'string') {
        return disasterType.startsWith('Others:') ? disasterTypeColors['Others'] : disasterTypeColors[disasterType] || disasterTypeColors['Multi-disaster'];
    }
    return disasterTypeColors['Multi-disaster'];
}

// Function to get the correct pin based on disaster types
function getCorrectPin(disasterType) {
    if (Array.isArray(disasterType)) {
        if (disasterType.length === 0) {
            return disasterTypePins['Others'];
        } else if (disasterType.length > 1) {
            return disasterTypePins['Multi-disaster'];
        } else {
            const type = disasterType[0];
            const pinType = type.startsWith('Others:') ? 'Others' : type;
            return disasterTypePins[pinType] || disasterTypePins['Others'];
        }
    } else if (typeof disasterType === 'string') {
        try {
            const parsed = JSON.parse(disasterType);
            return getCorrectPin(parsed);
        } catch (e) {
            const pinType = disasterType.startsWith('Others:') ? 'Others' : disasterType;
            return disasterTypePins[pinType] || disasterTypePins['Others'];
        }
    }
    return disasterTypePins['Others'];
}

function formatDisasterType(disasterType) {
    if (Array.isArray(disasterType)) {
        return disasterType.map(type =>
            type.startsWith('Others:') ? type.replace('Others:', '').trim() : type
        ).join(', ');
    }
    if (typeof disasterType === 'string') {
        return disasterType.startsWith('Others:') ? disasterType.replace('Others:', '').trim() : disasterType;
    }
    return 'Unknown';
}

function matchesDisasterFilter(center, filterValue) {
    if (filterValue === 'All') return true;

    const disasterType = center.disaster_type;
    const typeArray = Array.isArray(disasterType) ? disasterType : (disasterType ? [disasterType] : []);

    if (filterValue === 'Multi-disaster') return typeArray.length > 1;

    if (typeArray.length !== 1) return false;

    const singleType = typeArray[0];
    if (filterValue === 'Others') {
        return typeof singleType === 'string' && singleType.startsWith('Others');
    }
    return singleType === filterValue;
}

function updateStats() {
    const visibleCenters = centers.filter(center => {
        const statusFilter = document.getElementById('status-filter').value;
        const disasterFilter = document.getElementById('disaster-filter').value;
        const searchQuery = document.getElementById('search').value.toLowerCase();
        const barangayFilterElement = document.getElementById('barangay');
        const barangayFilter = barangayFilterElement ? barangayFilterElement.value || 'All' : 'All';

        const matchesStatus = statusFilter === 'All' || center.status === statusFilter;
        const matchesDisaster = matchesDisasterFilter(center, disasterFilter);
        const matchesSearch = center.name.toLowerCase().includes(searchQuery) || center.address.toLowerCase().includes(searchQuery);
        const matchesBarangay = barangayFilter === 'All' || center.barangay === barangayFilter;

        return matchesStatus && matchesDisaster && matchesSearch && matchesBarangay;
    });

    const totalCentersElement = document.getElementById('total-centers');
    const activeCentersElement = document.getElementById('active-centers');

    if (totalCentersElement) totalCentersElement.textContent = visibleCenters.length;
    if (activeCentersElement) activeCentersElement.textContent = visibleCenters.filter(c => c.status === 'Active').length;
}

// Update addMarkers to use the new popup content generator
function addMarkers(centersData = centers) {
    markers.forEach(marker => map.removeLayer(marker));
    markers = [];
    const safe = (v) => (v === undefined || v === null || v === '') ? 'All' : v;
    const status = safe(document.getElementById('status-filter')?.value);
    const disaster = safe(document.getElementById('disaster-filter')?.value);
    const barangay = safe(document.getElementById('barangay')?.value);
    const search = (document.getElementById('search')?.value || '').toLowerCase();
    const filteredCenters = centersData.filter(center => {
        const matchesStatus = status === 'All' || center.status === status;
        const matchesDisaster = matchesDisasterFilter(center, disaster);
        const matchesSearch = center.name.toLowerCase().includes(search) || center.address.toLowerCase().includes(search);
        const matchesBarangay = barangay === 'All' || center.barangay === barangay;
        return matchesStatus && matchesDisaster && matchesSearch && matchesBarangay;
    });
    filteredCenters.forEach(center => {
        // Get the correct pin image based on disaster type
        const pinUrl = getCorrectPin(center.disaster_type);

        // Create custom icon using the pin image
        const markerIcon = L.icon({
            iconUrl: pinUrl,
            iconSize: [32, 32],
            iconAnchor: [16, 32],
            popupAnchor: [0, -32]
        });

        const marker = L.marker([center.latitude, center.longitude], {
            icon: markerIcon
        });
        marker.bindPopup(getCenterInfoPopup(center));
        marker.addTo(map);
        markers.push(marker);
    });
    updateStats();
}

function filterMarkers() {
    // Get the currently visible centers after filtering
    const visibleCenters = getVisibleCenters();

    // Check if the currently routed center is still visible
    if (currentCenterId) {
        const isCurrentCenterVisible = visibleCenters.some(c => c.id === currentCenterId);

        // If the current routed center is no longer visible, clear the route
        if (!isCurrentCenterVisible) {
            console.log('🔄 Current routed center is filtered out, clearing route...');
            clearCurrentRoute();
        }
    }

    addMarkers(centers);
}

function initializeMapFilters() {
    // Initialize filter event listeners
    const statusFilter = document.getElementById('status-filter');
    const disasterFilter = document.getElementById('disaster-filter');
    const searchInput = document.getElementById('search');
    const barangayFilter = document.getElementById('barangay');

    if (statusFilter) {
        statusFilter.addEventListener('change', filterMarkers);
    }
    if (disasterFilter) {
        disasterFilter.addEventListener('change', filterMarkers);
    }
    if (searchInput) {
        searchInput.addEventListener('input', filterMarkers);
    }
    // Only add barangay filter listener if it exists (BDRRMC users don't have this filter)
    if (barangayFilter) {
        barangayFilter.addEventListener('change', filterMarkers);
    }
}

function resetMap() {
    console.log('🔄 Resetting map to initial state...');

    // Clear current route completely
    clearCurrentRoute();

    // Reset all marker popups to initial state (show "Show Route" button)
    markers.forEach(marker => {
        const latLng = marker.getLatLng();
        const center = centers.find(c =>
            Math.abs(c.latitude - latLng.lat) < 0.0001 &&
            Math.abs(c.longitude - latLng.lng) < 0.0001
        );
        if (center) {
            marker.setPopupContent(getCenterInfoPopup(center));
        }
    });

    // Reset map view to show all markers and user location
    if (markers.length > 0) {
        const group = new L.featureGroup(markers);
        if (userMarker) {
            group.addLayer(userMarker);
        }
        map.fitBounds(group.getBounds().pad(0.1));
    } else {
        // Fallback to default view if no markers
        map.setView([10.3157, 123.8854], 13);
    }

    // Reset filters to show all markers
    const searchInput = document.getElementById('search');
    const statusFilter = document.getElementById('status-filter');
    const disasterFilter = document.getElementById('disaster-filter');
    const barangayFilter = document.getElementById('barangay');

    if (searchInput) searchInput.value = '';
    if (statusFilter) statusFilter.value = 'All';
    if (disasterFilter) disasterFilter.value = 'All';
    // Only reset barangay filter if it exists (BDRRMC users don't have this filter)
    if (barangayFilter) barangayFilter.value = 'All';

    // Close all open popups
    map.closePopup();

    // Refilter markers to show all
    filterMarkers();

    console.log('✅ Map reset to initial state completed!');
}

// Show route to specific center with transport mode
function showRouteToCenter(centerId, mode = null) {
    const center = centers.find(c => c.id === centerId);
    if (!center || !userLocation) {
        alert('User location not available. Please allow location access.');
        return;
    }

    // Use provided mode or current transport mode
    const transportMode = mode || currentTransportMode;

    // Force clear current route first
    if (currentRoute) {
        try {
            map.removeControl(currentRoute);
        } catch (e) {
            console.log('Error removing current route:', e);
        }
        currentRoute = null;
    }

    // More aggressive clearing of routing controls
    const routingControls = document.querySelectorAll('.leaflet-routing-container');
    routingControls.forEach(control => {
        if (control.parentNode) {
            control.parentNode.removeChild(control);
        }
    });

    // Clear any routing-related layers
    map.eachLayer(function(layer) {
        if (layer instanceof L.Routing.Control ||
            (layer.options && layer.options.router) ||
            layer._routing) {
            try {
                map.removeLayer(layer);
            } catch (e) {
                console.log('Error removing layer:', e);
            }
        }
    });

    currentCenterId = centerId;

    // Create new routing control with proper profile and service URL
    let serviceUrl;
    let profile;

    // Use different service URLs for different transport modes
    switch(transportMode) {
        case 'walking':
            serviceUrl = 'https://routing.openstreetmap.de/routed-foot/route/v1';
            profile = 'foot';
            break;
        case 'cycling':
            serviceUrl = 'https://routing.openstreetmap.de/routed-bike/route/v1';
            profile = 'bike';
            break;
        case 'driving':
        default:
            serviceUrl = 'https://router.project-osrm.org/route/v1';
            profile = 'car';
            break;
    }

    console.log(`🗺️ Creating route with mode: ${transportMode}, profile: ${profile}, service: ${serviceUrl}`);

    const routerOptions = {
        waypoints: [
            L.latLng(userLocation.lat, userLocation.lng),
            L.latLng(center.latitude, center.longitude)
        ],
        router: L.Routing.osrmv1({
            profile: profile,
            serviceUrl: serviceUrl
        }),
        lineOptions: {
            styles: [{
                color: routeColors[transportMode],
                weight: 6,
                opacity: 0.8
            }]
        },
        createMarker: () => null,
        addWaypoints: false,
        draggableWaypoints: false,
        routeWhileDragging: false,
        fitSelectedRoutes: true,
        show: true
    };

    // Add small delay to ensure previous control is fully removed
    setTimeout(() => {
        currentRoute = L.Routing.control(routerOptions).addTo(map);

        currentRoute.on('routesfound', e => {
            const route = e.routes[0];
            showRouteDetails(center.name, route, transportMode);

            // Update popup to show route controls
            updateMarkerPopupWithRouteControls(centerId, center);
        });
    }, 100);
}

// Get profile for transport mode
function getProfileForMode(mode) {
    console.log('🚗 Getting profile for mode:', mode);
    switch(mode) {
        case 'walking':
            console.log('👟 Using walking profile');
            return 'foot';
        case 'cycling':
            console.log('🚴 Using cycling profile');
            return 'bike';
        case 'driving':
        default:
            console.log('🚗 Using driving profile');
            return 'car';
    }
}

// Show route details - now just logs to console, no panel created
function showRouteDetails(centerName, route, mode) {
    const distance = (route.summary.totalDistance / 1000).toFixed(2);
    const time = Math.round(route.summary.totalTime / 60);
    console.log(`📍 Route to ${centerName}:`);
    console.log(`   Distance: ${distance}km`);
    console.log(`   Time: ${time} minutes`);
    console.log(`   Mode: ${mode}`);
    console.log(`   Raw time: ${route.summary.totalTime} seconds`);
}

// Toggle directions panel minimize/maximize
function toggleDirectionsPanel() {
    const panel = document.getElementById('directions-panel');
    const content = panel.querySelector('div:nth-child(2)'); // The content div
    const toggleBtn = document.getElementById('toggle-directions-btn');
    const toggleIcon = toggleBtn.querySelector('i');

    if (content.style.display === 'none') {
        // Expand
        content.style.display = 'block';
        toggleIcon.className = 'fas fa-chevron-up';
        panel.style.maxHeight = '500px';
    } else {
        // Minimize
        content.style.display = 'none';
        toggleIcon.className = 'fas fa-chevron-down';
        panel.style.maxHeight = 'auto';
    }
}

// Panel functions removed - using only Leaflet routing control now

// Clear current route
function clearCurrentRoute() {
    // Force clear current route
    if (currentRoute) {
        try {
            map.removeControl(currentRoute);
        } catch (e) {
            console.log('Error removing current route:', e);
        }
        currentRoute = null;
    }

    // Remove routing control DOM elements
    const routingControls = document.querySelectorAll('.leaflet-routing-container');
    routingControls.forEach(control => {
        if (control.parentNode) {
            control.parentNode.removeChild(control);
        }
    });

    // Clear routing layers
    map.eachLayer(function(layer) {
        if (layer instanceof L.Routing.Control ||
            (layer.options && layer.options.router) ||
            layer._routing) {
            try {
                map.removeLayer(layer);
            } catch (e) {
                console.log('Error removing layer:', e);
            }
        }
    });

    // Remove any existing directions panel
    const panel = document.getElementById('directions-panel');
    if (panel) {
        panel.remove();
    }

    currentCenterId = null;

    // Reset all marker popups to show route button
    markers.forEach(marker => {
        const latLng = marker.getLatLng();
        const center = centers.find(c =>
            Math.abs(c.latitude - latLng.lat) < 0.0001 &&
            Math.abs(c.longitude - latLng.lng) < 0.0001
        );
        if (center) {
            marker.setPopupContent(getCenterInfoPopup(center));
        }
    });
}

// Update marker popup with route controls
function updateMarkerPopupWithRouteControls(centerId, center) {
    const marker = markers.find(m => {
        const latLng = m.getLatLng();
        return Math.abs(center.latitude - latLng.lat) < 0.0001 &&
               Math.abs(center.longitude - latLng.lng) < 0.0001;
    });

    if (marker) {
        const disasterTypeStr = formatDisasterType(center.disaster_type);

        const routeControlsHtml = `
            <div id="route-info-content-${center.id}" style="font-family: 'Inter', 'Segoe UI', sans-serif; min-width: 280px; max-width: 320px;">
                <!-- Header with center name -->
                <div style="background: #3b82f6; color: white; padding: 12px 16px; margin: -10px -10px 12px -10px; border-radius: 8px 8px 0 0;">
                    <h3 style="margin: 0; font-size: 16px; font-weight: 600; line-height: 1.3;">
                        <i class="fas fa-route mr-2" style="color: #dbeafe;"></i>${center.name}
                    </h3>
                    <div style="font-size: 12px; color: #dbeafe; margin-top: 4px; font-weight: 500;">
                        <i class="fas fa-info-circle mr-1"></i>Route Active
                    </div>
                </div>

                <!-- Content section -->
                <div style="padding: 0 4px;">
                    <p><strong>Address:</strong> ${center.address}</p>
                    <p><strong>Capacity:</strong> ${center.capacity}</p>
                    <p><strong>Status:</strong> ${center.status}</p>
                    <p><strong>Disaster Type:</strong> ${disasterTypeStr}</p>
                    <p><strong>Contact:</strong> ${center.contact}</p>
                </div>

                <!-- Clear route button -->
                <div style="padding-top: 12px; border-top: 1px solid #e2e8f0;">
                    <button onclick="clearCurrentRoute()" style="width: 100%; padding: 10px 16px; background: #6b7280; color: white; border: none; border-radius: 8px; font-size: 13px; font-weight: 600; cursor: pointer; transition: all 0.2s ease; display: flex; align-items: center; justify-content: center; gap: 8px;" onmouseover="this.style.background='#4b5563'" onmouseout="this.style.background='#6b7280'">
                        <i class="fas fa-times"></i>Clear Route
                    </button>
                </div>
            </div>
        `;
        marker.setPopupContent(routeControlsHtml);
    }
}

// Set transport mode and update UI
function setTransportMode(mode) {
    currentTransportMode = mode;

    // Update button states
    document.querySelectorAll('.transport-mode-btn').forEach(btn => {
        btn.classList.remove('active', 'bg-blue-500', 'text-white');
        btn.classList.add('bg-gray-300', 'text-gray-700');
    });

    const activeBtn = document.getElementById(`transport-${mode}`);
    if (activeBtn) {
        activeBtn.classList.remove('bg-gray-300', 'text-gray-700');
        activeBtn.classList.add('active', 'bg-blue-500', 'text-white');
    }

    // If there's a current route, update it with the new mode
    if (currentRoute && currentCenterId) {
        showRouteToCenter(currentCenterId, mode);
    }
}

// Enhanced Location Permission Modal
function showLocationPermissionModal(error) {
    // Remove any existing modal
    const existingModal = document.getElementById('locationPermissionModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Determine error message and icon based on error type
    let title, message, icon, iconColor;

    if (error.code === 1) {
        title = 'Location Permission Required';
        message = 'To enable routing features and show directions to evacuation centers, we need access to your location. Please allow location access when prompted by your browser.';
        icon = '📍';
        iconColor = 'text-blue-500';
    } else if (error.code === 2) {
        title = 'Location Unavailable';
        message = 'Your location is currently unavailable. Please check your device\'s location settings and ensure GPS is enabled to use routing features.';
        icon = '🛰️';
        iconColor = 'text-orange-500';
    } else if (error.code === 3) {
        title = 'Location Request Timeout';
        message = 'The location request timed out. Please try again to enable routing features and get directions to evacuation centers.';
        icon = '⏱️';
        iconColor = 'text-yellow-500';
    } else {
        title = 'Location Not Supported';
        message = 'Your browser doesn\'t support location services. Routing features will not be available. Please try using a modern browser like Chrome, Firefox, or Safari.';
        icon = '🚫';
        iconColor = 'text-red-500';
    }

    // Create modal HTML
    const modalHTML = `
        <div id="locationPermissionModal" class="fixed inset-0 z-50 flex items-center justify-center p-4" style="background-color: rgba(0, 0, 0, 0.5);">
            <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all">
                <!-- Header -->
                <div class="p-6 text-center border-b border-gray-100">
                    <div class="text-4xl mb-3">${icon}</div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">${title}</h3>
                    <p class="text-gray-600 text-sm leading-relaxed">${message}</p>
                </div>

                <!-- Features Section -->
                <div class="p-6 bg-gradient-to-br from-blue-50 to-indigo-50">
                    <h4 class="font-semibold text-gray-800 mb-3 flex items-center">
                        <span class="text-blue-500 mr-2">🗺️</span>
                        Routing Features Include:
                    </h4>
                    <ul class="space-y-2 text-sm text-gray-700">
                        <li class="flex items-center">
                            <span class="text-green-500 mr-2">✓</span>
                            Turn-by-turn directions to evacuation centers
                        </li>
                        <li class="flex items-center">
                            <span class="text-green-500 mr-2">✓</span>
                            Multiple transport modes (walking, driving, cycling)
                        </li>
                        <li class="flex items-center">
                            <span class="text-green-500 mr-2">✓</span>
                            Real-time distance and travel time estimates
                        </li>
                        <li class="flex items-center">
                            <span class="text-green-500 mr-2">✓</span>
                            Find nearest evacuation centers to your location
                        </li>
                    </ul>
                </div>

                <!-- Action Buttons -->
                <div class="p-6 flex justify-center">
                    <button onclick="closeLocationModal()" class="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg">
                        OK
                    </button>
                </div>

                <!-- Help Text -->
                <div class="px-6 pb-6">
                    <div class="bg-amber-50 border border-amber-200 rounded-lg p-3">
                        <p class="text-xs text-amber-800 flex items-start">
                            <span class="text-amber-500 mr-2 mt-0.5">💡</span>
                            <span>
                                <strong>Need help?</strong> Look for a location icon in your browser's address bar and click "Allow" when prompted.
                            </span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Add animation
    setTimeout(() => {
        const modal = document.getElementById('locationPermissionModal');
        if (modal) {
            modal.querySelector('.bg-white').style.transform = 'scale(1)';
        }
    }, 10);
}



// Close location modal
function closeLocationModal() {
    const modal = document.getElementById('locationPermissionModal');
    if (modal) {
        modal.style.opacity = '0';
        setTimeout(() => {
            modal.remove();
        }, 200);
    }
}