<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Checking <NAME_EMAIL>:\n";

$invitation = \App\Models\Invitation::where('email', '<EMAIL>')->first();

if ($invitation) {
    echo "Found invitation:\n";
    echo json_encode($invitation->toArray(), JSON_PRETTY_PRINT) . "\n";
} else {
    echo "No invitation found\n";
}

echo "\nChecking all invitations:\n";
$allInvitations = \App\Models\Invitation::all();
foreach ($allInvitations as $inv) {
    echo "- {$inv->email} (Status: {$inv->status}, Created: {$inv->created_at})\n";
}

echo "\nChecking users table:\n";
$user = \App\Models\User::where('email', '<EMAIL>')->first();
if ($user) {
    echo "Found user:\n";
    echo json_encode($user->toArray(), JSON_PRETTY_PRINT) . "\n";
} else {
    echo "No user found\n";
} 