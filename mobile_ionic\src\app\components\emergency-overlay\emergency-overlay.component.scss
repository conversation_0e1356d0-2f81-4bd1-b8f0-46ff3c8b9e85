// Emergency Overlay Component Styles
.emergency-overlay-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(10px);
  animation: emergencyFadeIn 0.3s ease-out;
}

.emergency-alert-container {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  position: relative;
  animation: emergencySlideIn 0.5s ease-out;
  border: 4px solid;
}

// Emergency Header
.emergency-header {
  padding: 24px 20px 16px;
  text-align: center;
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
}

.disaster-icon-container {
  position: relative;
  display: inline-block;
  margin-bottom: 16px;
}

.disaster-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.3);
  object-fit: cover;
}

.severity-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.emergency-title-container {
  margin-bottom: 12px;
}

.emergency-title {
  font-size: 18px;
  font-weight: 800;
  color: white;
  margin: 0 0 4px 0;
  text-transform: uppercase;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.warning-icon {
  font-size: 20px;
  animation: warningPulse 1s infinite;
}

.disaster-type {
  font-size: 24px;
  font-weight: 900;
  color: white;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.countdown-container {
  position: absolute;
  top: 16px;
  right: 16px;
}

.countdown-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(255, 255, 255, 0.4);
}

.countdown-text {
  font-size: 12px;
  font-weight: 700;
  color: white;
}

// Emergency Message
.emergency-message-container {
  padding: 20px;
  background: white;
}

.emergency-message {
  margin-bottom: 16px;
}

.message-title {
  font-size: 18px;
  font-weight: 700;
  color: #333;
  margin: 0 0 8px 0;
}

.message-content {
  font-size: 16px;
  line-height: 1.5;
  color: #555;
  margin: 0;
}

.emergency-instructions {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  padding: 16px;
  margin-top: 16px;
}

.instructions-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 700;
  color: #856404;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-icon {
  font-size: 16px;
}

.instructions-text {
  font-size: 14px;
  color: #856404;
  margin: 0;
  line-height: 1.4;
}

// Action Buttons
.emergency-actions {
  padding: 0 20px 20px;
  background: white;
  display: flex;
  flex-direction: column;
  gap: 12px;
  position: relative;
  z-index: 999;
  pointer-events: auto;
}

.emergency-button {
  width: 100%;
  padding: 16px 20px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 1000;
  pointer-events: auto;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.primary-action {
  background: #28a745;
  color: white;
  box-shadow: 0 4px 16px rgba(40, 167, 69, 0.3);
}

.primary-action:hover {
  background: #218838;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.secondary-action {
  background: #6c757d;
  color: white;
  box-shadow: 0 4px 16px rgba(108, 117, 125, 0.3);
}

.secondary-action:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

.button-icon {
  font-size: 20px;
}

.button-text {
  flex: 1;
  text-align: center;
  margin: 0 12px;
}

.button-arrow {
  font-size: 18px;
}

// Emergency Footer
.emergency-footer {
  padding: 16px 20px;
  background: rgba(0, 0, 0, 0.05);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.timestamp {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #666;
  justify-content: center;
}

.time-icon {
  font-size: 14px;
}

.critical-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 8px;
  padding: 8px 16px;
  background: #dc3545;
  color: white;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.critical-pulse {
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
  animation: criticalPulse 1s infinite;
}

// Background Pattern
.emergency-background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
  background-size: 30px 30px;
  background-image: 
    linear-gradient(45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, rgba(255, 255, 255, 0.1) 75%),
    linear-gradient(-45deg, transparent 75%, rgba(255, 255, 255, 0.1) 75%);
  animation: patternMove 20s linear infinite;
}

// Disaster-specific color schemes
.disaster-earthquake {
  border-color: #FF8C00;
  .emergency-header {
    background: linear-gradient(135deg, #FF8C00, #FFA500);
  }
  .severity-badge {
    background: #FF6B00;
  }
  .primary-action {
    background: #FF8C00;
    box-shadow: 0 4px 16px rgba(255, 140, 0, 0.3);
  }
  .primary-action:hover {
    background: #FF7700;
  }
}

.disaster-flood {
  border-color: #0066CC;
  .emergency-header {
    background: linear-gradient(135deg, #0066CC, #4A90E2);
  }
  .severity-badge {
    background: #0052A3;
  }
  .primary-action {
    background: #0066CC;
    box-shadow: 0 4px 16px rgba(0, 102, 204, 0.3);
  }
  .primary-action:hover {
    background: #0052A3;
  }
}

.disaster-typhoon {
  border-color: #228B22;
  .emergency-header {
    background: linear-gradient(135deg, #228B22, #32CD32);
  }
  .severity-badge {
    background: #1F7A1F;
  }
  .primary-action {
    background: #228B22;
    box-shadow: 0 4px 16px rgba(34, 139, 34, 0.3);
  }
  .primary-action:hover {
    background: #1F7A1F;
  }
}

.disaster-fire {
  border-color: #DC143C;
  .emergency-header {
    background: linear-gradient(135deg, #DC143C, #FF4500);
  }
  .severity-badge {
    background: #B91C3C;
  }
  .primary-action {
    background: #DC143C;
    box-shadow: 0 4px 16px rgba(220, 20, 60, 0.3);
  }
  .primary-action:hover {
    background: #B91C3C;
  }
}

.disaster-landslide {
  border-color: #8B4513;
  .emergency-header {
    background: linear-gradient(135deg, #8B4513, #A0522D);
  }
  .severity-badge {
    background: #654321;
  }
  .primary-action {
    background: #8B4513;
    box-shadow: 0 4px 16px rgba(139, 69, 19, 0.3);
  }
  .primary-action:hover {
    background: #654321;
  }
}

.disaster-general {
  border-color: #666666;
  .emergency-header {
    background: linear-gradient(135deg, #666666, #888888);
  }
  .severity-badge {
    background: #555555;
  }
  .primary-action {
    background: #666666;
    box-shadow: 0 4px 16px rgba(102, 102, 102, 0.3);
  }
  .primary-action:hover {
    background: #555555;
  }
}

// Severity-specific styles
.severity-critical {
  .severity-badge {
    background: #dc3545 !important;
    animation: criticalBadgePulse 1s infinite;
  }
  .emergency-alert-container {
    animation: criticalShake 0.5s infinite;
  }
}

.severity-high {
  .severity-badge {
    background: #fd7e14 !important;
  }
}

.severity-medium {
  .severity-badge {
    background: #ffc107 !important;
    color: #333 !important;
  }
}

.severity-low {
  .severity-badge {
    background: #28a745 !important;
  }
}

// Animations
@keyframes emergencyFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes emergencySlideIn {
  from {
    transform: translateY(-50px) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

@keyframes warningPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes criticalPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

@keyframes criticalBadgePulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(220, 53, 69, 0);
  }
}

@keyframes criticalShake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-2px);
  }
  75% {
    transform: translateX(2px);
  }
}

@keyframes patternMove {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 30px 30px;
  }
}

// Responsive design
@media (max-width: 480px) {
  .emergency-overlay-container {
    padding: 16px;
  }

  .emergency-alert-container {
    max-width: 100%;
  }

  .disaster-icon {
    width: 56px;
    height: 56px;
  }

  .emergency-title {
    font-size: 16px;
  }

  .disaster-type {
    font-size: 20px;
  }

  .message-title {
    font-size: 16px;
  }

  .message-content {
    font-size: 14px;
  }

  .emergency-button {
    padding: 14px 16px;
    font-size: 14px;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .emergency-alert-container {
    border-width: 6px;
  }

  .emergency-button {
    border: 2px solid rgba(255, 255, 255, 0.3);
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .emergency-alert-container,
  .warning-icon,
  .critical-pulse,
  .severity-badge,
  .emergency-background-pattern {
    animation: none !important;
  }
}
