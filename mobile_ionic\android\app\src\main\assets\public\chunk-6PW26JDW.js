import{a as k}from"./chunk-GOWQWRZR.js";import{f as y,j as v,k as w}from"./chunk-P4NBILQA.js";import{b as x}from"./chunk-Y33LKJAV.js";import{b as m}from"./chunk-ROLYNLUZ.js";import{b as c,f as o,g as h,j as f,k as g}from"./chunk-ZOYALB5L.js";import{p}from"./chunk-UYQ7EZNZ.js";import{e as u}from"./chunk-BAKMWPBW.js";import{h as a}from"./chunk-B7O3QC5Z.js";var I=":host{--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff);background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit)}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}.button-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-padding-start:0.7em;padding-inline-start:0.7em;-webkit-padding-end:0.7em;padding-inline-end:0.7em;padding-top:0;padding-bottom:0;display:inline-block;position:relative;width:100%;height:100%;border:0;outline:none;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-box-sizing:border-box;box-sizing:border-box}.button-inner{display:-ms-flexbox;display:flex;-ms-flex-flow:column nowrap;flex-flow:column nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.horizontal-wrapper{display:-ms-flexbox;display:flex;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%}::slotted(*){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:5px;margin-inline-end:5px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:5px;margin-inline-start:5px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}::slotted([slot=icon-only]){padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:10px;margin-inline-start:10px;-webkit-margin-end:10px;margin-inline-end:10px;margin-top:0;margin-bottom:0;min-width:0.9em;font-size:1.8em}:host(.item-option-expandable){-ms-flex-negative:0;flex-shrink:0;-webkit-transition-duration:0;transition-duration:0;-webkit-transition-property:none;transition-property:none;-webkit-transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1);transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1)}:host(.item-option-disabled){pointer-events:none}:host(.item-option-disabled) .button-native{cursor:default;opacity:0.5;pointer-events:none}:host{font-size:clamp(16px, 1rem, 35.2px)}:host(.ion-activated){background:var(--ion-color-primary-shade, #004acd)}:host(.ion-color.ion-activated){background:var(--ion-color-shade)}",j=I,W=":host{--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff);background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit)}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}.button-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-padding-start:0.7em;padding-inline-start:0.7em;-webkit-padding-end:0.7em;padding-inline-end:0.7em;padding-top:0;padding-bottom:0;display:inline-block;position:relative;width:100%;height:100%;border:0;outline:none;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-box-sizing:border-box;box-sizing:border-box}.button-inner{display:-ms-flexbox;display:flex;-ms-flex-flow:column nowrap;flex-flow:column nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.horizontal-wrapper{display:-ms-flexbox;display:flex;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%}::slotted(*){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:5px;margin-inline-end:5px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:5px;margin-inline-start:5px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}::slotted([slot=icon-only]){padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:10px;margin-inline-start:10px;-webkit-margin-end:10px;margin-inline-end:10px;margin-top:0;margin-bottom:0;min-width:0.9em;font-size:1.8em}:host(.item-option-expandable){-ms-flex-negative:0;flex-shrink:0;-webkit-transition-duration:0;transition-duration:0;-webkit-transition-property:none;transition-property:none;-webkit-transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1);transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1)}:host(.item-option-disabled){pointer-events:none}:host(.item-option-disabled) .button-native{cursor:default;opacity:0.5;pointer-events:none}:host{font-size:0.875rem;font-weight:500;text-transform:uppercase}",C=W,$=(()=>{let t=class{constructor(i){c(this,i),this.onClick=e=>{e.target.closest("ion-item-option")&&e.preventDefault()},this.color=void 0,this.disabled=!1,this.download=void 0,this.expandable=!1,this.href=void 0,this.rel=void 0,this.target=void 0,this.type="button"}render(){let{disabled:i,expandable:e,href:n}=this,s=n===void 0?"button":"a",l=m(this),d=s==="button"?{type:this.type}:{download:this.download,href:this.href,target:this.target};return o(h,{key:"1b7708dd178dc2c9280652ca3da38c84ba7b767f",onClick:this.onClick,class:x(this.color,{[l]:!0,"item-option-disabled":i,"item-option-expandable":e,"ion-activatable":!0})},o(s,Object.assign({key:"d9f899f5425ad6b97071494485aa3ca90bc89d30"},d,{class:"button-native",part:"native",disabled:i}),o("span",{key:"adc2cf72b4363be9b9eeb3584723e2bfc862af20",class:"button-inner"},o("slot",{key:"e668fe8e655a74e6a35e979e0cd488506b962dbf",name:"top"}),o("div",{key:"2ddcdb92b6b19c3cc549a7aee2400d1a6eeb51f1",class:"horizontal-wrapper"},o("slot",{key:"441f13df18b72e5ed6bb51b157722e065b5847d2",name:"start"}),o("slot",{key:"425d815874b49e1628880160d7175ed3ca36ca39",name:"icon-only"}),o("slot",{key:"27437d3fa3365b12bc030704e18481fdfb14aebb"}),o("slot",{key:"bd39330771c7f85c6df10f7f9050335ee7f14ff0",name:"end"})),o("slot",{key:"440cb6dc7743d50b261d4bf61d2c24e24b89e58c",name:"bottom"})),l==="md"&&o("ion-ripple-effect",{key:"29632941464bbb34551cf64961187643f62bf755"})))}get el(){return f(this)}};return t.style={ios:j,md:C},t})(),A="ion-item-options{top:0;right:0;-ms-flex-pack:end;justify-content:flex-end;display:none;position:absolute;height:100%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1}:host-context([dir=rtl]) ion-item-options{-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] ion-item-options{-ms-flex-pack:start;justify-content:flex-start}[dir=rtl] ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){ion-item-options:dir(rtl){-ms-flex-pack:start;justify-content:flex-start}ion-item-options:dir(rtl):not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}}.item-options-start{right:auto;left:0;-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) .item-options-start{-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] .item-options-start{-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){.item-options-start:dir(rtl){-ms-flex-pack:end;justify-content:flex-end}}[dir=ltr] .item-options-start ion-item-option:first-child,[dir=rtl] .item-options-start ion-item-option:last-child{padding-left:var(--ion-safe-area-left)}[dir=ltr] .item-options-end ion-item-option:last-child,[dir=rtl] .item-options-end ion-item-option:first-child{padding-right:var(--ion-safe-area-right)}:host-context([dir=rtl]) .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}[dir=rtl] .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}@supports selector(:dir(rtl)){.item-sliding-active-slide:dir(rtl).item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}}.item-sliding-active-slide ion-item-options{display:-ms-flexbox;display:flex;visibility:hidden}.item-sliding-active-slide.item-sliding-active-options-start .item-options-start,.item-sliding-active-slide.item-sliding-active-options-end ion-item-options:not(.item-options-start){width:100%;visibility:visible}.item-options-ios{border-bottom-width:0;border-bottom-style:solid;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))))}.item-options-ios.item-options-end{border-bottom-width:0.55px}.list-ios-lines-none .item-options-ios{border-bottom-width:0}.list-ios-lines-full .item-options-ios,.list-ios-lines-inset .item-options-ios.item-options-end{border-bottom-width:0.55px}",E=A,R="ion-item-options{top:0;right:0;-ms-flex-pack:end;justify-content:flex-end;display:none;position:absolute;height:100%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1}:host-context([dir=rtl]) ion-item-options{-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] ion-item-options{-ms-flex-pack:start;justify-content:flex-start}[dir=rtl] ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){ion-item-options:dir(rtl){-ms-flex-pack:start;justify-content:flex-start}ion-item-options:dir(rtl):not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}}.item-options-start{right:auto;left:0;-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) .item-options-start{-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] .item-options-start{-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){.item-options-start:dir(rtl){-ms-flex-pack:end;justify-content:flex-end}}[dir=ltr] .item-options-start ion-item-option:first-child,[dir=rtl] .item-options-start ion-item-option:last-child{padding-left:var(--ion-safe-area-left)}[dir=ltr] .item-options-end ion-item-option:last-child,[dir=rtl] .item-options-end ion-item-option:first-child{padding-right:var(--ion-safe-area-right)}:host-context([dir=rtl]) .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}[dir=rtl] .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}@supports selector(:dir(rtl)){.item-sliding-active-slide:dir(rtl).item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}}.item-sliding-active-slide ion-item-options{display:-ms-flexbox;display:flex;visibility:hidden}.item-sliding-active-slide.item-sliding-active-options-start .item-options-start,.item-sliding-active-slide.item-sliding-active-options-end ion-item-options:not(.item-options-start){width:100%;visibility:visible}.item-options-md{border-bottom-width:0;border-bottom-style:solid;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))))}.list-md-lines-none .item-options-md{border-bottom-width:0}.list-md-lines-full .item-options-md,.list-md-lines-inset .item-options-md.item-options-end{border-bottom-width:1px}",M=R,H=(()=>{let t=class{constructor(i){c(this,i),this.ionSwipe=g(this,"ionSwipe",7),this.side="end"}fireSwipeEvent(){return a(this,null,function*(){this.ionSwipe.emit({side:this.side})})}render(){let i=m(this),e=p(this.side);return o(h,{key:"7df4b71547524bf359c48e1b40ccbc44e850f632",class:{[i]:!0,[`item-options-${i}`]:!0,"item-options-start":!e,"item-options-end":e}})}get el(){return f(this)}};return t.style={ios:E,md:M},t})(),L="ion-item-sliding{display:block;position:relative;width:100%;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}ion-item-sliding .item{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.item-sliding-active-slide .item{position:relative;-webkit-transition:-webkit-transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);transition:-webkit-transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);transition:transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);transition:transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1), -webkit-transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);opacity:1;z-index:2;pointer-events:none;will-change:transform}.item-sliding-closing ion-item-options{pointer-events:none}.item-sliding-active-swipe-end .item-options-end .item-option-expandable{padding-left:100%;-ms-flex-order:1;order:1;-webkit-transition-duration:0.6s;transition-duration:0.6s;-webkit-transition-property:padding-left;transition-property:padding-left}:host-context([dir=rtl]) .item-sliding-active-swipe-end .item-options-end .item-option-expandable{-ms-flex-order:-1;order:-1}[dir=rtl] .item-sliding-active-swipe-end .item-options-end .item-option-expandable{-ms-flex-order:-1;order:-1}@supports selector(:dir(rtl)){.item-sliding-active-swipe-end .item-options-end .item-option-expandable:dir(rtl){-ms-flex-order:-1;order:-1}}.item-sliding-active-swipe-start .item-options-start .item-option-expandable{padding-right:100%;-ms-flex-order:-1;order:-1;-webkit-transition-duration:0.6s;transition-duration:0.6s;-webkit-transition-property:padding-right;transition-property:padding-right}:host-context([dir=rtl]) .item-sliding-active-swipe-start .item-options-start .item-option-expandable{-ms-flex-order:1;order:1}[dir=rtl] .item-sliding-active-swipe-start .item-options-start .item-option-expandable{-ms-flex-order:1;order:1}@supports selector(:dir(rtl)){.item-sliding-active-swipe-start .item-options-start .item-option-expandable:dir(rtl){-ms-flex-order:1;order:1}}",D=L,O=30,S=.55,r,_=class{constructor(t){c(this,t),this.ionDrag=g(this,"ionDrag",7),this.item=null,this.openAmount=0,this.initialOpenAmount=0,this.optsWidthRightSide=0,this.optsWidthLeftSide=0,this.sides=0,this.optsDirty=!0,this.contentEl=null,this.initialContentScrollY=!0,this.state=2,this.disabled=!1}disabledChanged(){this.gesture&&this.gesture.enable(!this.disabled)}connectedCallback(){return a(this,null,function*(){let{el:t}=this;this.item=t.querySelector("ion-item"),this.contentEl=y(t),this.mutationObserver=k(t,"ion-item-option",()=>a(this,null,function*(){yield this.updateOptions()})),yield this.updateOptions(),this.gesture=(yield import("./chunk-WZNRZUGA.js")).createGesture({el:t,gestureName:"item-swipe",gesturePriority:100,threshold:5,canStart:i=>this.canStart(i),onStart:()=>this.onStart(),onMove:i=>this.onMove(i),onEnd:i=>this.onEnd(i)}),this.disabledChanged()})}disconnectedCallback(){this.gesture&&(this.gesture.destroy(),this.gesture=void 0),this.item=null,this.leftOptions=this.rightOptions=void 0,r===this.el&&(r=void 0),this.mutationObserver&&(this.mutationObserver.disconnect(),this.mutationObserver=void 0)}getOpenAmount(){return Promise.resolve(this.openAmount)}getSlidingRatio(){return Promise.resolve(this.getSlidingRatioSync())}open(t){return a(this,null,function*(){var i;if((this.item=(i=this.item)!==null&&i!==void 0?i:this.el.querySelector("ion-item"))===null)return;let n=this.getOptions(t);if(!n)return;t===void 0&&(t=n===this.leftOptions?"start":"end"),t=p(t)?"end":"start";let s=this.openAmount<0,l=this.openAmount>0;s&&n===this.leftOptions||l&&n===this.rightOptions||(this.closeOpened(),this.state=4,requestAnimationFrame(()=>{this.calculateOptsWidth();let d=t==="end"?this.optsWidthRightSide:-this.optsWidthLeftSide;r=this.el,this.setOpenAmount(d,!1),this.state=t==="end"?8:16}))})}close(){return a(this,null,function*(){this.setOpenAmount(0,!0)})}closeOpened(){return a(this,null,function*(){return r!==void 0?(r.close(),r=void 0,!0):!1})}getOptions(t){return t===void 0?this.leftOptions||this.rightOptions:t==="start"?this.leftOptions:this.rightOptions}updateOptions(){return a(this,null,function*(){let t=this.el.querySelectorAll("ion-item-options"),i=0;this.leftOptions=this.rightOptions=void 0;for(let e=0;e<t.length;e++){let n=t.item(e),s=n.componentOnReady!==void 0?yield n.componentOnReady():n;(p(s.side)?"end":"start")==="start"?(this.leftOptions=s,i|=1):(this.rightOptions=s,i|=2)}this.optsDirty=!0,this.sides=i})}canStart(t){if(document.dir==="rtl"?window.innerWidth-t.startX<15:t.startX<15)return!1;let n=r;return n&&n!==this.el&&this.closeOpened(),!!(this.rightOptions||this.leftOptions)}onStart(){this.item=this.el.querySelector("ion-item");let{contentEl:t}=this;t&&(this.initialContentScrollY=v(t)),r=this.el,this.tmr!==void 0&&(clearTimeout(this.tmr),this.tmr=void 0),this.openAmount===0&&(this.optsDirty=!0,this.state=4),this.initialOpenAmount=this.openAmount,this.item&&(this.item.style.transition="none")}onMove(t){this.optsDirty&&this.calculateOptsWidth();let i=this.initialOpenAmount-t.deltaX;switch(this.sides){case 2:i=Math.max(0,i);break;case 1:i=Math.min(0,i);break;case 3:break;case 0:return;default:u("[ion-item-sliding] - invalid ItemSideFlags value",this.sides);break}let e;i>this.optsWidthRightSide?(e=this.optsWidthRightSide,i=e+(i-e)*S):i<-this.optsWidthLeftSide&&(e=-this.optsWidthLeftSide,i=e+(i-e)*S),this.setOpenAmount(i,!1)}onEnd(t){let{contentEl:i,initialContentScrollY:e}=this;i&&w(i,e);let n=t.velocityX,s=this.openAmount>0?this.optsWidthRightSide:-this.optsWidthLeftSide,l=this.openAmount>0==!(n<0),d=Math.abs(n)>.3,z=Math.abs(this.openAmount)<Math.abs(s/2);T(l,d,z)&&(s=0);let b=this.state;this.setOpenAmount(s,!0),b&32&&this.rightOptions?this.rightOptions.fireSwipeEvent():b&64&&this.leftOptions&&this.leftOptions.fireSwipeEvent()}calculateOptsWidth(){this.optsWidthRightSide=0,this.rightOptions&&(this.rightOptions.style.display="flex",this.optsWidthRightSide=this.rightOptions.offsetWidth,this.rightOptions.style.display=""),this.optsWidthLeftSide=0,this.leftOptions&&(this.leftOptions.style.display="flex",this.optsWidthLeftSide=this.leftOptions.offsetWidth,this.leftOptions.style.display=""),this.optsDirty=!1}setOpenAmount(t,i){if(this.tmr!==void 0&&(clearTimeout(this.tmr),this.tmr=void 0),!this.item)return;let{el:e}=this,n=this.item.style;if(this.openAmount=t,i&&(n.transition=""),t>0)this.state=t>=this.optsWidthRightSide+O?40:8;else if(t<0)this.state=t<=-this.optsWidthLeftSide-O?80:16;else{e.classList.add("item-sliding-closing"),this.gesture&&this.gesture.enable(!1),this.tmr=setTimeout(()=>{this.state=2,this.tmr=void 0,this.gesture&&this.gesture.enable(!this.disabled),e.classList.remove("item-sliding-closing")},600),r=void 0,n.transform="";return}n.transform=`translate3d(${-t}px,0,0)`,this.ionDrag.emit({amount:t,ratio:this.getSlidingRatioSync()})}getSlidingRatioSync(){return this.openAmount>0?this.openAmount/this.optsWidthRightSide:this.openAmount<0?this.openAmount/this.optsWidthLeftSide:0}render(){let t=m(this);return o(h,{key:"f8aea4bb9802b111ef358cc6c172a635637ae1f8",class:{[t]:!0,"item-sliding-active-slide":this.state!==2,"item-sliding-active-options-end":(this.state&8)!==0,"item-sliding-active-options-start":(this.state&16)!==0,"item-sliding-active-swipe-end":(this.state&32)!==0,"item-sliding-active-swipe-start":(this.state&64)!==0}})}get el(){return f(this)}static get watchers(){return{disabled:["disabledChanged"]}}},T=(t,i,e)=>!i&&e||t&&i;_.style=D;export{$ as ion_item_option,H as ion_item_options,_ as ion_item_sliding};
