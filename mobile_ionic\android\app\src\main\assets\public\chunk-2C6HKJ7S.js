import{Ba as b,E as g,Gb as _,Lb as x,M as e,N as n,O as l,S as o,_ as t,fa as m,gc as v,lb as u,n as s,ra as p,xb as f}from"./chunk-6NKRJMOW.js";import"./chunk-ZCNEFSEA.js";import"./chunk-YBOCXFN3.js";import"./chunk-B57F2HZP.js";import"./chunk-SV2ZKNWA.js";import"./chunk-V72YNCQ7.js";import"./chunk-HC6MZPB3.js";import"./chunk-7CISFAID.js";import"./chunk-RS5W3JWO.js";import"./chunk-FQJQVDRA.js";import"./chunk-Y33LKJAV.js";import"./chunk-ROLYNLUZ.js";import"./chunk-ZOYALB5L.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-3ICVSFCN.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-MWMNFIBI.js";import"./chunk-R65YCV6G.js";import"./chunk-4EMV5IOT.js";import"./chunk-XQ4KGEVA.js";import"./chunk-TZQIROCS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import"./chunk-B7O3QC5Z.js";var y=(()=>{class i{constructor(r){this.router=r}nextPage(){this.router.navigate(["/onboarding-3"])}previousPage(){this.router.navigate(["/welcome"])}skipOnboarding(){localStorage.setItem("onboardingComplete","true"),this.router.navigate(["/tabs/home"])}static{this.\u0275fac=function(a){return new(a||i)(g(b))}}static{this.\u0275cmp=s({type:i,selectors:[["app-onboarding-2"]],standalone:!0,features:[m],decls:25,vars:0,consts:[[1,"onboarding-bg"],[1,"onboarding-wrapper"],[1,"skip-container"],["fill","clear",1,"skip-btn",3,"click"],[1,"illustration-container"],[1,"delivery-illustration"],["src","assets/icon/locationFinal.png","alt","Real-time Routes",1,"delivery-icon"],["name","checkmark-circle",1,"check-icon"],[1,"content-container"],[1,"onboarding-title"],[1,"onboarding-description"],[1,"indicators-container"],[1,"indicator"],[1,"indicator","active"],[1,"button-container"],[1,"nav-buttons"],["fill","clear",1,"back-btn",3,"click"],["expand","block",1,"next-btn",3,"click"]],template:function(a,c){a&1&&(e(0,"ion-content",0)(1,"div",1)(2,"div",2)(3,"ion-button",3),o("click",function(){return c.skipOnboarding()}),t(4," Skip "),n()(),e(5,"div",4)(6,"div",5),l(7,"img",6)(8,"ion-icon",7),n()(),e(9,"div",8)(10,"h2",9),t(11,"Real-time Routes"),n(),e(12,"p",10),t(13," In every emergency, seconds matter, knowing your safe zone can mean the difference between panic and protection. "),n()(),e(14,"div",11),l(15,"div",12)(16,"div",13)(17,"div",12)(18,"div",12),n(),e(19,"div",14)(20,"div",15)(21,"ion-button",16),o("click",function(){return c.previousPage()}),t(22," Back "),n(),e(23,"ion-button",17),o("click",function(){return c.nextPage()}),t(24," Next "),n()()()()())},dependencies:[v,f,_,x,p,u],styles:[".onboarding-bg[_ngcontent-%COMP%]{--background: white}.onboarding-wrapper[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:100vh;padding:1.5rem;position:relative;justify-content:space-between}.skip-container[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;margin-bottom:2rem}.skip-btn[_ngcontent-%COMP%]{--color: #6c757d;font-size:1rem}.illustration-container[_ngcontent-%COMP%]{flex:0 0 auto;display:flex;align-items:center;justify-content:center;margin:1rem 0}.delivery-illustration[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center;justify-content:center}.delivery-icon[_ngcontent-%COMP%]{width:150px;height:150px;object-fit:contain;filter:drop-shadow(0 4px 8px rgba(40,167,69,.2))}.check-icon[_ngcontent-%COMP%]{position:absolute;top:-20px;right:-20px;font-size:60px;color:#007bff;background:#fff;border-radius:50%}.content-container[_ngcontent-%COMP%]{text-align:center;margin-bottom:1.5rem}.onboarding-title[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;color:#212529;margin-bottom:1rem}.onboarding-description[_ngcontent-%COMP%]{font-size:1.1rem;color:#6c757d;line-height:1.5;margin:0;padding:0 1rem}.indicators-container[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:.5rem;margin-bottom:2rem}.indicator[_ngcontent-%COMP%]{width:8px;height:8px;border-radius:50%;background-color:#dee2e6;transition:all .3s ease}.indicator.active[_ngcontent-%COMP%]{background-color:#007bff;width:24px;border-radius:4px}.button-container[_ngcontent-%COMP%]{margin-bottom:2rem}.nav-buttons[_ngcontent-%COMP%]{display:flex;gap:1rem;align-items:center}.back-btn[_ngcontent-%COMP%]{--color: #6c757d;font-size:1rem;flex:0 0 auto}.next-btn[_ngcontent-%COMP%]{--background: #007bff;--color: white;--border-radius: 25px;font-weight:600;font-size:1.1rem;height:50px;flex:1}"]})}}return i})();export{y as Onboarding2Page};
