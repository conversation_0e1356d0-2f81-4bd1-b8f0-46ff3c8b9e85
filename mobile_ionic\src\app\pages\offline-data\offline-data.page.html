<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="goBack()">
        <ion-icon name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>Offline Data</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="refreshOfflineData()" [disabled]="!isOnline">
        <ion-icon name="refresh"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Offline Data</ion-title>
    </ion-toolbar>
  </ion-header>

  <!-- Network Status -->
  <ion-card>
    <ion-card-header>
      <ion-card-title>
        <ion-icon [name]="isOnline ? 'wifi' : 'wifi-outline'" 
                  [color]="isOnline ? 'success' : 'danger'"></ion-icon>
        Network Status
      </ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-chip [color]="isOnline ? 'success' : 'danger'">
        <ion-icon [name]="isOnline ? 'checkmark-circle' : 'close-circle'"></ion-icon>
        <ion-label>{{ isOnline ? 'Online' : 'Offline' }}</ion-label>
      </ion-chip>
    </ion-card-content>
  </ion-card>

  <!-- Cache Summary -->
  <ion-card *ngIf="offlineDataSummary">
    <ion-card-header>
      <ion-card-title>
        <ion-icon name="archive" color="primary"></ion-icon>
        Cache Summary
      </ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-list>
        <ion-item>
          <ion-icon name="business" slot="start" color="primary"></ion-icon>
          <ion-label>
            <h3>Evacuation Centers</h3>
            <p>{{ offlineDataSummary.evacuationCenters }} centers cached</p>
          </ion-label>
        </ion-item>
        
        <ion-item>
          <ion-icon name="location" slot="start" color="success"></ion-icon>
          <ion-label>
            <h3>User Location</h3>
            <p>{{ offlineDataSummary.userLocation ? 'Cached' : 'Not cached' }}</p>
          </ion-label>
        </ion-item>
        
        <ion-item>
          <ion-icon name="call" slot="start" color="warning"></ion-icon>
          <ion-label>
            <h3>Emergency Contacts</h3>
            <p>{{ offlineDataSummary.emergencyContacts }} contacts cached</p>
          </ion-label>
        </ion-item>
        
        <ion-item>
          <ion-icon name="information-circle" slot="start" color="secondary"></ion-icon>
          <ion-label>
            <h3>Disaster Information</h3>
            <p>{{ offlineDataSummary.disasterInfo }} guides cached</p>
          </ion-label>
        </ion-item>
        
        <ion-item>
          <ion-icon name="folder" slot="start" color="medium"></ion-icon>
          <ion-label>
            <h3>Total Cache Size</h3>
            <p>{{ formatCacheSize(offlineDataSummary.cacheSize) }}</p>
          </ion-label>
        </ion-item>
      </ion-list>
    </ion-card-content>
  </ion-card>

  <!-- Emergency Contacts -->
  <ion-card *ngIf="emergencyContacts.length > 0">
    <ion-card-header>
      <ion-card-title>
        <ion-icon name="call" color="danger"></ion-icon>
        Emergency Contacts
      </ion-card-title>
      <ion-card-subtitle>{{ emergencyContacts.length }} contacts available offline</ion-card-subtitle>
    </ion-card-header>
    <ion-card-content>
      <ion-list>
        <ion-item *ngFor="let contact of emergencyContacts" 
                  button 
                  (click)="callEmergencyNumber(contact)">
          <ion-icon [name]="getContactTypeIcon(contact.type)" 
                    slot="start" 
                    [color]="getContactTypeColor(contact.type)"></ion-icon>
          <ion-label>
            <h3>{{ contact.name }}</h3>
            <p>{{ contact.number }}</p>
            <p class="contact-description">{{ contact.description }}</p>
          </ion-label>
          <ion-chip slot="end" [color]="getContactTypeColor(contact.type)" outline>
            <ion-label>{{ contact.type }}</ion-label>
          </ion-chip>
          <ion-badge slot="end" *ngIf="contact.available24h" color="success">24/7</ion-badge>
        </ion-item>
      </ion-list>
    </ion-card-content>
  </ion-card>

  <!-- Disaster Preparedness Info -->
  <ion-card *ngIf="disasterInfo.length > 0">
    <ion-card-header>
      <ion-card-title>
        <ion-icon name="shield-checkmark" color="success"></ion-icon>
        Disaster Preparedness
      </ion-card-title>
      <ion-card-subtitle>{{ disasterInfo.length }} guides available offline</ion-card-subtitle>
    </ion-card-header>
    <ion-card-content>
      <ion-list>
        <ion-item *ngFor="let info of disasterInfo">
          <ion-icon [name]="getDisasterIcon(info.disasterType)" 
                    slot="start" 
                    color="warning"></ion-icon>
          <ion-label>
            <h3>{{ info.title }}</h3>
            <p>{{ info.instructions.length }} emergency instructions</p>
            <p>{{ info.preparationSteps.length }} preparation steps</p>
          </ion-label>
          <ion-chip slot="end" color="warning" outline>
            <ion-label>{{ info.disasterType }}</ion-label>
          </ion-chip>
        </ion-item>
      </ion-list>
    </ion-card-content>
  </ion-card>

  <!-- Cache Management -->
  <ion-card>
    <ion-card-header>
      <ion-card-title>
        <ion-icon name="settings" color="medium"></ion-icon>
        Cache Management
      </ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-list>
        <ion-item *ngFor="let metadata of cacheMetadata">
          <ion-icon name="document" slot="start" color="medium"></ion-icon>
          <ion-label>
            <h3>{{ metadata.key }}</h3>
            <p>Size: {{ formatCacheSize(metadata.size / 1024) }}</p>
            <p>Updated: {{ formatDate(metadata.lastUpdated) }}</p>
            <p>Expires: {{ formatDate(metadata.expiresAt) }}</p>
          </ion-label>
        </ion-item>
      </ion-list>
      
      <div class="cache-actions">
        <ion-button 
          expand="block" 
          fill="outline" 
          color="primary"
          (click)="refreshOfflineData()"
          [disabled]="!isOnline">
          <ion-icon name="refresh" slot="start"></ion-icon>
          Refresh Data
        </ion-button>
        
        <ion-button 
          expand="block" 
          fill="outline" 
          color="danger"
          (click)="clearAllCache()">
          <ion-icon name="trash" slot="start"></ion-icon>
          Clear All Cache
        </ion-button>
      </div>
    </ion-card-content>
  </ion-card>

</ion-content>
