# Mapbox Migration Documentation

## Overview
This document outlines the migration from OpenStreetMap to Mapbox as the primary tile provider for the WebAlerto application.

## Changes Made

### 1. Laravel Backend Configuration
- **Added Mapbox configuration** to `.env.example`:
  ```
  MAPBOX_ACCESS_TOKEN=your_mapbox_access_token_here
  ```
- **Created mapping configuration file** at `config/mapping.php` with Mapbox settings
- **Cleared compiled views** to ensure new configuration is used

### 2. Web Application Updates
- **MapManager.js**: Updated to use Mapbox tiles instead of OpenStreetMap
- **map.blade.php**: Replaced OSM tile layer with Mapbox tile layer
- **MapSearch.js**: Migrated from Nominatim (OSM) to Mapbox Geocoding API
- **app.blade.php**: Updated comments to reflect Mapbox integration

### 3. Mobile Application Updates
- **map.page.ts**: Updated online tile layer to use Mapbox
- **disaster-map-modal.component.ts**: Replaced OSM tiles with Mapbox
- **earthquake-map.page.ts**: Updated to use Mapbox tiles
- **offline-map.service.ts**: Modified to cache Mapbox tiles for offline use

## Configuration Details

### Mapbox Access Token
The application uses the following Mapbox access token:
```
pk.eyJ1IjoianVucmVsMDcwNDA1IiwiYSI6ImNtYjNocGs1YjBxc2cydnB5OG14NmNzYTIifQ.FGsozY9ibdn28Rg91_msIg
```

### Tile Layer Configuration
- **Style**: `mapbox://styles/mapbox/streets-v12`
- **Tile Size**: 512x512 (with zoomOffset: -1)
- **Max Zoom**: 19
- **Attribution**: Includes both Mapbox and OpenStreetMap credits

### Geocoding Service
- **Search URL**: `https://api.mapbox.com/geocoding/v5/mapbox.places`
- **Reverse Geocoding**: Same endpoint with coordinate-based queries
- **Country Filter**: Limited to Philippines (`country: 'ph'`)

## Features Preserved
- ✅ Map markers and popups
- ✅ Zoom controls and navigation
- ✅ Evacuation center mapping
- ✅ Disaster zone visualization
- ✅ Offline map caching (mobile)
- ✅ Location search functionality
- ✅ Reverse geocoding

## Testing
A test file `test-mapbox.html` has been created to verify the Mapbox integration works correctly.

## Environment Variables Required

### Laravel (.env)
```
MAPBOX_ACCESS_TOKEN=pk.eyJ1IjoianVucmVsMDcwNDA1IiwiYSI6ImNtYjNocGs1YjBxc2cydnB5OG14NmNzYTIifQ.FGsozY9ibdn28Rg91_msIg
```

### Mobile App (environment.ts)
The mobile app already has the Mapbox access token configured in:
- `mobile_ionic/src/environments/environment.ts`
- `mobile_ionic/src/environments/environment.prod.ts`

## Migration Benefits
1. **Better Performance**: Mapbox tiles are optimized for faster loading
2. **Enhanced Styling**: More modern and visually appealing map styles
3. **Better Mobile Support**: Improved rendering on mobile devices
4. **Consistent Experience**: Same tile provider across web and mobile apps
5. **Professional Attribution**: Proper Mapbox attribution and branding

## Rollback Plan
If needed, the migration can be rolled back by:
1. Reverting tile layer URLs to OpenStreetMap
2. Restoring Nominatim geocoding service
3. Updating offline map service to cache OSM tiles
4. Removing Mapbox configuration

## Notes
- The existing Leaflet library is retained for map functionality
- All map features and interactions remain unchanged
- Offline functionality is preserved with Mapbox tile caching
- The migration maintains backward compatibility with existing data
