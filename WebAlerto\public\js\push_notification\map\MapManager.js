export class MapManager {
    constructor(mapId, options = {}) {
        this.map = null;
        this.mapId = mapId;
        this.options = {
            defaultCenter: [10.3157, 123.8854],
            defaultZoom: 13,
            maxZoom: 19,
            minZoom: 1,
            mapboxAccessToken: 'pk.eyJ1IjoianVucmVsMDcwNDA1IiwiYSI6ImNtYjNocGs1YjBxc2cydnB5OG14NmNzYTIifQ.FGsozY9ibdn28Rg91_msIg',
            mapboxStyle: 'mapbox://styles/mapbox/streets-v12',
            ...options
        };
    }

    initialize() {
        this.map = L.map(this.mapId, {
            maxZoom: this.options.maxZoom,
            minZoom: this.options.minZoom
        }).setView(this.options.defaultCenter, this.options.defaultZoom);

        // Use Mapbox tiles instead of OpenStreetMap
        L.tileLayer(`https://api.mapbox.com/styles/v1/mapbox/streets-v12/tiles/{z}/{x}/{y}?access_token=${this.options.mapboxAccessToken}`, {
            attribution: '© <a href="https://www.mapbox.com/about/maps/">Mapbox</a> © <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a> <strong><a href="https://www.mapbox.com/map-feedback/" target="_blank">Improve this map</a></strong>',
            tileSize: 512,
            zoomOffset: -1
        }).addTo(this.map);

        return this.map;
    }

    getMap() {
        return this.map;
    }
}
