#map {
  width: 100%;
  height: 100vh;
  position: relative;
}

ion-toggle {
  margin-right: 10px;
}

/* Blinking effect for temporary marker */
@keyframes blink {
  0% { opacity: 1; }
  50% { opacity: 0.4; }
  100% { opacity: 1; }
}

:host ::ng-deep .blinking {
  animation: blink 1.5s infinite;
}

/* Message overlay on map */
.map-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  pointer-events: none;
}

.message-box {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 15px 20px;
  border-radius: 8px;
  font-size: 18px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}
