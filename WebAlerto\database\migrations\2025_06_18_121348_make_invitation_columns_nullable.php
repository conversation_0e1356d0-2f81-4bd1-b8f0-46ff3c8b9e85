<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invitations', function (Blueprint $table) {
            $table->string('title')->nullable()->change();
            $table->string('first_name')->nullable()->change();
            $table->string('middle_name')->nullable()->change();
            $table->string('last_name')->nullable()->change();
            $table->string('suffix')->nullable()->change();
            $table->string('role')->nullable()->change();
            $table->string('token', 64)->nullable()->change();
            $table->timestamp('expires_at')->nullable()->change();
            $table->timestamp('accepted_at')->nullable()->change();
            $table->foreignId('invited_by')->nullable()->change();
            $table->enum('status', ['pending', 'accepted', 'expired'])->nullable()->change();
            $table->foreignId('user_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invitations', function (Blueprint $table) {
            $table->string('title')->nullable(false)->change();
            $table->string('first_name')->nullable(false)->change();
            $table->string('middle_name')->nullable(false)->change();
            $table->string('last_name')->nullable(false)->change();
            $table->string('suffix')->nullable(false)->change();
            $table->string('role')->nullable(false)->change();
            $table->string('token', 64)->nullable(false)->change();
            $table->timestamp('expires_at')->nullable(false)->change();
            $table->timestamp('accepted_at')->nullable(false)->change();
            $table->foreignId('invited_by')->nullable(false)->change();
            $table->enum('status', ['pending', 'accepted', 'expired'])->nullable(false)->change();
            $table->foreignId('user_id')->nullable(false)->change();
        });
    }
};
