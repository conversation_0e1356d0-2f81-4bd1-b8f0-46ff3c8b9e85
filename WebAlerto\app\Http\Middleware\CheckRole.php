<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CheckRole
{
    public function handle(Request $request, Closure $next, ...$roles)
    {
        if (!$request->user()) {
            Log::error('User not authenticated');
            abort(403, 'Unauthorized action. Please login.');
        }

        $userRole = $request->user()->role;
        Log::info('Checking user role', [
            'user_id' => $request->user()->id,
            'user_role' => $userRole,
            'required_roles' => $roles
        ]);

        if (!in_array($userRole, $roles)) {
            Log::warning('User role mismatch', [
                'user_id' => $request->user()->id,
                'user_role' => $userRole,
                'required_roles' => $roles
            ]);
            abort(403, 'Unauthorized action. Insufficient permissions.');
        }

        return $next($request);
    }
}
