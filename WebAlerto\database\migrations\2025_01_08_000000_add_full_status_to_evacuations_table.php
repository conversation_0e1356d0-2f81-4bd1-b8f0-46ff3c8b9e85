<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('evacuations', function (Blueprint $table) {
            // Modify the status column to include 'Full' option
            $table->enum('status', ['Active', 'Inactive', 'Under Maintenance', 'Full'])->default('Active')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('evacuations', function (Blueprint $table) {
            // Revert back to original status options
            $table->enum('status', ['Active', 'Inactive', 'Under Maintenance'])->default('Active')->change();
        });
    }
};
