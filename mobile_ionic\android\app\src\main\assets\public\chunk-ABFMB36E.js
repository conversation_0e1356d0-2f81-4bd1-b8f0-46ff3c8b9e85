import{a as _}from"./chunk-2Q4P6BHH.js";import{d as H,e as B,i as L}from"./chunk-P4NBILQA.js";import"./chunk-ICC5SWU3.js";import{a as j}from"./chunk-H2PHEXKY.js";import{a as Z}from"./chunk-TKDDGDM7.js";import"./chunk-WMEG6PAA.js";import{d as V}from"./chunk-V72YNCQ7.js";import{a as D}from"./chunk-HC6MZPB3.js";import{a as $,b as N}from"./chunk-FQJQVDRA.js";import{a as z,b as I}from"./chunk-Y33LKJAV.js";import{a as m,b as p}from"./chunk-ROLYNLUZ.js";import{b as u,c as S,d as k,f as a,g as v,j as x,k as y,l as P}from"./chunk-ZOYALB5L.js";import{b as M,c as T,e as O,m as E,s as W}from"./chunk-UYQ7EZNZ.js";import{a as F}from"./chunk-3ICVSFCN.js";import"./chunk-MCRJI3T3.js";import{a as f,e as R,f as Y}from"./chunk-BAKMWPBW.js";import{h as c}from"./chunk-B7O3QC5Z.js";var Q="html.plt-mobile ion-app{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}html.plt-mobile ion-app [contenteditable]{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}ion-app.force-statusbar-padding{--ion-safe-area-top:20px}",tt=Q,ot=class{constructor(t){u(this,t)}componentDidLoad(){nt(()=>c(this,null,function*(){let t=m(window,"hybrid");if(f.getBoolean("_testing")||import("./chunk-4WTDJNQ3.js").then(n=>n.startTapClick(f)),f.getBoolean("statusTap",t)&&import("./chunk-XFTLDHHO.js").then(n=>n.startStatusTap()),f.getBoolean("inputShims",et())){let n=m(window,"ios")?"ios":"android";import("./chunk-AGU5VUXT.js").then(r=>r.startInputShims(f,n))}let o=yield import("./chunk-MAEQNGSV.js"),e=t||F();f.getBoolean("hardwareBackButton",e)?o.startHardwareBackButton():(F()&&R("[ion-app] - experimentalCloseWatcher was set to `true`, but hardwareBackButton was set to `false`. Both config options must be `true` for the Close Watcher API to be used."),o.blockHardwareBackButton()),typeof window<"u"&&import("./chunk-VVWBOFYP.js").then(n=>n.startKeyboardAssist(window)),import("./chunk-HEGLPHQ4.js").then(n=>this.focusVisible=n.startFocusVisible())}))}setFocus(t){return c(this,null,function*(){this.focusVisible&&this.focusVisible.setFocus(t)})}render(){let t=p(this);return a(v,{key:"03aa892f986330078d112b1e8b010df98fa7e39e",class:{[t]:!0,"ion-page":!0,"force-statusbar-padding":f.getBoolean("_forceStatusbarPadding")}})}get el(){return x(this)}},et=()=>!!(m(window,"ios")&&m(window,"mobile")||m(window,"android")&&m(window,"mobileweb")),nt=t=>{"requestIdleCallback"in window?window.requestIdleCallback(t):setTimeout(t,32)};ot.style=tt;var it=".sc-ion-buttons-ios-h{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:99}.sc-ion-buttons-ios-s ion-button{--padding-top:0;--padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-buttons-ios-s ion-button{--padding-top:3px;--padding-bottom:3px;--padding-start:5px;--padding-end:5px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;min-height:32px}.sc-ion-buttons-ios-s .button-has-icon-only{--padding-top:0;--padding-bottom:0}.sc-ion-buttons-ios-s ion-button:not(.button-round){--border-radius:4px}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button{--color:initial;--border-color:initial;--background-focused:var(--ion-color-contrast)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-solid,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-solid{--background:var(--ion-color-contrast);--background-focused:#000;--background-focused-opacity:.12;--background-activated:#000;--background-activated-opacity:.12;--background-hover:var(--ion-color-base);--background-hover-opacity:0.45;--color:var(--ion-color-base);--color-focused:var(--ion-color-base)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-clear,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-clear{--color-activated:var(--ion-color-contrast);--color-focused:var(--ion-color-contrast)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-outline,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-outline{--color-activated:var(--ion-color-base);--color-focused:var(--ion-color-contrast);--background-activated:var(--ion-color-contrast)}.sc-ion-buttons-ios-s .button-clear,.sc-ion-buttons-ios-s .button-outline{--background-activated:transparent;--background-focused:currentColor;--background-hover:transparent}.sc-ion-buttons-ios-s .button-solid:not(.ion-color){--background-focused:#000;--background-focused-opacity:.12;--background-activated:#000;--background-activated-opacity:.12}.sc-ion-buttons-ios-s ion-icon[slot=start]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:1.41em;line-height:0.67}.sc-ion-buttons-ios-s ion-icon[slot=end]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-start:0.4em;margin-inline-start:0.4em;font-size:1.41em;line-height:0.67}.sc-ion-buttons-ios-s ion-icon[slot=icon-only]{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;font-size:1.65em;line-height:0.67}",rt=it,st=".sc-ion-buttons-md-h{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:99}.sc-ion-buttons-md-s ion-button{--padding-top:0;--padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-buttons-md-s ion-button{--padding-top:3px;--padding-bottom:3px;--padding-start:8px;--padding-end:8px;--box-shadow:none;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;min-height:32px}.sc-ion-buttons-md-s .button-has-icon-only{--padding-top:0;--padding-bottom:0}.sc-ion-buttons-md-s ion-button:not(.button-round){--border-radius:2px}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button{--color:initial;--color-focused:var(--ion-color-contrast);--color-hover:var(--ion-color-contrast);--background-activated:transparent;--background-focused:var(--ion-color-contrast);--background-hover:var(--ion-color-contrast)}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button-solid,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button-solid{--background:var(--ion-color-contrast);--background-activated:transparent;--background-focused:var(--ion-color-shade);--background-hover:var(--ion-color-base);--color:var(--ion-color-base);--color-focused:var(--ion-color-base);--color-hover:var(--ion-color-base)}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button-outline,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button-outline{--border-color:var(--ion-color-contrast)}.sc-ion-buttons-md-s .button-has-icon-only.button-clear{--padding-top:12px;--padding-end:12px;--padding-bottom:12px;--padding-start:12px;--border-radius:50%;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;width:3rem;height:3rem}.sc-ion-buttons-md-s .button{--background-hover:currentColor}.sc-ion-buttons-md-s .button-solid{--color:var(--ion-toolbar-background, var(--ion-background-color, #fff));--background:var(--ion-toolbar-color, var(--ion-text-color, #424242));--background-activated:transparent;--background-focused:currentColor}.sc-ion-buttons-md-s .button-outline{--color:initial;--background:transparent;--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor;--border-color:currentColor}.sc-ion-buttons-md-s .button-clear{--color:initial;--background:transparent;--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor}.sc-ion-buttons-md-s ion-icon[slot=start]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:1.4em}.sc-ion-buttons-md-s ion-icon[slot=end]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-start:0.4em;margin-inline-start:0.4em;font-size:1.4em}.sc-ion-buttons-md-s ion-icon[slot=icon-only]{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;font-size:1.8em}",at=st,Qt=(()=>{let t=class{constructor(o){u(this,o),this.collapse=!1}render(){let o=p(this);return a(v,{key:"58c1fc5eb867d0731c63549b1ccb3ec3bbbe6e1b",class:{[o]:!0,"buttons-collapse":this.collapse}},a("slot",{key:"0c8f95b9840c8fa0c4e50be84c5159620a3eb5c8"}))}};return t.style={ios:rt,md:at},t})(),lt=':host{--background:var(--ion-background-color, #fff);--color:var(--ion-text-color, #000);--padding-top:0px;--padding-bottom:0px;--padding-start:0px;--padding-end:0px;--keyboard-offset:0px;--offset-top:0px;--offset-bottom:0px;--overflow:auto;display:block;position:relative;-ms-flex:1;flex:1;width:100%;height:100%;margin:0 !important;padding:0 !important;font-family:var(--ion-font-family, inherit);contain:size style}:host(.ion-color) .inner-scroll{background:var(--ion-color-base);color:var(--ion-color-contrast)}#background-content{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);position:absolute;background:var(--background)}.inner-scroll{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:calc(var(--padding-top) + var(--offset-top));padding-bottom:calc(var(--padding-bottom) + var(--keyboard-offset) + var(--offset-bottom));position:absolute;color:var(--color);-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;-ms-touch-action:pan-x pan-y pinch-zoom;touch-action:pan-x pan-y pinch-zoom}.scroll-y,.scroll-x{-webkit-overflow-scrolling:touch;z-index:0;will-change:scroll-position}.scroll-y{overflow-y:var(--overflow);overscroll-behavior-y:contain}.scroll-x{overflow-x:var(--overflow);overscroll-behavior-x:contain}.overscroll::before,.overscroll::after{position:absolute;width:1px;height:1px;content:""}.overscroll::before{bottom:-1px}.overscroll::after{top:-1px}:host(.content-sizing){display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;min-height:0;contain:none}:host(.content-sizing) .inner-scroll{position:relative;top:0;bottom:0;margin-top:calc(var(--offset-top) * -1);margin-bottom:calc(var(--offset-bottom) * -1)}.transition-effect{display:none;position:absolute;width:100%;height:100vh;opacity:0;pointer-events:none}:host(.content-ltr) .transition-effect{left:-100%;}:host(.content-rtl) .transition-effect{right:-100%;}.transition-cover{position:absolute;right:0;width:100%;height:100%;background:black;opacity:0.1}.transition-shadow{display:block;position:absolute;width:100%;height:100%;-webkit-box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03);box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03)}:host(.content-ltr) .transition-shadow{right:0;}:host(.content-rtl) .transition-shadow{left:0;-webkit-transform:scaleX(-1);transform:scaleX(-1)}::slotted([slot=fixed]){position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0)}',ct=lt,dt=class{constructor(t){u(this,t),this.ionScrollStart=y(this,"ionScrollStart",7),this.ionScroll=y(this,"ionScroll",7),this.ionScrollEnd=y(this,"ionScrollEnd",7),this.watchDog=null,this.isScrolling=!1,this.lastScroll=0,this.queued=!1,this.cTop=-1,this.cBottom=-1,this.isMainContent=!0,this.resizeTimeout=null,this.inheritedAttributes={},this.tabsElement=null,this.detail={scrollTop:0,scrollLeft:0,type:"scroll",event:void 0,startX:0,startY:0,startTime:0,currentX:0,currentY:0,velocityX:0,velocityY:0,deltaX:0,deltaY:0,currentTime:0,data:void 0,isScrolling:!0},this.color=void 0,this.fullscreen=!1,this.fixedSlotPlacement="after",this.forceOverscroll=void 0,this.scrollX=!1,this.scrollY=!0,this.scrollEvents=!1}componentWillLoad(){this.inheritedAttributes=O(this.el)}connectedCallback(){if(this.isMainContent=this.el.closest("ion-menu, ion-popover, ion-modal")===null,T(this.el)){let t=this.tabsElement=this.el.closest("ion-tabs");t!==null&&(this.tabsLoadCallback=()=>this.resize(),t.addEventListener("ionTabBarLoaded",this.tabsLoadCallback))}}disconnectedCallback(){if(this.onScrollEnd(),T(this.el)){let{tabsElement:t,tabsLoadCallback:o}=this;t!==null&&o!==void 0&&t.removeEventListener("ionTabBarLoaded",o),this.tabsElement=null,this.tabsLoadCallback=void 0}}onResize(){this.resizeTimeout&&(clearTimeout(this.resizeTimeout),this.resizeTimeout=null),this.resizeTimeout=setTimeout(()=>{this.el.offsetParent!==null&&this.resize()},100)}shouldForceOverscroll(){let{forceOverscroll:t}=this,o=p(this);return t===void 0?o==="ios"&&m("ios"):t}resize(){this.fullscreen?S(()=>this.readDimensions()):(this.cTop!==0||this.cBottom!==0)&&(this.cTop=this.cBottom=0,P(this))}readDimensions(){let t=ht(this.el),o=Math.max(this.el.offsetTop,0),e=Math.max(t.offsetHeight-o-this.el.offsetHeight,0);(o!==this.cTop||e!==this.cBottom)&&(this.cTop=o,this.cBottom=e,P(this))}onScroll(t){let o=Date.now(),e=!this.isScrolling;this.lastScroll=o,e&&this.onScrollStart(),!this.queued&&this.scrollEvents&&(this.queued=!0,S(n=>{this.queued=!1,this.detail.event=t,pt(this.detail,this.scrollEl,n,e),this.ionScroll.emit(this.detail)}))}getScrollElement(){return c(this,null,function*(){return this.scrollEl||(yield new Promise(t=>M(this.el,t))),Promise.resolve(this.scrollEl)})}getBackgroundElement(){return c(this,null,function*(){return this.backgroundContentEl||(yield new Promise(t=>M(this.el,t))),Promise.resolve(this.backgroundContentEl)})}scrollToTop(t=0){return this.scrollToPoint(void 0,0,t)}scrollToBottom(t=0){return c(this,null,function*(){let o=yield this.getScrollElement(),e=o.scrollHeight-o.clientHeight;return this.scrollToPoint(void 0,e,t)})}scrollByPoint(t,o,e){return c(this,null,function*(){let n=yield this.getScrollElement();return this.scrollToPoint(t+n.scrollLeft,o+n.scrollTop,e)})}scrollToPoint(t,o,e=0){return c(this,null,function*(){let n=yield this.getScrollElement();if(e<32){o!=null&&(n.scrollTop=o),t!=null&&(n.scrollLeft=t);return}let r,i=0,s=new Promise(g=>r=g),l=n.scrollTop,d=n.scrollLeft,h=o!=null?o-l:0,b=t!=null?t-d:0,w=g=>{let q=Math.min(1,(g-i)/e)-1,A=Math.pow(q,3)+1;h!==0&&(n.scrollTop=Math.floor(A*h+l)),b!==0&&(n.scrollLeft=Math.floor(A*b+d)),A<1?requestAnimationFrame(w):r()};return requestAnimationFrame(g=>{i=g,w(g)}),s})}onScrollStart(){this.isScrolling=!0,this.ionScrollStart.emit({isScrolling:!0}),this.watchDog&&clearInterval(this.watchDog),this.watchDog=setInterval(()=>{this.lastScroll<Date.now()-120&&this.onScrollEnd()},100)}onScrollEnd(){this.watchDog&&clearInterval(this.watchDog),this.watchDog=null,this.isScrolling&&(this.isScrolling=!1,this.ionScrollEnd.emit({isScrolling:!1}))}render(){let{fixedSlotPlacement:t,inheritedAttributes:o,isMainContent:e,scrollX:n,scrollY:r,el:i}=this,s=j(i)?"rtl":"ltr",l=p(this),d=this.shouldForceOverscroll(),h=l==="ios";return this.resize(),a(v,Object.assign({key:"f2a24aa66dbf5c76f9d4b06f708eb73cadc239df",role:e?"main":void 0,class:I(this.color,{[l]:!0,"content-sizing":z("ion-popover",this.el),overscroll:d,[`content-${s}`]:!0}),style:{"--offset-top":`${this.cTop}px`,"--offset-bottom":`${this.cBottom}px`}},o),a("div",{key:"6480ca7648b278abb36477b3838bccbcd4995e2a",ref:b=>this.backgroundContentEl=b,id:"background-content",part:"background"}),t==="before"?a("slot",{name:"fixed"}):null,a("div",{key:"29a23b663f5f0215bb000820c01e1814c0d55985",class:{"inner-scroll":!0,"scroll-x":n,"scroll-y":r,overscroll:(n||r)&&d},ref:b=>this.scrollEl=b,onScroll:this.scrollEvents?b=>this.onScroll(b):void 0,part:"scroll"},a("slot",{key:"0fe1bd05609a4b88ae2ce9addf5d5dc5dc1806f0"})),h?a("div",{class:"transition-effect"},a("div",{class:"transition-cover"}),a("div",{class:"transition-shadow"})):null,t==="after"?a("slot",{name:"fixed"}):null)}get el(){return x(this)}},bt=t=>{var o;return t.parentElement?t.parentElement:!((o=t.parentNode)===null||o===void 0)&&o.host?t.parentNode.host:null},ht=t=>{let o=t.closest("ion-tabs");if(o)return o;let e=t.closest("ion-app, ion-page, .ion-page, page-inner, .popover-content");return e||bt(t)},pt=(t,o,e,n)=>{let r=t.currentX,i=t.currentY,s=t.currentTime,l=o.scrollLeft,d=o.scrollTop,h=e-s;if(n&&(t.startTime=e,t.startX=l,t.startY=d,t.velocityX=t.velocityY=0),t.currentTime=e,t.currentX=t.scrollLeft=l,t.currentY=t.scrollTop=d,t.deltaX=l-t.startX,t.deltaY=d-t.startY,h>0&&h<100){let b=(l-r)/h,w=(d-i)/h;t.velocityX=b*.7+t.velocityX*.3,t.velocityY=w*.7+t.velocityY*.3}};dt.style=ct;var U=(t,o)=>{S(()=>{let e=t.scrollTop,n=t.scrollHeight-t.clientHeight,r=10,i=n-r,s=e-i,l=E(0,1-s/r,1);k(()=>{o.style.setProperty("--opacity-scale",l.toString())})})},ft="ion-footer{display:block;position:relative;-ms-flex-order:1;order:1;width:100%;z-index:10}ion-footer.footer-toolbar-padding ion-toolbar:last-of-type{padding-bottom:var(--ion-safe-area-bottom, 0)}.footer-ios ion-toolbar:first-of-type{--border-width:0.55px 0 0}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.footer-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.footer-translucent-ios ion-toolbar{--opacity:.8}}.footer-ios.ion-no-border ion-toolbar:first-of-type{--border-width:0}.footer-collapse-fade ion-toolbar{--opacity-scale:inherit}",ut=ft,gt="ion-footer{display:block;position:relative;-ms-flex-order:1;order:1;width:100%;z-index:10}ion-footer.footer-toolbar-padding ion-toolbar:last-of-type{padding-bottom:var(--ion-safe-area-bottom, 0)}.footer-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.footer-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}",mt=gt,to=(()=>{let t=class{constructor(o){u(this,o),this.keyboardCtrl=null,this.checkCollapsibleFooter=()=>{if(p(this)!=="ios")return;let{collapse:n}=this,r=n==="fade";if(this.destroyCollapsibleFooter(),r){let i=this.el.closest("ion-app,ion-page,.ion-page,page-inner"),s=i?B(i):null;if(!s){L(this.el);return}this.setupFadeFooter(s)}},this.setupFadeFooter=e=>c(this,null,function*(){let n=this.scrollEl=yield H(e);this.contentScrollCallback=()=>{U(n,this.el)},n.addEventListener("scroll",this.contentScrollCallback),U(n,this.el)}),this.keyboardVisible=!1,this.collapse=void 0,this.translucent=!1}componentDidLoad(){this.checkCollapsibleFooter()}componentDidUpdate(){this.checkCollapsibleFooter()}connectedCallback(){return c(this,null,function*(){this.keyboardCtrl=yield _((o,e)=>c(this,null,function*(){o===!1&&e!==void 0&&(yield e),this.keyboardVisible=o}))})}disconnectedCallback(){this.keyboardCtrl&&this.keyboardCtrl.destroy()}destroyCollapsibleFooter(){this.scrollEl&&this.contentScrollCallback&&(this.scrollEl.removeEventListener("scroll",this.contentScrollCallback),this.contentScrollCallback=void 0)}render(){let{translucent:o,collapse:e}=this,n=p(this),r=this.el.closest("ion-tabs"),i=r?.querySelector(":scope > ion-tab-bar");return a(v,{key:"ddc228f1a1e7fa4f707dccf74db2490ca3241137",role:"contentinfo",class:{[n]:!0,[`footer-${n}`]:!0,"footer-translucent":o,[`footer-translucent-${n}`]:o,"footer-toolbar-padding":!this.keyboardVisible&&(!i||i.slot!=="bottom"),[`footer-collapse-${e}`]:e!==void 0}},n==="ios"&&o&&a("div",{key:"e16ed4963ff94e06de77eb8038201820af73937c",class:"footer-background"}),a("slot",{key:"f186934febf85d37133d9351a96c1a64b0a4b203"}))}get el(){return x(this)}};return t.style={ios:ut,md:mt},t})(),vt="all 0.2s ease-in-out",K=t=>{let o=document.querySelector(`${t}.ion-cloned-element`);if(o!==null)return o;let e=document.createElement(t);return e.classList.add("ion-cloned-element"),e.style.setProperty("display","none"),document.body.appendChild(e),e},G=t=>{if(!t)return;let o=t.querySelectorAll("ion-toolbar");return{el:t,toolbars:Array.from(o).map(e=>{let n=e.querySelector("ion-title");return{el:e,background:e.shadowRoot.querySelector(".toolbar-background"),ionTitleEl:n,innerTitleEl:n?n.shadowRoot.querySelector(".toolbar-title"):null,ionButtonsEl:Array.from(e.querySelectorAll("ion-buttons"))}})}},xt=(t,o,e)=>{S(()=>{let n=t.scrollTop,r=E(1,1+-n/500,1.1);e.querySelector("ion-refresher.refresher-native")===null&&k(()=>{wt(o.toolbars,r)})})},X=(t,o)=>{t.collapse!=="fade"&&(o===void 0?t.style.removeProperty("--opacity-scale"):t.style.setProperty("--opacity-scale",o.toString()))},yt=(t,o,e)=>{if(!t[0].isIntersecting)return;let n=t[0].intersectionRatio>.9||e<=0?0:(1-t[0].intersectionRatio)*100/75;X(o.el,n===1?void 0:n)},kt=(t,o,e,n)=>{k(()=>{let r=n.scrollTop;yt(t,o,r);let i=t[0],s=i.intersectionRect,l=s.width*s.height,d=i.rootBounds.width*i.rootBounds.height,h=l===0&&d===0,b=Math.abs(s.left-i.boundingClientRect.left),w=Math.abs(s.right-i.boundingClientRect.right),g=l>0&&(b>=5||w>=5);h||g||(i.isIntersecting?(C(o,!1),C(e)):(s.x===0&&s.y===0||s.width!==0&&s.height!==0)&&r>0&&(C(o),C(e,!1),X(o.el)))})},C=(t,o=!0)=>{let e=t.el,r=t.toolbars.map(i=>i.ionTitleEl);o?(e.classList.remove("header-collapse-condense-inactive"),r.forEach(i=>{i&&i.removeAttribute("aria-hidden")})):(e.classList.add("header-collapse-condense-inactive"),r.forEach(i=>{i&&i.setAttribute("aria-hidden","true")}))},wt=(t=[],o=1,e=!1)=>{t.forEach(n=>{let r=n.ionTitleEl,i=n.innerTitleEl;!r||r.size!=="large"||(i.style.transition=e?vt:"",i.style.transform=`scale3d(${o}, ${o}, 1)`)})},J=(t,o,e)=>{S(()=>{let n=t.scrollTop,r=o.clientHeight,i=e?e.clientHeight:0;if(e!==null&&n<i){o.style.setProperty("--opacity-scale","0"),t.style.setProperty("clip-path",`inset(${r}px 0px 0px 0px)`);return}let s=n-i,d=E(0,s/10,1);k(()=>{t.style.removeProperty("clip-path"),o.style.setProperty("--opacity-scale",d.toString())})})},St="ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-ios ion-toolbar:last-of-type{--border-width:0 0 0.55px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.header-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.header-translucent-ios ion-toolbar{--opacity:.8}.header-collapse-condense-inactive .header-background{-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}}.header-ios.ion-no-border ion-toolbar:last-of-type{--border-width:0}.header-collapse-fade ion-toolbar{--opacity-scale:inherit}.header-collapse-condense{z-index:9}.header-collapse-condense ion-toolbar{position:-webkit-sticky;position:sticky;top:0}.header-collapse-condense ion-toolbar:first-of-type{padding-top:0px;z-index:1}.header-collapse-condense ion-toolbar{--background:var(--ion-background-color, #fff);z-index:0}.header-collapse-condense ion-toolbar:last-of-type{--border-width:0px}.header-collapse-condense ion-toolbar ion-searchbar{padding-top:0px;padding-bottom:13px}.header-collapse-main{--opacity-scale:1}.header-collapse-main ion-toolbar{--opacity-scale:inherit}.header-collapse-main ion-toolbar.in-toolbar ion-title,.header-collapse-main ion-toolbar.in-toolbar ion-buttons{-webkit-transition:all 0.2s ease-in-out;transition:all 0.2s ease-in-out}.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-buttons.buttons-collapse{opacity:0;pointer-events:none}.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-buttons.buttons-collapse{visibility:hidden}ion-header.header-ios:not(.header-collapse-main):has(~ion-content ion-header.header-ios[collapse=condense],~ion-content ion-header.header-ios.header-collapse-condense){opacity:0}",Ct=St,Tt="ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.header-collapse-condense{display:none}.header-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}",Et=Tt,oo=(()=>{let t=class{constructor(o){u(this,o),this.inheritedAttributes={},this.setupFadeHeader=(e,n)=>c(this,null,function*(){let r=this.scrollEl=yield H(e);this.contentScrollCallback=()=>{J(this.scrollEl,this.el,n)},r.addEventListener("scroll",this.contentScrollCallback),J(this.scrollEl,this.el,n)}),this.collapse=void 0,this.translucent=!1}componentWillLoad(){this.inheritedAttributes=O(this.el)}componentDidLoad(){this.checkCollapsibleHeader()}componentDidUpdate(){this.checkCollapsibleHeader()}disconnectedCallback(){this.destroyCollapsibleHeader()}checkCollapsibleHeader(){return c(this,null,function*(){if(p(this)!=="ios")return;let{collapse:e}=this,n=e==="condense",r=e==="fade";if(this.destroyCollapsibleHeader(),n){let i=this.el.closest("ion-app,ion-page,.ion-page,page-inner"),s=i?B(i):null;k(()=>{let l=K("ion-title");l.size="large",K("ion-back-button")}),yield this.setupCondenseHeader(s,i)}else if(r){let i=this.el.closest("ion-app,ion-page,.ion-page,page-inner"),s=i?B(i):null;if(!s){L(this.el);return}let l=s.querySelector('ion-header[collapse="condense"]');yield this.setupFadeHeader(s,l)}})}destroyCollapsibleHeader(){this.intersectionObserver&&(this.intersectionObserver.disconnect(),this.intersectionObserver=void 0),this.scrollEl&&this.contentScrollCallback&&(this.scrollEl.removeEventListener("scroll",this.contentScrollCallback),this.contentScrollCallback=void 0),this.collapsibleMainHeader&&(this.collapsibleMainHeader.classList.remove("header-collapse-main"),this.collapsibleMainHeader=void 0)}setupCondenseHeader(o,e){return c(this,null,function*(){if(!o||!e){L(this.el);return}if(typeof IntersectionObserver>"u")return;this.scrollEl=yield H(o);let n=e.querySelectorAll("ion-header");if(this.collapsibleMainHeader=Array.from(n).find(l=>l.collapse!=="condense"),!this.collapsibleMainHeader)return;let r=G(this.collapsibleMainHeader),i=G(this.el);if(!r||!i)return;C(r,!1),X(r.el,0);let s=l=>{kt(l,r,i,this.scrollEl)};this.intersectionObserver=new IntersectionObserver(s,{root:o,threshold:[.25,.3,.4,.5,.6,.7,.8,.9,1]}),this.intersectionObserver.observe(i.toolbars[i.toolbars.length-1].el),this.contentScrollCallback=()=>{xt(this.scrollEl,i,o)},this.scrollEl.addEventListener("scroll",this.contentScrollCallback),k(()=>{this.collapsibleMainHeader!==void 0&&this.collapsibleMainHeader.classList.add("header-collapse-main")})})}render(){let{translucent:o,inheritedAttributes:e}=this,n=p(this),r=this.collapse||"none",i=z("ion-menu",this.el)?"none":"banner";return a(v,Object.assign({key:"b6cc27f0b08afc9fcc889683525da765d80ba672",role:i,class:{[n]:!0,[`header-${n}`]:!0,"header-translucent":this.translucent,[`header-collapse-${r}`]:!0,[`header-translucent-${n}`]:this.translucent}},e),n==="ios"&&o&&a("div",{key:"395766d4dcee3398bc91960db21f922095292f14",class:"header-background"}),a("slot",{key:"09a67ece27b258ff1248805d43d92a49b2c6859a"}))}get el(){return x(this)}};return t.style={ios:Ct,md:Et},t})(),zt=":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}",It=zt,eo=(()=>{let t=class{constructor(o){u(this,o),this.ionNavWillLoad=y(this,"ionNavWillLoad",7),this.ionNavWillChange=y(this,"ionNavWillChange",3),this.ionNavDidChange=y(this,"ionNavDidChange",3),this.lockController=Z(),this.gestureOrAnimationInProgress=!1,this.mode=p(this),this.delegate=void 0,this.animated=!0,this.animation=void 0,this.swipeHandler=void 0}swipeHandlerChanged(){this.gesture&&this.gesture.enable(this.swipeHandler!==void 0)}connectedCallback(){return c(this,null,function*(){let o=()=>{this.gestureOrAnimationInProgress=!0,this.swipeHandler&&this.swipeHandler.onStart()};this.gesture=(yield import("./chunk-DOLRGU6D.js")).createSwipeBackGesture(this.el,()=>!this.gestureOrAnimationInProgress&&!!this.swipeHandler&&this.swipeHandler.canStart(),()=>o(),e=>{var n;return(n=this.ani)===null||n===void 0?void 0:n.progressStep(e)},(e,n,r)=>{if(this.ani){this.ani.onFinish(()=>{this.gestureOrAnimationInProgress=!1,this.swipeHandler&&this.swipeHandler.onEnd(e)},{oneTimeCallback:!0});let i=e?-.001:.001;e?i+=D([0,0],[.32,.72],[0,1],[1,1],n)[0]:(this.ani.easing("cubic-bezier(1, 0, 0.68, 0.28)"),i+=D([0,0],[1,0],[.68,.28],[1,1],n)[0]),this.ani.progressEnd(e?1:0,i,r)}else this.gestureOrAnimationInProgress=!1}),this.swipeHandlerChanged()})}componentWillLoad(){this.ionNavWillLoad.emit()}disconnectedCallback(){this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}commit(o,e,n){return c(this,null,function*(){let r=yield this.lockController.lock(),i=!1;try{i=yield this.transition(o,e,n)}catch(s){Y("[ion-router-outlet] - Exception in commit:",s)}return r(),i})}setRouteId(o,e,n,r){return c(this,null,function*(){return{changed:yield this.setRoot(o,e,{duration:n==="root"?0:void 0,direction:n==="back"?"back":"forward",animationBuilder:r}),element:this.activeEl}})}getRouteId(){return c(this,null,function*(){let o=this.activeEl;return o?{id:o.tagName,element:o,params:this.activeParams}:void 0})}setRoot(o,e,n){return c(this,null,function*(){if(this.activeComponent===o&&W(e,this.activeParams))return!1;let r=this.activeEl,i=yield $(this.delegate,this.el,o,["ion-page","ion-page-invisible"],e);return this.activeComponent=o,this.activeEl=i,this.activeParams=e,yield this.commit(i,r,n),yield N(this.delegate,r),!0})}transition(r,i){return c(this,arguments,function*(o,e,n={}){if(e===o)return!1;this.ionNavWillChange.emit();let{el:s,mode:l}=this,d=this.animated&&f.getBoolean("animated",!0),h=n.animationBuilder||this.animation||f.get("navAnimation");return yield V(Object.assign(Object.assign({mode:l,animated:d,enteringEl:o,leavingEl:e,baseEl:s,deepWait:T(s),progressCallback:n.progressAnimation?b=>{b!==void 0&&!this.gestureOrAnimationInProgress?(this.gestureOrAnimationInProgress=!0,b.onFinish(()=>{this.gestureOrAnimationInProgress=!1,this.swipeHandler&&this.swipeHandler.onEnd(!1)},{oneTimeCallback:!0}),b.progressEnd(0,0,0)):this.ani=b}:void 0},n),{animationBuilder:h})),this.ionNavDidChange.emit(),!0})}render(){return a("slot",{key:"e34e02b5154172c8d5cdd187b6ea58119b6946eb"})}get el(){return x(this)}static get watchers(){return{swipeHandler:["swipeHandlerChanged"]}}};return t.style=It,t})(),Ht=":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{top:0;-webkit-padding-start:90px;padding-inline-start:90px;-webkit-padding-end:90px;padding-inline-end:90px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);position:absolute;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0);font-size:min(1.0625rem, 20.4px);font-weight:600;text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host{inset-inline-start:0}:host(.title-small){-webkit-padding-start:9px;padding-inline-start:9px;-webkit-padding-end:9px;padding-inline-end:9px;padding-top:6px;padding-bottom:16px;position:relative;font-size:min(0.8125rem, 23.4px);font-weight:normal}:host(.title-large){-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:2px;padding-bottom:4px;-webkit-transform-origin:left center;transform-origin:left center;position:static;-ms-flex-align:end;align-items:flex-end;min-width:100%;font-size:min(2.125rem, 61.2px);font-weight:700;text-align:start}:host(.title-large.title-rtl){-webkit-transform-origin:right center;transform-origin:right center}:host(.title-large.ion-cloned-element){--color:var(--ion-text-color, #000);font-family:var(--ion-font-family)}:host(.title-large) .toolbar-title{-webkit-transform-origin:inherit;transform-origin:inherit;width:auto}:host-context([dir=rtl]):host(.title-large) .toolbar-title,:host-context([dir=rtl]).title-large .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}@supports selector(:dir(rtl)){:host(.title-large:dir(rtl)) .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}}",Bt=Ht,Lt=":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:0;padding-bottom:0;font-size:1.25rem;font-weight:500;letter-spacing:0.0125em}:host(.title-small){width:100%;height:100%;font-size:0.9375rem;font-weight:normal}",Pt=Lt,no=(()=>{let t=class{constructor(o){u(this,o),this.ionStyle=y(this,"ionStyle",7),this.color=void 0,this.size=void 0}sizeChanged(){this.emitStyle()}connectedCallback(){this.emitStyle()}emitStyle(){let o=this.getSize();this.ionStyle.emit({[`title-${o}`]:!0})}getSize(){return this.size!==void 0?this.size:"default"}render(){let o=p(this),e=this.getSize();return a(v,{key:"3f7b19c99961dbb86c0a925218332528b22e6880",class:I(this.color,{[o]:!0,[`title-${e}`]:!0,"title-rtl":document.dir==="rtl"})},a("div",{key:"12054fbdd60e40a15875e442c20143766fc34fc3",class:"toolbar-title"},a("slot",{key:"9f14fb14a67d4bd1e4536a4d64a637fbe5a151c7"})))}get el(){return x(this)}static get watchers(){return{size:["sizeChanged"]}}};return t.style={ios:Bt,md:Pt},t})(),At=":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-color-step-50, var(--ion-background-color-step-50, #f7f7f7)));--color:var(--ion-toolbar-color, var(--ion-text-color, #000));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.2)))));--padding-top:3px;--padding-bottom:3px;--padding-start:4px;--padding-end:4px;--min-height:44px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:4;order:4;min-width:0}:host(.toolbar-segment) .toolbar-content{display:-ms-inline-flexbox;display:inline-flex}:host(.toolbar-searchbar) .toolbar-container{padding-top:0;padding-bottom:0}:host(.toolbar-searchbar) ::slotted(*){-ms-flex-item-align:start;align-self:start}:host(.toolbar-searchbar) ::slotted(ion-chip){margin-top:3px}::slotted(ion-buttons){min-height:38px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:3;order:3}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}:host(.toolbar-title-large) .toolbar-container{-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:start;align-items:flex-start}:host(.toolbar-title-large) .toolbar-content ion-title{-ms-flex:1;flex:1;-ms-flex-order:8;order:8;min-width:100%}",Mt=At,Ot=":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-background-color, #fff));--color:var(--ion-toolbar-color, var(--ion-text-color, #424242));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, #c1c4cd))));--padding-top:0;--padding-bottom:0;--padding-start:0;--padding-end:0;--min-height:56px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:3;order:3;min-width:0;max-width:100%}::slotted(.buttons-first-slot){-webkit-margin-start:4px;margin-inline-start:4px}::slotted(.buttons-last-slot){-webkit-margin-end:4px;margin-inline-end:4px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:4;order:4}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}",Ft=Ot,io=(()=>{let t=class{constructor(o){u(this,o),this.childrenStyles=new Map,this.color=void 0}componentWillLoad(){let o=Array.from(this.el.querySelectorAll("ion-buttons")),e=o.find(i=>i.slot==="start");e&&e.classList.add("buttons-first-slot");let n=o.reverse(),r=n.find(i=>i.slot==="end")||n.find(i=>i.slot==="primary")||n.find(i=>i.slot==="secondary");r&&r.classList.add("buttons-last-slot")}childrenStyle(o){o.stopPropagation();let e=o.target.tagName,n=o.detail,r={},i=this.childrenStyles.get(e)||{},s=!1;Object.keys(n).forEach(l=>{let d=`toolbar-${l}`,h=n[l];h!==i[d]&&(s=!0),h&&(r[d]=!0)}),s&&(this.childrenStyles.set(e,r),P(this))}render(){let o=p(this),e={};return this.childrenStyles.forEach(n=>{Object.assign(e,n)}),a(v,{key:"402afe7ce0c97883cedd0e48a5a0492a9bfe76ae",class:Object.assign(Object.assign({},e),I(this.color,{[o]:!0,"in-toolbar":z("ion-toolbar",this.el)}))},a("div",{key:"2465a6dc8d507ec650538378d1be2abd399c58ad",class:"toolbar-background",part:"background"}),a("div",{key:"6075096afd12303b961e4fe9ad345ef2887573af",class:"toolbar-container",part:"container"},a("slot",{key:"8b7eec1148cfeb339d87cdf9273f2104703e7601",name:"start"}),a("slot",{key:"b102d3926cade24faf78b7af78ad5e192c4c0308",name:"secondary"}),a("div",{key:"c6ab2e978328324c6f9e7892024cbcd8b8987067",class:"toolbar-content",part:"content"},a("slot",{key:"86f8952c4355a9df5b4bbb95e9d0cafefd272d5b"})),a("slot",{key:"501e43431da6b9dd35b47b79222f948d445f7a78",name:"primary"}),a("slot",{key:"84bf1a15a5e52e8e94df9f479c4ce18004f5ab57",name:"end"})))}get el(){return x(this)}};return t.style={ios:Mt,md:Ft},t})();export{ot as ion_app,Qt as ion_buttons,dt as ion_content,to as ion_footer,oo as ion_header,eo as ion_router_outlet,no as ion_title,io as ion_toolbar};
