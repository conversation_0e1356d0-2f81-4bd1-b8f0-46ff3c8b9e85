<?php

namespace App\Services;

use App\Models\User;
use App\Models\Invitation;
use App\Notifications\AdminEventNotification;
use App\Notifications\AdminInvitationNotification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

use Illuminate\Http\Request;


class EmailNotificationService
{
    /**
     * Send notification to all admin users
     */

    public function notifyAllAdmins(Request $request)
    {
        try {
            $adminUsers = User::whereIn('role', ['super_admin', 'chairman', 'officer', 'assistant'])
                             ->where('is_active', true)

                             ->get();

            if ($adminUsers->isEmpty()) {
                Log::warning('No admin users found to send email notification');
                return false;
            }


            Notification::send($adminUsers, new AdminEventNotification($request->title, $request->message, $request->eventType, $request->eventData));

            Log::info('Email notification sent to admin users', [
                'title' => $request->title,
                'event_type' => $request->eventType,

                'recipients_count' => $adminUsers->count()
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send email notification to admin users', [
                'error' => $e->getMessage(),

                'title' => $request->title,
                'event_type' => $request->eventType

            ]);
            return false;
        }
    }

    /**
     * Send notification to specific barangay admins
     */
    public function notifyBarangayAdmins(string $barangay, string $title, string $message, string $eventType, array $eventData = [])
    {
        try {
            $barangayAdmins = User::where('barangay', $barangay)
                                 ->whereIn('role', ['admin', 'chairman', 'officer'])
                                 ->where('status', 'Active')
                                 ->get();

            if ($barangayAdmins->isEmpty()) {
                Log::warning("No admin users found for barangay: {$barangay}");
                return false;
            }

            Notification::send($barangayAdmins, new AdminEventNotification($title, $message, $eventType, $eventData));

            Log::info('Email notification sent to barangay admins', [
                'barangay' => $barangay,
                'title' => $title,
                'event_type' => $eventType,
                'recipients_count' => $barangayAdmins->count()
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send email notification to barangay admins', [
                'error' => $e->getMessage(),
                'barangay' => $barangay,
                'title' => $title,
                'event_type' => $eventType
            ]);
            return false;
        }
    }

    /**
     * Send notification to specific user
     */
    public function notifyUser(User $user, string $title, string $message, string $eventType, array $eventData = [])
    {
        try {
            $user->notify(new AdminEventNotification($title, $message, $eventType, $eventData));

            Log::info('Email notification sent to user', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'title' => $title,
                'event_type' => $eventType
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send email notification to user', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'user_email' => $user->email,
                'title' => $title,
                'event_type' => $eventType
            ]);
            return false;
        }
    }

    /**
     * Send admin invitation
     */
    public function sendAdminInvitation(array $invitationData, User $invitedBy)
    {
        try {
            // Debug logging to see what data is being passed
            Log::info('Creating invitation with data:', $invitationData);
            
            // Create invitation record
            $invitation = Invitation::create([
                'email' => $invitationData['email'],
                'title' => $invitationData['title'] ?? null,
                'first_name' => $invitationData['first_name'],
                'middle_name' => $invitationData['middle_name'] ?? null,
                'last_name' => $invitationData['last_name'],
                'suffix' => $invitationData['suffix'] ?? null,
                'position' => $invitationData['position'],
                'barangay' => $invitationData['barangay'],
                'role' => $invitationData['role'],
                'invited_by' => $invitedBy->id,
                'user_id' => $invitationData['user_id'] ?? null,
            ]);

            // Send invitation email
            Notification::route('mail', $invitationData['email'])
                ->notify(new AdminInvitationNotification($invitation->toArray()));

            Log::info('Admin invitation sent', [
                'invitation_id' => $invitation->id,
                'email' => $invitationData['email'],
                'invited_by' => $invitedBy->id,
                'user_id' => $invitationData['user_id'] ?? null,
                'role' => $invitationData['role'],
                'barangay' => $invitationData['barangay']
            ]);

            return $invitation;
        } catch (\Exception $e) {
            Log::error('Failed to send admin invitation', [
                'error' => $e->getMessage(),
                'email' => $invitationData['email'],
                'invited_by' => $invitedBy->id,
                'invitation_data' => $invitationData
            ]);
            return false;
        }
    }

    /**
     * Resend admin invitation
     */
    public function resendAdminInvitation(Invitation $invitation)
    {
        try {
            // Update expiration date
            $invitation->update([
                'expires_at' => now()->addDays(7),
                'status' => 'pending'
            ]);

            // Send invitation email
            Notification::route('mail', $invitation->email)
                ->notify(new AdminInvitationNotification($invitation->toArray()));

            Log::info('Admin invitation resent', [
                'invitation_id' => $invitation->id,
                'email' => $invitation->email
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to resend admin invitation', [
                'error' => $e->getMessage(),
                'invitation_id' => $invitation->id,
                'email' => $invitation->email
            ]);
            return false;
        }
    }

    /**
     * Clean up expired invitations
     */
    public function cleanupExpiredInvitations()
    {
        try {
            $expiredCount = Invitation::expired()
                ->where('status', 'pending')
                ->update(['status' => 'expired']);

            Log::info('Expired invitations cleaned up', [
                'expired_count' => $expiredCount
            ]);

            return $expiredCount;
        } catch (\Exception $e) {
            Log::error('Failed to cleanup expired invitations', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
} 