<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebAlerto - Geolocation Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
    </style>
</head>
<body class="flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full">
        <div class="text-center mb-6">
            <div class="text-4xl mb-4">🌍</div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">WebAlerto Geolocation Test</h1>
            <p class="text-gray-600">Test if your localhost can access location services</p>
        </div>
        
        <!-- Current URL Info -->
        <div class="mb-6 p-4 bg-blue-50 rounded-lg">
            <h3 class="font-semibold text-blue-800 mb-2">Current Environment</h3>
            <div class="text-sm text-blue-700">
                <div><strong>URL:</strong> <span id="currentUrl"></span></div>
                <div><strong>Protocol:</strong> <span id="currentProtocol"></span></div>
                <div><strong>Host:</strong> <span id="currentHost"></span></div>
            </div>
        </div>

        <!-- Test Buttons -->
        <div class="space-y-4 mb-6">
            <button onclick="testBasicLocation()" class="w-full bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200">
                <i class="fas fa-location-arrow mr-2"></i>
                Test Basic Location
            </button>
            
            <button onclick="testHighAccuracyLocation()" class="w-full bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200">
                <i class="fas fa-crosshairs mr-2"></i>
                Test High Accuracy Location
            </button>
            
            <button onclick="testPermissionStatus()" class="w-full bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200">
                <i class="fas fa-shield-alt mr-2"></i>
                Check Permission Status
            </button>
        </div>

        <!-- Results -->
        <div id="results" class="space-y-3">
            <!-- Results will be populated here -->
        </div>

        <!-- Instructions -->
        <div class="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
            <h4 class="font-semibold text-amber-800 mb-2">
                <i class="fas fa-lightbulb mr-2"></i>
                Quick Setup Tips
            </h4>
            <ul class="text-sm text-amber-700 space-y-1">
                <li>• Click the lock icon in your browser's address bar</li>
                <li>• Set Location permission to "Allow"</li>
                <li>• Refresh the page and try again</li>
                <li>• Make sure device location services are enabled</li>
            </ul>
        </div>

        <!-- Back to Dashboard -->
        <div class="mt-6 text-center">
            <a href="{{ route('components.dashboard') }}" class="text-blue-600 hover:text-blue-800 font-medium">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <script>
        // Display current environment info
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('currentProtocol').textContent = window.location.protocol;
        document.getElementById('currentHost').textContent = window.location.host;

        function addResult(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                success: 'bg-green-50 border-green-200 text-green-800',
                error: 'bg-red-50 border-red-200 text-red-800',
                warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
                info: 'bg-blue-50 border-blue-200 text-blue-800'
            };
            
            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️'
            };

            const resultDiv = document.createElement('div');
            resultDiv.className = `p-3 rounded-lg border ${colors[type]}`;
            resultDiv.innerHTML = `
                <div class="flex items-start">
                    <span class="mr-2">${icons[type]}</span>
                    <div class="flex-1">
                        <div class="font-medium">${message}</div>
                        <div class="text-xs opacity-75 mt-1">${timestamp}</div>
                    </div>
                </div>
            `;
            
            document.getElementById('results').appendChild(resultDiv);
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testBasicLocation() {
            clearResults();
            addResult('Testing basic geolocation...', 'info');

            if (!navigator.geolocation) {
                addResult('Geolocation is not supported by this browser', 'error');
                return;
            }

            try {
                const position = await new Promise((resolve, reject) => {
                    navigator.geolocation.getCurrentPosition(resolve, reject, {
                        enableHighAccuracy: false,
                        timeout: 10000,
                        maximumAge: 300000
                    });
                });

                addResult(`Location found! Latitude: ${position.coords.latitude.toFixed(6)}, Longitude: ${position.coords.longitude.toFixed(6)}`, 'success');
                addResult(`Accuracy: ±${position.coords.accuracy.toFixed(1)} meters`, 'info');
                
            } catch (error) {
                addResult(`Location error: ${error.message}`, 'error');
                addResult(`Error code: ${error.code}`, 'error');
                
                switch(error.code) {
                    case 1:
                        addResult('Permission denied. Please allow location access in your browser.', 'warning');
                        break;
                    case 2:
                        addResult('Position unavailable. Check your device GPS settings.', 'warning');
                        break;
                    case 3:
                        addResult('Request timeout. Try again or check your connection.', 'warning');
                        break;
                }
            }
        }

        async function testHighAccuracyLocation() {
            clearResults();
            addResult('Testing high accuracy geolocation...', 'info');

            if (!navigator.geolocation) {
                addResult('Geolocation is not supported by this browser', 'error');
                return;
            }

            try {
                const position = await new Promise((resolve, reject) => {
                    navigator.geolocation.getCurrentPosition(resolve, reject, {
                        enableHighAccuracy: true,
                        timeout: 15000,
                        maximumAge: 0
                    });
                });

                addResult(`High accuracy location found!`, 'success');
                addResult(`Latitude: ${position.coords.latitude.toFixed(6)}`, 'success');
                addResult(`Longitude: ${position.coords.longitude.toFixed(6)}`, 'success');
                addResult(`Accuracy: ±${position.coords.accuracy.toFixed(1)} meters`, 'info');
                
                if (position.coords.altitude) {
                    addResult(`Altitude: ${position.coords.altitude.toFixed(1)} meters`, 'info');
                }
                
            } catch (error) {
                addResult(`High accuracy location failed: ${error.message}`, 'error');
                addResult('Trying fallback to basic location...', 'warning');
                
                // Fallback to basic location
                setTimeout(testBasicLocation, 1000);
            }
        }

        async function testPermissionStatus() {
            clearResults();
            addResult('Checking permission status...', 'info');

            if (!navigator.permissions) {
                addResult('Permissions API not supported in this browser', 'warning');
                addResult('Try testing location directly instead', 'info');
                return;
            }

            try {
                const permission = await navigator.permissions.query({name: 'geolocation'});
                
                switch(permission.state) {
                    case 'granted':
                        addResult('Location permission is GRANTED ✅', 'success');
                        addResult('You can proceed with location testing', 'success');
                        break;
                    case 'denied':
                        addResult('Location permission is DENIED ❌', 'error');
                        addResult('Please reset site permissions and allow location access', 'warning');
                        break;
                    case 'prompt':
                        addResult('Location permission will be PROMPTED 🔔', 'warning');
                        addResult('Browser will ask for permission when you test location', 'info');
                        break;
                    default:
                        addResult(`Unknown permission state: ${permission.state}`, 'warning');
                }
                
            } catch (error) {
                addResult(`Permission check failed: ${error.message}`, 'error');
            }
        }

        // Auto-check environment on page load
        window.onload = function() {
            addResult('WebAlerto Geolocation Test loaded successfully', 'success');
            
            if (location.protocol === 'https:') {
                addResult('Secure HTTPS connection detected', 'success');
            } else if (location.hostname === 'localhost' || location.hostname === '127.0.0.1') {
                addResult('Localhost detected - geolocation should work', 'success');
            } else {
                addResult('Insecure HTTP connection - geolocation may be blocked', 'warning');
            }
        };
    </script>
</body>
</html>
