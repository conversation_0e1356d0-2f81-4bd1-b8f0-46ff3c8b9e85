@extends('layout.app')

@section('content')
<div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-sky-50 to-sky-100 py-8">
    <div class="bg-white/90 rounded-2xl shadow-xl border border-sky-200 p-8 w-full max-w-lg">
        <h2 class="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
            <i class="fas fa-user-check text-emerald-600"></i>
            Complete Your Account Setup
        </h2>
        <p class="text-gray-600 mb-6">Set your password and complete your profile to activate your admin account.</p>
        @if ($errors->any())
            <div class="mb-4 text-red-600 text-sm">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li class="mb-1">• {{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif
        <form id="acceptInvitationForm" class="space-y-4" method="POST" action="{{ route('admin.invitation.accept.post') }}">
            @csrf
            <input type="hidden" name="email" value="{{ old('email', $email ?? request('email')) }}">
            <input type="hidden" name="token" value="{{ old('token', $token ?? request('token')) }}">
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Title</label>
                <select name="title" id="title" class="w-full border-gray-300 rounded-lg shadow-sm focus:border-emerald-500 focus:ring-emerald-500">
                    <option value="">Select Title</option>
                    <option value="Mr.">Mr.</option>
                    <option value="Ms.">Ms.</option>
                    <option value="Mrs.">Mrs.</option>
                    <option value="Dr.">Dr.</option>
                    <option value="Engr.">Engr.</option>
                    <option value="Atty.">Atty.</option>
                </select>
            </div>
            <div>
                <label for="first_name" class="block text-sm font-medium text-gray-700 mb-1">First Name *</label>
                <input type="text" name="first_name" id="first_name" class="w-full border-gray-300 rounded-lg shadow-sm focus:border-emerald-500 focus:ring-emerald-500" required>
            </div>
            <div>
                <label for="middle_name" class="block text-sm font-medium text-gray-700 mb-1">Middle Name</label>
                <input type="text" name="middle_name" id="middle_name" class="w-full border-gray-300 rounded-lg shadow-sm focus:border-emerald-500 focus:ring-emerald-500">
            </div>
            <div>
                <label for="last_name" class="block text-sm font-medium text-gray-700 mb-1">Last Name *</label>
                <input type="text" name="last_name" id="last_name" class="w-full border-gray-300 rounded-lg shadow-sm focus:border-emerald-500 focus:ring-emerald-500" required>
            </div>
            <div>
                <label for="suffix" class="block text-sm font-medium text-gray-700 mb-1">Suffix</label>
                <input type="text" name="suffix" id="suffix" class="w-full border-gray-300 rounded-lg shadow-sm focus:border-emerald-500 focus:ring-emerald-500" placeholder="Jr., Sr., III, etc.">
            </div>
            <div>
                <label for="password" class="block text-sm font-medium text-gray-700 mb-1">New Password *</label>
                <input type="password" name="password" id="password" class="w-full border-gray-300 rounded-lg shadow-sm focus:border-emerald-500 focus:ring-emerald-500" required minlength="8">
            </div>
            <div>
                <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-1">Confirm Password *</label>
                <input type="password" name="password_confirmation" id="password_confirmation" class="w-full border-gray-300 rounded-lg shadow-sm focus:border-emerald-500 focus:ring-emerald-500" required minlength="8">
            </div>
            <div class="flex justify-end gap-3 pt-4">
                <button type="submit" class="px-6 py-2 bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white rounded-lg font-semibold transition-all duration-200 transform hover:scale-105">
                    <i class="fas fa-check mr-2"></i>Activate Account
                </button>
            </div>
        </form>
    </div>
</div>
@endsection 