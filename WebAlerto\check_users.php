<?php

require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Models\Evacuation;
use App\Models\Notification;

try {
    echo "=== Current Database Status ===\n\n";

    // Check users
    $totalUsers = User::count();
    $systemAdmins = User::where('role', 'system_admin')->count();
    $cdrrmc = User::where('role', 'super_admin')->count();
    $bdrrmc = User::where('role', 'admin')->count();

    echo "👥 USERS:\n";
    echo "  Total Users: $totalUsers\n";
    echo "  System Admins: $systemAdmins\n";
    echo "  CDRRMC (super_admin): $cdrrmc\n";
    echo "  BDRRMC (admin): $bdrrmc\n\n";

    // Check evacuation centers
    $totalCenters = Evacuation::count();
    $activeCenters = Evacuation::where('status', 'Active')->count();

    echo "🏢 EVACUATION CENTERS:\n";
    echo "  Total Centers: $totalCenters\n";
    echo "  Active Centers: $activeCenters\n\n";

    // Check notifications
    $totalNotifications = Notification::count();

    echo "📢 NOTIFICATIONS:\n";
    echo "  Total Notifications: $totalNotifications\n\n";

    // Show sample users
    echo "👤 SAMPLE USERS:\n";
    $users = User::take(5)->get(['id', 'email', 'role', 'city', 'barangay', 'status']);
    foreach ($users as $user) {
        echo "  ID: {$user->id} | {$user->email} | {$user->role} | City: {$user->city} | Barangay: {$user->barangay} | {$user->status}\n";
    }

    echo "\n=== Dashboard Statistics Test ===\n";

    // Test CDRRMC user if exists
    $cdrrmc_user = User::where('role', 'super_admin')->first();
    if ($cdrrmc_user) {
        echo "\n🔍 Testing CDRRMC Dashboard Statistics:\n";
        echo "  CDRRMC User: {$cdrrmc_user->email} (City: {$cdrrmc_user->city})\n";
        
        // Get barangay service
        $barangayService = app(\App\Services\BarangayService::class);
        $accessibleBarangays = $barangayService->getAccessibleBarangays($cdrrmc_user);
        echo "  Accessible Barangays: " . count($accessibleBarangays) . "\n";
        echo "  First 3 Barangays: " . implode(', ', array_slice($accessibleBarangays, 0, 3)) . "\n";
        
        // Count BDRRMC users in their city
        $bdrrmc_in_city = User::where('role', 'admin')
                             ->whereIn('barangay', $accessibleBarangays)
                             ->count();
        echo "  BDRRMC Users in City: $bdrrmc_in_city\n";
        
        // Count evacuation centers in their city
        $centers_in_city = Evacuation::whereIn('barangay', $accessibleBarangays)->count();
        echo "  Evacuation Centers in City: $centers_in_city\n";
    }

    echo "\n✅ Database check completed!\n";

} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "📋 Stack trace: " . $e->getTraceAsString() . "\n";
}
