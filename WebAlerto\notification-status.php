<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\DeviceToken;
use App\Models\Notification;
use App\Models\User;
use App\Models\Barangay;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "📊 WebAlerto Notification System Status\n";
echo "=======================================\n\n";

// Database Statistics
echo "🗄️  Database Statistics:\n";
echo "========================\n";
$totalNotifications = Notification::count();
$sentNotifications = Notification::where('sent', true)->count();
$pendingNotifications = Notification::where('sent', false)->count();
$totalUsers = User::count();
$totalBarangays = Barangay::count();
$activeTokens = DeviceToken::where('is_active', true)->count();
$totalTokens = DeviceToken::count();

echo "Notifications:\n";
echo "  - Total: {$totalNotifications}\n";
echo "  - Sent: {$sentNotifications}\n";
echo "  - Pending: {$pendingNotifications}\n\n";

echo "Users: {$totalUsers}\n";
echo "Barangays: {$totalBarangays}\n\n";

echo "FCM Tokens:\n";
echo "  - Total: {$totalTokens}\n";
echo "  - Active: {$activeTokens}\n\n";

// Recent Notifications
echo "📋 Recent Notifications (Last 5):\n";
echo "==================================\n";
$recentNotifications = Notification::latest()->limit(5)->get();

if ($recentNotifications->count() > 0) {
    foreach ($recentNotifications as $notification) {
        $status = $notification->sent ? "✅ Sent" : "⏳ Pending";
        echo "ID: {$notification->id} | {$status} | {$notification->category} | {$notification->barangay}\n";
        echo "   Title: {$notification->title}\n";
        echo "   Created: {$notification->created_at}\n\n";
    }
} else {
    echo "No notifications found.\n\n";
}

// System Health Check
echo "🏥 System Health Check:\n";
echo "=======================\n";

// Check if affected_areas column exists (should not exist)
try {
    $hasAffectedAreas = \Illuminate\Support\Facades\Schema::hasColumn('notifications', 'affected_areas');
    if ($hasAffectedAreas) {
        echo "❌ affected_areas column still exists (should be removed)\n";
    } else {
        echo "✅ affected_areas column successfully removed\n";
    }
} catch (Exception $e) {
    echo "⚠️  Could not check affected_areas column: " . $e->getMessage() . "\n";
}

// Check barangay field
try {
    $hasBarangay = \Illuminate\Support\Facades\Schema::hasColumn('notifications', 'barangay');
    if ($hasBarangay) {
        echo "✅ barangay column exists\n";
        
        // Check for null barangays
        $nullBarangays = Notification::whereNull('barangay')->count();
        if ($nullBarangays > 0) {
            echo "⚠️  Found {$nullBarangays} notifications with null barangay\n";
        } else {
            echo "✅ No null barangay values found\n";
        }
    } else {
        echo "❌ barangay column missing\n";
    }
} catch (Exception $e) {
    echo "⚠️  Could not check barangay column: " . $e->getMessage() . "\n";
}

// Check notification model fillable fields
try {
    $notification = new Notification();
    $fillable = $notification->getFillable();
    
    if (in_array('affected_areas', $fillable)) {
        echo "❌ affected_areas still in fillable array\n";
    } else {
        echo "✅ affected_areas removed from fillable array\n";
    }
    
    if (in_array('barangay', $fillable)) {
        echo "✅ barangay in fillable array\n";
    } else {
        echo "❌ barangay missing from fillable array\n";
    }
} catch (Exception $e) {
    echo "⚠️  Could not check model fillable: " . $e->getMessage() . "\n";
}

echo "\n";

// User Roles Summary
echo "👥 User Roles Summary:\n";
echo "=====================\n";
$userRoles = User::selectRaw('role, COUNT(*) as count')->groupBy('role')->get();
foreach ($userRoles as $roleData) {
    echo "{$roleData->role}: {$roleData->count} users\n";
}

echo "\n";

// FCM Token Details
echo "📱 FCM Token Details:\n";
echo "====================\n";
if ($activeTokens > 0) {
    $tokensByProject = DeviceToken::where('is_active', true)
        ->selectRaw('project_id, COUNT(*) as count')
        ->groupBy('project_id')
        ->get();
    
    foreach ($tokensByProject as $project) {
        echo "Project {$project->project_id}: {$project->count} active tokens\n";
    }
    
    $recentTokens = DeviceToken::where('is_active', true)
        ->orderBy('last_used_at', 'desc')
        ->limit(3)
        ->get();
    
    echo "\nMost Recently Used Tokens:\n";
    foreach ($recentTokens as $token) {
        $lastUsed = $token->last_used_at ? $token->last_used_at->diffForHumans() : 'Never';
        echo "- Token ending in " . substr($token->token, -10) . " (Last used: {$lastUsed})\n";
    }
} else {
    echo "No active FCM tokens found.\n";
    echo "To register a token: php test-notification.php register <token>\n";
}

echo "\n";

// Quick Test Commands
echo "🛠️  Quick Test Commands:\n";
echo "========================\n";
echo "Run comprehensive tests:\n";
echo "  php test-notification-system.php\n\n";
echo "Test web interface:\n";
echo "  php test-web-notification.php\n\n";
echo "Complete end-to-end test:\n";
echo "  php test-complete-notification.php\n\n";
echo "Send test notification:\n";
echo "  php test-notification.php send \"Test Title\" \"Test Message\"\n\n";
echo "List FCM tokens:\n";
echo "  php test-notification.php list\n\n";

// Web Interface URLs
echo "🌐 Web Interface URLs:\n";
echo "=====================\n";
echo "Create Notification: http://localhost:8000/notification/create\n";
echo "View History: http://localhost:8000/notification\n";
echo "Admin Dashboard: http://localhost:8000/admin\n\n";

echo "✅ Notification system is ready for use!\n";
