<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>Map</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Map</ion-title>
    </ion-toolbar>
  </ion-header>

  <!-- Travel Mode Selector - Only show when in filter mode or from search -->

  <!-- Map Container -->
  <div id="map"></div>

  <!-- Location Request Button - Shows when GPS is having issues -->
  <div class="location-request-container" *ngIf="showLocationRequestButton">
    <ion-button expand="block" color="primary" (click)="requestLocationExplicitly()">
      <ion-icon name="locate" slot="start"></ion-icon>
      Enable Location Access
    </ion-button>
    <p class="location-help-text">Tap the button above to enable location access</p>
  </div>



  <!-- Route Summary Card -->
  <div *ngIf="routeTime && routeDistance" class="route-summary-card" (click)="showDirectionsPanel = true">
    <ion-icon
      [name]="travelMode === 'foot-walking' ? 'walk-outline' :
              travelMode === 'cycling-regular' ? 'bicycle-outline' :
              'car-outline'"
      [color]="travelMode === 'foot-walking' ? 'primary' :
              travelMode === 'cycling-regular' ? 'success' :
              'danger'">
    </ion-icon>
    <div class="summary-text">
      <strong>{{ (routeTime/60).toFixed(0) }} min</strong> •
      {{ (routeDistance/1000).toFixed(2) }} km
      <div class="travel-mode">{{ getTravelModeName() }}</div>
    </div>
    <ion-icon name="chevron-up" class="expand-icon"></ion-icon>
  </div>

  <!-- Directions Panel -->
  <app-directions-panel
    *ngIf="showDirectionsPanel && currentDirections.length > 0"
    [directions]="currentDirections"
    [travelMode]="travelMode"
    [totalDistance]="routeDistance"
    [totalDuration]="routeTime"
    (close)="showDirectionsPanel = false">
  </app-directions-panel>

  <!-- GPS Toggle Button -->
  <ion-fab vertical="top" horizontal="end" slot="fixed">
    <ion-fab-button size="small" (click)="toggleGps({detail: {checked: !gpsEnabled}})" [color]="gpsEnabled ? 'success' : 'medium'">
      <ion-icon [name]="gpsEnabled ? 'locate' : 'locate-outline'"></ion-icon>
    </ion-fab-button>
  </ion-fab>







  <!-- GPS Status Indicator -->
  <div class="gps-status" [class.active]="gpsEnabled" (click)="showLocationHelp()">
    <ion-icon [name]="gpsEnabled ? 'location' : 'location-outline'"></ion-icon>
    <span>GPS {{ gpsEnabled ? 'Active' : 'Inactive' }}</span>
  </div>

  <!-- Disaster Type Indicator -->
  <div *ngIf="isFilterMode && currentDisasterType !== 'all'" class="disaster-type-indicator">
    <ion-icon [name]="currentDisasterType.toLowerCase().includes('earthquake') ? 'earth-outline' :
                     currentDisasterType.toLowerCase().includes('typhoon') ? 'thunderstorm-outline' :
                     currentDisasterType.toLowerCase().includes('flood') ? 'water-outline' : 'alert-circle-outline'">
    </ion-icon>
    <span>{{ currentDisasterType }} Evacuation Centers</span>
  </div>

  <!-- Route Button - Only show when in filter mode or from search -->
  <ion-fab *ngIf="isFilterMode || evacuationCenters.length > 0" vertical="bottom" horizontal="end" slot="fixed">
    <ion-fab-button color="primary" (click)="routeToTwoNearestCenters()">
      <ion-icon name="navigate-outline"></ion-icon>
    </ion-fab-button>
    <ion-label class="fab-label">Route to Nearest Centers</ion-label>
  </ion-fab>

  <!-- Directions Button - Only show when route is active -->
  <ion-fab *ngIf="currentDirections.length > 0 && !showDirectionsPanel" vertical="bottom" horizontal="start" slot="fixed">
    <ion-fab-button color="tertiary" (click)="showDirectionsPanel = true">
      <ion-icon name="list-outline"></ion-icon>
    </ion-fab-button>
    <ion-label class="fab-label">Show Directions</ion-label>
  </ion-fab>


</ion-content>