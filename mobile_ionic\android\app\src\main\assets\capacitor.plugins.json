[{"pkg": "@capacitor-firebase/messaging", "classpath": "io.capawesome.capacitorjs.plugins.firebase.messaging.FirebaseMessagingPlugin"}, {"pkg": "@capacitor/app", "classpath": "com.capacitorjs.plugins.app.AppPlugin"}, {"pkg": "@capacitor/filesystem", "classpath": "com.capacitorjs.plugins.filesystem.FilesystemPlugin"}, {"pkg": "@capacitor/geolocation", "classpath": "com.capacitorjs.plugins.geolocation.GeolocationPlugin"}, {"pkg": "@capacitor/haptics", "classpath": "com.capacitorjs.plugins.haptics.HapticsPlugin"}, {"pkg": "@capacitor/keyboard", "classpath": "com.capacitorjs.plugins.keyboard.KeyboardPlugin"}, {"pkg": "@capacitor/local-notifications", "classpath": "com.capacitorjs.plugins.localnotifications.LocalNotificationsPlugin"}, {"pkg": "@capacitor/network", "classpath": "com.capacitorjs.plugins.network.NetworkPlugin"}, {"pkg": "@capacitor/screen-orientation", "classpath": "com.capacitorjs.plugins.screenorientation.ScreenOrientationPlugin"}, {"pkg": "@capacitor/status-bar", "classpath": "com.capacitorjs.plugins.statusbar.StatusBarPlugin"}]