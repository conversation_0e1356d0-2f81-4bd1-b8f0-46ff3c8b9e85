import{A as Qe,a as Ke,i as Ve,j as He,k as Xe,l as ke,o as Ge,sa as Je}from"./chunk-6NKRJMOW.js";import{c as ae,f as tr,g as rr,h as _}from"./chunk-B7O3QC5Z.js";var qe=tr((Ze,we)=>{"use strict";(function(R){if(typeof Ze=="object"&&typeof we<"u")we.exports=R();else if(typeof define=="function"&&define.amd)define([],R);else{var g;typeof window<"u"?g=window:typeof global<"u"?g=global:typeof self<"u"?g=self:g=this,g.localforage=R()}})(function(){var R,g,v;return function b(A,P,E){function F(M,K){if(!P[M]){if(!A[M]){var d=typeof ae=="function"&&ae;if(!K&&d)return d(M,!0);if(D)return D(M,!0);var m=new Error("Cannot find module '"+M+"'");throw m.code="MODULE_NOT_FOUND",m}var N=P[M]={exports:{}};A[M][0].call(N.exports,function(O){var V=A[M][1][O];return F(V||O)},N,N.exports,b,A,P,E)}return P[M].exports}for(var D=typeof ae=="function"&&ae,U=0;U<E.length;U++)F(E[U]);return F}({1:[function(b,A,P){(function(E){"use strict";var F=E.MutationObserver||E.WebKitMutationObserver,D;if(F){var U=0,M=new F(O),K=E.document.createTextNode("");M.observe(K,{characterData:!0}),D=function(){K.data=U=++U%2}}else if(!E.setImmediate&&typeof E.MessageChannel<"u"){var d=new E.MessageChannel;d.port1.onmessage=O,D=function(){d.port2.postMessage(0)}}else"document"in E&&"onreadystatechange"in E.document.createElement("script")?D=function(){var L=E.document.createElement("script");L.onreadystatechange=function(){O(),L.onreadystatechange=null,L.parentNode.removeChild(L),L=null},E.document.documentElement.appendChild(L)}:D=function(){setTimeout(O,0)};var m,N=[];function O(){m=!0;for(var L,H,B=N.length;B;){for(H=N,N=[],L=-1;++L<B;)H[L]();B=N.length}m=!1}A.exports=V;function V(L){N.push(L)===1&&!m&&D()}}).call(this,typeof global<"u"?global:typeof self<"u"?self:typeof window<"u"?window:{})},{}],2:[function(b,A,P){"use strict";var E=b(1);function F(){}var D={},U=["REJECTED"],M=["FULFILLED"],K=["PENDING"];A.exports=d;function d(l){if(typeof l!="function")throw new TypeError("resolver must be a function");this.state=K,this.queue=[],this.outcome=void 0,l!==F&&V(this,l)}d.prototype.catch=function(l){return this.then(null,l)},d.prototype.then=function(l,w){if(typeof l!="function"&&this.state===M||typeof w!="function"&&this.state===U)return this;var p=new this.constructor(F);if(this.state!==K){var C=this.state===M?l:w;N(p,C,this.outcome)}else this.queue.push(new m(p,l,w));return p};function m(l,w,p){this.promise=l,typeof w=="function"&&(this.onFulfilled=w,this.callFulfilled=this.otherCallFulfilled),typeof p=="function"&&(this.onRejected=p,this.callRejected=this.otherCallRejected)}m.prototype.callFulfilled=function(l){D.resolve(this.promise,l)},m.prototype.otherCallFulfilled=function(l){N(this.promise,this.onFulfilled,l)},m.prototype.callRejected=function(l){D.reject(this.promise,l)},m.prototype.otherCallRejected=function(l){N(this.promise,this.onRejected,l)};function N(l,w,p){E(function(){var C;try{C=w(p)}catch(z){return D.reject(l,z)}C===l?D.reject(l,new TypeError("Cannot resolve promise with itself")):D.resolve(l,C)})}D.resolve=function(l,w){var p=L(O,w);if(p.status==="error")return D.reject(l,p.value);var C=p.value;if(C)V(l,C);else{l.state=M,l.outcome=w;for(var z=-1,$=l.queue.length;++z<$;)l.queue[z].callFulfilled(w)}return l},D.reject=function(l,w){l.state=U,l.outcome=w;for(var p=-1,C=l.queue.length;++p<C;)l.queue[p].callRejected(w);return l};function O(l){var w=l&&l.then;if(l&&(typeof l=="object"||typeof l=="function")&&typeof w=="function")return function(){w.apply(l,arguments)}}function V(l,w){var p=!1;function C(W){p||(p=!0,D.reject(l,W))}function z(W){p||(p=!0,D.resolve(l,W))}function $(){w(z,C)}var Y=L($);Y.status==="error"&&C(Y.value)}function L(l,w){var p={};try{p.value=l(w),p.status="success"}catch(C){p.status="error",p.value=C}return p}d.resolve=H;function H(l){return l instanceof this?l:D.resolve(new this(F),l)}d.reject=B;function B(l){var w=new this(F);return D.reject(w,l)}d.all=ue;function ue(l){var w=this;if(Object.prototype.toString.call(l)!=="[object Array]")return this.reject(new TypeError("must be an array"));var p=l.length,C=!1;if(!p)return this.resolve([]);for(var z=new Array(p),$=0,Y=-1,W=new this(F);++Y<p;)X(l[Y],Y);return W;function X(te,ie){w.resolve(te).then(le,function(q){C||(C=!0,D.reject(W,q))});function le(q){z[ie]=q,++$===p&&!C&&(C=!0,D.resolve(W,z))}}}d.race=Z;function Z(l){var w=this;if(Object.prototype.toString.call(l)!=="[object Array]")return this.reject(new TypeError("must be an array"));var p=l.length,C=!1;if(!p)return this.resolve([]);for(var z=-1,$=new this(F);++z<p;)Y(l[z]);return $;function Y(W){w.resolve(W).then(function(X){C||(C=!0,D.resolve($,X))},function(X){C||(C=!0,D.reject($,X))})}}},{1:1}],3:[function(b,A,P){(function(E){"use strict";typeof E.Promise!="function"&&(E.Promise=b(2))}).call(this,typeof global<"u"?global:typeof self<"u"?self:typeof window<"u"?window:{})},{2:2}],4:[function(b,A,P){"use strict";var E=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};function F(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function D(){try{if(typeof indexedDB<"u")return indexedDB;if(typeof webkitIndexedDB<"u")return webkitIndexedDB;if(typeof mozIndexedDB<"u")return mozIndexedDB;if(typeof OIndexedDB<"u")return OIndexedDB;if(typeof msIndexedDB<"u")return msIndexedDB}catch{return}}var U=D();function M(){try{if(!U||!U.open)return!1;var e=typeof openDatabase<"u"&&/(Safari|iPhone|iPad|iPod)/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent)&&!/BlackBerry/.test(navigator.platform),r=typeof fetch=="function"&&fetch.toString().indexOf("[native code")!==-1;return(!e||r)&&typeof indexedDB<"u"&&typeof IDBKeyRange<"u"}catch{return!1}}function K(e,r){e=e||[],r=r||{};try{return new Blob(e,r)}catch(n){if(n.name!=="TypeError")throw n;for(var t=typeof BlobBuilder<"u"?BlobBuilder:typeof MSBlobBuilder<"u"?MSBlobBuilder:typeof MozBlobBuilder<"u"?MozBlobBuilder:WebKitBlobBuilder,a=new t,o=0;o<e.length;o+=1)a.append(e[o]);return a.getBlob(r.type)}}typeof Promise>"u"&&b(3);var d=Promise;function m(e,r){r&&e.then(function(t){r(null,t)},function(t){r(t)})}function N(e,r,t){typeof r=="function"&&e.then(r),typeof t=="function"&&e.catch(t)}function O(e){return typeof e!="string"&&(console.warn(e+" used as a key, but it is not a string."),e=String(e)),e}function V(){if(arguments.length&&typeof arguments[arguments.length-1]=="function")return arguments[arguments.length-1]}var L="local-forage-detect-blob-support",H=void 0,B={},ue=Object.prototype.toString,Z="readonly",l="readwrite";function w(e){for(var r=e.length,t=new ArrayBuffer(r),a=new Uint8Array(t),o=0;o<r;o++)a[o]=e.charCodeAt(o);return t}function p(e){return new d(function(r){var t=e.transaction(L,l),a=K([""]);t.objectStore(L).put(a,"key"),t.onabort=function(o){o.preventDefault(),o.stopPropagation(),r(!1)},t.oncomplete=function(){var o=navigator.userAgent.match(/Chrome\/(\d+)/),n=navigator.userAgent.match(/Edge\//);r(n||!o||parseInt(o[1],10)>=43)}}).catch(function(){return!1})}function C(e){return typeof H=="boolean"?d.resolve(H):p(e).then(function(r){return H=r,H})}function z(e){var r=B[e.name],t={};t.promise=new d(function(a,o){t.resolve=a,t.reject=o}),r.deferredOperations.push(t),r.dbReady?r.dbReady=r.dbReady.then(function(){return t.promise}):r.dbReady=t.promise}function $(e){var r=B[e.name],t=r.deferredOperations.pop();if(t)return t.resolve(),t.promise}function Y(e,r){var t=B[e.name],a=t.deferredOperations.pop();if(a)return a.reject(r),a.promise}function W(e,r){return new d(function(t,a){if(B[e.name]=B[e.name]||Ee(),e.db)if(r)z(e),e.db.close();else return t(e.db);var o=[e.name];r&&o.push(e.version);var n=U.open.apply(U,o);r&&(n.onupgradeneeded=function(i){var s=n.result;try{s.createObjectStore(e.storeName),i.oldVersion<=1&&s.createObjectStore(L)}catch(f){if(f.name==="ConstraintError")console.warn('The database "'+e.name+'" has been upgraded from version '+i.oldVersion+" to version "+i.newVersion+', but the storage "'+e.storeName+'" already exists.');else throw f}}),n.onerror=function(i){i.preventDefault(),a(n.error)},n.onsuccess=function(){var i=n.result;i.onversionchange=function(s){s.target.close()},t(i),$(e)}})}function X(e){return W(e,!1)}function te(e){return W(e,!0)}function ie(e,r){if(!e.db)return!0;var t=!e.db.objectStoreNames.contains(e.storeName),a=e.version<e.db.version,o=e.version>e.db.version;if(a&&(e.version!==r&&console.warn('The database "'+e.name+`" can't be downgraded from version `+e.db.version+" to version "+e.version+"."),e.version=e.db.version),o||t){if(t){var n=e.db.version+1;n>e.version&&(e.version=n)}return!0}return!1}function le(e){return new d(function(r,t){var a=new FileReader;a.onerror=t,a.onloadend=function(o){var n=btoa(o.target.result||"");r({__local_forage_encoded_blob:!0,data:n,type:e.type})},a.readAsBinaryString(e)})}function q(e){var r=w(atob(e.data));return K([r],{type:e.type})}function Se(e){return e&&e.__local_forage_encoded_blob}function rt(e){var r=this,t=r._initReady().then(function(){var a=B[r._dbInfo.name];if(a&&a.dbReady)return a.dbReady});return N(t,e,e),t}function nt(e){z(e);for(var r=B[e.name],t=r.forages,a=0;a<t.length;a++){var o=t[a];o._dbInfo.db&&(o._dbInfo.db.close(),o._dbInfo.db=null)}return e.db=null,X(e).then(function(n){return e.db=n,ie(e)?te(e):n}).then(function(n){e.db=r.db=n;for(var i=0;i<t.length;i++)t[i]._dbInfo.db=n}).catch(function(n){throw Y(e,n),n})}function k(e,r,t,a){a===void 0&&(a=1);try{var o=e.db.transaction(e.storeName,r);t(null,o)}catch(n){if(a>0&&(!e.db||n.name==="InvalidStateError"||n.name==="NotFoundError"))return d.resolve().then(function(){if(!e.db||n.name==="NotFoundError"&&!e.db.objectStoreNames.contains(e.storeName)&&e.version<=e.db.version)return e.db&&(e.version=e.db.version+1),te(e)}).then(function(){return nt(e).then(function(){k(e,r,t,a-1)})}).catch(t);t(n)}}function Ee(){return{forages:[],db:null,dbReady:null,deferredOperations:[]}}function at(e){var r=this,t={db:null};if(e)for(var a in e)t[a]=e[a];var o=B[t.name];o||(o=Ee(),B[t.name]=o),o.forages.push(r),r._initReady||(r._initReady=r.ready,r.ready=rt);var n=[];function i(){return d.resolve()}for(var s=0;s<o.forages.length;s++){var f=o.forages[s];f!==r&&n.push(f._initReady().catch(i))}var c=o.forages.slice(0);return d.all(n).then(function(){return t.db=o.db,X(t)}).then(function(u){return t.db=u,ie(t,r._defaultConfig.version)?te(t):u}).then(function(u){t.db=o.db=u,r._dbInfo=t;for(var h=0;h<c.length;h++){var y=c[h];y!==r&&(y._dbInfo.db=t.db,y._dbInfo.version=t.version)}})}function ot(e,r){var t=this;e=O(e);var a=new d(function(o,n){t.ready().then(function(){k(t._dbInfo,Z,function(i,s){if(i)return n(i);try{var f=s.objectStore(t._dbInfo.storeName),c=f.get(e);c.onsuccess=function(){var u=c.result;u===void 0&&(u=null),Se(u)&&(u=q(u)),o(u)},c.onerror=function(){n(c.error)}}catch(u){n(u)}})}).catch(n)});return m(a,r),a}function it(e,r){var t=this,a=new d(function(o,n){t.ready().then(function(){k(t._dbInfo,Z,function(i,s){if(i)return n(i);try{var f=s.objectStore(t._dbInfo.storeName),c=f.openCursor(),u=1;c.onsuccess=function(){var h=c.result;if(h){var y=h.value;Se(y)&&(y=q(y));var I=e(y,h.key,u++);I!==void 0?o(I):h.continue()}else o()},c.onerror=function(){n(c.error)}}catch(h){n(h)}})}).catch(n)});return m(a,r),a}function st(e,r,t){var a=this;e=O(e);var o=new d(function(n,i){var s;a.ready().then(function(){return s=a._dbInfo,ue.call(r)==="[object Blob]"?C(s.db).then(function(f){return f?r:le(r)}):r}).then(function(f){k(a._dbInfo,l,function(c,u){if(c)return i(c);try{var h=u.objectStore(a._dbInfo.storeName);f===null&&(f=void 0);var y=h.put(f,e);u.oncomplete=function(){f===void 0&&(f=null),n(f)},u.onabort=u.onerror=function(){var I=y.error?y.error:y.transaction.error;i(I)}}catch(I){i(I)}})}).catch(i)});return m(o,t),o}function ft(e,r){var t=this;e=O(e);var a=new d(function(o,n){t.ready().then(function(){k(t._dbInfo,l,function(i,s){if(i)return n(i);try{var f=s.objectStore(t._dbInfo.storeName),c=f.delete(e);s.oncomplete=function(){o()},s.onerror=function(){n(c.error)},s.onabort=function(){var u=c.error?c.error:c.transaction.error;n(u)}}catch(u){n(u)}})}).catch(n)});return m(a,r),a}function ct(e){var r=this,t=new d(function(a,o){r.ready().then(function(){k(r._dbInfo,l,function(n,i){if(n)return o(n);try{var s=i.objectStore(r._dbInfo.storeName),f=s.clear();i.oncomplete=function(){a()},i.onabort=i.onerror=function(){var c=f.error?f.error:f.transaction.error;o(c)}}catch(c){o(c)}})}).catch(o)});return m(t,e),t}function ut(e){var r=this,t=new d(function(a,o){r.ready().then(function(){k(r._dbInfo,Z,function(n,i){if(n)return o(n);try{var s=i.objectStore(r._dbInfo.storeName),f=s.count();f.onsuccess=function(){a(f.result)},f.onerror=function(){o(f.error)}}catch(c){o(c)}})}).catch(o)});return m(t,e),t}function lt(e,r){var t=this,a=new d(function(o,n){if(e<0){o(null);return}t.ready().then(function(){k(t._dbInfo,Z,function(i,s){if(i)return n(i);try{var f=s.objectStore(t._dbInfo.storeName),c=!1,u=f.openKeyCursor();u.onsuccess=function(){var h=u.result;if(!h){o(null);return}e===0||c?o(h.key):(c=!0,h.advance(e))},u.onerror=function(){n(u.error)}}catch(h){n(h)}})}).catch(n)});return m(a,r),a}function dt(e){var r=this,t=new d(function(a,o){r.ready().then(function(){k(r._dbInfo,Z,function(n,i){if(n)return o(n);try{var s=i.objectStore(r._dbInfo.storeName),f=s.openKeyCursor(),c=[];f.onsuccess=function(){var u=f.result;if(!u){a(c);return}c.push(u.key),u.continue()},f.onerror=function(){o(f.error)}}catch(u){o(u)}})}).catch(o)});return m(t,e),t}function vt(e,r){r=V.apply(this,arguments);var t=this.config();e=typeof e!="function"&&e||{},e.name||(e.name=e.name||t.name,e.storeName=e.storeName||t.storeName);var a=this,o;if(!e.name)o=d.reject("Invalid arguments");else{var n=e.name===t.name&&a._dbInfo.db,i=n?d.resolve(a._dbInfo.db):X(e).then(function(s){var f=B[e.name],c=f.forages;f.db=s;for(var u=0;u<c.length;u++)c[u]._dbInfo.db=s;return s});e.storeName?o=i.then(function(s){if(s.objectStoreNames.contains(e.storeName)){var f=s.version+1;z(e);var c=B[e.name],u=c.forages;s.close();for(var h=0;h<u.length;h++){var y=u[h];y._dbInfo.db=null,y._dbInfo.version=f}var I=new d(function(S,T){var x=U.open(e.name,f);x.onerror=function(j){var ne=x.result;ne.close(),T(j)},x.onupgradeneeded=function(){var j=x.result;j.deleteObjectStore(e.storeName)},x.onsuccess=function(){var j=x.result;j.close(),S(j)}});return I.then(function(S){c.db=S;for(var T=0;T<u.length;T++){var x=u[T];x._dbInfo.db=S,$(x._dbInfo)}}).catch(function(S){throw(Y(e,S)||d.resolve()).catch(function(){}),S})}}):o=i.then(function(s){z(e);var f=B[e.name],c=f.forages;s.close();for(var u=0;u<c.length;u++){var h=c[u];h._dbInfo.db=null}var y=new d(function(I,S){var T=U.deleteDatabase(e.name);T.onerror=function(){var x=T.result;x&&x.close(),S(T.error)},T.onblocked=function(){console.warn('dropInstance blocked for database "'+e.name+'" until all open connections are closed')},T.onsuccess=function(){var x=T.result;x&&x.close(),I(x)}});return y.then(function(I){f.db=I;for(var S=0;S<c.length;S++){var T=c[S];$(T._dbInfo)}}).catch(function(I){throw(Y(e,I)||d.resolve()).catch(function(){}),I})})}return m(o,r),o}var ht={_driver:"asyncStorage",_initStorage:at,_support:M(),iterate:it,getItem:ot,setItem:st,removeItem:ft,clear:ct,length:ut,key:lt,keys:dt,dropInstance:vt};function mt(){return typeof openDatabase=="function"}var Q="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",gt="~~local_forage_type~",De=/^~~local_forage_type~([^~]+)~/,se="__lfsc__:",de=se.length,ve="arbf",he="blob",Ce="si08",Ae="ui08",Re="uic8",xe="si16",Ne="si32",Te="ur16",Oe="ui32",Le="fl32",Be="fl64",Pe=de+ve.length,Fe=Object.prototype.toString;function Me(e){var r=e.length*.75,t=e.length,a,o=0,n,i,s,f;e[e.length-1]==="="&&(r--,e[e.length-2]==="="&&r--);var c=new ArrayBuffer(r),u=new Uint8Array(c);for(a=0;a<t;a+=4)n=Q.indexOf(e[a]),i=Q.indexOf(e[a+1]),s=Q.indexOf(e[a+2]),f=Q.indexOf(e[a+3]),u[o++]=n<<2|i>>4,u[o++]=(i&15)<<4|s>>2,u[o++]=(s&3)<<6|f&63;return c}function me(e){var r=new Uint8Array(e),t="",a;for(a=0;a<r.length;a+=3)t+=Q[r[a]>>2],t+=Q[(r[a]&3)<<4|r[a+1]>>4],t+=Q[(r[a+1]&15)<<2|r[a+2]>>6],t+=Q[r[a+2]&63];return r.length%3===2?t=t.substring(0,t.length-1)+"=":r.length%3===1&&(t=t.substring(0,t.length-2)+"=="),t}function yt(e,r){var t="";if(e&&(t=Fe.call(e)),e&&(t==="[object ArrayBuffer]"||e.buffer&&Fe.call(e.buffer)==="[object ArrayBuffer]")){var a,o=se;e instanceof ArrayBuffer?(a=e,o+=ve):(a=e.buffer,t==="[object Int8Array]"?o+=Ce:t==="[object Uint8Array]"?o+=Ae:t==="[object Uint8ClampedArray]"?o+=Re:t==="[object Int16Array]"?o+=xe:t==="[object Uint16Array]"?o+=Te:t==="[object Int32Array]"?o+=Ne:t==="[object Uint32Array]"?o+=Oe:t==="[object Float32Array]"?o+=Le:t==="[object Float64Array]"?o+=Be:r(new Error("Failed to get type for BinaryArray"))),r(o+me(a))}else if(t==="[object Blob]"){var n=new FileReader;n.onload=function(){var i=gt+e.type+"~"+me(this.result);r(se+he+i)},n.readAsArrayBuffer(e)}else try{r(JSON.stringify(e))}catch(i){console.error("Couldn't convert value into a JSON string: ",e),r(null,i)}}function pt(e){if(e.substring(0,de)!==se)return JSON.parse(e);var r=e.substring(Pe),t=e.substring(de,Pe),a;if(t===he&&De.test(r)){var o=r.match(De);a=o[1],r=r.substring(o[0].length)}var n=Me(r);switch(t){case ve:return n;case he:return K([n],{type:a});case Ce:return new Int8Array(n);case Ae:return new Uint8Array(n);case Re:return new Uint8ClampedArray(n);case xe:return new Int16Array(n);case Te:return new Uint16Array(n);case Ne:return new Int32Array(n);case Oe:return new Uint32Array(n);case Le:return new Float32Array(n);case Be:return new Float64Array(n);default:throw new Error("Unkown type: "+t)}}var ge={serialize:yt,deserialize:pt,stringToBuffer:Me,bufferToString:me};function ze(e,r,t,a){e.executeSql("CREATE TABLE IF NOT EXISTS "+r.storeName+" (id INTEGER PRIMARY KEY, key unique, value)",[],t,a)}function bt(e){var r=this,t={db:null};if(e)for(var a in e)t[a]=typeof e[a]!="string"?e[a].toString():e[a];var o=new d(function(n,i){try{t.db=openDatabase(t.name,String(t.version),t.description,t.size)}catch(s){return i(s)}t.db.transaction(function(s){ze(s,t,function(){r._dbInfo=t,n()},function(f,c){i(c)})},i)});return t.serializer=ge,o}function J(e,r,t,a,o,n){e.executeSql(t,a,o,function(i,s){s.code===s.SYNTAX_ERR?i.executeSql("SELECT name FROM sqlite_master WHERE type='table' AND name = ?",[r.storeName],function(f,c){c.rows.length?n(f,s):ze(f,r,function(){f.executeSql(t,a,o,n)},n)},n):n(i,s)},n)}function _t(e,r){var t=this;e=O(e);var a=new d(function(o,n){t.ready().then(function(){var i=t._dbInfo;i.db.transaction(function(s){J(s,i,"SELECT * FROM "+i.storeName+" WHERE key = ? LIMIT 1",[e],function(f,c){var u=c.rows.length?c.rows.item(0).value:null;u&&(u=i.serializer.deserialize(u)),o(u)},function(f,c){n(c)})})}).catch(n)});return m(a,r),a}function wt(e,r){var t=this,a=new d(function(o,n){t.ready().then(function(){var i=t._dbInfo;i.db.transaction(function(s){J(s,i,"SELECT * FROM "+i.storeName,[],function(f,c){for(var u=c.rows,h=u.length,y=0;y<h;y++){var I=u.item(y),S=I.value;if(S&&(S=i.serializer.deserialize(S)),S=e(S,I.key,y+1),S!==void 0){o(S);return}}o()},function(f,c){n(c)})})}).catch(n)});return m(a,r),a}function Ue(e,r,t,a){var o=this;e=O(e);var n=new d(function(i,s){o.ready().then(function(){r===void 0&&(r=null);var f=r,c=o._dbInfo;c.serializer.serialize(r,function(u,h){h?s(h):c.db.transaction(function(y){J(y,c,"INSERT OR REPLACE INTO "+c.storeName+" (key, value) VALUES (?, ?)",[e,u],function(){i(f)},function(I,S){s(S)})},function(y){if(y.code===y.QUOTA_ERR){if(a>0){i(Ue.apply(o,[e,f,t,a-1]));return}s(y)}})})}).catch(s)});return m(n,t),n}function It(e,r,t){return Ue.apply(this,[e,r,t,1])}function St(e,r){var t=this;e=O(e);var a=new d(function(o,n){t.ready().then(function(){var i=t._dbInfo;i.db.transaction(function(s){J(s,i,"DELETE FROM "+i.storeName+" WHERE key = ?",[e],function(){o()},function(f,c){n(c)})})}).catch(n)});return m(a,r),a}function Et(e){var r=this,t=new d(function(a,o){r.ready().then(function(){var n=r._dbInfo;n.db.transaction(function(i){J(i,n,"DELETE FROM "+n.storeName,[],function(){a()},function(s,f){o(f)})})}).catch(o)});return m(t,e),t}function Dt(e){var r=this,t=new d(function(a,o){r.ready().then(function(){var n=r._dbInfo;n.db.transaction(function(i){J(i,n,"SELECT COUNT(key) as c FROM "+n.storeName,[],function(s,f){var c=f.rows.item(0).c;a(c)},function(s,f){o(f)})})}).catch(o)});return m(t,e),t}function Ct(e,r){var t=this,a=new d(function(o,n){t.ready().then(function(){var i=t._dbInfo;i.db.transaction(function(s){J(s,i,"SELECT key FROM "+i.storeName+" WHERE id = ? LIMIT 1",[e+1],function(f,c){var u=c.rows.length?c.rows.item(0).key:null;o(u)},function(f,c){n(c)})})}).catch(n)});return m(a,r),a}function At(e){var r=this,t=new d(function(a,o){r.ready().then(function(){var n=r._dbInfo;n.db.transaction(function(i){J(i,n,"SELECT key FROM "+n.storeName,[],function(s,f){for(var c=[],u=0;u<f.rows.length;u++)c.push(f.rows.item(u).key);a(c)},function(s,f){o(f)})})}).catch(o)});return m(t,e),t}function Rt(e){return new d(function(r,t){e.transaction(function(a){a.executeSql("SELECT name FROM sqlite_master WHERE type='table' AND name <> '__WebKitDatabaseInfoTable__'",[],function(o,n){for(var i=[],s=0;s<n.rows.length;s++)i.push(n.rows.item(s).name);r({db:e,storeNames:i})},function(o,n){t(n)})},function(a){t(a)})})}function xt(e,r){r=V.apply(this,arguments);var t=this.config();e=typeof e!="function"&&e||{},e.name||(e.name=e.name||t.name,e.storeName=e.storeName||t.storeName);var a=this,o;return e.name?o=new d(function(n){var i;e.name===t.name?i=a._dbInfo.db:i=openDatabase(e.name,"","",0),e.storeName?n({db:i,storeNames:[e.storeName]}):n(Rt(i))}).then(function(n){return new d(function(i,s){n.db.transaction(function(f){function c(I){return new d(function(S,T){f.executeSql("DROP TABLE IF EXISTS "+I,[],function(){S()},function(x,j){T(j)})})}for(var u=[],h=0,y=n.storeNames.length;h<y;h++)u.push(c(n.storeNames[h]));d.all(u).then(function(){i()}).catch(function(I){s(I)})},function(f){s(f)})})}):o=d.reject("Invalid arguments"),m(o,r),o}var Nt={_driver:"webSQLStorage",_initStorage:bt,_support:mt(),iterate:wt,getItem:_t,setItem:It,removeItem:St,clear:Et,length:Dt,key:Ct,keys:At,dropInstance:xt};function Tt(){try{return typeof localStorage<"u"&&"setItem"in localStorage&&!!localStorage.setItem}catch{return!1}}function $e(e,r){var t=e.name+"/";return e.storeName!==r.storeName&&(t+=e.storeName+"/"),t}function Ot(){var e="_localforage_support_test";try{return localStorage.setItem(e,!0),localStorage.removeItem(e),!1}catch{return!0}}function Lt(){return!Ot()||localStorage.length>0}function Bt(e){var r=this,t={};if(e)for(var a in e)t[a]=e[a];return t.keyPrefix=$e(e,r._defaultConfig),Lt()?(r._dbInfo=t,t.serializer=ge,d.resolve()):d.reject()}function Pt(e){var r=this,t=r.ready().then(function(){for(var a=r._dbInfo.keyPrefix,o=localStorage.length-1;o>=0;o--){var n=localStorage.key(o);n.indexOf(a)===0&&localStorage.removeItem(n)}});return m(t,e),t}function Ft(e,r){var t=this;e=O(e);var a=t.ready().then(function(){var o=t._dbInfo,n=localStorage.getItem(o.keyPrefix+e);return n&&(n=o.serializer.deserialize(n)),n});return m(a,r),a}function Mt(e,r){var t=this,a=t.ready().then(function(){for(var o=t._dbInfo,n=o.keyPrefix,i=n.length,s=localStorage.length,f=1,c=0;c<s;c++){var u=localStorage.key(c);if(u.indexOf(n)===0){var h=localStorage.getItem(u);if(h&&(h=o.serializer.deserialize(h)),h=e(h,u.substring(i),f++),h!==void 0)return h}}});return m(a,r),a}function zt(e,r){var t=this,a=t.ready().then(function(){var o=t._dbInfo,n;try{n=localStorage.key(e)}catch{n=null}return n&&(n=n.substring(o.keyPrefix.length)),n});return m(a,r),a}function Ut(e){var r=this,t=r.ready().then(function(){for(var a=r._dbInfo,o=localStorage.length,n=[],i=0;i<o;i++){var s=localStorage.key(i);s.indexOf(a.keyPrefix)===0&&n.push(s.substring(a.keyPrefix.length))}return n});return m(t,e),t}function $t(e){var r=this,t=r.keys().then(function(a){return a.length});return m(t,e),t}function Yt(e,r){var t=this;e=O(e);var a=t.ready().then(function(){var o=t._dbInfo;localStorage.removeItem(o.keyPrefix+e)});return m(a,r),a}function Wt(e,r,t){var a=this;e=O(e);var o=a.ready().then(function(){r===void 0&&(r=null);var n=r;return new d(function(i,s){var f=a._dbInfo;f.serializer.serialize(r,function(c,u){if(u)s(u);else try{localStorage.setItem(f.keyPrefix+e,c),i(n)}catch(h){(h.name==="QuotaExceededError"||h.name==="NS_ERROR_DOM_QUOTA_REACHED")&&s(h),s(h)}})})});return m(o,t),o}function jt(e,r){if(r=V.apply(this,arguments),e=typeof e!="function"&&e||{},!e.name){var t=this.config();e.name=e.name||t.name,e.storeName=e.storeName||t.storeName}var a=this,o;return e.name?o=new d(function(n){e.storeName?n($e(e,a._defaultConfig)):n(e.name+"/")}).then(function(n){for(var i=localStorage.length-1;i>=0;i--){var s=localStorage.key(i);s.indexOf(n)===0&&localStorage.removeItem(s)}}):o=d.reject("Invalid arguments"),m(o,r),o}var Kt={_driver:"localStorageWrapper",_initStorage:Bt,_support:Tt(),iterate:Mt,getItem:Ft,setItem:Wt,removeItem:Yt,clear:Pt,length:$t,key:zt,keys:Ut,dropInstance:jt},Vt=function(r,t){return r===t||typeof r=="number"&&typeof t=="number"&&isNaN(r)&&isNaN(t)},Ht=function(r,t){for(var a=r.length,o=0;o<a;){if(Vt(r[o],t))return!0;o++}return!1},Ye=Array.isArray||function(e){return Object.prototype.toString.call(e)==="[object Array]"},re={},We={},ee={INDEXEDDB:ht,WEBSQL:Nt,LOCALSTORAGE:Kt},Xt=[ee.INDEXEDDB._driver,ee.WEBSQL._driver,ee.LOCALSTORAGE._driver],fe=["dropInstance"],ye=["clear","getItem","iterate","key","keys","length","removeItem","setItem"].concat(fe),kt={description:"",driver:Xt.slice(),name:"localforage",size:4980736,storeName:"keyvaluepairs",version:1};function Gt(e,r){e[r]=function(){var t=arguments;return e.ready().then(function(){return e[r].apply(e,t)})}}function pe(){for(var e=1;e<arguments.length;e++){var r=arguments[e];if(r)for(var t in r)r.hasOwnProperty(t)&&(Ye(r[t])?arguments[0][t]=r[t].slice():arguments[0][t]=r[t])}return arguments[0]}var Qt=function(){function e(r){F(this,e);for(var t in ee)if(ee.hasOwnProperty(t)){var a=ee[t],o=a._driver;this[t]=o,re[o]||this.defineDriver(a)}this._defaultConfig=pe({},kt),this._config=pe({},this._defaultConfig,r),this._driverSet=null,this._initDriver=null,this._ready=!1,this._dbInfo=null,this._wrapLibraryMethodsWithReady(),this.setDriver(this._config.driver).catch(function(){})}return e.prototype.config=function(t){if((typeof t>"u"?"undefined":E(t))==="object"){if(this._ready)return new Error("Can't call config() after localforage has been used.");for(var a in t){if(a==="storeName"&&(t[a]=t[a].replace(/\W/g,"_")),a==="version"&&typeof t[a]!="number")return new Error("Database version must be a number.");this._config[a]=t[a]}return"driver"in t&&t.driver?this.setDriver(this._config.driver):!0}else return typeof t=="string"?this._config[t]:this._config},e.prototype.defineDriver=function(t,a,o){var n=new d(function(i,s){try{var f=t._driver,c=new Error("Custom driver not compliant; see https://mozilla.github.io/localForage/#definedriver");if(!t._driver){s(c);return}for(var u=ye.concat("_initStorage"),h=0,y=u.length;h<y;h++){var I=u[h],S=!Ht(fe,I);if((S||t[I])&&typeof t[I]!="function"){s(c);return}}var T=function(){for(var ne=function(qt){return function(){var er=new Error("Method "+qt+" is not implemented by the current driver"),je=d.reject(er);return m(je,arguments[arguments.length-1]),je}},be=0,Zt=fe.length;be<Zt;be++){var _e=fe[be];t[_e]||(t[_e]=ne(_e))}};T();var x=function(ne){re[f]&&console.info("Redefining LocalForage driver: "+f),re[f]=t,We[f]=ne,i()};"_support"in t?t._support&&typeof t._support=="function"?t._support().then(x,s):x(!!t._support):x(!0)}catch(j){s(j)}});return N(n,a,o),n},e.prototype.driver=function(){return this._driver||null},e.prototype.getDriver=function(t,a,o){var n=re[t]?d.resolve(re[t]):d.reject(new Error("Driver not found."));return N(n,a,o),n},e.prototype.getSerializer=function(t){var a=d.resolve(ge);return N(a,t),a},e.prototype.ready=function(t){var a=this,o=a._driverSet.then(function(){return a._ready===null&&(a._ready=a._initDriver()),a._ready});return N(o,t,t),o},e.prototype.setDriver=function(t,a,o){var n=this;Ye(t)||(t=[t]);var i=this._getSupportedDrivers(t);function s(){n._config.driver=n.driver()}function f(h){return n._extend(h),s(),n._ready=n._initStorage(n._config),n._ready}function c(h){return function(){var y=0;function I(){for(;y<h.length;){var S=h[y];return y++,n._dbInfo=null,n._ready=null,n.getDriver(S).then(f).catch(I)}s();var T=new Error("No available storage method found.");return n._driverSet=d.reject(T),n._driverSet}return I()}}var u=this._driverSet!==null?this._driverSet.catch(function(){return d.resolve()}):d.resolve();return this._driverSet=u.then(function(){var h=i[0];return n._dbInfo=null,n._ready=null,n.getDriver(h).then(function(y){n._driver=y._driver,s(),n._wrapLibraryMethodsWithReady(),n._initDriver=c(i)})}).catch(function(){s();var h=new Error("No available storage method found.");return n._driverSet=d.reject(h),n._driverSet}),N(this._driverSet,a,o),this._driverSet},e.prototype.supports=function(t){return!!We[t]},e.prototype._extend=function(t){pe(this,t)},e.prototype._getSupportedDrivers=function(t){for(var a=[],o=0,n=t.length;o<n;o++){var i=t[o];this.supports(i)&&a.push(i)}return a},e.prototype._wrapLibraryMethodsWithReady=function(){for(var t=0,a=ye.length;t<a;t++)Gt(this,ye[t])},e.prototype.createInstance=function(t){return new e(t)},e}(),Jt=new Qt;A.exports=Jt},{3:3}]},{},[4])(4)})});var oe=rr(qe());var ce={SecureStorage:"ionicSecureStorage",IndexedDB:oe.default.INDEXEDDB,LocalStorage:oe.default.LOCALSTORAGE},et={name:"_ionicstorage",storeName:"_ionickv",dbKey:"_ionickey",driverOrder:[ce.SecureStorage,ce.IndexedDB,ce.LocalStorage]},G=class{constructor(g=et){this._db=null,this._secureStorageDriver=null;let v=Object.assign({},et,g||{});this._config=v}create(){return _(this,null,function*(){let g=oe.default.createInstance(this._config);return this._db=g,yield g.setDriver(this._config.driverOrder||[]),this})}defineDriver(g){return _(this,null,function*(){return g._driver===ce.SecureStorage&&(this._secureStorageDriver=g),oe.default.defineDriver(g)})}get driver(){var g;return((g=this._db)===null||g===void 0?void 0:g.driver())||null}assertDb(){if(!this._db)throw new Error("Database not created. Must call create() first");return this._db}get(g){return this.assertDb().getItem(g)}set(g,v){return this.assertDb().setItem(g,v)}remove(g){return this.assertDb().removeItem(g)}clear(){return this.assertDb().clear()}length(){return this.assertDb().length()}keys(){return this.assertDb().keys()}forEach(g){return this.assertDb().iterate(g)}setEncryptionKey(g){var v;if(this._secureStorageDriver)(v=this._secureStorageDriver)===null||v===void 0||v.setEncryptionKey(g);else throw new Error("@ionic-enterprise/secure-storage not installed. Encryption support not available")}};var tt=new Xe("STORAGE_CONFIG_TOKEN"),Ie=class extends G{constructor(){super()}create(){return _(this,null,function*(){return this})}defineDriver(){return _(this,null,function*(){})}get driver(){return"noop"}get(g){return _(this,null,function*(){return null})}set(g,v){return _(this,null,function*(){})}remove(g){return _(this,null,function*(){})}clear(){return _(this,null,function*(){})}length(){return _(this,null,function*(){return 0})}keys(){return _(this,null,function*(){return[]})}forEach(g){return _(this,null,function*(){})}setEncryptionKey(g){}};function ar(R,g){return Je(R)?new Ie:new G(g)}var hr=(()=>{class R{static forRoot(v=null){return{ngModule:R,providers:[{provide:tt,useValue:v},{provide:G,useFactory:ar,deps:[Qe,tt]}]}}}return R.\u0275fac=function(v){return new(v||R)},R.\u0275mod=Ge({type:R}),R.\u0275inj=He({}),R})();var pr=(()=>{class R{constructor(v){this.storage=v,this._storage=null,this.isInitialized=!1,this.initPromise=null,this.CACHE_EXPIRY={evacuationCenters:24*60*60*1e3,userLocation:60*60*1e3,mapData:7*24*60*60*1e3,emergencyContacts:30*24*60*60*1e3,disasterInfo:24*60*60*1e3},this.offlineDataSubject=new Ke(!1),this.offlineDataAvailable$=this.offlineDataSubject.asObservable(),this.init()}init(){return _(this,null,function*(){return this.initPromise?this.initPromise:(this.initPromise=this.initializeStorage(),this.initPromise)})}initializeStorage(){return _(this,null,function*(){try{let v=yield this.storage.create();this._storage=v,this.isInitialized=!0,console.log("\u2705 Offline Storage initialized successfully"),yield this.checkOfflineDataAvailability()}catch(v){console.error("\u274C Failed to initialize offline storage:",v),this.isInitialized=!1}})}ensureInitialized(){return _(this,null,function*(){if(this.isInitialized||(yield this.init()),!this._storage)throw new Error("Storage not initialized")})}setItem(v,b,A){return _(this,null,function*(){yield this.ensureInitialized();let P=A||this.CACHE_EXPIRY[v]||this.CACHE_EXPIRY.evacuationCenters,E=new Date(Date.now()+P),F={data:b,metadata:{key:v,lastUpdated:new Date,expiresAt:E,size:JSON.stringify(b).length}};yield this._storage.set(v,F),console.log(`\u{1F4BE} Cached ${v} (expires: ${E.toLocaleString()})`),yield this.checkOfflineDataAvailability()})}getItem(v){return _(this,null,function*(){yield this.ensureInitialized();try{let b=yield this._storage.get(v);if(!b)return null;let A=new Date,P=new Date(b.metadata.expiresAt);return A>P?(console.log(`\u23F0 Cache expired for ${v}, removing...`),yield this.removeItem(v),null):(console.log(`\u{1F4E6} Retrieved cached ${v} (expires: ${P.toLocaleString()})`),b.data)}catch(b){return console.error(`Error getting ${v} from cache:`,b),null}})}removeItem(v){return _(this,null,function*(){yield this.ensureInitialized(),yield this._storage.remove(v),console.log(`\u{1F5D1}\uFE0F Removed ${v} from cache`),yield this.checkOfflineDataAvailability()})}clearAll(){return _(this,null,function*(){yield this.ensureInitialized(),yield this._storage.clear(),console.log("\u{1F9F9} Cleared all offline cache"),this.offlineDataSubject.next(!1)})}getCacheMetadata(){return _(this,null,function*(){yield this.ensureInitialized();let v=[];return yield this._storage.forEach((b,A)=>{b&&b.metadata&&v.push(b.metadata)}),v})}checkOfflineDataAvailability(){return _(this,null,function*(){try{let v=yield this.getItem("evacuationCenters"),b=!!(v&&v.length>0);this.offlineDataSubject.next(b)}catch(v){console.error("Error checking offline data availability:",v),this.offlineDataSubject.next(!1)}})}cacheEvacuationCenters(v){return _(this,null,function*(){yield this.setItem("evacuationCenters",v),console.log(`\u{1F4BE} Cached ${v.length} evacuation centers`)})}getCachedEvacuationCenters(){return _(this,null,function*(){return yield this.getItem("evacuationCenters")})}cacheUserLocation(v){return _(this,null,function*(){yield this.setItem("userLocation",v),console.log(`\u{1F4CD} Cached user location: [${v.lat}, ${v.lng}]`)})}getCachedUserLocation(){return _(this,null,function*(){return yield this.getItem("userLocation")})}cacheEmergencyContacts(v){return _(this,null,function*(){yield this.setItem("emergencyContacts",v),console.log(`\u{1F4DE} Cached ${v.length} emergency contacts`)})}getCachedEmergencyContacts(){return _(this,null,function*(){return yield this.getItem("emergencyContacts")})}cacheDisasterInfo(v){return _(this,null,function*(){yield this.setItem("disasterInfo",v),console.log(`\u{1F32A}\uFE0F Cached ${v.length} disaster information items`)})}getCachedDisasterInfo(){return _(this,null,function*(){return yield this.getItem("disasterInfo")})}getCacheSize(){return _(this,null,function*(){return(yield this.getCacheMetadata()).reduce((b,A)=>b+A.size,0)})}cleanExpiredCache(){return _(this,null,function*(){yield this.ensureInitialized();let v=new Date,b=[];yield this._storage.forEach((A,P)=>{if(A&&A.metadata){let E=new Date(A.metadata.expiresAt);v>E&&b.push(P)}});for(let A of b)yield this.removeItem(A);b.length>0&&console.log(`\u{1F9F9} Cleaned ${b.length} expired cache items`)})}isOfflineMode(){return!navigator.onLine}getOfflineDataSummary(){return _(this,null,function*(){let v=yield this.getCachedEvacuationCenters(),b=yield this.getCachedUserLocation(),A=yield this.getCachedEmergencyContacts(),P=yield this.getCachedDisasterInfo(),E=yield this.getCacheSize();return{evacuationCenters:v?.length||0,userLocation:!!b,emergencyContacts:A?.length||0,disasterInfo:P?.length||0,cacheSize:Math.round(E/1024),isOffline:this.isOfflineMode()}})}static{this.\u0275fac=function(b){return new(b||R)(ke(G))}}static{this.\u0275prov=Ve({token:R,factory:R.\u0275fac,providedIn:"root"})}}return R})();export{hr as a,pr as b};
