<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\DeviceToken;

echo "=== Latest Token Check ===\n\n";

try {
    // Get the most recent active token
    $activeToken = DeviceToken::where('is_active', true)->orderBy('updated_at', 'desc')->first();
    
    if ($activeToken) {
        echo "📱 Active Token Details:\n";
        echo "   ID: " . $activeToken->id . "\n";
        echo "   Token: " . substr($activeToken->token, 0, 50) . "...\n";
        echo "   Device Type: " . $activeToken->device_type . "\n";
        echo "   Project ID: " . ($activeToken->project_id ?? 'null') . "\n";
        echo "   Created: " . $activeToken->created_at . "\n";
        echo "   Updated: " . $activeToken->updated_at . "\n";
        echo "   Is Active: " . ($activeToken->is_active ? 'Yes' : 'No') . "\n\n";
        
        // Check if this is the new FCM token from the logcat
        $expectedTokenStart = "er8fw2H7Rra7KBOYVTNp8D:APA91bF-A1Vn6DrvJmWcP_evJZnmO884oG96xYB4OZ7B6yyRSu-8tbyZgX9ZfZIyU1kuLZW-l47L0Zo81iTmjr-uXfbSrl-kmXk7oGVOWRKgoEkfJBRbuUY";
        
        if (strpos($activeToken->token, 'er8fw2H7Rra7KBOYVTNp8D') !== false) {
            echo "🎉 SUCCESS! This is the NEW FCM token from the mobile app!\n";
            echo "   The token matches the one generated at 22:09:10\n\n";
        } else {
            echo "⚠️  This appears to be an older token\n";
            echo "   Expected token to start with: er8fw2H7Rra7KBOYVTNp8D\n";
            echo "   Actual token starts with: " . substr($activeToken->token, 0, 20) . "\n\n";
        }
        
        // Check if updated recently
        $updatedAt = new DateTime($activeToken->updated_at);
        $now = new DateTime();
        $diff = $now->diff($updatedAt);
        
        if ($diff->i < 5) { // Updated within last 5 minutes
            echo "✅ Token was updated recently (" . $diff->i . " minutes ago)\n";
        } else {
            echo "⏰ Token was last updated " . $diff->h . " hours and " . $diff->i . " minutes ago\n";
        }
        
    } else {
        echo "❌ No active tokens found\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== Check Complete ===\n";
