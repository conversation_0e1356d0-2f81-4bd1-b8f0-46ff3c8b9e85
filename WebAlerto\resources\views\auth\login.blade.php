<!-- filepath: d:\laravel\WebAlerto\resources\views\auth\login.blade.php -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - WebAlerto</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .custom-shadow {
            box-shadow: 0 20px 25px -5px rgba(14, 165, 233, 0.1), 0 10px 10px -5px rgba(14, 165, 233, 0.04);
        }
        .hover-shadow {
            transition: all 0.3s ease;
        }
        .hover-shadow:hover {
            box-shadow: 0 25px 30px -5px rgba(14, 165, 233, 0.2), 0 15px 15px -5px rgba(14, 165, 233, 0.1);
        }
        .disabled-link {
            pointer-events: none;
            opacity: 0.5;
            cursor: not-allowed;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-sky-50 to-blue-50 min-h-screen">
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="max-w-md w-full space-y-8 bg-white p-10 rounded-xl custom-shadow hover-shadow">
            <div class="text-center">
                <img src="{{ asset('image/ALERTO Logo.png') }}" alt="WebAlerto Logo" class="w-40 h-40 mx-auto mb-6 object-contain">
                <h2 class="text-3xl md:text-4xl font-extrabold text-sky-600 leading-tight">Welcome Back</h2>
                <p class="mt-2 text-sm text-sky-600">Please sign in to your account</p>
            </div>

            @if ($errors->any())
                <div class="bg-red-50 border-l-4 border-red-500 p-4 rounded-md">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-red-700">{{ $errors->first() }}</p>
                        </div>
                    </div>
                </div>
            @endif

            @if (session('success'))
                <div class="bg-green-50 border-l-4 border-green-500 p-4 rounded-md">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-green-700">{{ session('success') }}</p>
                        </div>
                    </div>
                </div>
            @endif

            <form class="mt-8 space-y-6" action="{{ route('login') }}" method="POST" id="loginForm">
                @csrf
                <div class="space-y-4">
                    <div>
                        <label for="email" class="block text-sm font-medium text-sky-700">Email Address</label>
                        <div class="mt-1">
                            <input id="email" name="email" type="email" required 
                                class="appearance-none block w-full px-3 py-2.5 border border-sky-200 rounded-lg shadow-sm placeholder-sky-300 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition duration-150 ease-in-out"
                                placeholder="Enter your email">
                        </div>
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-medium text-sky-700">Password</label>
                        <div class="mt-1">
                            <input id="password" name="password" type="password" required 
                                class="appearance-none block w-full px-3 py-2.5 border border-sky-200 rounded-lg shadow-sm placeholder-sky-300 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition duration-150 ease-in-out"
                                placeholder="Enter your password">
                        </div>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input id="remember" name="remember" type="checkbox" 
                            class="h-4 w-4 text-sky-600 focus:ring-sky-500 border-sky-300 rounded">
                        <label for="remember" class="ml-2 block text-sm text-sky-700">Remember me</label>
                    </div>
                    <a href="{{ route('password.change.form') }}" 
                        id="changePasswordLink"
                        class="text-sm font-medium text-sky-600 hover:text-sky-500 transition duration-150 ease-in-out disabled-link">
                        Change Password
                    </a>
                </div>

                <div>
                    <button type="submit" 
                        class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-sky-600 hover:bg-sky-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 transition duration-150 ease-in-out">
                        Sign in
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');
            const changePasswordLink = document.getElementById('changePasswordLink');
            const loginForm = document.getElementById('loginForm');

            function validateFields() {
                const email = emailInput.value.trim();
                const password = passwordInput.value.trim();
                
                if (email && password) {
                    changePasswordLink.classList.remove('disabled-link');
                } else {
                    changePasswordLink.classList.add('disabled-link');
                }
            }

            // Add event listeners to both inputs
            emailInput.addEventListener('input', validateFields);
            passwordInput.addEventListener('input', validateFields);

            // Prevent form submission if fields are empty
            loginForm.addEventListener('submit', function(e) {
                const email = emailInput.value.trim();
                const password = passwordInput.value.trim();
                
                if (!email || !password) {
                    e.preventDefault();
                    alert('Please fill in all required fields');
                }
            });

            // Initial validation
            validateFields();
        });
    </script>
</body>
</html>