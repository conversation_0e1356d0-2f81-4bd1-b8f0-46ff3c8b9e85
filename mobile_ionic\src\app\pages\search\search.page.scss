ion-header {
  background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);

  ion-toolbar {
    --background: transparent;
    --color: white;
  }
}

ion-content {
  --background: #f8f9fa;
}

.search-container {
  padding: 10px 16px 0;

  ion-searchbar {
    --border-radius: 10px;
    --box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    --placeholder-color: var(--ion-color-medium);
    --icon-color: var(--ion-color-primary);
  }

  .search-hint {
    font-size: 12px;
    margin: 0 0 10px 16px;
    display: block;
  }
}

.search-results {
  padding: 0 16px 16px;
}

.loading-container,
.error-container,
.no-results,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 32px 16px;
  min-height: 200px;

  ion-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  p {
    margin: 8px 0;
    font-size: 16px;
  }

  ion-button {
    margin-top: 16px;
  }
}

ion-list {
  background: transparent;
  padding: 0;
}

ion-item {
  --padding-start: 16px;
  --inner-padding-end: 16px;
  --background: white;
  margin-bottom: 10px;
  border-radius: 10px;
  --border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  h2 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 4px;
    color: var(--ion-color-dark);
  }

  p {
    font-size: 14px;
    color: var(--ion-color-medium);
    margin: 2px 0;
  }

  ion-badge {
    margin-right: 6px;
    padding: 4px 8px;
    border-radius: 4px;
  }
}

.empty-state {
  h3 {
    font-size: 18px;
    font-weight: 500;
    margin: 8px 0;
    color: var(--ion-color-dark);
  }

  p {
    margin-bottom: 20px;
  }
}