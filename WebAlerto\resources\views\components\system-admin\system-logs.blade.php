@extends('layout.app')

@section('title', 'System Logs - Technical Administrator')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-gray-50 to-slate-100 py-8">
    <div class="max-w-[1920px] mx-auto px-6 sm:px-8 lg:px-12">
        
        <!-- Header Section -->
        <div class="bg-white/90 backdrop-blur-sm rounded-xl shadow-xl border border-slate-200 p-6 mb-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div class="p-3 bg-gradient-to-br from-slate-600 to-gray-700 rounded-lg shadow-lg">
                        <i class="fas fa-file-alt text-white text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">System Logs</h1>
                        <p class="text-sm text-gray-600 mt-1">Monitor system activity and troubleshoot issues</p>
                    </div>
                </div>
                
                <div class="flex items-center gap-3">
                    <button onclick="refreshLogs()" class="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-sync-alt"></i>
                        <span>Refresh</span>
                    </button>
                    <button onclick="downloadLogs()" class="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-download"></i>
                        <span>Download</span>
                    </button>
                </div>
            </div>
            
            @if(isset($error))
                <div class="mt-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    <i class="fas fa-exclamation-triangle mr-2"></i>{{ $error }}
                </div>
            @endif
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Logs</p>
                        <p class="text-3xl font-bold text-gray-900">{{ number_format($stats['total']) }}</p>
                        <p class="text-xs text-gray-500 mt-1">Today</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-gray-500 to-gray-600 rounded-xl shadow-lg">
                        <i class="fas fa-list text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-red-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Errors</p>
                        <p class="text-3xl font-bold text-red-600">{{ number_format($stats['error']) }}</p>
                        <p class="text-xs text-gray-500 mt-1">Critical issues</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-red-500 to-red-600 rounded-xl shadow-lg">
                        <i class="fas fa-exclamation-circle text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-orange-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Warnings</p>
                        <p class="text-3xl font-bold text-orange-600">{{ number_format($stats['warning']) }}</p>
                        <p class="text-xs text-gray-500 mt-1">Potential issues</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-lg">
                        <i class="fas fa-exclamation-triangle text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-blue-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Info</p>
                        <p class="text-3xl font-bold text-blue-600">{{ number_format($stats['info']) }}</p>
                        <p class="text-xs text-gray-500 mt-1">Information</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
                        <i class="fas fa-info-circle text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-green-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Debug</p>
                        <p class="text-3xl font-bold text-green-600">{{ number_format($stats['debug']) }}</p>
                        <p class="text-xs text-gray-500 mt-1">Debug info</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                        <i class="fas fa-bug text-white text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200 p-6 mb-6">
            <form method="GET" action="{{ route('system-admin.system-logs') }}" class="flex flex-wrap items-center gap-4">
                <div class="flex-1 min-w-[200px]">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    <input type="text" name="search" value="{{ $search }}" placeholder="Search logs..." class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                
                <div class="min-w-[150px]">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Level</label>
                    <select name="level" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" onchange="this.form.submit()">
                        <option value="all" {{ $level === 'all' ? 'selected' : '' }}>All Levels</option>
                        <option value="error" {{ $level === 'error' ? 'selected' : '' }}>Error</option>
                        <option value="warning" {{ $level === 'warning' ? 'selected' : '' }}>Warning</option>
                        <option value="info" {{ $level === 'info' ? 'selected' : '' }}>Info</option>
                        <option value="debug" {{ $level === 'debug' ? 'selected' : '' }}>Debug</option>
                    </select>
                </div>

                <div class="min-w-[150px]">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Date</label>
                    <input type="date" name="date" value="{{ $date }}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" onchange="this.form.submit()">
                </div>
            </form>
        </div>

        <!-- Logs Table -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200 p-6">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h3 class="text-lg font-bold text-gray-900">System Log Entries</h3>
                    <p class="text-sm text-gray-600">
                        Showing {{ count($paginatedLogs) }} of {{ number_format($pagination['total_logs']) }} logs
                    </p>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                @if(count($paginatedLogs) > 0)
                <div class="space-y-3">
                    @foreach($paginatedLogs as $log)
                    <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center gap-3 mb-2">
                                    <span class="px-2 py-1 rounded-full text-xs font-medium
                                        @if($log['level'] === 'error') bg-red-100 text-red-800
                                        @elseif($log['level'] === 'warning') bg-orange-100 text-orange-800
                                        @elseif($log['level'] === 'info') bg-blue-100 text-blue-800
                                        @elseif($log['level'] === 'debug') bg-green-100 text-green-800
                                        @else bg-gray-100 text-gray-800 @endif">
                                        {{ strtoupper($log['level']) }}
                                    </span>
                                    <span class="text-sm text-gray-600">{{ $log['timestamp'] }}</span>
                                </div>
                                
                                <div class="text-sm text-gray-900 mb-2">
                                    {{ $log['message'] }}
                                </div>
                                
                                @if(!empty(trim($log['context'])))
                                <details class="mt-2">
                                    <summary class="text-xs text-gray-500 cursor-pointer hover:text-gray-700">
                                        Show Context
                                    </summary>
                                    <pre class="mt-2 text-xs bg-gray-100 p-3 rounded border overflow-x-auto">{{ trim($log['context']) }}</pre>
                                </details>
                                @endif
                            </div>
                            
                            <div class="flex items-center gap-2 ml-4">
                                <button onclick="copyLogEntry('{{ addslashes($log['full_line']) }}')" class="p-2 text-gray-400 hover:text-gray-600 transition-colors" title="Copy Log Entry">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
                @else
                <div class="text-center py-12">
                    <i class="fas fa-file-alt text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-500">No log entries found</p>
                    @if($level !== 'all' || $search || $date)
                    <p class="text-sm text-gray-400 mt-2">Try adjusting your filters</p>
                    @endif
                </div>
                @endif
            </div>
            
            <!-- Pagination -->
            @if($pagination['total_pages'] > 1)
            <div class="mt-6 flex justify-center">
                <div class="flex items-center gap-2">
                    @if($pagination['current_page'] > 1)
                    <a href="{{ request()->fullUrlWithQuery(['page' => $pagination['current_page'] - 1]) }}" class="px-3 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                    @endif
                    
                    <span class="px-4 py-2 text-sm text-gray-600">
                        Page {{ $pagination['current_page'] }} of {{ $pagination['total_pages'] }}
                    </span>
                    
                    @if($pagination['has_more'])
                    <a href="{{ request()->fullUrlWithQuery(['page' => $pagination['current_page'] + 1]) }}" class="px-3 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                    @endif
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

<script>
function refreshLogs() {
    window.location.reload();
}

function downloadLogs() {
    // Create a simple text file with current logs
    const logs = @json($paginatedLogs);
    let content = 'System Logs Export - ' + new Date().toISOString() + '\n';
    content += '='.repeat(50) + '\n\n';
    
    logs.forEach(log => {
        content += `[${log.timestamp}] ${log.level.toUpperCase()}: ${log.message}\n`;
        if (log.context.trim()) {
            content += log.context + '\n';
        }
        content += '-'.repeat(30) + '\n';
    });
    
    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'system-logs-' + new Date().toISOString().split('T')[0] + '.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

function copyLogEntry(logEntry) {
    navigator.clipboard.writeText(logEntry).then(() => {
        // Show a brief success message
        const button = event.target.closest('button');
        const originalIcon = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check text-green-600"></i>';
        setTimeout(() => {
            button.innerHTML = originalIcon;
        }, 1000);
    }).catch(err => {
        console.error('Failed to copy log entry:', err);
    });
}
</script>
@endsection
