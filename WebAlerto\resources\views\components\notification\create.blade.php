@extends('layout.app')

@section('content')
<style>
    /* Custom responsive utilities */
    @media (max-width: 475px) {
        .xs\:hidden { display: none !important; }
        .xs\:inline { display: inline !important; }
    }

    /* Enhanced mobile touch targets */
    @media (max-width: 768px) {
        /* Form controls */
        input, textarea, select {
            min-height: 44px;
            font-size: 16px; /* Prevents zoom on iOS */
        }

        /* Optimized button sizing */
        button, a {
            min-height: 40px;
        }

        /* Primary action buttons */
        .btn-primary-mobile {
            max-width: 280px;
            margin: 0 auto;
        }
    }
</style>
<div class="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-sky-100 py-8">
    <!-- Header Section -->
    <div class="max-w-5xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 mb-4 sm:mb-6">
        <div class="bg-white/80 backdrop-blur-sm rounded-lg sm:rounded-xl shadow-lg border border-sky-200 p-3 sm:p-4">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
                <div class="flex items-center gap-2 sm:gap-3">
                    <div class="p-1.5 sm:p-2 bg-gradient-to-br from-sky-500 to-blue-600 rounded-lg shadow-md">
                        <img src="{{ asset('image/ALERTO Logo.png') }}" alt="ALERTO Logo" class="w-5 h-5 sm:w-6 sm:h-6 object-contain">
                    </div>
                    <div>
                        <h1 class="text-xl sm:text-2xl font-bold text-gray-900">Create Emergency Alert</h1>
                        <p class="text-xs sm:text-sm text-gray-600 mt-0.5 sm:mt-1">Send critical notifications to all users</p>
                    </div>
                </div>
                <a href="{{ route('components.notification.index') }}"
                   class="btn-primary-mobile inline-flex items-center justify-center gap-2 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2.5 rounded-lg font-medium shadow-md transition-all duration-200 text-sm w-full sm:w-auto">
                    <i class="fas fa-arrow-left text-sm"></i>
                    <span>Back to History</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    @if(session('success'))
        <div class="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 mb-6 sm:mb-8">
            <div id="successMessage" class="bg-green-50 border-l-4 border-green-500 text-green-700 p-3 sm:p-4 rounded-lg sm:rounded-xl shadow-sm">
                <div class="flex items-center gap-2 sm:gap-3">
                    <i class="fas fa-check-circle text-green-600 text-sm sm:text-base"></i>
                    <div>
                        <strong class="font-semibold text-sm sm:text-base">Success!</strong>
                        <p class="text-sm sm:text-base">{{ session('success') }}</p>
                    </div>
                </div>
            </div>
        </div>
    @endif

    @if($errors->any())
        <div class="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 mb-6 sm:mb-8">
            <div class="bg-red-50 border-l-4 border-red-500 text-red-700 p-3 sm:p-4 rounded-lg sm:rounded-xl shadow-sm">
                <div class="flex items-start gap-2 sm:gap-3">
                    <i class="fas fa-exclamation-triangle text-red-600 mt-1 text-sm sm:text-base"></i>
                    <div>
                        <strong class="font-semibold text-sm sm:text-base">Please fix the following errors:</strong>
                        <ul class="list-disc list-inside space-y-1 mt-2 text-sm sm:text-base">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Main Form Container -->
    <div class="max-w-5xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
        <div class="bg-white/80 backdrop-blur-sm rounded-lg sm:rounded-xl shadow-lg border border-sky-200 overflow-hidden">

            <!-- Form Header -->
            <div class="bg-gradient-to-r from-sky-600 to-blue-600 px-4 sm:px-6 py-3 sm:py-4">
                <div class="flex items-center gap-2 sm:gap-3">
                    <div class="p-1.5 sm:p-2 bg-white/20 rounded-lg">
                        <i class="fas fa-edit text-white text-base sm:text-lg"></i>
                    </div>
                    <div>
                        <h2 class="text-lg sm:text-xl font-bold text-white">Alert Information</h2>
                        <p class="text-xs sm:text-sm text-sky-100">Fill in the details for your emergency notification</p>
                    </div>
                </div>
            </div>

            <form action="{{ route('components.notification.store') }}" method="POST" class="p-4 sm:p-6">
                @csrf

                <!-- Main Form Grid -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 mb-6 sm:mb-8">
                    <!-- Left Column - Basic Information -->
                    <div class="space-y-4 sm:space-y-6">
                        <div class="bg-gradient-to-br from-sky-50 to-blue-50 rounded-lg sm:rounded-xl p-4 sm:p-6 border border-sky-200">
                            <div class="flex items-center gap-2 sm:gap-3 mb-4 sm:mb-6">
                                <div class="p-1.5 sm:p-2 bg-sky-100 rounded-lg">
                                    <i class="fas fa-info-circle text-sky-600 text-sm sm:text-base"></i>
                                </div>
                                <h3 class="text-base sm:text-lg font-semibold text-gray-900">Basic Information</h3>
                            </div>

                            <div class="space-y-4 sm:space-y-6">
                                <div>
                                    <label for="title" class="block text-xs sm:text-sm font-semibold text-gray-700 mb-2">
                                        <i class="fas fa-heading text-sky-600 mr-2"></i>Alert Title
                                    </label>
                                    <input
                                        type="text"
                                        name="title"
                                        id="title"
                                        value="{{ old('title') }}"
                                        placeholder="Enter a clear, descriptive title"
                                        class="w-full border-2 border-sky-200 px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg sm:rounded-xl outline-none focus:ring-1 focus:ring-sky-500 transition-all text-base sm:text-lg bg-white/80 backdrop-blur-sm"
                                        required>
                                </div>

                                <div>
                                    <label for="message" class="block text-xs sm:text-sm font-semibold text-gray-700 mb-2">
                                        <i class="fas fa-message text-sky-600 mr-2"></i>Alert Description
                                    </label>
                                    <textarea
                                        name="message"
                                        id="message"
                                        rows="5"
                                        placeholder="Provide detailed information about the emergency..."
                                        class="w-full border-2 border-sky-200 px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg sm:rounded-xl outline-none focus:ring-1 focus:ring-sky-500 transition-all resize-none text-sm sm:text-base bg-white/80 backdrop-blur-sm"
                                        required>{{ old('message') }}</textarea>
                                    <div class="text-xs text-gray-500 mt-1">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        Be specific and include actionable information
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column - Category and Priority -->
                    <div class="space-y-4 sm:space-y-4">
                        <!-- Target Area Info (for super_admin only) -->
                        @if(auth()->user()->hasRole('super_admin'))
                        <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-3 sm:p-4 border border-green-200">
                            <div class="flex items-center gap-2 mb-2 sm:mb-3">
                                <div class="p-1 sm:p-1.5 bg-green-100 rounded-lg">
                                    <i class="fas fa-globe text-green-600 text-sm sm:text-base"></i>
                                </div>
                                <h3 class="text-sm sm:text-base font-semibold text-gray-900">Broadcast Alert</h3>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-info-circle text-green-600 text-sm"></i>
                                <p class="text-xs sm:text-sm text-green-700 font-medium">
                                    This alert will be sent to ALL users across ALL barangays
                                </p>
                            </div>
                        </div>
                        @endif

                        <div class="bg-gradient-to-br from-orange-50 to-red-50 rounded-lg p-3 sm:p-4 border border-orange-200">
                            <div class="flex items-center gap-2 mb-3 sm:mb-4">
                                <div class="p-1 sm:p-1.5 bg-orange-100 rounded-lg">
                                    <i class="fas fa-exclamation-triangle text-orange-600 text-sm sm:text-base"></i>
                                </div>
                                <h3 class="text-sm sm:text-base font-semibold text-gray-900">Alert Classification</h3>
                            </div>

                            <div class="space-y-4">
                                <div>
                                    <label for="category" class="block text-xs font-semibold text-gray-700 mb-1">
                                        <i class="fas fa-tag text-orange-600 mr-1"></i>Disaster Category
                                    </label>
                                    <select
                                        name="category"
                                        id="category"
                                        class="w-full border-2 border-orange-200 px-3 py-2 rounded-lg outline-none focus:ring-1 focus:ring-orange-500 transition-all bg-white/80 backdrop-blur-sm text-sm"
                                        required>
                                        <option value="">Select disaster type</option>
                                        <option value="typhoon" {{ old('category') == 'typhoon' ? 'selected' : '' }}>🌪️ Typhoon</option>
                                        <option value="flood" {{ old('category') == 'flood' ? 'selected' : '' }}>🌊 Flood</option>
                                        <option value="fire" {{ old('category') == 'fire' ? 'selected' : '' }}>🔥 Fire</option>
                                        <option value="earthquake" {{ old('category') == 'earthquake' ? 'selected' : '' }}>🌍 Earthquake</option>
                                        <option value="landslide" {{ old('category') == 'landslide' ? 'selected' : '' }}>⛰️ Landslide</option>
                                        <option value="others" {{ old('category') == 'others' ? 'selected' : '' }}>🔧 Others</option>
                                    </select>
                                    <div class="text-xs text-gray-500 mt-1">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        Select the type of disaster or emergency
                                    </div>

                                    <!-- Custom disaster type input field (shown when "Others" is selected) -->
                                    <div id="customDisasterInput" class="mt-3 hidden">
                                        <label for="custom_disaster_type" class="block text-xs font-semibold text-gray-700 mb-1">
                                            <i class="fas fa-edit text-purple-600 mr-1"></i>Specify Disaster Type
                                        </label>
                                        <input type="text" id="custom_disaster_type" name="custom_disaster_type"
                                               class="w-full border-2 border-purple-200 px-3 py-2 rounded-lg outline-none focus:ring-1 focus:ring-purple-500 transition-all bg-white/80 backdrop-blur-sm text-sm"
                                               placeholder="Enter specific disaster type (e.g., Volcanic Eruption, Tsunami, etc.)"
                                               value="{{ old('custom_disaster_type') }}">
                                        <div class="text-xs text-gray-500 mt-1">
                                            <i class="fas fa-info-circle mr-1"></i>
                                            Please specify the type of disaster or emergency
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-xs font-semibold text-gray-700 mb-2">
                                        <i class="fas fa-exclamation-circle text-orange-600 mr-1"></i>Priority Level
                                    </label>
                                    <div class="space-y-2">
                                        <label class="group flex items-center p-3 border-2 rounded-lg cursor-pointer transition-all hover:shadow-md
                                            {{ old('severity') === 'high' ? 'border-red-500 bg-red-50 shadow-md' : 'border-gray-200 bg-white/80 hover:border-red-300' }}">
                                            <input type="radio" name="severity" value="high"
                                                   class="form-radio text-red-600 focus:ring-red-500" {{ old('severity') === 'high' ? 'checked' : '' }}>
                                            <div class="ml-3 flex items-center gap-2">
                                                <div class="p-1.5 bg-red-100 rounded-lg group-hover:bg-red-200 transition-colors">
                                                    <i class="fas fa-exclamation-triangle text-red-600"></i>
                                                </div>
                                                <div>
                                                    <div class="font-semibold text-red-700 text-sm">High Priority</div>
                                                    <div class="text-xs text-red-600">Immediate action required</div>
                                                </div>
                                            </div>
                                        </label>

                                        <label class="group flex items-center p-3 border-2 rounded-lg cursor-pointer transition-all hover:shadow-md
                                            {{ old('severity') === 'medium' ? 'border-yellow-500 bg-yellow-50 shadow-md' : 'border-gray-200 bg-white/80 hover:border-yellow-300' }}">
                                            <input type="radio" name="severity" value="medium"
                                                   class="form-radio text-yellow-600 focus:ring-yellow-500" {{ old('severity') === 'medium' ? 'checked' : '' }}>
                                            <div class="ml-3 flex items-center gap-2">
                                                <div class="p-1.5 bg-yellow-100 rounded-lg group-hover:bg-yellow-200 transition-colors">
                                                    <i class="fas fa-exclamation-circle text-yellow-600"></i>
                                                </div>
                                                <div>
                                                    <div class="font-semibold text-yellow-700 text-sm">Medium Priority</div>
                                                    <div class="text-xs text-yellow-600">Caution advised</div>
                                                </div>
                                            </div>
                                        </label>

                                        <label class="group flex items-center p-3 border-2 rounded-lg cursor-pointer transition-all hover:shadow-md
                                            {{ old('severity') === 'low' ? 'border-green-500 bg-green-50 shadow-md' : 'border-gray-200 bg-white/80 hover:border-green-300' }}">
                                            <input type="radio" name="severity" value="low"
                                                   class="form-radio text-green-600 focus:ring-green-500" {{ old('severity') === 'low' ? 'checked' : '' }}>
                                            <div class="ml-3 flex items-center gap-2">
                                                <div class="p-1.5 bg-green-100 rounded-lg group-hover:bg-green-200 transition-colors">
                                                    <i class="fas fa-info-circle text-green-600"></i>
                                                </div>
                                                <div>
                                                    <div class="font-semibold text-green-700 text-sm">Low Priority</div>
                                                    <div class="text-xs text-green-600">Informational</div>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hidden fields for automatic push notification sending -->
                <input type="hidden" name="send_push_notification" value="1">
                <input type="hidden" name="target_devices" value="all">

                <!-- Action Buttons -->
                <div class="flex flex-col gap-3 sm:gap-2 pt-4 border-t-2 border-gray-200">
                    <div class="text-xs sm:text-xs text-gray-600 flex items-center gap-1 justify-center sm:justify-start">
                        <i class="fas fa-shield-alt text-sky-600"></i>
                        @if(auth()->user()->hasRole('super_admin'))
                            <span class="text-center sm:text-left">This alert will be sent to ALL registered users across ALL barangays</span>
                        @else
                            <span class="text-center sm:text-left">This alert will be sent to users in {{ auth()->user()->barangay }}</span>
                        @endif
                    </div>

                    <div class="flex flex-col sm:flex-row gap-2 sm:justify-end">
                        <a href="{{ route('components.notification.index') }}"
                           class="btn-primary-mobile inline-flex items-center justify-center gap-1 px-4 py-2.5 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium rounded-lg shadow-sm transition-all duration-200 text-sm order-2 sm:order-1">
                            <i class="fas fa-arrow-left text-sm"></i>
                            Cancel
                        </a>
                        <button type="submit"
                                class="btn-primary-mobile inline-flex items-center justify-center gap-1 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white px-4 py-2.5 rounded-lg font-semibold shadow-md transition-all duration-200 text-sm order-1 sm:order-2">
                            <i class="fas fa-paper-plane text-sm"></i>
                            Send Alert Now
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>              
</div>

<script>
    // Initialize functionality when page loads
    document.addEventListener('DOMContentLoaded', function() {
        const categorySelect = document.getElementById('category');
        const customDisasterInput = document.getElementById('customDisasterInput');
        const customDisasterField = document.getElementById('custom_disaster_type');

        // Function to toggle custom disaster input
        function toggleCustomDisasterInput() {
            if (categorySelect.value === 'others') {
                customDisasterInput.classList.remove('hidden');
                customDisasterField.required = true;
            } else {
                customDisasterInput.classList.add('hidden');
                customDisasterField.required = false;
                customDisasterField.value = '';
            }
        }

        // Initial check on page load
        toggleCustomDisasterInput();

        // Listen for changes in category selection
        categorySelect.addEventListener('change', toggleCustomDisasterInput);

        // Handle form submission
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            // If category is not "others", clear the custom disaster type field
            if (categorySelect.value !== 'others') {
                customDisasterField.value = '';
            }
        });
    });
</script>
@endsection








