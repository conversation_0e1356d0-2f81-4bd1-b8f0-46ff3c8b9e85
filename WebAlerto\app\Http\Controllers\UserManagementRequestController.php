<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\UserManagementRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class UserManagementRequestController extends Controller
{
    /**
     * Display Chairman's user management requests
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        // Only Chairmen can access this
        if (!$user->is<PERSON>hairman()) {
            abort(403, 'Unauthorized access. Only Chairmen can access user management requests.');
        }

        $status = $request->input('status', 'all');

        // Get requests made by this Chairman
        $query = UserManagementRequest::with(['targetUser', 'reviewer'])
            ->where('requester_id', $user->id);

        if ($status !== 'all') {
            $query->where('status', $status);
        }

        $requests = $query->orderBy('created_at', 'desc')->paginate(10);

        // Get manageable users for new requests
        $manageableUsers = $user->getManageableUsers();

        return view('components.user_management.chairman-requests', compact('requests', 'manageableUsers', 'status'));
    }

    /**
     * Store a new user management request
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        // Only Chairmen can make requests
        if (!$user->isChairman()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Only Chairmen can make user management requests.'
            ], 403);
        }

        // Validate based on request type
        if ($request->input('request_type') === 'registration') {
            $request->validate([
                'action_type' => 'required|in:registration',
                'first_name' => 'required|string|max:255',
                'last_name' => 'required|string|max:255',
                'email' => 'required|email|max:255|unique:users,email',
                'position' => 'required|string|max:255',
                'reason' => 'required|string|min:20|max:500'
            ]);
        } else {
            $request->validate([
                'target_user_id' => 'required|exists:users,id',
                'action_type' => 'required|in:deactivate,delete',
                'reason' => 'required|string|min:10|max:500'
            ]);
        }

        if ($request->input('request_type') === 'registration') {
            // Handle registration request
            $actionType = 'registration';

            // Check for existing pending registration request with same email
            $existingRequest = UserManagementRequest::where('requester_id', $user->id)
                ->where('action_type', 'registration')
                ->where('requested_email', $request->email)
                ->where('status', 'pending')
                ->first();

            if ($existingRequest) {
                return response()->json([
                    'success' => false,
                    'message' => 'You already have a pending registration request for this email address.'
                ], 422);
            }

            // Create the registration request
            $managementRequest = UserManagementRequest::create([
                'requester_id' => $user->id,
                'target_user_id' => null, // No target user for registration
                'action_type' => $actionType,
                'reason' => $request->reason,
                'requested_first_name' => $request->first_name,
                'requested_last_name' => $request->last_name,
                'requested_email' => $request->email,
                'requested_position' => $request->position,
                'requested_barangay' => $user->barangay
            ]);

            $successMessage = 'User registration request submitted successfully. The SuperAdmin will review your request and create the account if approved.';
        } else {
            // Handle user management request (deactivate/delete)
            $targetUser = User::findOrFail($request->target_user_id);

            // Verify Chairman can request action on this user
            if (!$user->canRequestUserManagement($targetUser)) {
                return response()->json([
                    'success' => false,
                    'message' => 'You cannot request management actions for this user.'
                ], 403);
            }

            // Check for existing pending request
            $existingRequest = UserManagementRequest::where('requester_id', $user->id)
                ->where('target_user_id', $targetUser->id)
                ->where('status', 'pending')
                ->first();

            if ($existingRequest) {
                return response()->json([
                    'success' => false,
                    'message' => 'You already have a pending request for this user.'
                ], 422);
            }

            // Create the request
            $managementRequest = UserManagementRequest::create([
                'requester_id' => $user->id,
                'target_user_id' => $targetUser->id,
                'action_type' => $request->action_type,
                'reason' => $request->reason
            ]);

            $successMessage = 'User management request submitted successfully. The SuperAdmin will review your request.';
        }

        return response()->json([
            'success' => true,
            'message' => $successMessage,
            'request_id' => $managementRequest->id
        ]);
    }

    /**
     * Cancel a pending request
     */
    public function cancel($id)
    {
        $user = Auth::user();

        $managementRequest = UserManagementRequest::where('id', $id)
            ->where('requester_id', $user->id)
            ->where('status', 'pending')
            ->firstOrFail();

        $managementRequest->update([
            'status' => 'rejected',
            'reviewed_by' => $user->id,
            'review_notes' => 'Cancelled by requester',
            'reviewed_at' => now()
        ]);

        return redirect()->back()->with('success', 'Request cancelled successfully.');
    }
}
