ion-content {
  --background: #f0f2f5;
}

.notification-header {
  background: white;
  padding: 16px;
  border-bottom: 1px solid #e4e6ea;
  position: sticky;
  top: 0;
  z-index: 10;
}

.notification-tabs {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.tab-button {
  background: none;
  border: none;
  font-size: 16px;
  font-weight: 600;
  color: #65676b;
  padding: 8px 12px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &.active {
    color: #1877f2;
    background: #e7f3ff;
  }

  &:hover {
    background: #f2f3f4;
  }
}

.unread-badge {
  background: #e41e3f;
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 6px;
  min-width: 18px;
  text-align: center;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  font-size: 17px;
  font-weight: 600;
  color: #050505;
}

.see-all-btn {
  background: none;
  border: none;
  color: #1877f2;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;

  &:hover {
    background: #f2f3f4;
  }
}

.notifications-container {
  padding: 0;
}

.no-notifications {
  text-align: center;
  padding: 60px 20px;
  color: #65676b;

  .no-notifications-icon {
    font-size: 64px;
    color: #bcc0c4;
    margin-bottom: 16px;
  }

  h3 {
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #050505;
  }

  p {
    font-size: 15px;
    margin: 0;
    line-height: 1.4;
  }
}

.notification-item {
  background: white;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e6ea;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;

  &:hover {
    background: #f7f8fa;
  }

  &.unread {
    background: #f0f8ff;
    
    &:hover {
      background: #e7f3ff;
    }
  }

  &:last-child {
    border-bottom: none;
  }
}

.notification-icon {
  position: relative;
  flex-shrink: 0;
}

.icon-image {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e4e6ea;
}

.icon-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  font-size: 12px;

  &.badge-success {
    background: #42b883;
    color: white;
  }

  &.badge-danger {
    background: #e41e3f;
    color: white;
  }

  &.badge-info {
    background: #1877f2;
    color: white;
  }

  &.badge-primary {
    background: #03b2dd;
    color: white;
  }

  ion-icon {
    font-size: 14px;
  }
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-text {
  margin-bottom: 4px;
  line-height: 1.3;
}

.notification-title {
  color: #050505;
  font-size: 15px;
  font-weight: 400;
  display: block;
  margin-bottom: 2px;
}

.notification-description {
  color: #65676b;
  font-size: 13px;
  display: block;
  line-height: 1.4;
}

.notification-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #65676b;
}

.notification-time {
  font-weight: 500;
}

.notification-reactions {
  &::before {
    content: "•";
    margin-right: 8px;
  }
}

.unread-indicator {
  position: absolute;
  top: 50%;
  right: 16px;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background: #1877f2;
  border-radius: 50%;
}

.load-more-container {
  padding: 20px;
  text-align: center;
  background: white;
  border-top: 1px solid #e4e6ea;

  ion-button {
    --color: #1877f2;
    font-weight: 600;
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  ion-content {
    --background: #18191a;
  }

  .notification-header {
    background: #242526;
    border-bottom-color: #3a3b3c;
  }

  .tab-button {
    color: #b0b3b8;

    &.active {
      color: #2d88ff;
      background: #263951;
    }

    &:hover {
      background: #3a3b3c;
    }
  }

  .section-title {
    color: #e4e6ea;
  }

  .see-all-btn {
    color: #2d88ff;

    &:hover {
      background: #3a3b3c;
    }
  }

  .notification-item {
    background: #242526;
    border-bottom-color: #3a3b3c;

    &:hover {
      background: #3a3b3c;
    }

    &.unread {
      background: #263951;
      
      &:hover {
        background: #2d4373;
      }
    }
  }

  .notification-title {
    color: #e4e6ea;
  }

  .notification-description,
  .notification-meta {
    color: #b0b3b8;
  }

  .no-notifications {
    color: #b0b3b8;

    h3 {
      color: #e4e6ea;
    }
  }

  .load-more-container {
    background: #242526;
    border-top-color: #3a3b3c;
  }
}
