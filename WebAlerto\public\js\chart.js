document.addEventListener('DOMContentLoaded', function () {
    // Data is now passed from server-side Laravel controller
    const stats = window.alertStats || {
        totalAlerts: 0,
        activeCenters: 0,
        barangayCount: 0,
        totalNotifications: 0
    };

    const ctx = document.getElementById('alertsChart').getContext('2d');

    new Chart(ctx, {
        type: 'bar',
        data: {
            // Labels for all items, including the new Notifications category
            labels: ['Emergency Alerts', 'Active Centers', 'Barangays', 'Notifications'],
            datasets: [
                {
                    label: 'Total Count',
                    data: [
                        stats.totalAlerts,
                        stats.activeCenters,
                        stats.barangayCount,
                        stats.totalNotifications
                    ],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.6)', // red
                        'rgba(75, 192, 192, 0.6)', // green
                        'rgba(54, 162, 235, 0.6)', // blue
                        'rgba(255, 159, 64, 0.6)'  // orange (for Notifications)
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)', // red
                        'rgba(75, 192, 192, 1)', // green
                        'rgba(54, 162, 235, 1)', // blue
                        'rgba(255, 159, 64, 1)'  // orange (for Notifications)
                    ],
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                legend: { 
                    display: true,  // Make sure the legend is enabled
                    position: 'top' // You can adjust the position if needed (e.g., top, left, bottom, right)
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${context.raw}`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 5,
                        callback: function(value) {
                            return Number.isInteger(value) ? value : '';
                        }
                    }
                }
            }
        }
    });
});
