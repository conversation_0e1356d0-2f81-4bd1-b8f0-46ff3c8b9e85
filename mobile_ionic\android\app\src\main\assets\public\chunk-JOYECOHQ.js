import{d as c}from"./chunk-ZMIDTE6M.js";import{i as a,l as n}from"./chunk-6NKRJMOW.js";import{h as s}from"./chunk-B7O3QC5Z.js";var h=(()=>{class t{constructor(i){this.loadingController=i,this.activeLoader=null}showLoading(i="Please wait..."){return s(this,null,function*(){yield this.dismissLoading(),this.activeLoader=yield this.loadingController.create({message:i,spinner:"circular",cssClass:"accessible-loading"}),yield this.activeLoader.present(),this.fixAccessibility(this.activeLoader)})}dismissLoading(){return s(this,null,function*(){if(this.activeLoader){try{yield this.activeLoader.dismiss()}catch(i){console.warn("Error dismissing loader:",i)}this.activeLoader=null}})}fixAccessibility(i){if(!i||!i.querySelector(".loading-wrapper"))return;let e=i.shadowRoot?.querySelector(".overlay-hidden");e&&e.hasAttribute("aria-hidden")&&(e.removeAttribute("aria-hidden"),e.setAttribute("inert","")),i.onDidDismiss().then(()=>{let r=document.querySelector(":focus-within");r&&"focus"in r&&r.focus()})}static{this.\u0275fac=function(o){return new(o||t)(n(c))}}static{this.\u0275prov=a({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();export{h as a};
