<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
            'title',
            'first_name',
            'middle_name',
            'last_name' ,
            'suffix',
            'position',
            'city',
            'barangay',
            'email',
            'password',
            'role',
            'status',
            'last_viewed_requests'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    /**
     * Get the city name (convert from PSGC code if needed)
     */
    public function getCityNameAttribute()
    {
        if (!$this->city) {
            return null;
        }

        // If it's already a city name (not numeric), return as is
        if (!is_numeric($this->city)) {
            return $this->city;
        }

        // If it's a PSGC code, convert to city name
        try {
            $barangayService = app(\App\Services\BarangayService::class);
            return $barangayService->getCityNameFromCode($this->city) ?: $this->city;
        } catch (\Exception $e) {
            return $this->city; // Fallback to original value
        }
    }

    /**
     * Get the device tokens for the user.
     */
    public function deviceTokens()
    {
        return $this->hasMany(DeviceToken::class);
    }

    /**
     * Get management requests made by this user
     */
    public function managementRequests()
    {
        return $this->hasMany(UserManagementRequest::class, 'requester_id');
    }

    /**
     * Get management requests targeting this user
     */
    public function targetedRequests()
    {
        return $this->hasMany(UserManagementRequest::class, 'target_user_id');
    }

    /**
     * Get management requests reviewed by this user
     */
    public function reviewedRequests()
    {
        return $this->hasMany(UserManagementRequest::class, 'reviewed_by');
    }

    /**
     * Route notifications for the FCM channel.
     *
     * @param  \Illuminate\Notifications\Notification  $notification
     * @return string|null
     */
    public function routeNotificationForFcm($notification)
    {
        return $this->device_token;
    }

    /**
     * Check if user has access to a specific barangay
     */
    public function hasBarangayAccess($barangay)
    {
        return $this->barangay === $barangay;
    }

    /**
     * Scope a query to only include users from a specific barangay
     */
    public function scopeFromBarangay($query, $barangay)
    {
        return $query->where('barangay', $barangay);
    }

    /**
     * Get all data related to user's barangay
     */
    public function getBarangayData($model)
    {
        return $model->where('barangay', $this->barangay);
    }

    public function hasRole($role)
    {
        return strtolower($this->role) === strtolower($role);
    }

    public function canManageUser(User $user)
    {
        // System Administrator can manage all users
        if (strtolower($this->role) === 'system_admin') return true;

        // Super Administrator can manage all admin users
        if (strtolower($this->role) === 'super_admin') {
            return strtolower($user->role) === 'admin';
        }

        // Admin users can manage users in their barangay (if they are chairman)
        if (strtolower($this->role) === 'admin' && $this->isChairman() && $this->barangay === $user->barangay) {
            return strtolower($user->role) === 'admin' && !$user->isChairman();
        }

        return false;
    }

    /**
     * Check if user is a Chairman (Admin role with Chairman position)
     */
    public function isChairman()
    {
        // Check if role is 'admin' with position 'chairman'
        return strtolower($this->role) === 'admin' && strtolower($this->position) === 'chairman';
    }

    /**
     * Check if Chairman can request management action for a user
     */
    public function canRequestUserManagement(User $targetUser)
    {
        // Must be a Chairman
        if (!$this->isChairman()) return false;

        // Must be in same barangay
        if ($this->barangay !== $targetUser->barangay) return false;

        // Cannot request action on themselves
        if ($this->id === $targetUser->id) return false;

        // Can only request actions on non-Chairman admin users in their barangay
        return strtolower($targetUser->role) === 'admin' &&
               !$targetUser->isChairman();
    }

    /**
     * Check if user has enhanced management authority (Chairman)
     */
    public function hasEnhancedAuthority()
    {
        return $this->isChairman();
    }

    /**
     * Get users that this Chairman can request management actions for
     */
    public function getManageableUsers()
    {
        if (!$this->isChairman()) {
            return collect();
        }

        return User::where('barangay', $this->barangay)
                   ->where('role', 'admin')
                   ->where('id', '!=', $this->id)
                   ->whereRaw('LOWER(position) != ?', ['chairman'])
                   ->get();
    }

    /**
     * Check if user has access to a specific barangay data
     */
    public function canAccessBarangay($barangay)
    {
        // System Administrator has access to all barangays
        if (strtolower($this->role) === 'system_admin') return true;

        // Super Administrator has access to all barangays
        if (strtolower($this->role) === 'super_admin') return true;

        // Admin users can only access their own barangay
        return $this->barangay === $barangay;
    }

    /**
     * Check if user has city-wide monitoring access
     */
    public function hasCityWideAccess()
    {
        return strtolower($this->role) === 'system_admin' ||
               strtolower($this->role) === 'super_admin';
    }

    /**
     * Check if user is a System Administrator (Technical Administrator)
     */
    public function isSystemAdmin()
    {
        return strtolower($this->role) === 'system_admin';
    }

    /**
     * Check if user is a Super Administrator (CDRRMC - City-level)
     */
    public function isSuperAdmin()
    {
        return strtolower($this->role) === 'super_admin';
    }

    /**
     * Check if user is an Administrator (BDRRMC - Barangay-level)
     */
    public function isAdmin()
    {
        return strtolower($this->role) === 'admin';
    }

    /**
     * Check if user can create/manage user accounts
     */
    public function canManageUserAccounts()
    {
        return $this->isSystemAdmin();
    }

    /**
     * Check if user can view city-wide disaster data
     */
    public function canViewCityWideData()
    {
        return $this->isSystemAdmin() || $this->isSuperAdmin();
    }

    /**
     * Check if user can manage disaster reports
     */
    public function canManageDisasterReports()
    {
        return $this->isSuperAdmin() || $this->isAdmin();
    }

    /**
     * Check if user has technical system access
     */
    public function hasTechnicalAccess()
    {
        return $this->isSystemAdmin();
    }

    /**
     * Get user's role display name
     */
    public function getRoleDisplayName()
    {
        switch (strtolower($this->role)) {
            case 'system_admin':
                return 'Technical Administrator';
            case 'super_admin':
                return 'CDRRMC (City-Level)';
            case 'admin':
                return 'BDRRMC (Barangay-Level)';
            default:
                return ucfirst($this->role);
        }
    }

    /**
     * Get user's access level description
     */
    public function getAccessLevelDescription()
    {
        switch (strtolower($this->role)) {
            case 'system_admin':
                return 'System configurations, user management, and technical maintenance';
            case 'super_admin':
                return 'City-wide disaster monitoring and oversight across all barangays';
            case 'admin':
                return 'Barangay-specific disaster management and reporting';
            default:
                return 'Limited access';
        }
    }

    /**
     * Get user's specific responsibilities
     */
    public function getResponsibilities()
    {
        switch (strtolower($this->role)) {
            case 'system_admin':
                return [
                    'Creates and manages user accounts (CDRRMC, BDRRMC)',
                    'Handles system configurations and access control',
                    'Performs technical maintenance and system monitoring',
                    'Ensures only authorized users can access the system'
                ];
            case 'super_admin':
                return [
                    'Full access to all barangays\' disaster reports and data',
                    'Monitors and analyzes disaster incidents across Cebu City',
                    'Views dashboards with summarized reports per barangay',
                    'Compares disaster data across different barangays'
                ];
            case 'admin':
                return [
                    'Manages disaster reports within their own barangay only',
                    'Cannot view or edit data from other barangays',
                    'Provides barangay-level updates to CDRRMC',
                    'Manages local resources and incident responses'
                ];
            default:
                return ['Limited system access'];
        }
    }

    /**
     * Get accessible barangays for the user
     */
    public function getAccessibleBarangays()
    {
        $barangayService = app(\App\Services\BarangayService::class);
        return $barangayService->getAccessibleBarangays($this);
    }

    /**
     * Get the user's full name attribute
     */
    public function getFullNameAttribute()
    {
        $name = '';

        // Add title if present
        if ($this->title) {
            $name .= $this->title . ' ';
        }

        // Add first name
        $name .= $this->first_name;

        // Add middle name if present
        if ($this->middle_name) {
            $name .= ' ' . $this->middle_name;
        }

        // Add last name
        $name .= ' ' . $this->last_name;

        // Add suffix if present
        if ($this->suffix) {
            $name .= ', ' . $this->suffix;
        }

        return trim($name);
    }
}

