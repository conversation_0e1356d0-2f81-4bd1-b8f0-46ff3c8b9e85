{"version": 3, "sources": ["../../../../../../node_modules/@capacitor/local-notifications/dist/esm/web.js"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\nexport class LocalNotificationsWeb extends WebPlugin {\n  constructor() {\n    super(...arguments);\n    this.pending = [];\n    this.deliveredNotifications = [];\n    this.hasNotificationSupport = () => {\n      if (!('Notification' in window) || !Notification.requestPermission) {\n        return false;\n      }\n      if (Notification.permission !== 'granted') {\n        // don't test for `new Notification` if permission has already been granted\n        // otherwise this sends a real notification on supported browsers\n        try {\n          new Notification('');\n        } catch (e) {\n          if (e.name == 'TypeError') {\n            return false;\n          }\n        }\n      }\n      return true;\n    };\n  }\n  async getDeliveredNotifications() {\n    const deliveredSchemas = [];\n    for (const notification of this.deliveredNotifications) {\n      const deliveredSchema = {\n        title: notification.title,\n        id: parseInt(notification.tag),\n        body: notification.body\n      };\n      deliveredSchemas.push(deliveredSchema);\n    }\n    return {\n      notifications: deliveredSchemas\n    };\n  }\n  async removeDeliveredNotifications(delivered) {\n    for (const toRemove of delivered.notifications) {\n      const found = this.deliveredNotifications.find(n => n.tag === String(toRemove.id));\n      found === null || found === void 0 ? void 0 : found.close();\n      this.deliveredNotifications = this.deliveredNotifications.filter(() => !found);\n    }\n  }\n  async removeAllDeliveredNotifications() {\n    for (const notification of this.deliveredNotifications) {\n      notification.close();\n    }\n    this.deliveredNotifications = [];\n  }\n  async createChannel() {\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async deleteChannel() {\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async listChannels() {\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async schedule(options) {\n    if (!this.hasNotificationSupport()) {\n      throw this.unavailable('Notifications not supported in this browser.');\n    }\n    for (const notification of options.notifications) {\n      this.sendNotification(notification);\n    }\n    return {\n      notifications: options.notifications.map(notification => ({\n        id: notification.id\n      }))\n    };\n  }\n  async getPending() {\n    return {\n      notifications: this.pending\n    };\n  }\n  async registerActionTypes() {\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async cancel(pending) {\n    this.pending = this.pending.filter(notification => !pending.notifications.find(n => n.id === notification.id));\n  }\n  async areEnabled() {\n    const {\n      display\n    } = await this.checkPermissions();\n    return {\n      value: display === 'granted'\n    };\n  }\n  async changeExactNotificationSetting() {\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async checkExactNotificationSetting() {\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async requestPermissions() {\n    if (!this.hasNotificationSupport()) {\n      throw this.unavailable('Notifications not supported in this browser.');\n    }\n    const display = this.transformNotificationPermission(await Notification.requestPermission());\n    return {\n      display\n    };\n  }\n  async checkPermissions() {\n    if (!this.hasNotificationSupport()) {\n      throw this.unavailable('Notifications not supported in this browser.');\n    }\n    const display = this.transformNotificationPermission(Notification.permission);\n    return {\n      display\n    };\n  }\n  transformNotificationPermission(permission) {\n    switch (permission) {\n      case 'granted':\n        return 'granted';\n      case 'denied':\n        return 'denied';\n      default:\n        return 'prompt';\n    }\n  }\n  sendPending() {\n    var _a;\n    const toRemove = [];\n    const now = new Date().getTime();\n    for (const notification of this.pending) {\n      if (((_a = notification.schedule) === null || _a === void 0 ? void 0 : _a.at) && notification.schedule.at.getTime() <= now) {\n        this.buildNotification(notification);\n        toRemove.push(notification);\n      }\n    }\n    this.pending = this.pending.filter(notification => !toRemove.find(n => n === notification));\n  }\n  sendNotification(notification) {\n    var _a;\n    if ((_a = notification.schedule) === null || _a === void 0 ? void 0 : _a.at) {\n      const diff = notification.schedule.at.getTime() - new Date().getTime();\n      this.pending.push(notification);\n      setTimeout(() => {\n        this.sendPending();\n      }, diff);\n      return;\n    }\n    this.buildNotification(notification);\n  }\n  buildNotification(notification) {\n    const localNotification = new Notification(notification.title, {\n      body: notification.body,\n      tag: String(notification.id)\n    });\n    localNotification.addEventListener('click', this.onClick.bind(this, notification), false);\n    localNotification.addEventListener('show', this.onShow.bind(this, notification), false);\n    localNotification.addEventListener('close', () => {\n      this.deliveredNotifications = this.deliveredNotifications.filter(() => !this);\n    }, false);\n    this.deliveredNotifications.push(localNotification);\n    return localNotification;\n  }\n  onClick(notification) {\n    const data = {\n      actionId: 'tap',\n      notification\n    };\n    this.notifyListeners('localNotificationActionPerformed', data);\n  }\n  onShow(notification) {\n    this.notifyListeners('localNotificationReceived', notification);\n  }\n}\n"], "mappings": ";;;;;;;;AACO,IAAM,wBAAN,cAAoC,UAAU;AAAA,EACnD,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,UAAU,CAAC;AAChB,SAAK,yBAAyB,CAAC;AAC/B,SAAK,yBAAyB,MAAM;AAClC,UAAI,EAAE,kBAAkB,WAAW,CAAC,aAAa,mBAAmB;AAClE,eAAO;AAAA,MACT;AACA,UAAI,aAAa,eAAe,WAAW;AAGzC,YAAI;AACF,cAAI,aAAa,EAAE;AAAA,QACrB,SAAS,GAAG;AACV,cAAI,EAAE,QAAQ,aAAa;AACzB,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACM,4BAA4B;AAAA;AAChC,YAAM,mBAAmB,CAAC;AAC1B,iBAAW,gBAAgB,KAAK,wBAAwB;AACtD,cAAM,kBAAkB;AAAA,UACtB,OAAO,aAAa;AAAA,UACpB,IAAI,SAAS,aAAa,GAAG;AAAA,UAC7B,MAAM,aAAa;AAAA,QACrB;AACA,yBAAiB,KAAK,eAAe;AAAA,MACvC;AACA,aAAO;AAAA,QACL,eAAe;AAAA,MACjB;AAAA,IACF;AAAA;AAAA,EACM,6BAA6B,WAAW;AAAA;AAC5C,iBAAW,YAAY,UAAU,eAAe;AAC9C,cAAM,QAAQ,KAAK,uBAAuB,KAAK,OAAK,EAAE,QAAQ,OAAO,SAAS,EAAE,CAAC;AACjF,kBAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,MAAM;AAC1D,aAAK,yBAAyB,KAAK,uBAAuB,OAAO,MAAM,CAAC,KAAK;AAAA,MAC/E;AAAA,IACF;AAAA;AAAA,EACM,kCAAkC;AAAA;AACtC,iBAAW,gBAAgB,KAAK,wBAAwB;AACtD,qBAAa,MAAM;AAAA,MACrB;AACA,WAAK,yBAAyB,CAAC;AAAA,IACjC;AAAA;AAAA,EACM,gBAAgB;AAAA;AACpB,YAAM,KAAK,cAAc,yBAAyB;AAAA,IACpD;AAAA;AAAA,EACM,gBAAgB;AAAA;AACpB,YAAM,KAAK,cAAc,yBAAyB;AAAA,IACpD;AAAA;AAAA,EACM,eAAe;AAAA;AACnB,YAAM,KAAK,cAAc,yBAAyB;AAAA,IACpD;AAAA;AAAA,EACM,SAAS,SAAS;AAAA;AACtB,UAAI,CAAC,KAAK,uBAAuB,GAAG;AAClC,cAAM,KAAK,YAAY,8CAA8C;AAAA,MACvE;AACA,iBAAW,gBAAgB,QAAQ,eAAe;AAChD,aAAK,iBAAiB,YAAY;AAAA,MACpC;AACA,aAAO;AAAA,QACL,eAAe,QAAQ,cAAc,IAAI,mBAAiB;AAAA,UACxD,IAAI,aAAa;AAAA,QACnB,EAAE;AAAA,MACJ;AAAA,IACF;AAAA;AAAA,EACM,aAAa;AAAA;AACjB,aAAO;AAAA,QACL,eAAe,KAAK;AAAA,MACtB;AAAA,IACF;AAAA;AAAA,EACM,sBAAsB;AAAA;AAC1B,YAAM,KAAK,cAAc,yBAAyB;AAAA,IACpD;AAAA;AAAA,EACM,OAAO,SAAS;AAAA;AACpB,WAAK,UAAU,KAAK,QAAQ,OAAO,kBAAgB,CAAC,QAAQ,cAAc,KAAK,OAAK,EAAE,OAAO,aAAa,EAAE,CAAC;AAAA,IAC/G;AAAA;AAAA,EACM,aAAa;AAAA;AACjB,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,MAAM,KAAK,iBAAiB;AAChC,aAAO;AAAA,QACL,OAAO,YAAY;AAAA,MACrB;AAAA,IACF;AAAA;AAAA,EACM,iCAAiC;AAAA;AACrC,YAAM,KAAK,cAAc,yBAAyB;AAAA,IACpD;AAAA;AAAA,EACM,gCAAgC;AAAA;AACpC,YAAM,KAAK,cAAc,yBAAyB;AAAA,IACpD;AAAA;AAAA,EACM,qBAAqB;AAAA;AACzB,UAAI,CAAC,KAAK,uBAAuB,GAAG;AAClC,cAAM,KAAK,YAAY,8CAA8C;AAAA,MACvE;AACA,YAAM,UAAU,KAAK,gCAAgC,MAAM,aAAa,kBAAkB,CAAC;AAC3F,aAAO;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA;AAAA,EACM,mBAAmB;AAAA;AACvB,UAAI,CAAC,KAAK,uBAAuB,GAAG;AAClC,cAAM,KAAK,YAAY,8CAA8C;AAAA,MACvE;AACA,YAAM,UAAU,KAAK,gCAAgC,aAAa,UAAU;AAC5E,aAAO;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA;AAAA,EACA,gCAAgC,YAAY;AAC1C,YAAQ,YAAY;AAAA,MAClB,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT;AACE,eAAO;AAAA,IACX;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI;AACJ,UAAM,WAAW,CAAC;AAClB,UAAM,OAAM,oBAAI,KAAK,GAAE,QAAQ;AAC/B,eAAW,gBAAgB,KAAK,SAAS;AACvC,YAAM,KAAK,aAAa,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO,aAAa,SAAS,GAAG,QAAQ,KAAK,KAAK;AAC1H,aAAK,kBAAkB,YAAY;AACnC,iBAAS,KAAK,YAAY;AAAA,MAC5B;AAAA,IACF;AACA,SAAK,UAAU,KAAK,QAAQ,OAAO,kBAAgB,CAAC,SAAS,KAAK,OAAK,MAAM,YAAY,CAAC;AAAA,EAC5F;AAAA,EACA,iBAAiB,cAAc;AAC7B,QAAI;AACJ,SAAK,KAAK,aAAa,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI;AAC3E,YAAM,OAAO,aAAa,SAAS,GAAG,QAAQ,KAAI,oBAAI,KAAK,GAAE,QAAQ;AACrE,WAAK,QAAQ,KAAK,YAAY;AAC9B,iBAAW,MAAM;AACf,aAAK,YAAY;AAAA,MACnB,GAAG,IAAI;AACP;AAAA,IACF;AACA,SAAK,kBAAkB,YAAY;AAAA,EACrC;AAAA,EACA,kBAAkB,cAAc;AAC9B,UAAM,oBAAoB,IAAI,aAAa,aAAa,OAAO;AAAA,MAC7D,MAAM,aAAa;AAAA,MACnB,KAAK,OAAO,aAAa,EAAE;AAAA,IAC7B,CAAC;AACD,sBAAkB,iBAAiB,SAAS,KAAK,QAAQ,KAAK,MAAM,YAAY,GAAG,KAAK;AACxF,sBAAkB,iBAAiB,QAAQ,KAAK,OAAO,KAAK,MAAM,YAAY,GAAG,KAAK;AACtF,sBAAkB,iBAAiB,SAAS,MAAM;AAChD,WAAK,yBAAyB,KAAK,uBAAuB,OAAO,MAAM,CAAC,IAAI;AAAA,IAC9E,GAAG,KAAK;AACR,SAAK,uBAAuB,KAAK,iBAAiB;AAClD,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,cAAc;AACpB,UAAM,OAAO;AAAA,MACX,UAAU;AAAA,MACV;AAAA,IACF;AACA,SAAK,gBAAgB,oCAAoC,IAAI;AAAA,EAC/D;AAAA,EACA,OAAO,cAAc;AACnB,SAAK,gBAAgB,6BAA6B,YAAY;AAAA,EAChE;AACF;", "names": []}