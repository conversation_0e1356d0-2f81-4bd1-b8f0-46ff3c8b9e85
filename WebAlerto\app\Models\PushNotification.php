<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Log;

class PushNotification extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'message',
        'category',
        'severity',
        'data',
        'sent',
        'scheduled_at',
        'expires_at',
        'priority',
        'sound',
        'badge',
        'click_action',
        'icon',
        'color',
        'tag',
        'group',
        'silent',
        'vibrate',
        'actions'
    ];

    protected $casts = [
        'data' => 'array',
        'sent' => 'boolean',
        'scheduled_at' => 'datetime',
        'expires_at' => 'datetime',
        'silent' => 'boolean',
        'vibrate' => 'boolean',
        'actions' => 'array'
    ];

    protected $attributes = [
        'severity' => 'normal',
        'priority' => 'normal',
        'sound' => 'default',
        'badge' => '1',
        'silent' => false,
        'vibrate' => true
    ];

    public function getNotificationPayload()
    {
        $payload = [
            'notification' => [
                'title' => $this->title,
                'body' => $this->message,
                'sound' => $this->getSound(),
                'badge' => $this->badge,
                'priority' => $this->getPriority(),
                'icon' => $this->icon,
                'color' => $this->color,
                'tag' => $this->tag,
                'click_action' => $this->click_action ?? 'FLUTTER_NOTIFICATION_CLICK'
            ],
            'data' => array_merge($this->data ?? [], [
                'title' => $this->title,
                'body' => $this->message,
                'category' => $this->category,
                'severity' => $this->severity,
                'notification_id' => $this->id,
                'time' => now()->toIso8601String(),
                'priority' => $this->getPriority(),
                'sound' => $this->getSound(),
                'badge' => $this->badge,
                'icon' => $this->icon,
                'color' => $this->color,
                'tag' => $this->tag,
                'group' => $this->group,
                'silent' => $this->silent,
                'vibrate' => $this->vibrate,
                'actions' => $this->actions
            ])
        ];

        // Add Android specific options
        if ($this->shouldAddAndroidOptions()) {
            $payload['android'] = [
                'priority' => $this->getAndroidPriority(),
                'notification' => [
                    'sound' => $this->getAndroidSound(),
                    'channel_id' => $this->getAndroidChannelId(),
                    'click_action' => $this->click_action,
                    'icon' => $this->icon,
                    'color' => $this->color,
                    'tag' => $this->tag,
                    'group' => $this->group,
                    'silent' => $this->silent,
                    'vibrate' => $this->vibrate,
                    'actions' => $this->actions
                ]
            ];
        }

        // Add iOS specific options
        if ($this->shouldAddIOSOptions()) {
            $payload['apns'] = [
                'payload' => [
                    'aps' => [
                        'sound' => $this->getIOSSound(),
                        'badge' => (int)$this->badge,
                        'category' => $this->category,
                        'thread-id' => $this->group,
                        'mutable-content' => 1,
                        'content-available' => $this->silent ? 1 : 0
                    ]
                ],
                'headers' => [
                    'apns-priority' => $this->getIOSPriority(),
                    'apns-expiration' => $this->getExpirationTimestamp()
                ]
            ];
        }

        return $payload;
    }

    protected function getSound()
    {
        switch ($this->severity) {
            case 'high':
                return 'emergency.mp3';
            case 'medium':
                return 'alert.mp3';
            default:
                return $this->sound;
        }
    }

    protected function getPriority()
    {
        switch ($this->severity) {
            case 'high':
                return 'high';
            case 'medium':
                return 'normal';
            default:
                return $this->priority;
        }
    }

    protected function getAndroidPriority()
    {
        switch ($this->getPriority()) {
            case 'high':
                return 'high';
            case 'normal':
                return 'normal';
            default:
                return 'default';
        }
    }

    protected function getAndroidSound()
    {
        return $this->getSound();
    }

    protected function getAndroidChannelId()
    {
        switch ($this->severity) {
            case 'high':
                return 'high_priority';
            case 'medium':
                return 'default';
            default:
                return 'low_priority';
        }
    }

    protected function getIOSSound()
    {
        return $this->getSound();
    }

    protected function getIOSPriority()
    {
        switch ($this->getPriority()) {
            case 'high':
                return '10';
            case 'normal':
                return '5';
            default:
                return '0';
        }
    }

    protected function getExpirationTimestamp()
    {
        if ($this->expires_at) {
            return $this->expires_at->timestamp;
        }
        return time() + 3600; // Default 1 hour expiration
    }

    protected function shouldAddAndroidOptions()
    {
        return $this->icon || $this->color || $this->tag || $this->group || $this->silent || $this->vibrate || $this->actions;
    }

    protected function shouldAddIOSOptions()
    {
        return $this->category || $this->group || $this->silent || $this->badge;
    }

    public function isExpired()
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    public function isScheduled()
    {
        return $this->scheduled_at && $this->scheduled_at->isFuture();
    }

    public function shouldSend()
    {
        return !$this->sent && !$this->isExpired() && !$this->isScheduled();
    }

    public function markAsSent()
    {
        $this->sent = true;
        $this->save();
        Log::info('Notification marked as sent', ['id' => $this->id]);
    }

    public function markAsFailed($error)
    {
        Log::error('Notification failed to send', [
            'id' => $this->id,
            'error' => $error
        ]);
    }
} 