<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Evacuation;
use App\Models\Notification;
use App\Models\MobileUser;
use App\Models\UserManagementRequest;
use App\Services\PSGCService;
use App\Services\BarangayService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Carbon\Carbon;
use App\Services\OrganizationalPositionService;
use App\Mail\AdminRegistrationMail;

class SystemAdminController extends Controller
{
    protected $psgcService;
    protected $barangayService;

    public function __construct(PSGCService $psgcService, BarangayService $barangayService)
    {
        $this->psgcService = $psgcService;
        $this->barangayService = $barangayService;
    }
    /**
     * System Administrator Dashboard
     */
    public function dashboard(Request $request)
    {
        $user = Auth::user();

        // Ensure only System Administrators can access
        if (!$user->isSystemAdmin()) {
            abort(403, 'Access denied. System Administrator access required.');
        }

        try {
            // Get filter parameters
            $selectedCity = $request->get('city');
            $selectedBarangay = $request->get('barangay');

            // Get available cities and barangays for filters
            $cities = $this->barangayService->getAllProvincialCities();
            $barangays = [];
            if ($selectedCity) {
                $barangays = $this->barangayService->getBarangaysByCity($selectedCity);
            }

            // Build query filters
            $userQuery = User::query();
            $evacuationQuery = Evacuation::query();
            $notificationQuery = Notification::query();

            if ($selectedCity) {
                $userQuery->where('city', $selectedCity);
                $evacuationQuery->where('city', $selectedCity);
                $notificationQuery->where('city', $selectedCity);
            }

            if ($selectedBarangay) {
                $userQuery->where('barangay', $selectedBarangay);
                $evacuationQuery->where('barangay', $selectedBarangay);
                $notificationQuery->where('barangay', $selectedBarangay);
            }

            // System-wide statistics with filters
            $stats = [
                'total_users' => (clone $userQuery)->count(),
                'system_admins' => (clone $userQuery)->where('role', 'system_admin')->count(),
                'super_admins' => (clone $userQuery)->where('role', 'super_admin')->count(),
                'admins' => (clone $userQuery)->where('role', 'admin')->count(),
                'active_users' => (clone $userQuery)->where('status', 'Active')->count(),
                'inactive_users' => (clone $userQuery)->where('status', '!=', 'Active')->count(),
                'total_mobile_users' => MobileUser::count(), // Mobile users don't have city/barangay filters
                'total_notifications' => (clone $notificationQuery)->count(),
                'total_evacuation_centers' => (clone $evacuationQuery)->count(),
                'active_evacuation_centers' => (clone $evacuationQuery)->where('status', 'active')->count(),
                'pending_requests' => UserManagementRequest::where('status', 'pending')->count(),
            ];

            // Recent activity logs with filters
            $recentUsers = (clone $userQuery)->orderBy('created_at', 'desc')->take(10)->get();
            $recentNotifications = (clone $notificationQuery)->orderBy('created_at', 'desc')->take(10)->get();
            $recentRequests = UserManagementRequest::with(['requester', 'targetUser'])
                ->orderBy('created_at', 'desc')
                ->take(10)
                ->get();

            // System health metrics
            $systemHealth = [
                'database_status' => $this->checkDatabaseHealth(),
                'user_activity' => $this->getUserActivityMetrics(),
                'system_load' => $this->getSystemLoadMetrics(),
            ];

            // Role distribution data for charts (with proper filtering)
            $roleDistribution = [
                'system_admin' => $stats['system_admins'],
                'super_admin' => $stats['super_admins'], // CDRRMC users
                'admin' => $stats['admins'], // BDRRMC users
            ];

            // Additional user statistics for charts
            $userStats = [
                'total_users' => $stats['total_users'],
                'cdrrmc_users' => $stats['super_admins'], // CDRRMC count
                'bdrrmc_users' => $stats['admins'], // BDRRMC count
                'system_admin_users' => $stats['system_admins'], // System Admin count
                'active_users' => $stats['active_users'],
                'inactive_users' => $stats['inactive_users']
            ];

            // Monthly user registration trends
            $monthlyRegistrations = $this->getMonthlyRegistrationTrends();

            return view('components.system-admin.dashboard', compact(
                'stats',
                'recentUsers',
                'recentNotifications',
                'recentRequests',
                'systemHealth',
                'roleDistribution',
                'userStats',
                'monthlyRegistrations',
                'cities',
                'barangays',
                'selectedCity',
                'selectedBarangay'
            ));

        } catch (\Exception $e) {
            Log::error('System Admin Dashboard Error', ['error' => $e->getMessage()]);
            
            // Return with minimal stats in case of error
            $stats = [
                'total_users' => 0,
                'system_admins' => 0,
                'super_admins' => 0,
                'admins' => 0,
                'active_users' => 0,
                'inactive_users' => 0,
                'total_mobile_users' => 0,
                'total_notifications' => 0,
                'total_evacuation_centers' => 0,
                'active_evacuation_centers' => 0,
                'pending_requests' => 0,
            ];

            return view('components.system-admin.dashboard', compact('stats'))
                ->with('error', 'Some dashboard data could not be loaded.');
        }
    }

    /**
     * User Management for System Administrators
     */
    public function userManagement(Request $request)
    {
        $user = Auth::user();

        if (!$user->isSystemAdmin()) {
            abort(403, 'Access denied. System Administrator access required.');
        }

        // Get filter parameters
        $selectedCity = $request->get('city');
        $selectedBarangay = $request->get('barangay');
        $selectedRole = $request->get('role');

        // Get available cities and barangays for filters
        $cities = $this->barangayService->getAllProvincialCities();
        $barangays = [];
        if ($selectedCity) {
            $barangays = $this->barangayService->getBarangaysByCity($selectedCity);
        }

        // Build query with filters
        $query = User::with(['managementRequests']);

        if ($selectedCity) {
            $query->where('city', $selectedCity);
        }

        if ($selectedBarangay) {
            $query->where('barangay', $selectedBarangay);
        }

        if ($selectedRole) {
            $query->where('role', $selectedRole);
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('components.system-admin.user-management', compact(
            'users',
            'cities',
            'barangays',
            'selectedCity',
            'selectedBarangay',
            'selectedRole'
        ));
    }

    /**
     * Get barangays for a specific city (AJAX endpoint)
     */
    public function getBarangaysByCity(Request $request)
    {
        $user = Auth::user();

        if (!$user->isSystemAdmin()) {
            return response()->json(['error' => 'Access denied'], 403);
        }

        $city = $request->get('city');
        if (!$city) {
            return response()->json(['barangays' => []]);
        }

        try {
            $barangays = $this->barangayService->getBarangaysByCity($city);
            return response()->json(['barangays' => $barangays]);
        } catch (\Exception $e) {
            Log::error('Failed to get barangays for city', ['city' => $city, 'error' => $e->getMessage()]);
            return response()->json(['error' => 'Failed to load barangays'], 500);
        }
    }

    /**
     * Mobile Users Management for System Administrators
     */
    public function mobileUsers(Request $request)
    {
        $user = Auth::user();

        if (!$user->isSystemAdmin()) {
            abort(403, 'Access denied. System Administrator access required.');
        }

        // Get filter parameters
        $search = $request->input('search');
        $selectedCity = $request->input('city');
        $barangay = $request->input('barangay');
        $status = $request->input('status');

        // Build query for device tokens (mobile devices that have connected)
        $query = \App\Models\DeviceToken::with('user');

        // Apply search filter
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('device_type', 'like', "%{$search}%")
                  ->orWhere('device_name', 'like', "%{$search}%")
                  ->orWhere('token', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('email', 'like', "%{$search}%")
                               ->orWhereRaw("CONCAT(first_name, ' ', last_name) like ?", ["%{$search}%"]);
                  });
            });
        }

        // Apply status filter (active/inactive tokens)
        if ($status) {
            if ($status === 'Active') {
                $query->where('is_active', true);
            } elseif ($status === 'Inactive') {
                $query->where('is_active', false);
            }
        }

        $mobileUsers = $query->orderBy('created_at', 'desc')->paginate(20);

        // Get unique device types for filter dropdown (instead of barangays)
        $barangays = \App\Models\DeviceToken::distinct()->pluck('device_type')->filter()->sort()->values();

        // Mobile users don't need city filter, but initialize empty array to prevent errors
        $cities = collect();

        // Get statistics for device tokens
        $stats = [
            'total_users' => \App\Models\DeviceToken::count(),
            'active_users' => \App\Models\DeviceToken::where('is_active', true)->count(),
            'inactive_users' => \App\Models\DeviceToken::where('is_active', false)->count(),
            'recent_registrations' => \App\Models\DeviceToken::where('created_at', '>=', now()->subDays(30))->count(),
        ];

        return view('components.system-admin.mobile-users', compact('mobileUsers', 'cities', 'barangays', 'stats', 'search', 'selectedCity', 'barangay', 'status'));
    }

    /**
     * Create new user (any role)
     */
    public function createUser(Request $request)
    {
        $user = Auth::user();
        
        if (!$user->isSystemAdmin()) {
            return response()->json(['success' => false, 'message' => 'Access denied.'], 403);
        }

        $validatedData = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'middle_name' => 'nullable|string|max:255',
            'email' => 'required|email|unique:users,email',
            'role' => 'required|in:system_admin,super_admin,admin',
            'position' => 'required|string|max:255',
            'city' => 'required_if:role,super_admin,admin|nullable|string|max:255',
            'barangay' => 'required_if:role,admin|nullable|string|max:255',
        ]);

        // Validate position against organizational chart
        $validPositions = OrganizationalPositionService::getAllPositionsFlat();
        if (!in_array($validatedData['position'], $validPositions)) {
            // Allow custom positions but log them for review
            Log::info('Custom position used in user registration', [
                'position' => $validatedData['position'],
                'role' => $validatedData['role'],
                'created_by' => $user->id
            ]);
        }

        try {
            // Generate temporary password
            $tempPassword = Str::random(12);

            // For BDRRMC users (admin role), ensure city is properly set
            $cityValue = null;
            $barangayValue = null;

            if ($validatedData['role'] === 'admin') {
                $barangayValue = $validatedData['barangay'];
                // If city is not provided for BDRRMC user, try to derive it from barangay
                if (empty($validatedData['city']) && $barangayValue) {
                    // Try to find the city from existing barangay data or PSGC
                    $derivedCity = $this->deriveCityFromBarangay($barangayValue);
                    $cityValue = $derivedCity ?: $validatedData['city']; // Use derived city or provided city
                } else {
                    $cityValue = $validatedData['city'];
                }
            } elseif ($validatedData['role'] === 'super_admin') {
                $cityValue = $validatedData['city'];
            }
            // system_admin role gets null for both city and barangay

            $newUser = User::create([
                'first_name' => $validatedData['first_name'],
                'last_name' => $validatedData['last_name'],
                'middle_name' => $validatedData['middle_name'],
                'email' => $validatedData['email'],
                'password' => Hash::make($tempPassword),
                'role' => $validatedData['role'],
                'position' => $validatedData['position'],
                'city' => $cityValue,
                'barangay' => $barangayValue,
                'status' => 'Active',
            ]);

            // Send email with credentials
            try {
                $this->sendUserCredentials($newUser, $tempPassword);
                $emailMessage = " Login credentials have been sent to {$newUser->email}.";
            } catch (\Exception $e) {
                Log::warning('Failed to send credentials email', [
                    'user_id' => $newUser->id,
                    'error' => $e->getMessage()
                ]);
                $emailMessage = " Please manually provide the temporary password: {$tempPassword}";
            }

            Log::info('User created by System Administrator', [
                'created_user_id' => $newUser->id,
                'created_by' => $user->id,
                'role' => $request->role,
                'email_sent' => isset($emailMessage) && strpos($emailMessage, 'sent') !== false
            ]);

            return response()->json([
                'success' => true,
                'message' => "User account created successfully for {$newUser->first_name} {$newUser->last_name}.{$emailMessage}",
                'user' => $newUser
            ]);

        } catch (\Exception $e) {
            Log::error('User creation failed', ['error' => $e->getMessage()]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to create user.'
            ], 500);
        }
    }

    /**
     * Check database health
     */
    private function checkDatabaseHealth()
    {
        try {
            DB::connection()->getPdo();
            return 'healthy';
        } catch (\Exception $e) {
            return 'error';
        }
    }

    /**
     * Get user activity metrics
     */
    private function getUserActivityMetrics()
    {
        try {
            $activeToday = User::whereDate('updated_at', Carbon::today())->count();
            $activeThisWeek = User::whereBetween('updated_at', [
                Carbon::now()->startOfWeek(),
                Carbon::now()->endOfWeek()
            ])->count();

            return [
                'active_today' => $activeToday,
                'active_this_week' => $activeThisWeek,
            ];
        } catch (\Exception $e) {
            return ['active_today' => 0, 'active_this_week' => 0];
        }
    }

    /**
     * Get system load metrics
     */
    private function getSystemLoadMetrics()
    {
        try {
            $memoryUsage = memory_get_usage(true);
            $memoryLimit = ini_get('memory_limit');
            
            return [
                'memory_usage' => $this->formatBytes($memoryUsage),
                'memory_limit' => $memoryLimit,
                'php_version' => PHP_VERSION,
            ];
        } catch (\Exception $e) {
            return [
                'memory_usage' => 'Unknown',
                'memory_limit' => 'Unknown',
                'php_version' => PHP_VERSION,
            ];
        }
    }

    /**
     * Get monthly registration trends
     */
    private function getMonthlyRegistrationTrends()
    {
        try {
            return User::select(
                DB::raw('MONTH(created_at) as month'),
                DB::raw('YEAR(created_at) as year'),
                DB::raw('COUNT(*) as count')
            )
            ->where('created_at', '>=', Carbon::now()->subMonths(12))
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();
        } catch (\Exception $e) {
            return collect();
        }
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Get cities from PSGC API
     */
    public function getCities()
    {
        try {
            $cities = $this->psgcService->getCebuProvinceCities();

            return response()->json([
                'success' => true,
                'data' => $cities
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch cities', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch cities',
                'data' => []
            ], 500);
        }
    }

    /**
     * Get barangays for a specific city
     */
    public function getBarangays($cityCode)
    {
        try {
            // Find city name from the cities list
            $cities = $this->psgcService->getCebuProvinceCities();
            $city = collect($cities)->firstWhere('code', $cityCode);
            $cityName = $city['name'] ?? 'Unknown City';

            $barangays = $this->psgcService->getCityBarangays($cityCode, $cityName);

            return response()->json([
                'success' => true,
                'data' => $barangays
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch barangays', [
                'city_code' => $cityCode,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch barangays',
                'data' => []
            ], 500);
        }
    }

    /**
     * Derive city from barangay name using PSGC data
     */
    private function deriveCityFromBarangay($barangayName)
    {
        try {
            // Get all cities in Cebu Province
            $cities = $this->psgcService->getCebuProvinceCities();

            foreach ($cities as $city) {
                // Get barangays for this city
                $barangays = $this->psgcService->getCityBarangays($city['code'], $city['name']);

                // Check if the barangay exists in this city
                $barangayExists = collect($barangays)->contains(function ($barangay) use ($barangayName) {
                    return strtolower($barangay['name']) === strtolower($barangayName);
                });

                if ($barangayExists) {
                    Log::info("Found city for barangay", [
                        'barangay' => $barangayName,
                        'city' => $city['name']
                    ]);
                    return $city['name'];
                }
            }

            // If not found in any city, return null to indicate unknown
            Log::warning("Could not find city for barangay in Cebu Province", [
                'barangay' => $barangayName
            ]);
            return null;

        } catch (\Exception $e) {
            Log::error('Failed to derive city from barangay', [
                'barangay' => $barangayName,
                'error' => $e->getMessage()
            ]);

            // Return null instead of defaulting to Cebu City
            return null;
        }
    }

    /**
     * Display system logs for Technical Administrators
     */
    public function systemLogs(Request $request)
    {
        $user = Auth::user();

        if (!$user->isSystemAdmin()) {
            abort(403, 'Access denied. System Administrator access required.');
        }

        try {
            // Get filter parameters
            $level = $request->input('level', 'all');
            $date = $request->input('date');
            $search = $request->input('search');
            $perPage = $request->input('per_page', 50);

            // Read Laravel log files
            $logs = $this->readLogFiles($level, $date, $search);

            // Paginate the logs
            $currentPage = $request->input('page', 1);
            $offset = ($currentPage - 1) * $perPage;
            $paginatedLogs = array_slice($logs, $offset, $perPage);

            // Create pagination data
            $totalLogs = count($logs);
            $totalPages = ceil($totalLogs / $perPage);

            $pagination = [
                'current_page' => $currentPage,
                'total_pages' => $totalPages,
                'total_logs' => $totalLogs,
                'per_page' => $perPage,
                'has_more' => $currentPage < $totalPages
            ];

            // Get log statistics
            $stats = $this->getLogStatistics();

            return view('components.system-admin.system-logs', compact(
                'paginatedLogs',
                'pagination',
                'stats',
                'level',
                'date',
                'search'
            ));

        } catch (\Exception $e) {
            Log::error('System Logs Error', ['error' => $e->getMessage()]);

            return view('components.system-admin.system-logs', [
                'paginatedLogs' => [],
                'pagination' => ['current_page' => 1, 'total_pages' => 0, 'total_logs' => 0],
                'stats' => ['error' => 0, 'warning' => 0, 'info' => 0, 'debug' => 0],
                'level' => 'all',
                'date' => null,
                'search' => null,
                'error' => 'Unable to load system logs.'
            ]);
        }
    }

    /**
     * Read and parse Laravel log files
     */
    private function readLogFiles($level = 'all', $date = null, $search = null)
    {
        $logs = [];
        $logPath = storage_path('logs');

        try {
            // Determine which log files to read
            if ($date) {
                $logFile = $logPath . '/laravel-' . $date . '.log';
                $logFiles = file_exists($logFile) ? [$logFile] : [];
            } else {
                // Get recent log files (last 7 days)
                $logFiles = [];
                for ($i = 0; $i < 7; $i++) {
                    $fileDate = Carbon::now()->subDays($i)->format('Y-m-d');
                    $logFile = $logPath . '/laravel-' . $fileDate . '.log';
                    if (file_exists($logFile)) {
                        $logFiles[] = $logFile;
                    }
                }

                // Also include laravel.log if it exists
                $mainLogFile = $logPath . '/laravel.log';
                if (file_exists($mainLogFile)) {
                    array_unshift($logFiles, $mainLogFile);
                }
            }

            foreach ($logFiles as $logFile) {
                $fileContent = file_get_contents($logFile);
                $logEntries = $this->parseLogContent($fileContent);
                $logs = array_merge($logs, $logEntries);
            }

            // Filter logs
            $logs = $this->filterLogs($logs, $level, $search);

            // Sort by timestamp (newest first)
            usort($logs, function($a, $b) {
                return strtotime($b['timestamp']) - strtotime($a['timestamp']);
            });

        } catch (\Exception $e) {
            Log::error('Error reading log files', ['error' => $e->getMessage()]);
        }

        return $logs;
    }

    /**
     * Parse log file content into structured data
     */
    private function parseLogContent($content)
    {
        $logs = [];
        $lines = explode("\n", $content);
        $currentLog = null;

        foreach ($lines as $line) {
            if (empty(trim($line))) continue;

            // Check if line starts with timestamp pattern [YYYY-MM-DD HH:MM:SS]
            if (preg_match('/^\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\].*?\.(\w+): (.+)/', $line, $matches)) {
                // Save previous log if exists
                if ($currentLog) {
                    $logs[] = $currentLog;
                }

                // Start new log entry
                $currentLog = [
                    'timestamp' => $matches[1],
                    'level' => strtolower($matches[2]),
                    'message' => $matches[3],
                    'context' => '',
                    'full_line' => $line
                ];
            } elseif ($currentLog) {
                // Continuation of previous log entry
                $currentLog['context'] .= $line . "\n";
                $currentLog['full_line'] .= "\n" . $line;
            }
        }

        // Add the last log entry
        if ($currentLog) {
            $logs[] = $currentLog;
        }

        return $logs;
    }

    /**
     * Filter logs based on criteria
     */
    private function filterLogs($logs, $level, $search)
    {
        return array_filter($logs, function($log) use ($level, $search) {
            // Filter by level
            if ($level !== 'all' && $log['level'] !== $level) {
                return false;
            }

            // Filter by search term
            if ($search && stripos($log['message'] . ' ' . $log['context'], $search) === false) {
                return false;
            }

            return true;
        });
    }

    /**
     * Get log statistics
     */
    private function getLogStatistics()
    {
        $stats = [
            'error' => 0,
            'warning' => 0,
            'info' => 0,
            'debug' => 0,
            'total' => 0
        ];

        try {
            // Get today's logs for statistics
            $todayLogs = $this->readLogFiles('all', Carbon::now()->format('Y-m-d'));

            foreach ($todayLogs as $log) {
                $level = $log['level'];
                if (isset($stats[$level])) {
                    $stats[$level]++;
                }
                $stats['total']++;
            }

        } catch (\Exception $e) {
            Log::error('Error getting log statistics', ['error' => $e->getMessage()]);
        }

        return $stats;
    }

    /**
     * View user details
     */
    public function viewUser($id)
    {
        $user = Auth::user();

        if (!$user->isSystemAdmin()) {
            return response()->json(['success' => false, 'message' => 'Access denied.'], 403);
        }

        try {
            $targetUser = User::with(['managementRequests', 'targetedRequests', 'reviewedRequests'])->findOrFail($id);

            return response()->json([
                'success' => true,
                'user' => [
                    'id' => $targetUser->id,
                    'first_name' => $targetUser->first_name,
                    'middle_name' => $targetUser->middle_name,
                    'last_name' => $targetUser->last_name,
                    'email' => $targetUser->email,
                    'role' => $targetUser->role,
                    'role_display' => $targetUser->getRoleDisplayName(),
                    'position' => $targetUser->position,
                    'city' => $targetUser->city,
                    'city_name' => $targetUser->city_name,
                    'barangay' => $targetUser->barangay,
                    'status' => $targetUser->status,
                    'created_at' => $targetUser->created_at->format('M d, Y H:i:s'),
                    'updated_at' => $targetUser->updated_at->format('M d, Y H:i:s'),
                    'management_requests_count' => $targetUser->managementRequests->count(),
                    'targeted_requests_count' => $targetUser->targetedRequests->count(),
                    'reviewed_requests_count' => $targetUser->reviewedRequests->count(),
                    'responsibilities' => $targetUser->getResponsibilities(),
                    'access_description' => $targetUser->getAccessLevelDescription(),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to view user', ['user_id' => $id, 'error' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => 'User not found.'], 404);
        }
    }

    /**
     * Deactivate/Activate user account
     */
    public function toggleUserStatus($id)
    {
        $user = Auth::user();

        if (!$user->isSystemAdmin()) {
            return response()->json(['success' => false, 'message' => 'Access denied.'], 403);
        }

        try {
            $targetUser = User::findOrFail($id);

            // Prevent system admin from deactivating themselves
            if ($targetUser->id === $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'You cannot deactivate your own account.'
                ], 400);
            }

            // Toggle status
            $newStatus = $targetUser->status === 'Active' ? 'Inactive' : 'Active';
            $targetUser->status = $newStatus;
            $targetUser->save();

            Log::info('User status toggled by System Administrator', [
                'target_user_id' => $targetUser->id,
                'target_user_email' => $targetUser->email,
                'new_status' => $newStatus,
                'admin_id' => $user->id,
                'admin_email' => $user->email
            ]);

            return response()->json([
                'success' => true,
                'message' => "User account has been {$newStatus}.",
                'new_status' => $newStatus
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to toggle user status', [
                'user_id' => $id,
                'admin_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return response()->json(['success' => false, 'message' => 'Failed to update user status.'], 500);
        }
    }

    /**
     * Delete user account permanently
     */
    public function deleteUser($id)
    {
        $user = Auth::user();

        if (!$user->isSystemAdmin()) {
            return response()->json(['success' => false, 'message' => 'Access denied.'], 403);
        }

        try {
            $targetUser = User::findOrFail($id);

            // Prevent system admin from deleting themselves
            if ($targetUser->id === $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'You cannot delete your own account.'
                ], 400);
            }

            // Store user info for logging before deletion
            $deletedUserInfo = [
                'id' => $targetUser->id,
                'email' => $targetUser->email,
                'name' => $targetUser->first_name . ' ' . $targetUser->last_name,
                'role' => $targetUser->role,
                'position' => $targetUser->position,
                'city' => $targetUser->city,
                'barangay' => $targetUser->barangay,
            ];

            // Delete related records first (if any foreign key constraints)
            $targetUser->managementRequests()->delete();
            $targetUser->targetedRequests()->delete();
            $targetUser->reviewedRequests()->update(['reviewed_by' => null]);

            // Delete the user
            $targetUser->delete();

            Log::warning('User account deleted by System Administrator', [
                'deleted_user' => $deletedUserInfo,
                'admin_id' => $user->id,
                'admin_email' => $user->email,
                'deleted_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'User account has been permanently deleted.'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to delete user', [
                'user_id' => $id,
                'admin_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return response()->json(['success' => false, 'message' => 'Failed to delete user account.'], 500);
        }
    }

    /**
     * Edit user account
     */
    public function editUser(Request $request, $id)
    {
        $user = Auth::user();

        if (!$user->isSystemAdmin()) {
            return response()->json(['success' => false, 'message' => 'Access denied.'], 403);
        }

        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'middle_name' => 'nullable|string|max:255',
            'email' => 'required|email|unique:users,email,' . $id,
            'role' => 'required|in:system_admin,super_admin,admin',
            'position' => 'required|string|max:255',
            'city' => 'required_if:role,super_admin,admin|nullable|string|max:255',
            'barangay' => 'required_if:role,admin|nullable|string|max:255',
            'status' => 'required|in:Active,Inactive',
        ]);

        try {
            $targetUser = User::findOrFail($id);

            // Store original data for logging
            $originalData = $targetUser->toArray();

            // Update user data
            $targetUser->update([
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'middle_name' => $request->middle_name,
                'email' => $request->email,
                'role' => $request->role,
                'position' => $request->position,
                'city' => $request->role === 'system_admin' ? null : $request->city,
                'barangay' => $request->role === 'admin' ? $request->barangay : null,
                'status' => $request->status,
            ]);

            Log::info('User account updated by System Administrator', [
                'target_user_id' => $targetUser->id,
                'original_data' => $originalData,
                'updated_data' => $targetUser->fresh()->toArray(),
                'admin_id' => $user->id,
                'admin_email' => $user->email
            ]);

            return response()->json([
                'success' => true,
                'message' => 'User account updated successfully.',
                'user' => $targetUser->fresh()
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update user', [
                'user_id' => $id,
                'admin_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return response()->json(['success' => false, 'message' => 'Failed to update user account.'], 500);
        }
    }

    /**
     * Display request management for System Administrators
     */
    public function requestManagement(Request $request)
    {
        $user = Auth::user();

        if (!$user->isSystemAdmin()) {
            abort(403, 'Access denied. System Administrator access required.');
        }

        try {
            // Get filter parameters
            $status = $request->input('status', 'pending');
            $actionType = $request->input('action_type', 'all');
            $barangay = $request->input('barangay');
            $perPage = $request->input('per_page', 20);

            // Build query for user management requests
            $query = UserManagementRequest::with(['requester', 'targetUser', 'reviewer'])
                ->orderBy('created_at', 'desc');

            // Apply filters
            if ($status !== 'all') {
                $query->where('status', $status);
            }

            if ($actionType !== 'all') {
                $query->where('action_type', $actionType);
            }

            if ($barangay) {
                $query->whereHas('requester', function($q) use ($barangay) {
                    $q->where('barangay', $barangay);
                });
            }

            $requests = $query->paginate($perPage);

            // Get statistics
            $stats = $this->getRequestStatistics();

            // Get unique barangays for filter
            $barangays = UserManagementRequest::with('requester')
                ->get()
                ->pluck('requester.barangay')
                ->filter()
                ->unique()
                ->sort()
                ->values();

            return view('components.system-admin.request-management', compact(
                'requests',
                'stats',
                'status',
                'actionType',
                'barangay',
                'barangays'
            ));

        } catch (\Exception $e) {
            Log::error('Request Management Error', ['error' => $e->getMessage()]);

            return view('components.system-admin.request-management', [
                'requests' => collect(),
                'stats' => ['pending' => 0, 'approved' => 0, 'rejected' => 0, 'total' => 0],
                'status' => 'pending',
                'actionType' => 'all',
                'barangay' => null,
                'barangays' => collect(),
                'error' => 'Unable to load requests.'
            ]);
        }
    }

    /**
     * Get request statistics
     */
    private function getRequestStatistics()
    {
        $stats = [
            'pending' => UserManagementRequest::where('status', 'pending')->count(),
            'approved' => UserManagementRequest::where('status', 'approved')->count(),
            'rejected' => UserManagementRequest::where('status', 'rejected')->count(),
            'registration' => UserManagementRequest::where('action_type', 'registration')->count(),
            'deactivate' => UserManagementRequest::where('action_type', 'deactivate')->count(),
            'delete' => UserManagementRequest::where('action_type', 'delete')->count(),
            'total' => UserManagementRequest::count(),
        ];

        return $stats;
    }

    /**
     * View detailed request information
     */
    public function viewRequest($id)
    {
        $user = Auth::user();

        if (!$user->isSystemAdmin()) {
            return response()->json(['success' => false, 'message' => 'Access denied.'], 403);
        }

        try {
            $request = UserManagementRequest::with(['requester', 'targetUser', 'reviewer'])->findOrFail($id);

            return response()->json([
                'success' => true,
                'request' => [
                    'id' => $request->id,
                    'action_type' => $request->action_type,
                    'action_type_display' => $request->getActionTypeDisplayAttribute(),
                    'status' => $request->status,
                    'status_display' => $request->getStatusDisplayAttribute(),
                    'reason' => $request->reason,
                    'created_at' => $request->created_at->format('M d, Y H:i:s'),
                    'reviewed_at' => $request->reviewed_at ? $request->reviewed_at->format('M d, Y H:i:s') : null,
                    'review_notes' => $request->review_notes,
                    'requester' => $request->requester ? [
                        'id' => $request->requester->id,
                        'name' => $request->requester->first_name . ' ' . $request->requester->last_name,
                        'email' => $request->requester->email,
                        'position' => $request->requester->position,
                        'barangay' => $request->requester->barangay,
                    ] : null,
                    'target_user' => $request->targetUser ? [
                        'id' => $request->targetUser->id,
                        'name' => $request->targetUser->first_name . ' ' . $request->targetUser->last_name,
                        'email' => $request->targetUser->email,
                        'position' => $request->targetUser->position,
                        'role' => $request->targetUser->role,
                        'status' => $request->targetUser->status,
                    ] : null,
                    'reviewer' => $request->reviewer ? [
                        'id' => $request->reviewer->id,
                        'name' => $request->reviewer->first_name . ' ' . $request->reviewer->last_name,
                        'email' => $request->reviewer->email,
                    ] : null,
                    // Registration request specific data
                    'requested_first_name' => $request->requested_first_name,
                    'requested_last_name' => $request->requested_last_name,
                    'requested_email' => $request->requested_email,
                    'requested_position' => $request->requested_position,
                    'requested_barangay' => $request->requested_barangay,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to view request', ['request_id' => $id, 'error' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => 'Request not found.'], 404);
        }
    }

    /**
     * Approve or reject a user management request
     */
    public function reviewRequest(Request $request, $id)
    {
        $user = Auth::user();

        if (!$user->isSystemAdmin()) {
            return response()->json(['success' => false, 'message' => 'Access denied.'], 403);
        }

        $request->validate([
            'action' => 'required|in:approve,reject',
            'review_notes' => 'nullable|string|max:1000'
        ]);

        try {
            $managementRequest = UserManagementRequest::with(['requester', 'targetUser'])
                ->where('status', 'pending')
                ->findOrFail($id);

            DB::beginTransaction();

            // Update the request status
            $managementRequest->update([
                'status' => $request->action === 'approve' ? 'approved' : 'rejected',
                'reviewed_by' => $user->id,
                'review_notes' => $request->review_notes,
                'reviewed_at' => now()
            ]);

            $message = '';

            // If approved, execute the requested action
            if ($request->action === 'approve') {
                $message = $this->executeApprovedRequest($managementRequest);
            } else {
                $message = 'Request has been rejected.';
            }

            DB::commit();

            Log::info('User management request reviewed by System Administrator', [
                'request_id' => $managementRequest->id,
                'action_type' => $managementRequest->action_type,
                'decision' => $request->action,
                'reviewer_id' => $user->id,
                'reviewer_email' => $user->email,
                'requester_id' => $managementRequest->requester_id,
                'target_user_id' => $managementRequest->target_user_id
            ]);

            return response()->json([
                'success' => true,
                'message' => $message
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to review request', [
                'request_id' => $id,
                'admin_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return response()->json(['success' => false, 'message' => 'Failed to process request.'], 500);
        }
    }

    /**
     * Execute the approved request action
     */
    private function executeApprovedRequest(UserManagementRequest $managementRequest)
    {
        switch ($managementRequest->action_type) {
            case 'registration':
                return $this->executeRegistrationRequest($managementRequest);
            case 'deactivate':
                return $this->executeDeactivationRequest($managementRequest);
            case 'delete':
                return $this->executeDeletionRequest($managementRequest);
            default:
                throw new \Exception('Unknown action type: ' . $managementRequest->action_type);
        }
    }

    /**
     * Execute registration request
     */
    private function executeRegistrationRequest(UserManagementRequest $managementRequest)
    {
        // Generate temporary password
        $tempPassword = Str::random(12);

        // Determine city for the new BDRRMC user
        $derivedCity = $this->deriveCityFromBarangay($managementRequest->requested_barangay);
        $cityValue = $managementRequest->requester->city ?? $derivedCity;

        // Create the new user
        $newUser = User::create([
            'first_name' => $managementRequest->requested_first_name,
            'last_name' => $managementRequest->requested_last_name,
            'email' => $managementRequest->requested_email,
            'password' => Hash::make($tempPassword),
            'role' => 'admin', // Registration requests are for BDRRMC users
            'position' => $managementRequest->requested_position,
            'barangay' => $managementRequest->requested_barangay,
            'city' => $cityValue,
            'status' => 'Active',
        ]);

        // Send email with credentials (reuse existing email functionality)
        try {
            $this->sendUserCredentials($newUser, $tempPassword);
        } catch (\Exception $e) {
            Log::warning('Failed to send credentials email', [
                'user_id' => $newUser->id,
                'error' => $e->getMessage()
            ]);
        }

        return "User account created successfully for {$newUser->first_name} {$newUser->last_name}. Login credentials have been sent to {$newUser->email}.";
    }

    /**
     * Execute deactivation request
     */
    private function executeDeactivationRequest(UserManagementRequest $managementRequest)
    {
        $targetUser = $managementRequest->targetUser;
        $targetUser->update(['status' => 'Inactive']);

        return "User account for {$targetUser->first_name} {$targetUser->last_name} has been deactivated.";
    }

    /**
     * Execute deletion request
     */
    private function executeDeletionRequest(UserManagementRequest $managementRequest)
    {
        $targetUser = $managementRequest->targetUser;
        $userName = $targetUser->first_name . ' ' . $targetUser->last_name;

        // Delete related records
        $targetUser->managementRequests()->delete();
        $targetUser->targetedRequests()->delete();
        $targetUser->reviewedRequests()->update(['reviewed_by' => null]);

        // Delete the user
        $targetUser->delete();

        return "User account for {$userName} has been permanently deleted.";
    }

    /**
     * Check if email address is available for registration
     */
    public function checkEmailAvailability(Request $request)
    {
        $user = Auth::user();

        if (!$user->isSystemAdmin()) {
            return response()->json(['success' => false, 'message' => 'Access denied.'], 403);
        }

        $request->validate([
            'email' => 'required|email'
        ]);

        try {
            $email = $request->input('email');
            $exists = User::where('email', $email)->exists();

            return response()->json([
                'success' => true,
                'available' => !$exists,
                'message' => $exists ? 'Email is already in use' : 'Email is available'
            ]);

        } catch (\Exception $e) {
            Log::error('Email availability check failed', [
                'email' => $request->input('email'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'available' => false,
                'message' => 'Unable to verify email availability'
            ], 500);
        }
    }

    /**
     * Get positions for a specific role
     */
    public function getPositionsForRole(Request $request)
    {
        $user = Auth::user();

        if (!$user->isSystemAdmin()) {
            return response()->json(['success' => false, 'message' => 'Access denied.'], 403);
        }

        $request->validate([
            'role' => 'required|in:system_admin,super_admin,admin'
        ]);

        try {
            $role = $request->input('role');
            $categories = OrganizationalPositionService::getPositionCategories($role);

            return response()->json([
                'success' => true,
                'categories' => $categories
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get positions for role', [
                'role' => $request->input('role'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Unable to load positions'
            ], 500);
        }
    }

    /**
     * Send user credentials via email
     */
    private function sendUserCredentials(User $user, string $temporaryPassword)
    {
        try {
            Mail::to($user->email)->send(new AdminRegistrationMail($user, $temporaryPassword));

            Log::info('User credentials email sent successfully', [
                'user_id' => $user->id,
                'email' => $user->email,
                'role' => $user->role
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send user credentials email', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }
}
