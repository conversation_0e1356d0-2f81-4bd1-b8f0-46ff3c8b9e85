<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Notification;
use App\Models\Evacuation;
use Carbon\Carbon;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// Test parameters - June 2025
$selectedMonth = 6; // June
$selectedYear = 2025;

echo "Testing Fixed Barangay Statistics for {$selectedMonth}/{$selectedYear}\n";
echo "========================================================\n\n";

// Get all unique barangays (excluding City Hall and empty values)
$barangays = User::where('barangay', '!=', 'City Hall')
    ->where('barangay', '!=', '')
    ->whereNotNull('barangay')
    ->distinct('barangay')
    ->pluck('barangay')
    ->sort()
    ->values();

echo "Found barangays: " . implode(', ', $barangays->toArray()) . "\n\n";

$barangayStats = collect();

foreach ($barangays as $barangay) {
    // Get notifications count for this barangay and period
    $notificationsCount = Notification::where('barangay', $barangay)
        ->whereMonth('created_at', $selectedMonth)
        ->whereYear('created_at', $selectedYear)
        ->count();

    // Get evacuation centers count for this barangay and period
    $evacuationCentersCount = Evacuation::where('barangay', $barangay)
        ->where('status', 'Active')
        ->whereMonth('created_at', $selectedMonth)
        ->whereYear('created_at', $selectedYear)
        ->count();

    // Get users count for this barangay and period
    $activeUsersCount = User::where('barangay', $barangay)
        ->where('status', 'active')
        ->whereMonth('created_at', $selectedMonth)
        ->whereYear('created_at', $selectedYear)
        ->count();

    // Determine status based on activity
    $status = ($notificationsCount > 0 || $evacuationCentersCount > 0 || $activeUsersCount > 0) ? 'Active' : 'Inactive';

    echo "Barangay: {$barangay}\n";
    echo "  - Notifications Added: {$notificationsCount}\n";
    echo "  - Evacuation Centers Added: {$evacuationCentersCount}\n";
    echo "  - Users Added: {$activeUsersCount}\n";
    echo "  - Status: {$status}\n";
    echo "\n";

    $barangayStats->push((object) [
        'barangay' => $barangay,
        'notifications_count' => $notificationsCount,
        'evacuation_centers_count' => $evacuationCentersCount,
        'active_users_count' => $activeUsersCount,
        'status' => $status
    ]);
}

echo "Total records: " . $barangayStats->count() . "\n";

// Also test total counts for verification
echo "\n=== Verification ===\n";
echo "Total notifications in June 2025: " . Notification::whereMonth('created_at', 6)->whereYear('created_at', 2025)->count() . "\n";
echo "Total evacuation centers in June 2025: " . Evacuation::whereMonth('created_at', 6)->whereYear('created_at', 2025)->count() . "\n";
echo "Total users in June 2025: " . User::whereMonth('created_at', 6)->whereYear('created_at', 2025)->count() . "\n"; 