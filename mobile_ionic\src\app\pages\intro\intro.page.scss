.intro-bg {
  --background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.intro-wrapper {
  width: 100%;
  max-width: 350px;
  padding: 2rem;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.logo-container {
  margin-bottom: 3rem;
  align-self: center;
}

.main-icon {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  padding: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.title-container {
  margin-bottom: 3rem;
  align-self: center;
}

.app-title {
  font-size: 2.2rem;
  font-weight: 700;
  color: black;
  margin: 0 0 0.5rem 0;
  text-shadow: none;
  letter-spacing: -0.5px;
}

.app-subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  font-weight: 300;
}

.button-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.login-btn {
  --background: #007bff;
  --color: white;
  --border-radius: 25px;
  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  font-weight: 600;
  font-size: 1.1rem;
  height: 50px;
  width: 100%;
  margin-top: 1rem;
}

.signup-btn {
  --color: #007bff;
  --border-color: #007bff;
  --border-radius: 25px;
  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  font-weight: 600;
  font-size: 1.1rem;
  height: 50px;
  width: 100%;
}

.signup-btn:hover {
  --background: rgba(255, 255, 255, 0.1);
}

/* Responsive adjustments */
@media (max-height: 600px) {
  .intro-wrapper {
    padding: 1rem;
  }

  .logo-container {
    margin-bottom: 2rem;
  }

  .main-icon {
    width: 80px;
    height: 80px;
    padding: 15px;
  }

  .title-container {
    margin-bottom: 2rem;
  }

  .app-title {
    font-size: 2rem;
  }
}
