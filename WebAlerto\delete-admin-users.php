<?php

require_once 'vendor/autoload.php';

use App\Models\User;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Deleting all admin users...\n";

// Get all admin users (super_admin, chairman, officer, assistant)
$adminUsers = User::whereIn('role', ['super_admin', 'chairman', 'officer', 'assistant'])->get();

if ($adminUsers->count() === 0) {
    echo "No admin users found to delete.\n";
    exit(0);
}

echo "Found {$adminUsers->count()} admin user(s) to delete:\n";

foreach ($adminUsers as $user) {
    echo "- {$user->email} (Role: {$user->role}, Barangay: {$user->barangay})\n";
}

echo "\nAre you sure you want to delete all these admin users? (yes/no): ";
$handle = fopen("php://stdin", "r");
$line = fgets($handle);
fclose($handle);

if (trim(strtolower($line)) !== 'yes') {
    echo "Operation cancelled.\n";
    exit(0);
}

// Delete all admin users
$deletedCount = 0;
foreach ($adminUsers as $user) {
    try {
        $email = $user->email;
        $user->delete();
        echo "✓ Deleted: {$email}\n";
        $deletedCount++;
    } catch (Exception $e) {
        echo "✗ Failed to delete {$user->email}: " . $e->getMessage() . "\n";
    }
}

echo "\nOperation completed! Deleted {$deletedCount} admin user(s).\n";
echo "You can now reuse these email addresses for new admin registrations.\n"; 