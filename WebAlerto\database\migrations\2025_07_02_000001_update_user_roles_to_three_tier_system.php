<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing user roles to new three-tier system
        DB::transaction(function () {
            // Map current super_admin to system_admin
            DB::table('users')
                ->where('role', 'super_admin')
                ->update(['role' => 'system_admin']);

            // Map current admin to super_admin
            DB::table('users')
                ->where('role', 'admin')
                ->update(['role' => 'super_admin']);

            // Map chairman, officer, assistant to admin
            DB::table('users')
                ->whereIn('role', ['chairman', 'officer', 'assistant'])
                ->update(['role' => 'admin']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverse the role mapping
        DB::transaction(function () {
            // Map system_admin back to super_admin
            DB::table('users')
                ->where('role', 'system_admin')
                ->update(['role' => 'super_admin']);

            // Map super_admin back to admin
            DB::table('users')
                ->where('role', 'super_admin')
                ->update(['role' => 'admin']);

            // Map admin back to officer (default for barangay users)
            DB::table('users')
                ->where('role', 'admin')
                ->update(['role' => 'officer']);
        });
    }
};
