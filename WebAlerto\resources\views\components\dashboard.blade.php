@extends('layout.app')

@section('content')
<!-- Add Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
    integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
    crossorigin=""/>

<!-- Add Chart.js CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<div class="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-sky-100 py-8">
    <!-- Stats Section -->
    <div class="max-w-[1920px] mx-auto px-6 sm:px-8 lg:px-12">
        @if(auth()->user()->hasRole('super_admin') || auth()->user()->hasRole('system_admin'))
        <!-- Search and Filter Section -->
        <div class="bg-white/90 backdrop-blur-sm rounded-xl shadow-xl border border-sky-200 p-6 mb-6">
            <!-- Header -->
            <div class="flex items-center gap-3 mb-6">
                <div class="p-3 bg-gradient-to-br from-sky-500 to-blue-600 rounded-lg shadow-lg">
                    <i class="fas fa-filter text-white text-xl"></i>
                </div>
                <div>
                    <h2 class="text-xl font-bold text-gray-900">Dashboard Filters</h2>
                    <p class="text-sm text-gray-600 mt-1">
                        @if(auth()->user()->hasRole('system_admin'))
                            Filter dashboard data by city and barangay
                        @else
                            Filter dashboard data by barangay
                        @endif
                    </p>
                </div>
            </div>

            <!-- Filter Controls -->
            @if(auth()->user()->hasRole('super_admin') || auth()->user()->hasRole('system_admin'))
            <form action="{{ route('components.dashboard') }}" method="GET">
                @if(auth()->user()->hasRole('system_admin'))
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <!-- City Filter (Only for System Admin) -->
                    <div class="relative">
                        <label for="city" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-city mr-1"></i>Filter by City
                        </label>
                        <select name="city" id="city" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" onchange="this.form.submit()">
                            <option value="">All Cities</option>
                            @foreach($cities as $city)
                                <option value="{{ $city }}" {{ $selectedCity == $city ? 'selected' : '' }}>
                                    {{ $city }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Barangay Filter -->
                    <div class="relative">
                        <label for="barangay" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-map-marker-alt mr-1"></i>Filter by Barangay
                        </label>
                        <select name="barangay" id="barangay" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" onchange="this.form.submit()">
                            <option value="">All Barangays</option>
                            @foreach($barangays as $barangay)
                                <option value="{{ $barangay }}" {{ $selectedBarangay == $barangay ? 'selected' : '' }}>
                                    {{ $barangay }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>


                @else
                <div class="grid grid-cols-1 gap-4">
                    <!-- Barangay Filter (Only for CDRRMC) -->
                    <div class="relative">
                        <label for="barangay" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-map-marker-alt mr-1"></i>Filter by Barangay
                        </label>
                        <select name="barangay" id="barangay" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" onchange="this.form.submit()">
                            <option value="">All Barangays</option>
                            @foreach($barangays as $barangay)
                                <option value="{{ $barangay }}" {{ $selectedBarangay == $barangay ? 'selected' : '' }}>
                                    {{ $barangay }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                @endif
            </form>
            @else
            <!-- BDRRMC users see only their barangay data - no filter needed -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center">
                    <i class="fas fa-map-marker-alt text-blue-600 mr-2"></i>
                    <span class="text-sm font-medium text-blue-800">
                        Showing data for: <strong>{{ auth()->user()->barangay }}</strong>
                    </span>
                </div>
            </div>
            @endif
        </div>
        @endif

        <!-- Summary Statistics Section -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <!-- Total Alerts -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Alerts</p>
                        <p class="text-3xl font-bold text-gray-900">{{ number_format($totalAlerts) }}</p>
                        <p class="text-xs text-gray-500 mt-1">
                            @if(auth()->user()->hasRole('system_admin'))
                                @if($selectedCity)
                                    {{ $selectedCity }}
                                @else
                                    Province-wide
                                @endif
                            @elseif(auth()->user()->hasRole('super_admin'))
                                @if($selectedBarangay && $selectedBarangay !== '')
                                    {{ $selectedBarangay }}
                                @else
                                    {{ auth()->user()->city_name }}
                                @endif
                            @else
                                {{ auth()->user()->barangay }}
                            @endif
                        </p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-red-500 to-pink-600 rounded-xl shadow-lg">
                        <i class="fas fa-bell text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Total Centers -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Centers</p>
                        <p class="text-3xl font-bold text-gray-900">{{ number_format($totalCenters) }}</p>
                        <p class="text-xs text-gray-500 mt-1">
                            @if(auth()->user()->hasRole('system_admin'))
                                @if($selectedCity)
                                    {{ $selectedCity }}
                                @else
                                    Province-wide
                                @endif
                            @elseif(auth()->user()->hasRole('super_admin'))
                                @if($selectedBarangay && $selectedBarangay !== '')
                                    {{ $selectedBarangay }}
                                @else
                                    {{ auth()->user()->city_name }}
                                @endif
                            @else
                                {{ auth()->user()->barangay }}
                            @endif
                        </p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg">
                        <i class="fas fa-building text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Total Users -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Users</p>
                        <p class="text-3xl font-bold text-gray-900">{{ number_format($totalUsers) }}</p>
                        <p class="text-xs text-gray-500 mt-1">
                            @if(auth()->user()->hasRole('system_admin'))
                                @if($selectedCity)
                                    {{ $selectedCity }}
                                @else
                                    Province-wide
                                @endif
                            @elseif(auth()->user()->hasRole('super_admin'))
                                @if($selectedBarangay && $selectedBarangay !== '')
                                    {{ $selectedBarangay }}
                                @else
                                    {{ auth()->user()->city_name }}
                                @endif
                            @else
                                {{ auth()->user()->barangay }}
                            @endif
                        </p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                </div>
            </div>


        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <!-- Monthly Trends Chart -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-bold text-gray-900">Monthly Trends</h3>
                        <p class="text-sm text-gray-600">
                            @if(auth()->user()->hasRole('super_admin'))
                                Alerts & Centers (6 months)
                                @if($selectedBarangay && $selectedBarangay !== '')
                                    - {{ $selectedBarangay }}
                                @else
                                    - All Barangays
                                @endif
                            @else
                                Alerts & Centers (6 months) - {{ auth()->user()->barangay }}
                            @endif
                        </p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg">
                        <i class="fas fa-chart-line text-white text-xl"></i>
                    </div>
                </div>
                <div class="h-64">
                    <canvas id="monthlyTrendsChart"></canvas>
                </div>
            </div>

            <!-- Disaster Type Distribution Chart -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-bold text-gray-900">Disaster Types</h3>
                        <p class="text-sm text-gray-600">
                            @if(auth()->user()->hasRole('super_admin'))
                                Active Centers Distribution
                                @if($selectedBarangay && $selectedBarangay !== '')
                                    - {{ $selectedBarangay }}
                                @else
                                    - All Barangays
                                @endif
                            @else
                                Active Centers Distribution - {{ auth()->user()->barangay }}
                            @endif
                        </p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl shadow-lg">
                        <i class="fas fa-chart-pie text-white text-xl"></i>
                    </div>
                </div>
                <div class="h-64">
                    <canvas id="disasterTypeChart"></canvas>
                </div>
            </div>

            <!-- Barangay Statistics Chart -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-bold text-gray-900">
                            @if(auth()->user()->hasRole('super_admin'))
                                @if($selectedBarangay && $selectedBarangay !== '')
                                    {{ $selectedBarangay }} Overview
                                @else
                                    System Overview
                                @endif
                            @else
                                {{ auth()->user()->barangay }} Overview
                            @endif
                        </h3>
                        <p class="text-sm text-gray-600">
                            @if(auth()->user()->hasRole('super_admin'))
                                @if($selectedBarangay && $selectedBarangay !== '')
                                    Overall Statistics
                                @else
                                    Overall System Statistics
                                @endif
                            @else
                                Overall Statistics
                            @endif
                        </p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl shadow-lg">
                        <i class="fas fa-chart-bar text-white text-xl"></i>
                    </div>
                </div>
                <div class="h-64">
                    <canvas id="barangayStatsChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Map Section -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-4 mb-8">
            <!-- Map Controls Header -->
            <div class="flex justify-between items-center mb-4">
                <div class="flex items-center gap-3">
                    <div class="p-2 bg-gradient-to-br from-sky-500 to-blue-600 rounded-lg shadow-sm">
                        <i class="fas fa-map-marked-alt text-white text-lg"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-gray-900">Evacuation Centers Map</h2>
                        <p class="text-xs text-gray-600 mt-1">View all center locations</p>
                    </div>
                </div>
            </div>

            <div class="relative w-full" style="height: 500px;">
                <!-- Legend Overlay for Dashboard Map -->
                <div class="absolute top-4 left-4 z-20 bg-white/95 backdrop-blur-sm rounded-lg shadow-lg border border-sky-200 p-3" style="max-width: 200px;">
                    <h5 class="text-xs font-bold text-gray-800 mb-2 text-center">Legend</h5>
                    <div class="grid grid-cols-1 gap-y-2">
                        <div class="flex items-center gap-2">
                            <img src="/image/pins/forTyphoon.png" alt="Typhoon" class="w-4 h-4 object-contain">
                            <span class="text-xs text-gray-700">Typhoon</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <img src="/image/pins/forFlood.png" alt="Flood" class="w-4 h-4 object-contain">
                            <span class="text-xs text-gray-700">Flood</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <img src="/image/pins/forFire.png" alt="Fire" class="w-4 h-4 object-contain">
                            <span class="text-xs text-gray-700">Fire</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <img src="/image/pins/forEarthquake.png" alt="Earthquake" class="w-4 h-4 object-contain">
                            <span class="text-xs text-gray-700">Earthquake</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <img src="/image/pins/forLandslide.png" alt="Landslide" class="w-4 h-4 object-contain">
                            <span class="text-xs text-gray-700">Landslide</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <img src="/image/pins/forOthers.png" alt="Others" class="w-4 h-4 object-contain">
                            <span class="text-xs text-gray-700">Others</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <img src="/image/pins/forMultiple.png" alt="Multi-disaster" class="w-4 h-4 object-contain">
                            <span class="text-xs text-gray-700">Multi-disaster</span>
                        </div>
                    </div>
                </div>
                <div id="map" class="w-full h-full rounded-xl overflow-hidden shadow-lg relative"></div>
            </div>
        </div>

        <!-- Recent Alerts Section -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg">
                        <i class="fas fa-bell text-white text-2xl"></i>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">Recent Alerts</h2>
                        <p class="text-gray-600 mt-1">Latest emergency notifications</p>
                    </div>
                </div>
                <a href="{{ route('components.notification.index') }}" 
                   class="inline-flex items-center gap-2 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg transition-all duration-200 transform hover:scale-105">
                    View All
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
            @if(count($recentAlerts) > 0)
                <div class="space-y-4">
                    @foreach($recentAlerts as $alert)
                        <div class="group p-4 rounded-xl border-1/2 transition-all duration-200 hover:shadow-md
                            @if($alert->type == 'Flood') bg-blue-50 border-blue-500 hover:bg-blue-100
                            @elseif($alert->type == 'Typhoon') bg-red-50 border-red-500 hover:bg-red-100
                            @elseif($alert->type == 'Earthquake') bg-yellow-50 border-yellow-500 hover:bg-yellow-100
                            @else bg-gray-50 border-gray-500 hover:bg-gray-100 @endif">
                            <div class="flex items-start gap-3">
                                <div class="flex-shrink-0 text-2xl">
                                    @if($alert->type == 'Flood') 💧
                                    @elseif($alert->type == 'Typhoon') 🌀
                                    @elseif($alert->type == 'Earthquake') ⛰️
                                    @else ℹ️ @endif
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="font-semibold text-gray-900 text-sm leading-tight">{{ $alert->message }}</p>
                                    @if($alert->barangay || ($alert->user && $alert->user->city))
                                    <div class="flex items-center gap-1 mt-1">
                                        <i class="fas fa-map-marker-alt text-gray-400 text-xs"></i>
                                        <span class="text-xs text-gray-600">
                                            @if(auth()->user()->hasRole('system_admin'))
                                                @if($alert->barangay && $alert->user && $alert->user->city_name)
                                                    {{ $alert->barangay }}, {{ $alert->user->city_name }}
                                                @elseif($alert->barangay)
                                                    {{ $alert->barangay }}
                                                @elseif($alert->user && $alert->user->city_name)
                                                    {{ $alert->user->city_name }}
                                                @endif
                                            @else
                                                {{ $alert->barangay }}
                                            @endif
                                        </span>
                                    </div>
                                    @endif
                                    <div class="flex items-center justify-between mt-2">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                            @if($alert->type == 'Flood') bg-blue-100 text-blue-800
                                            @elseif($alert->type == 'Typhoon') bg-red-100 text-red-800
                                            @elseif($alert->type == 'Earthquake') bg-yellow-100 text-yellow-800
                                            @else bg-gray-100 text-gray-800 @endif">
                                            {{ $alert->type }}
                                        </span>
                                        <span class="text-xs text-gray-500">{{ $alert->time_ago }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <div class="text-gray-400 text-4xl mb-3">📭</div>
                    <p class="text-gray-500 font-medium">No recent alerts</p>
                    <p class="text-gray-400 text-sm mt-1">All quiet for now</p>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@section('scripts')
<!-- Add Leaflet JS -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
    integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
    crossorigin=""></script>

<!-- Add external map.js for consistency with map blade -->
<script src="{{ asset('js/map.js') }}"></script>

<script>
window.isDashboardMap = true;
document.addEventListener('DOMContentLoaded', function () {
    // Chart.js Configuration
    Chart.defaults.font.family = 'Poppins, sans-serif';
    Chart.defaults.color = '#6b7280';
    
    // Monthly Trends Chart
    const monthlyCtx = document.getElementById('monthlyTrendsChart').getContext('2d');
    const monthlyData = @json($monthlyData);
    
    if (monthlyData.alerts.some(value => value > 0) || monthlyData.centers.some(value => value > 0)) {
        new Chart(monthlyCtx, {
            type: 'line',
            data: {
                labels: monthlyData.months,
                datasets: [
                    {
                        label: 'Emergency Alerts',
                        data: monthlyData.alerts,
                        borderColor: '#ef4444',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Active Centers',
                        data: monthlyData.centers,
                        borderColor: '#22c55e',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 5,
                        ticks: {
                            stepSize: 1,
                            callback: function(value) {
                                return Number.isInteger(value) ? value : '';
                            }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    } else {
        // Show no data message
        monthlyCtx.canvas.style.display = 'none';
        document.getElementById('monthlyTrendsChart').parentNode.innerHTML = `
            <div class="flex items-center justify-center h-full">
                <div class="text-center">
                    <div class="text-gray-400 text-4xl mb-3">📊</div>
                    <p class="text-gray-500 font-medium">No data available</p>
                    <p class="text-gray-400 text-sm mt-1">No trends data for this period</p>
                </div>
            </div>
        `;
    }

    // Disaster Type Distribution Chart
    const disasterCtx = document.getElementById('disasterTypeChart').getContext('2d');
    const disasterData = @json($disasterTypeData);
    
    if (disasterData.data.length > 0 && disasterData.data.some(value => value > 0)) {
        new Chart(disasterCtx, {
            type: 'doughnut',
            data: {
                labels: disasterData.labels,
                datasets: [{
                    data: disasterData.data,
                    backgroundColor: disasterData.colors,
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    }
                }
            }
        });
    } else {
        // Show no data message
        disasterCtx.canvas.style.display = 'none';
        document.getElementById('disasterTypeChart').parentNode.innerHTML = `
            <div class="flex items-center justify-center h-full">
                <div class="text-center">
                    <div class="text-gray-400 text-4xl mb-3">📊</div>
                    <p class="text-gray-500 font-medium">No data available</p>
                    <p class="text-gray-400 text-sm mt-1">No disaster type data</p>
                </div>
            </div>
        `;
    }

    // Barangay Statistics Chart
    const barangayCtx = document.getElementById('barangayStatsChart').getContext('2d');
    const barangayData = @json($barangayStatsData);
    
    if (barangayData.length > 0 && barangayData[0]) {
        const data = barangayData[0];

        // Configure chart data based on user role
        @if(auth()->user()->hasRole('admin'))
        // BDRRMC: Show Total Centers, Total Alerts, BDRRMC Users
        const chartLabels = ['Total Centers', 'Total Alerts', 'BDRRMC Users'];
        const chartData = [data.totalCenters, data.totalNotifications, {{ $bdrrmoUsers }}];
        const chartBackgroundColors = [
            'rgba(34, 197, 94, 0.8)',  // Green for centers
            'rgba(239, 68, 68, 0.8)',   // Red for alerts
            'rgba(59, 130, 246, 0.8)'   // Blue for BDRRMC users
        ];
        const chartBorderColors = ['#22c55e', '#ef4444', '#3b82f6'];
        @elseif(auth()->user()->hasRole('super_admin'))
        // CDRRMC: Show Total Centers, Total Alerts, BDRRMC Users, CDRRMC Users
        const chartLabels = ['Total Centers', 'Total Alerts', 'BDRRMC Users', 'CDRRMC Users'];
        const chartData = [data.totalCenters, data.totalNotifications, {{ $bdrrmoUsers }}, {{ $cdrrmoUsers }}];
        const chartBackgroundColors = [
            'rgba(34, 197, 94, 0.8)',  // Green for centers
            'rgba(239, 68, 68, 0.8)',   // Red for alerts
            'rgba(59, 130, 246, 0.8)',  // Blue for BDRRMC users
            'rgba(147, 51, 234, 0.8)'   // Purple for CDRRMC users
        ];
        const chartBorderColors = ['#22c55e', '#ef4444', '#3b82f6', '#9333ea'];
        @elseif(auth()->user()->hasRole('system_admin'))
        // System Admin: Show Total Centers, Total Alerts, CDRRMC Users, BDRRMC Users
        const chartLabels = ['Total Centers', 'Total Alerts', 'CDRRMC Users', 'BDRRMC Users'];
        const chartData = [data.totalCenters, data.totalNotifications, {{ $cdrrmoUsers }}, {{ $bdrrmoUsers }}];
        const chartBackgroundColors = [
            'rgba(34, 197, 94, 0.8)',  // Green for centers
            'rgba(239, 68, 68, 0.8)',   // Red for alerts
            'rgba(147, 51, 234, 0.8)',  // Purple for CDRRMC users
            'rgba(59, 130, 246, 0.8)'   // Blue for BDRRMC users
        ];
        const chartBorderColors = ['#22c55e', '#ef4444', '#9333ea', '#3b82f6'];
        @endif

        new Chart(barangayCtx, {
            type: 'bar',
            data: {
                labels: chartLabels,
                datasets: [
                    {
                        label: 'Count',
                        data: chartData,
                        backgroundColor: chartBackgroundColors,
                        borderColor: chartBorderColors,
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false // Remove legend since we're showing overall stats
                    },
                    tooltip: {
                        callbacks: {
                            afterBody: function(context) {
                                const dataIndex = context[0].dataIndex;
                                const data = barangayData[0];
                                const additionalInfo = [
                                    `Total Centers: ${data.totalCenters}`,
                                    `Active Barangay Users: ${data.activeBarangayUsers}`,
                                    `Active Mobile Users: ${data.activeMobileUsers}`
                                ];
                                return additionalInfo;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 5,
                        ticks: {
                            stepSize: 1,
                            callback: function(value) {
                                return Number.isInteger(value) ? value : '';
                            }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    } else {
        // Show no data message
        barangayCtx.canvas.style.display = 'none';
        document.getElementById('barangayStatsChart').parentNode.innerHTML = `
            <div class="flex items-center justify-center h-full">
                <div class="text-center">
                    <div class="text-gray-400 text-4xl mb-3">📊</div>
                    <p class="text-gray-500 font-medium">No data available</p>
                    <p class="text-gray-400 text-sm mt-1">No statistics data</p>
                </div>
            </div>
        `;
    }

    // Initialize map with data from Laravel (using same approach as map blade)
    const centers = @json($evacuationCenters);
    const isAdmin = {{ auth()->user()->hasRole('super_admin') || auth()->user()->hasRole('admin') ? 'true' : 'false' }};

    // Initialize map functionality (using external map.js)
    if (typeof initializeMap === 'function') {
        initializeMap(centers, isAdmin);
    }

    // Initialize location search functionality
    if (typeof initializeLocationSearch === 'function') {
        initializeLocationSearch();
    }

    // Initialize barangay filter functionality
    if (typeof initializeBarangayFilter === 'function') {
        initializeBarangayFilter();
    }

    // Dashboard-specific functionality
    @if(auth()->user()->hasRole('super_admin'))
    // Admin registration form functionality
    const registrationForm = document.getElementById('adminRegistrationForm');
    const roleInfo = document.getElementById('roleInfo');
    const adminRegistrationForm = document.getElementById('adminRegistrationForm');

    if (registrationForm && roleInfo && adminRegistrationForm) {
        // Handle role selection
        const roleSelect = document.getElementById('role');
        roleSelect.addEventListener('change', function() {
            if (this.value === 'officer') {
                roleInfo.classList.remove('hidden');
                const roleDescription = document.getElementById('roleDescription');
                roleDescription.textContent = 'BDRRMO Officer: Barangay disaster management operations with access to barangay-specific features.';
            } else {
                roleInfo.classList.add('hidden');
            }
        });

        // Handle form submission
        adminRegistrationForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Registering...';
            submitBtn.disabled = true;

            fetch('/admin-invitations/register-admin', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    Swal.fire({
                        icon: 'success',
                        title: 'Barangay User Registered Successfully!',
                        text: data.message,
                        confirmButtonColor: '#10b981'
                    }).then(() => {
                        // Reset form and hide it
                        adminRegistrationForm.reset();
                        registrationForm.classList.add('hidden');
                        roleInfo.classList.add('hidden');
                    });
                } else {
                    // Show error message
                    Swal.fire({
                        icon: 'error',
                        title: 'Registration Failed',
                        text: data.message || 'An error occurred during registration.',
                        confirmButtonColor: '#ef4444'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Registration Failed',
                    text: 'An unexpected error occurred. Please try again.',
                    confirmButtonColor: '#ef4444'
                });
            })
            .finally(() => {
                // Reset button state
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    }
    @endif

    // City-Barangay filter interaction for System Admin
    @if(auth()->user()->hasRole('system_admin'))
    const citySelect = document.getElementById('city');
    const barangaySelect = document.getElementById('barangay');

    if (citySelect && barangaySelect) {
        citySelect.addEventListener('change', function() {
            loadBarangaysForDashboard();
        });
    }

    async function loadBarangaysForDashboard() {
        // Clear barangay options
        barangaySelect.innerHTML = '<option value="">All Barangays</option>';
        barangaySelect.disabled = true;

        if (!citySelect.value) {
            return;
        }

        try {
            const response = await fetch(`{{ route('system-admin.get-barangays-by-city') }}?city=${encodeURIComponent(citySelect.value)}`, {
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Accept': 'application/json'
                }
            });

            const result = await response.json();

            if (result.barangays && Array.isArray(result.barangays)) {
                result.barangays.forEach(barangay => {
                    const option = document.createElement('option');
                    option.value = barangay;
                    option.textContent = barangay;
                    barangaySelect.appendChild(option);
                });
                barangaySelect.disabled = false;
            }
        } catch (error) {
            console.error('Failed to load barangays:', error);
        }
    }
    @endif
});
</script>

<style>
    body {
        font-family: 'Poppins', sans-serif;
    }
    #map {
        width: 100% !important;
        height: 100% !important;
        z-index: 1;
        position: relative;
        overflow: hidden;
    }
    .leaflet-container {
        position: relative !important;
        outline: none;
        overflow: hidden !important;
        background: white !important;
        width: 100% !important;
        height: 100% !important;
    }

    .leaflet-popup-content {
        margin: 0;
        padding: 1rem;
        font-family: 'Poppins', sans-serif;
    }
    .leaflet-popup-content-wrapper {
        border-radius: 1rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border: 1px solid #e0f2fe;
        background: white;
    }
    .leaflet-popup-tip {
        background: white;
    }
    .suggestion-item {
        transition: background-color 0.2s;
    }

    .suggestion-item:hover {
        background-color: #f0f9ff;
    }

    .selected-marker {
        animation: pulse 1.5s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.2);
            opacity: 0.8;
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    /* Legend Overlay Styling */
    .map-section-container .legend-overlay {
        position: absolute;
        top: 2rem;
        right: 3rem;
        z-index: 20;
        background: rgba(255,255,255,0.95);
        border-radius: 1rem;
        box-shadow: 0 4px 12px rgba(14, 165, 233, 0.15);
        border: 1px solid #bae6fd;
        padding: 1.25rem 1.5rem;
        min-width: 220px;
        display: flex;
        flex-direction: column;
        align-items: center;
    }
</style>
@endsection



