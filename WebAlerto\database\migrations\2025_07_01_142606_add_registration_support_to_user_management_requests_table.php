<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_management_requests', function (Blueprint $table) {
            // Make target_user_id nullable for registration requests
            $table->foreignId('target_user_id')->nullable()->change();

            // Update action_type enum to include registration
            $table->enum('action_type', ['deactivate', 'delete', 'registration'])->change();

            // Add fields for new user registration data
            $table->string('requested_first_name')->nullable();
            $table->string('requested_last_name')->nullable();
            $table->string('requested_email')->nullable();
            $table->string('requested_position')->nullable();
            $table->string('requested_barangay')->nullable();

            // Add index for registration requests
            $table->index(['action_type', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_management_requests', function (Blueprint $table) {
            // Revert target_user_id to not nullable
            $table->foreignId('target_user_id')->nullable(false)->change();

            // Revert action_type enum
            $table->enum('action_type', ['deactivate', 'delete'])->change();

            // Drop the new columns
            $table->dropColumn([
                'requested_first_name',
                'requested_last_name',
                'requested_email',
                'requested_position',
                'requested_barangay'
            ]);

            // Drop the index
            $table->dropIndex(['action_type', 'status']);
        });
    }
};
