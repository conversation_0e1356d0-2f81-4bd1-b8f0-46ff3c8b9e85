<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>ALERTO</title>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Inter Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        sans: ['Inter', 'ui-sans-serif', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        'sky': {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        }
                    }
                }
            }
        }
    </script>

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" crossorigin="anonymous" />

    <!-- Leaflet and its plugins -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet-control-geocoder/dist/Control.Geocoder.css" />
    
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.js"></script>
    <script src="https://unpkg.com/leaflet-control-geocoder/dist/Control.Geocoder.js"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <style type="text/tailwindcss">
        .sidebar-link {
            @apply flex items-center gap-2 md:gap-3 text-gray-700 hover:bg-sky-50 p-2 md:p-3 transition-all duration-200 text-sm md:text-base;
        }
        .sidebar-link.active {
            @apply bg-sky-50 font-semibold text-sky-600;
        }
        .sidebar-link i {
            @apply w-6 text-center text-base md:text-lg;
        }
        /* Responsive Sidebar Styles */
        .sidebar {
            @apply fixed top-0 left-0 h-screen w-64 bg-white shadow-lg z-50 flex flex-col transition-transform duration-300;
        }
        .sidebar-closed {
            @apply -translate-x-full;
        }
        .sidebar-open {
            @apply translate-x-0;
        }
        @media (min-width: 768px) {
            .sidebar {
                @apply translate-x-0;
            }
            .sidebar-closed {
                @apply -translate-x-full;
            }
            .sidebar-open {
                @apply translate-x-0;
            }
        }
        .main-content {
            @apply transition-all duration-300;
        }
        .main-content-shift {
            @apply md:ml-64;
        }
        .main-content-full {
            @apply ml-0;
        }
        /* Fallback for map container height */
        #map { min-height: 200px; min-width: 100%; }
        /* Responsive tables */
        table {
            @apply min-w-full text-xs md:text-sm;
        }
        .table-responsive {
            @apply block w-full overflow-x-auto;
        }
        /* Responsive forms */
        input, select, textarea, button {
            @apply text-sm md:text-base;
        }
        /* Responsive containers */
        .container-responsive {
            @apply w-full max-w-full px-2 md:px-6 mx-auto;
        }
        /* Responsive map container */
        .map-responsive, #map {
            @apply w-full h-[250px] md:h-[400px] overflow-hidden;
            min-width: 0;
        }
        /* Responsive cards */
        .card, .container, .map {
            @apply w-full p-2 md:p-4 mb-2 md:mb-4 shadow bg-white;
        }
        /* Responsive grid helpers */
        .grid-responsive {
            @apply grid grid-cols-1 md:grid-cols-2 gap-2 md:gap-4;
        }
        /* Responsive modal */
        .modal {
            @apply w-full max-w-lg mx-2 md:mx-auto;
        }
        /* Edge-to-edge utility for mobile */
        .edge-to-edge-mobile {
            @apply w-full max-w-none shadow-none px-0 mx-0;
        }
        @media (min-width: 768px) {
            .edge-to-edge-mobile {
                @apply shadow px-4 mx-auto;
            }
        }
    </style>

    @yield('head')
</head>
<body class="bg-gradient-to-br from-sky-50 to-sky-100" x-data="{ sidebarOpen: window.innerWidth >= 768 }" @resize.window="sidebarOpen = window.innerWidth >= 768">
    <!-- Header: Full width, fixed at top -->
    <header class="bg-gradient-to-r from-sky-500 to-sky-600 shadow-lg fixed top-0 left-0 right-0 z-40 w-full flex items-center text-sm md:text-base px-0" style="height: 80px;">
        <div class="flex justify-between items-center w-full py-2 md:py-3 gap-2 md:gap-4 pr-2 md:pr-6">
            <div class="flex items-center gap-0">
                <div class="flex items-center gap-0 cursor-pointer" @click="sidebarOpen = !sidebarOpen">
                    <img src="{{ asset('image/ALERTO Logo.png') }}" alt="ALERTO Logo" class="h-20 w-20 md:h-24 md:w-24 object-contain hover:opacity-80 transition-opacity mt-4 m-0 p-0">
                    <div class="flex flex-col justify-start m-0 p-0">
                        @if(auth()->check())
                            @if(auth()->user()->hasRole('system_admin'))
                                <h1 class="text-base md:text-xl font-bold text-white leading-tight">Technical Administrator Dashboard</h1>
                                <p class="text-xs text-sky-100 leading-tight">System configurations, user management, and technical maintenance</p>
                            @elseif(auth()->user()->hasRole('super_admin'))
                                <h1 class="text-base md:text-xl font-bold text-white leading-tight">CDRRMC Dashboard</h1>
                                <p class="text-xs text-sky-100 leading-tight">City-wide disaster monitoring and oversight</p>
                            @elseif(auth()->user()->hasRole('admin'))
                                <h1 class="text-base md:text-xl font-bold text-white leading-tight">BDRRMC Dashboard</h1>
                                <p class="text-xs text-sky-100 leading-tight">Barangay-specific disaster management</p>
                            @else
                                <h1 class="text-base md:text-xl font-bold text-white leading-tight">Alerto Admin Dashboard</h1>
                                <p class="text-xs text-sky-100 leading-tight">Local emergency management</p>
                            @endif
                        @else
                            <h1 class="text-base md:text-xl font-bold text-white leading-tight">Alerto Dashboard</h1>
                            <p class="text-xs text-sky-100 leading-tight">Emergency Response System</p>
                        @endif
                    </div>
                </div>
            </div>
            <div class="flex items-center gap-2 md:gap-4 relative">
                @auth
                <div class="hidden sm:block text-xs md:text-sm text-white">
                    <span class="font-medium">{{ Auth::user()->full_name }}</span>
                    <span class="block text-xs text-sky-100">
                        {{ Auth::user()->position }}
                        @if(Auth::user()->city && Auth::user()->barangay)
                            - {{ Auth::user()->barangay }}, {{ Auth::user()->city_name }}
                        @elseif(Auth::user()->city)
                            - {{ Auth::user()->city_name }}
                        @elseif(Auth::user()->barangay)
                            - {{ Auth::user()->barangay }}
                        @else
                            - Province-wide Access
                        @endif
                    </span>
                </div>
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center gap-2 focus:outline-none">
                        @php
                            $userFullName = trim((Auth::user()->title ? Auth::user()->title . ' ' : '') . Auth::user()->first_name . (Auth::user()->middle_name ? ' ' . Auth::user()->middle_name : '') . ' ' . Auth::user()->last_name . (Auth::user()->suffix ? ', ' . Auth::user()->suffix : ''));
                        @endphp
                        <img src="https://ui-avatars.com/api/?name={{ urlencode($userFullName) }}&background=F87171&color=fff"
                            alt="{{ $userFullName }}"
                            class="h-8 w-8 md:h-9 md:w-9 rounded-full border-2 border-sky-100 shadow-sm cursor-pointer hover:border-sky-200 transition bg-white">
                    </button>
                    <!-- Dropdown Menu -->
                    <div x-show="open" 
                         @click.away="open = false"
                         x-transition:enter="transition ease-out duration-100"
                         x-transition:enter-start="transform opacity-0 scale-95"
                         x-transition:enter-end="transform opacity-100 scale-100"
                         x-transition:leave="transition ease-in duration-75"
                         x-transition:leave-start="transform opacity-100 scale-100"
                         x-transition:leave-end="transform opacity-0 scale-95"
                         class="absolute right-2 md:right-0 mt-2 w-56 md:w-72 bg-white rounded-lg shadow-lg py-2 z-50 overflow-x-auto">
                        <!-- User Info -->
                        <div class="px-4 py-3 border-b border-gray-100">
                            <div class="flex items-center gap-2 md:gap-3">
                                <img src="https://ui-avatars.com/api/?name={{ urlencode($userFullName) }}&background=F87171&color=fff"
                                    alt="{{ $userFullName }}"
                                    class="h-10 w-10 md:h-12 md:w-12 rounded-full border-2 border-sky-200 bg-white">
                                <div class="flex-1">
                                    <h3 class="text-xs md:text-sm font-semibold text-gray-900">{{ Auth::user()->full_name }}</h3>
                                    <p class="text-xs text-gray-500">{{ Auth::user()->position }}</p>
                                    <span class="inline-block px-2 py-1 text-xs rounded-full font-medium
                                        @if(Auth::user()->hasRole('system_admin'))
                                            bg-purple-100 text-purple-800
                                        @elseif(Auth::user()->hasRole('super_admin'))
                                            bg-blue-100 text-blue-800
                                        @elseif(Auth::user()->hasRole('admin'))
                                            bg-green-100 text-green-800
                                        @else
                                            bg-gray-100 text-gray-800
                                        @endif
                                    ">
                                        {{ Auth::user()->getRoleDisplayName() }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <!-- User Details -->
                        <div class="px-4 py-3 space-y-2">
                            <div class="flex items-center gap-2 text-xs md:text-sm text-gray-600">
                                <i class="fas fa-envelope text-gray-400 w-5"></i>
                                <span class="truncate">{{ Auth::user()->email }}</span>
                            </div>

                            @if(Auth::user()->hasRole('system_admin'))
                                <div class="flex items-center gap-2 text-xs md:text-sm text-gray-600">
                                    <i class="fas fa-globe text-gray-400 w-5"></i>
                                    <span>Province-wide Access</span>
                                </div>
                                <div class="flex items-center gap-2 text-xs md:text-sm text-gray-600">
                                    <i class="fas fa-users-cog text-gray-400 w-5"></i>
                                    <span>All Cities & Municipalities</span>
                                </div>
                            @elseif(Auth::user()->hasRole('super_admin'))
                                @if(Auth::user()->city)
                                    <div class="flex items-center gap-2 text-xs md:text-sm text-gray-600">
                                        <i class="fas fa-city text-gray-400 w-5"></i>
                                        <span>{{ Auth::user()->city }}</span>
                                    </div>
                                @endif
                                <div class="flex items-center gap-2 text-xs md:text-sm text-gray-600">
                                    <i class="fas fa-eye text-gray-400 w-5"></i>
                                    <span>City-wide Monitoring</span>
                                </div>
                            @elseif(Auth::user()->hasRole('admin'))
                                @if(Auth::user()->city && Auth::user()->barangay)
                                    <div class="flex items-center gap-2 text-xs md:text-sm text-gray-600">
                                        <i class="fas fa-map-marker-alt text-gray-400 w-5"></i>
                                        <span>{{ Auth::user()->barangay }}, {{ Auth::user()->city }}</span>
                                    </div>
                                @elseif(Auth::user()->barangay)
                                    <div class="flex items-center gap-2 text-xs md:text-sm text-gray-600">
                                        <i class="fas fa-map-marker-alt text-gray-400 w-5"></i>
                                        <span>{{ Auth::user()->barangay }} Barangay</span>
                                    </div>
                                @endif
                                <div class="flex items-center gap-2 text-xs md:text-sm text-gray-600">
                                    <i class="fas fa-home text-gray-400 w-5"></i>
                                    <span>Barangay-level Access</span>
                                </div>
                            @endif
                        </div>
                        <!-- Actions -->
                        <div class="border-t border-gray-100">
                            <a href="{{ route('profile.edit') }}" class="flex items-center gap-2 px-4 py-2 text-xs md:text-sm text-gray-700 hover:bg-sky-50">
                                <i class="fas fa-user-edit text-gray-400"></i>
                                <span>Edit Profile</span>
                            </a>
                            <a href="{{ route('password.change.form') }}" class="flex items-center gap-2 px-4 py-2 text-xs md:text-sm text-gray-700 hover:bg-sky-50">
                                <i class="fas fa-key text-gray-400"></i>
                                <span>Change Password</span>
                            </a>
                            <a href="{{ route('logout') }}" class="flex items-center gap-2 px-4 py-2 text-xs md:text-sm text-red-600 hover:bg-red-50">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>Logout</span>
                            </a>
                        </div>
                    </div>
                </div>
                @else
                <div class="hidden sm:block text-xs md:text-sm text-white">
                    Welcome, Guest
                </div>
                @endauth
            </div>
        </div>
    </header>
    <!-- Flex container for sidebar and main content, with top padding for header -->
    <div class="flex pt-20">
        <!-- Sidebar: starts below header -->
        <aside class="sidebar h-[calc(100vh-80px)] mt-0 flex flex-col" style="top: 80px;" :class="[sidebarOpen ? 'sidebar-open' : 'sidebar-closed']" @keydown.window.escape="sidebarOpen = false">
            <!-- Navigation -->
            <nav class="flex-1 p-4 space-y-2 overflow-y-auto">
                <a href="{{ route('components.dashboard') }}" 
                   class="sidebar-link {{ Request::is('dashboard') ? 'active' : '' }}">
                    <i class="fa-solid fa-house"></i>
                    <span class="whitespace-nowrap">Dashboard</span>
                </a>
                <a href="{{ route('components.evacuation_management.evacuation-dashboard') }}" 
                   class="sidebar-link {{ Request::is('evacuation*') ? 'active' : '' }}">
                    <i class="fa-solid fa-building"></i>
                    <span class="whitespace-nowrap">Evacuation Centers</span>
                </a>
                <a href="{{ route('map') }}"
                   class="sidebar-link {{ Request::is('map*') ? 'active' : '' }}">
                    <i class="fa-solid fa-map"></i>
                    <span class="whitespace-nowrap">Map</span>
                </a>
                @if(auth()->user()->hasRole('system_admin'))
                <a href="{{ route('system-admin.user-management') }}"
                   class="sidebar-link {{ Request::is('system-admin/user-management*') ? 'active' : '' }}">
                    <i class="fa-solid fa-users-cog"></i>
                    <span class="whitespace-nowrap">User Management</span>
                </a>
                @else
                <a href="{{ route('components.user-management') }}"
                   class="sidebar-link {{ Request::is('user-management*') ? 'active' : '' }}">
                    <i class="fa-solid fa-users"></i>
                    <span class="whitespace-nowrap">User Management</span>
                </a>
                @endif

                @if(auth()->user()->isChairman())
                    <a href="{{ route('chairman.requests') }}"
                       class="sidebar-link {{ Request::is('user-management/requests*') ? 'active' : '' }}">
                        <i class="fa-solid fa-clipboard-list"></i>
                        <span class="whitespace-nowrap">Requests</span>
                        <span id="chairmanRequestsBadge" class="hidden ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center"></span>
                    </a>
                @endif
                <a href="{{ route('components.notification.index') }}"
                   class="sidebar-link {{ Request::is('notification*') ? 'active' : '' }}">
                    <i class="fa-solid fa-envelope-open-text"></i>
                    <span class="whitespace-nowrap">Notification</span>
                </a>

                @if(auth()->check() && auth()->user()->hasRole('system_admin'))
                <div class="pt-4 mt-4 border-t border-gray-200 space-y-2">
                    <h6 class="text-xs font-semibold text-gray-500 uppercase whitespace-nowrap">Technical Admin</h6>

                    <a href="{{ route('system-admin.dashboard') }}"
                       class="sidebar-link {{ Request::is('system-admin/dashboard*') ? 'active' : '' }}">
                        <i class="fa-solid fa-cogs"></i>
                        <span class="whitespace-nowrap">Technical Dashboard</span>
                    </a>
                    <a href="{{ route('system-admin.mobile-users') }}"
                       class="sidebar-link {{ Request::is('system-admin/mobile-users*') ? 'active' : '' }}">
                        <i class="fa-solid fa-mobile-alt"></i>
                        <span class="whitespace-nowrap">Mobile Users</span>
                    </a>

                    <a href="{{ route('system-admin.system-logs') }}"
                       class="sidebar-link {{ Request::is('system-admin/system-logs*') ? 'active' : '' }}">
                        <i class="fa-solid fa-file-alt"></i>
                        <span class="whitespace-nowrap">System Logs</span>
                    </a>
                </div>
                @endif

                @if(auth()->check() && auth()->user()->hasRole('super_admin'))
                <div class="pt-4 mt-4 border-t border-gray-200 space-y-2">
                    <h6 class="text-xs font-semibold text-gray-500 uppercase whitespace-nowrap">CDRRMC</h6>

                    <a href="{{ route('superadmin.dashboard') }}"
                       class="sidebar-link {{ Request::is('superadmin/dashboard*') ? 'active' : '' }}">
                        <i class="fa-solid fa-city"></i>
                        <span class="whitespace-nowrap">City Overview</span>
                    </a>
                    <a href="{{ route('superadmin.admin-users') }}"
                       class="sidebar-link {{ Request::is('superadmin/admin-users*') ? 'active' : '' }}">
                        <i class="fa-solid fa-user-shield"></i>
                        <span class="whitespace-nowrap">BDRRMC Users</span>
                    </a>
                </div>
                @endif
            </nav>
            <!-- Footer -->
            <div class="flex-none p-4 border-t bg-sky-500 mt-auto sticky bottom-0">
                <div class="text-center">
                    <p class="text-xl  text-white">ALERTO</p>
                    <p class="text-sm text-white">Copyright 2025</p>
                </div>
            </div>
        </aside>
        <!-- Main Content -->
        <div :class="[sidebarOpen && window.innerWidth >= 768 ? 'ml-64' : '', 'flex-1 transition-all duration-300']">
            <main class="pt-4 md:pt-8 px-0 md:px-4">
                @yield('content')
            </main>
        </div>
    </div>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobileSidebarOverlay');
            sidebar.classList.toggle('-translate-x-full');
            overlay.classList.toggle('hidden');
        }

        // Notification badge updates
        function updateNotificationBadges() {
            @if(auth()->user()->isChairman())
                // Update Chairman requests badge
                fetch('{{ route("chairman.requests.notification-count") }}')
                    .then(response => response.json())
                    .then(data => {
                        const badge = document.getElementById('chairmanRequestsBadge');
                        if (data.count > 0) {
                            badge.textContent = data.count;
                            badge.classList.remove('hidden');
                        } else {
                            badge.classList.add('hidden');
                        }
                    })
                    .catch(error => console.error('Error fetching chairman notification count:', error));
            @endif

            @if(auth()->user()->hasRole('super_admin'))
                // SuperAdmin requests badge functionality removed
                // const badge = document.getElementById('superadminRequestsBadge');
                // badge.classList.add('hidden');
            @endif
        }

        // Update badges on page load and every 30 seconds
        document.addEventListener('DOMContentLoaded', function() {
            updateNotificationBadges();
            setInterval(updateNotificationBadges, 30000); // Update every 30 seconds
        });
    </script>
    @yield('scripts')
    @stack('scripts')
    @auth
    <script>
    document.addEventListener('DOMContentLoaded', function () {
        // Check for incomplete profile (first_name, last_name, or position missing)
        @if (empty(Auth::user()->first_name) || empty(Auth::user()->last_name) || empty(Auth::user()->position))
            // Only show the prompt if not already on the profile edit page
            var editPath = '/user-management/{{ Auth::user()->id }}/edit';
            if (window.location.pathname !== editPath) {
                Swal.fire({
                    icon: 'info',
                    title: 'Complete Your Profile',
                    text: 'Please complete your personal information to access all features.',
                    confirmButtonText: 'Update Profile',
                    allowOutsideClick: true,
                    allowEscapeKey: true
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = editPath;
                    }
                });
            }
        @endif
    });
    </script>
    @endauth
    <!-- If the map still does not show, check the browser console for errors and ensure the map container exists and has height. -->
    <!-- Developer note: For best mobile responsiveness, use .container-responsive for main wrappers, .map-responsive for maps, .edge-to-edge-mobile for edge-to-edge map/cards, and Tailwind's responsive classes (e.g. px-2 md:px-4, text-xs md:text-sm, grid-cols-1 md:grid-cols-2, w-full, etc.) in all your Blade components and content. -->
</body>
</html>
