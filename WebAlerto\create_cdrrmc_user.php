<?php

require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Hash;

try {
    // Create or update CDRRMC user for Dalaguete
    $user = User::updateOrCreate(
        ['email' => '<EMAIL>'],
        [
            'first_name' => 'CDRRMC',
            'middle_name' => '',
            'last_name' => 'Dalaguete',
            'password' => Hash::make('password123'),
            'role' => 'super_admin',
            'position' => 'CDRRMC Chairperson',
            'city' => '072222000', // Dalaguete PSGC code
            'barangay' => null, // CDRRMC users don't have specific barangay
            'status' => 'Active',
            'email_verified_at' => now()
        ]
    );

    echo "✅ CDRRMC user created/updated successfully!\n";
    echo "📧 Email: " . $user->email . "\n";
    echo "🔑 Password: password123\n";
    echo "🏛️ Role: " . $user->role . "\n";
    echo "🏙️ City: " . $user->city . " (Dalaguete)\n";
    echo "🆔 User ID: " . $user->id . "\n";

} catch (\Exception $e) {
    echo "❌ Error creating CDRRMC user: " . $e->getMessage() . "\n";
}
