.directions-panel {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  max-height: 50vh;
  overflow-y: auto;
  z-index: 1000;
}

.directions-header {
  padding: 10px 0;
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 1001;
}

.directions-list {
  max-height: calc(50vh - 60px);
  overflow-y: auto;
}

ion-item {
  --padding-start: 16px;
  --inner-padding-end: 16px;
}

ion-icon {
  font-size: 24px;
}

h2 {
  font-weight: 600;
  margin: 0;
}

h3 {
  font-size: 14px;
  font-weight: 500;
  margin: 0;
}

p {
  font-size: 12px;
  color: var(--ion-color-medium);
  margin: 4px 0 0;
}

ion-note {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 50%;
  background-color: var(--ion-color-light);
  color: var(--ion-color-dark);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  min-height: 24px;
}
