/**
 * Shared map and location handling logic for adding and editing evacuation centers.
 */
document.addEventListener('DOMContentLoaded', function () {
    const mapElement = document.getElementById('map');
    if (!mapElement) return;

    let map;
    let marker;
    const searchInput = document.getElementById('search');
    const searchButton = document.getElementById('searchButton');
    const searchResultsContainer = document.getElementById('searchResults');

    // Get coordinates from user's city or default to Cebu City
    const cityCoords = window.EvacuationCenterData?.cityCoordinates || { lat: 10.3157, lng: 123.8854 };
    const initialLat = document.getElementById('latitude').value || cityCoords.lat;
    const initialLng = document.getElementById('longitude').value || cityCoords.lng;

    // Disaster type to pin mapping
    const disasterTypePins = {
        'Typhoon': '/image/pins/forTyphoon.png',
        'Flood': '/image/pins/forFlood.png',
        'Fire': '/image/pins/forFire.png',
        'Earthquake': '/image/pins/forEarthquake.png',
        'Landslide': '/image/pins/forLandslide.png',
        'Others': '/image/pins/forOthers.png',
        'Multi-disaster': '/image/pins/forMultiple.png'
    };

    // Function to get the correct pin based on selected disaster types
    function getCorrectPin() {
        const disasterCheckboxes = document.querySelectorAll('input[name="disaster_type[]"]:checked');
        const selectedTypes = Array.from(disasterCheckboxes).map(cb => cb.value);

        if (selectedTypes.length === 0) {
            return disasterTypePins['Others'];
        } else if (selectedTypes.length > 1) {
            return disasterTypePins['Multi-disaster'];
        } else {
            const type = selectedTypes[0];
            return disasterTypePins[type] || disasterTypePins['Others'];
        }
    }

    function initializeMap() {
        try {
            map = L.map('map', {
                zoomControl: true,
                maxZoom: 19,
                minZoom: 5,
                scrollWheelZoom: true
            }).setView([initialLat, initialLng], 15);

            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                maxZoom: 19
            }).addTo(map);

            // Use correct pin based on selected disaster types
            const correctPinUrl = getCorrectPin();
            const customIcon = L.icon({
                iconUrl: correctPinUrl,
                iconSize: [32, 32],
                iconAnchor: [16, 32],
                popupAnchor: [0, -32]
            });

            marker = L.marker([initialLat, initialLng], {
                draggable: true,
                icon: customIcon
            }).addTo(map);

            map.on('click', function (e) {
                marker.setLatLng(e.latlng);
                updateLocationDetails(e.latlng.lat, e.latlng.lng);
            });

            marker.on('dragend', function (e) {
                const latlng = e.target.getLatLng();
                updateLocationDetails(latlng.lat, latlng.lng);
            });

            // Initial load if coords exist
            if (document.getElementById('latitude').value) {
                 updateLocationDetails(initialLat, initialLng);
            }

            // Listen for disaster type changes to update pin
            const disasterCheckboxes = document.querySelectorAll('input[name="disaster_type[]"]');
            disasterCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateMarkerPin();
                });
            });

            document.getElementById('mapLoadingIndicator').classList.add('hidden');
        } catch (error) {
            console.error("Map initialization failed:", error);
            document.getElementById('mapLoadingIndicator').innerHTML = '<p class="text-red-500 font-semibold">Map failed to load. Please refresh.</p>';
        }
    }

    // Function to update marker pin when disaster types change
    function updateMarkerPin() {
        if (marker) {
            const correctPinUrl = getCorrectPin();
            const newIcon = L.icon({
                iconUrl: correctPinUrl,
                iconSize: [32, 32],
                iconAnchor: [16, 32],
                popupAnchor: [0, -32]
            });
            marker.setIcon(newIcon);
        }
    }

    function updateLocationDetails(lat, lng) {
        document.getElementById('latitude').value = lat.toFixed(7);
        document.getElementById('longitude').value = lng.toFixed(7);
        document.getElementById('selectedLatitude').textContent = lat.toFixed(7);
        document.getElementById('selectedLongitude').textContent = lng.toFixed(7);
        document.getElementById('selectedAddress').textContent = 'Fetching address...';

        fetch(`/reverse-geocode?lat=${lat}&lng=${lng}`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }
                populateLocationFields(data);
            })
            .catch(error => {
                console.error('Reverse geocoding failed:', error);
                document.getElementById('selectedAddress').textContent = 'Could not determine address.';
            });
    }

    function populateLocationFields(data) {
        // Normalize and clean up the data to prevent duplicates
        const cleanData = {
            building_name: (data.building_name || '').trim(),
            street: (data.street || '').trim(),
            barangay: (data.barangay || '').trim(),
            city: (data.city || '').trim(),
            province: (data.province || '').trim()
        };

        // Helper function to check if a value is a substring of another (case-insensitive)
        const isSubstringOf = (value1, value2) => {
            if (!value1 || !value2) return false;
            const v1 = value1.toLowerCase().trim();
            const v2 = value2.toLowerCase().trim();
            return v1.includes(v2) || v2.includes(v1);
        };

        // Helper function to check if a value is already represented in existing parts
        const isAlreadyRepresented = (value, existingParts) => {
            if (!value) return true;
            return existingParts.some(part => isSubstringOf(value, part));
        };

        // Build address parts intelligently to avoid duplication
        let addressParts = [];

        // Start with the most specific information
        if (cleanData.building_name) {
            addressParts.push(cleanData.building_name);
        }

        // Add street only if it's not already represented in building name
        if (cleanData.street && !isAlreadyRepresented(cleanData.street, addressParts)) {
            addressParts.push(cleanData.street);
        }

        // Add barangay only if it's not already represented
        if (cleanData.barangay && !isAlreadyRepresented(cleanData.barangay, addressParts)) {
            addressParts.push(cleanData.barangay);
        }

        // Add city only if it's not already represented
        if (cleanData.city && !isAlreadyRepresented(cleanData.city, addressParts)) {
            addressParts.push(cleanData.city);
        }

        // Add province only if it's not already represented
        if (cleanData.province && !isAlreadyRepresented(cleanData.province, addressParts)) {
            addressParts.push(cleanData.province);
        }

        // Create a clean, concise address display
        const displayAddress = addressParts.length > 0
            ? addressParts.join(', ')
            : (data.full_address || 'Address not found');

        document.getElementById('selectedAddress').textContent = displayAddress;

        // Set center name if it's empty and a building name was found
        const centerNameInput = document.getElementById('name');
        if (centerNameInput && !centerNameInput.value && data.building_name) {
            centerNameInput.value = data.building_name;
        }

        // Populate hidden fields for submission
        document.getElementById('building_name').value = data.building_name || '';
        document.getElementById('street_name').value = data.street || '';
        document.getElementById('barangay').value = data.barangay || '';
        document.getElementById('city').value = data.city || '';
        document.getElementById('province').value = data.province || '';
        document.getElementById('postal_code').value = data.postal_code || '';
    }

    function handleSearch() {
        const query = searchInput.value;
        if (query.length < 3) return;

        fetch(`/geocode?address=${encodeURIComponent(query)}&lat=${map.getCenter().lat}&lng=${map.getCenter().lng}`)
            .then(response => response.json())
            .then(data => {
                searchResultsContainer.innerHTML = '';
                searchResultsContainer.classList.remove('hidden');
                if (data.features && data.features.length > 0) {
                    data.features.forEach(feature => {
                        const div = document.createElement('div');
                        div.innerHTML = `<div class="p-3 hover:bg-sky-100 cursor-pointer">
                            <p class="font-medium text-gray-800">${feature.text}</p>
                            <p class="text-sm text-gray-500">${feature.place_name}</p>
                         </div>`;
                        div.onclick = () => {
                            const [lng, lat] = feature.center;
                            const selectedText = feature.text || '';
                            const placeName = feature.place_name || '';

                            map.setView([lat, lng], 17);
                            marker.setLatLng([lat, lng]);

                            // Use the search result data directly to avoid duplication
                            const addressComponents = placeName.split(', ');

                            // Create a structured data object from the search result
                            const searchData = {
                                building_name: selectedText,
                                street: '',
                                barangay: '',
                                city: '',
                                province: '',
                                full_address: placeName
                            };

                            // Try to parse address components from the place_name
                            if (addressComponents.length >= 2) {
                                if (addressComponents.length >= 4) {
                                    searchData.barangay = addressComponents[1]?.trim() || '';
                                    searchData.city = addressComponents[2]?.trim() || '';
                                    searchData.province = addressComponents[3]?.trim() || '';
                                } else if (addressComponents.length >= 3) {
                                    searchData.city = addressComponents[1]?.trim() || '';
                                    searchData.province = addressComponents[2]?.trim() || '';
                                } else {
                                    searchData.city = addressComponents[1]?.trim() || '';
                                }
                            }

                            // Update coordinates
                            document.getElementById('latitude').value = lat.toFixed(7);
                            document.getElementById('longitude').value = lng.toFixed(7);
                            document.getElementById('selectedLatitude').textContent = lat.toFixed(7);
                            document.getElementById('selectedLongitude').textContent = lng.toFixed(7);

                            // Populate fields with the parsed search data
                            populateLocationFields(searchData);

                            searchResultsContainer.classList.add('hidden');
                        };
                        searchResultsContainer.appendChild(div);
                    });
                } else {
                    searchResultsContainer.innerHTML = '<p class="p-3 text-gray-500">No results found.</p>';
                }
            })
            .catch(error => console.error('Geocoding search failed:', error));
    }

    searchInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleSearch();
        }
    });
    searchButton.addEventListener('click', handleSearch);
    
    // Hide search results when clicking outside
    document.addEventListener('click', function(e) {
        if (!searchResultsContainer.contains(e.target) && !searchInput.contains(e.target)) {
            searchResultsContainer.classList.add('hidden');
        }
    });

    initializeMap();
}); 