<?php

namespace App\Services;

use App\Models\Barangay;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class BarangayService
{
    protected $psgcService;
    protected $cacheTimeout = 1800; // 30 minutes

    public function __construct(PSGCService $psgcService = null)
    {
        $this->psgcService = $psgcService ?: new PSGCService();
    }

    /**
     * Get all active barangays for RBAC filtering (all Cebu Province)
     */
    public function getActiveBarangays()
    {
        $cacheKey = 'active_barangays_rbac_all_cebu_province';

        return Cache::remember($cacheKey, $this->cacheTimeout, function () {
            try {
                return Barangay::where('status', true)
                              ->orderBy('name')
                              ->pluck('name')
                              ->toArray();
            } catch (\Exception $e) {
                Log::warning('Failed to fetch active barangays from database', ['error' => $e->getMessage()]);

                // Fallback to PSGC service - get all barangays from all Cebu Province cities
                try {
                    $allBarangays = [];
                    $cities = $this->psgcService->getCebuProvinceCities();

                    foreach ($cities as $city) {
                        $cityBarangays = $this->psgcService->getCityBarangays($city['code'], $city['name']);
                        $barangayNames = collect($cityBarangays)->pluck('name')->toArray();
                        $allBarangays = array_merge($allBarangays, $barangayNames);
                    }

                    return collect($allBarangays)->unique()->sort()->values()->toArray();
                } catch (\Exception $psgcError) {
                    Log::error('Failed to fetch barangays from PSGC service', ['error' => $psgcError->getMessage()]);
                    return [];
                }
            }
        });
    }

    /**
     * Get barangays accessible to a specific user based on RBAC
     */
    public function getAccessibleBarangays(User $user)
    {
        // System Administrator has access to all barangays
        if ($user->hasRole('system_admin')) {
            return $this->getActiveBarangays();
        }

        // Super Administrator (CDRRMC) has access to barangays in their city
        if ($user->hasRole('super_admin')) {
            if ($user->city) {
                return $this->getBarangaysByCity($user->city);
            }
            // Fallback to all barangays if no city specified
            return $this->getActiveBarangays();
        }

        // Barangay users (BDRRMC) can only access their own barangay
        return [$user->barangay];
    }

    /**
     * Get city name from PSGC code
     */
    public function getCityNameFromCode($cityCode)
    {
        if (!$cityCode || !is_numeric($cityCode)) {
            return $cityCode; // Return as-is if not a PSGC code
        }

        $cacheKey = "city_name_from_code_{$cityCode}";

        return Cache::remember($cacheKey, $this->cacheTimeout, function () use ($cityCode) {
            try {
                $cities = $this->psgcService->getCebuProvinceCities();
                $city = collect($cities)->firstWhere('code', $cityCode);
                return $city ? $city['name'] : null;
            } catch (\Exception $e) {
                Log::error("Failed to get city name from code: {$cityCode}", [
                    'error' => $e->getMessage()
                ]);
                return null;
            }
        });
    }

    /**
     * Get ALL barangays for a specific city (accepts city name or PSGC code)
     * Prioritizes PSGC data to return complete official list of barangays
     */
    public function getBarangaysByCity(string $cityIdentifier)
    {
        $cacheKey = "barangays_by_city_{$cityIdentifier}";

        return Cache::remember($cacheKey, $this->cacheTimeout, function () use ($cityIdentifier) {
            try {
                // Determine if input is a PSGC code (numeric) or city name
                $isCode = is_numeric($cityIdentifier);

                if ($isCode) {
                    // Input is a PSGC code - get city name first
                    $cities = $this->psgcService->getCebuProvinceCities();
                    $city = collect($cities)->firstWhere('code', $cityIdentifier);

                    if (!$city) {
                        Log::warning("PSGC code not found: {$cityIdentifier}");
                        return [];
                    }

                    $cityName = $city['name'];
                    $cityCode = $cityIdentifier;
                } else {
                    // Input is a city name
                    $cityName = $cityIdentifier;

                    // Get city code from PSGC cities list
                    $cities = $this->psgcService->getCebuProvinceCities();
                    $city = collect($cities)->firstWhere('name', $cityName);

                    if (!$city) {
                        Log::warning("City name not found in PSGC data: {$cityName}");
                        return [];
                    }

                    $cityCode = $city['code'];
                }

                // Always get ALL barangays from PSGC service first (official complete list)
                $psgcBarangays = $this->psgcService->getCityBarangays($cityCode, $cityName);

                if (!empty($psgcBarangays)) {
                    // Return all official barangays from PSGC (complete list)
                    return collect($psgcBarangays)->pluck('name')->sort()->values()->toArray();
                }

                // Fallback to database only if PSGC fails
                $barangays = Barangay::where('status', true)
                                   ->where('address', 'LIKE', "%{$cityName}%")
                                   ->orderBy('name')
                                   ->pluck('name')
                                   ->toArray();

                return $barangays;

            } catch (\Exception $e) {
                Log::error("Failed to fetch barangays for city: {$cityIdentifier}", [
                    'error' => $e->getMessage()
                ]);
                return [];
            }
        });
    }

    /**
     * Check if a user can access a specific barangay
     */
    public function canUserAccessBarangay(User $user, string $barangay)
    {
        if ($user->hasCityWideAccess()) {
            return true;
        }
        
        return $user->barangay === $barangay;
    }

    /**
     * Get barangays for dropdown/select options based on user role
     */
    public function getBarangayOptions(User $user)
    {
        $barangays = $this->getAccessibleBarangays($user);
        
        // Return as key-value pairs for select options
        return collect($barangays)->mapWithKeys(function ($barangay) {
            return [$barangay => $barangay];
        })->toArray();
    }

    /**
     * Validate if a barangay exists in the PSGC-connected system (all Cebu Province)
     */
    public function isValidBarangay(string $barangay)
    {
        // Check database first
        $exists = Barangay::where('name', $barangay)
                          ->where('status', true)
                          ->exists();

        if ($exists) {
            return true;
        }

        // Check PSGC service as fallback - check all Cebu Province cities
        try {
            $cities = $this->psgcService->getCebuProvinceCities();

            foreach ($cities as $city) {
                $cityBarangays = $this->psgcService->getCityBarangays($city['code'], $city['name']);
                $validBarangays = collect($cityBarangays)->pluck('name')->toArray();

                if (in_array($barangay, $validBarangays)) {
                    return true;
                }
            }

            return false;
        } catch (\Exception $e) {
            Log::error('Failed to validate barangay against PSGC', [
                'barangay' => $barangay,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Apply barangay filter to a query based on user RBAC
     */
    public function applyBarangayFilter($query, User $user, string $barangayColumn = 'barangay', string $selectedBarangay = null)
    {
        if ($user->hasCityWideAccess()) {
            // Super admin can see all or filter by selected barangay
            if ($selectedBarangay && $selectedBarangay !== 'All' && $selectedBarangay !== '') {
                $query->where($barangayColumn, $selectedBarangay);
            }
            // No filter for "All" or empty selection
        } else {
            // Barangay users can only see their own barangay data
            $query->where($barangayColumn, $user->barangay);
        }
        
        return $query;
    }

    /**
     * Get barangay statistics for RBAC-filtered data
     */
    public function getBarangayStatistics(User $user)
    {
        $accessibleBarangays = $this->getAccessibleBarangays($user);
        
        $statistics = [];
        foreach ($accessibleBarangays as $barangay) {
            $statistics[$barangay] = [
                'name' => $barangay,
                'users_count' => User::where('barangay', $barangay)->count(),
                'evacuation_centers_count' => \App\Models\Evacuation::where('barangay', $barangay)->count(),
                'notifications_count' => \App\Models\Notification::where('barangay', $barangay)->count(),
            ];
        }
        
        return $statistics;
    }

    /**
     * Get all barangays from all Cebu Province cities (for system admin)
     */
    public function getAllCebuProvinceBarangays()
    {
        $cacheKey = 'all_cebu_province_barangays';

        return Cache::remember($cacheKey, $this->cacheTimeout, function () {
            try {
                $allBarangays = [];
                $cities = $this->psgcService->getCebuProvinceCities();

                foreach ($cities as $city) {
                    $cityBarangays = $this->psgcService->getCityBarangays($city['code'], $city['name']);
                    foreach ($cityBarangays as $barangay) {
                        $allBarangays[] = [
                            'name' => $barangay['name'],
                            'city' => $city['name'],
                            'full_name' => "{$barangay['name']}, {$city['name']}"
                        ];
                    }
                }

                return collect($allBarangays)->sortBy('full_name')->values()->toArray();
            } catch (\Exception $e) {
                Log::error('Failed to fetch all Cebu Province barangays', [
                    'error' => $e->getMessage()
                ]);
                return [];
            }
        });
    }

    /**
     * Refresh barangay cache
     */
    public function refreshCache()
    {
        Cache::forget('active_barangays_rbac_all_cebu_province');
        Cache::forget('all_cebu_province_barangays');
        $this->psgcService->clearCache();

        // Pre-warm cache
        $this->getActiveBarangays();

        Log::info('Barangay service cache refreshed');
    }

    /**
     * Sync user barangays with PSGC data
     * This method helps ensure user barangay data is consistent with PSGC
     */
    public function syncUserBarangays()
    {
        $validBarangays = $this->getActiveBarangays();
        $users = User::whereNotIn('role', ['super_admin'])->get();
        
        $updated = 0;
        $issues = [];
        
        foreach ($users as $user) {
            if (!in_array($user->barangay, $validBarangays)) {
                // Try to find a close match
                $closestMatch = $this->findClosestBarangayMatch($user->barangay, $validBarangays);
                
                if ($closestMatch) {
                    $originalBarangay = $user->barangay;
                    $user->barangay = $closestMatch;
                    $user->save();
                    
                    Log::info("Updated user barangay: {$user->email} from '{$originalBarangay}' to '{$closestMatch}'");
                    $updated++;
                } else {
                    $issues[] = [
                        'user_id' => $user->id,
                        'email' => $user->email,
                        'invalid_barangay' => $user->barangay
                    ];
                }
            }
        }
        
        if (!empty($issues)) {
            Log::warning('Users with invalid barangays found', ['issues' => $issues]);
        }
        
        return [
            'updated' => $updated,
            'issues' => $issues
        ];
    }

    /**
     * Find the closest matching barangay name
     */
    protected function findClosestBarangayMatch(string $userBarangay, array $validBarangays)
    {
        $userBarangay = strtolower(trim($userBarangay));
        
        foreach ($validBarangays as $validBarangay) {
            $validLower = strtolower($validBarangay);
            
            // Exact match (case insensitive)
            if ($userBarangay === $validLower) {
                return $validBarangay;
            }
            
            // Levenshtein distance for close matches
            if (levenshtein($userBarangay, $validLower) <= 2) {
                return $validBarangay;
            }
            
            // Contains match
            if (str_contains($validLower, $userBarangay) || str_contains($userBarangay, $validLower)) {
                return $validBarangay;
            }
        }
        
        return null;
    }

    /**
     * Get all barangays province-wide for System Admin
     */
    public function getAllProvincialBarangays()
    {
        try {
            // Get all barangays from all cities in Cebu Province
            $allBarangays = $this->getAllCebuProvinceBarangays();

            if (!empty($allBarangays)) {
                return collect($allBarangays)->pluck('name')->unique()->sort()->values()->toArray();
            }

            // Fallback: get all unique barangays from database
            $barangays = Barangay::where('status', true)
                               ->pluck('name')
                               ->unique()
                               ->sort()
                               ->values()
                               ->toArray();

            return $barangays;

        } catch (\Exception $e) {
            \Log::error('Failed to get provincial barangays', ['error' => $e->getMessage()]);

            // Emergency fallback: get all unique barangays from database
            return Barangay::where('status', true)
                          ->pluck('name')
                          ->unique()
                          ->sort()
                          ->values()
                          ->toArray();
        }
    }

    /**
     * Get all cities in Cebu Province for System Admin
     */
    public function getAllProvincialCities()
    {
        try {
            // Get all cities from Cebu Province
            $cities = $this->psgcService->getCebuProvinceCities();

            if (!empty($cities)) {
                return collect($cities)->pluck('name')->sort()->values()->toArray();
            }

            // Fallback: get all unique cities from database
            $cities = User::whereNotNull('city')
                         ->distinct()
                         ->pluck('city')
                         ->filter()
                         ->map(function($city) {
                             // Convert PSGC codes to city names if needed
                             if (is_numeric($city)) {
                                 return $this->getCityNameFromCode($city) ?? $city;
                             }
                             return $city;
                         })
                         ->unique()
                         ->sort()
                         ->values()
                         ->toArray();

            return $cities;

        } catch (\Exception $e) {
            \Log::error('Failed to get provincial cities', ['error' => $e->getMessage()]);

            // Emergency fallback: get all unique cities from database
            return User::whereNotNull('city')
                      ->distinct()
                      ->pluck('city')
                      ->filter()
                      ->unique()
                      ->sort()
                      ->values()
                      ->toArray();
        }
    }


}
