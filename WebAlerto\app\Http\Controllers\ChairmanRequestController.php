<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\UserManagementRequest;
use Illuminate\Support\Facades\Auth;

class ChairmanRequestController extends Controller
{
    /**
     * Display the chairman's requests
     */
    public function index()
    {
        $user = Auth::user();

        // Ensure only chairmen can access this
        if (!$user->is<PERSON>hairman()) {
            abort(403, 'Unauthorized access. Only Chairmen can view this page.');
        }
        
        // Get all requests made by this chairman
        $requests = UserManagementRequest::where('requester_id', $user->id)
            ->with(['targetUser', 'reviewedBy'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);
        
        // Count pending requests for notification badge
        $pendingCount = UserManagementRequest::where('requester_id', $user->id)
            ->where('status', 'pending')
            ->count();
        
        // Count recently updated requests (reviewed in last 24 hours)
        $recentlyUpdatedCount = UserManagementRequest::where('requester_id', $user->id)
            ->whereIn('status', ['approved', 'rejected'])
            ->where('reviewed_at', '>=', now()->subDay())
            ->count();
        
        return view('components.chairman.requests', compact(
            'requests', 
            'pendingCount', 
            'recentlyUpdatedCount'
        ));
    }
    
    /**
     * Mark requests as viewed (remove notification badge)
     */
    public function markAsViewed()
    {
        $user = Auth::user();

        if (!$user->isChairman()) {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
        }
        
        // Update the user's last_viewed_requests timestamp
        $user->update(['last_viewed_requests' => now()]);
        
        return response()->json(['success' => true]);
    }
    
    /**
     * Get notification count for AJAX requests
     */
    public function getNotificationCount()
    {
        $user = Auth::user();

        if (!$user->isChairman()) {
            return response()->json(['count' => 0, 'message' => 'Unauthorized']);
        }
        
        // Count requests that have been updated since last viewed
        $lastViewed = $user->last_viewed_requests ?? $user->created_at;
        
        $count = UserManagementRequest::where('requester_id', $user->id)
            ->whereIn('status', ['approved', 'rejected'])
            ->where('reviewed_at', '>', $lastViewed)
            ->count();
        
        return response()->json(['count' => $count]);
    }
}
