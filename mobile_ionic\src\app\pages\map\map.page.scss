/* Header styling - matching profile and search pages */
ion-header {
  background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);

  ion-toolbar {
    --background: transparent;
    --color: white;

    ion-title {
      color: white;
      font-weight: 600;
    }

    ion-back-button {
      --color: white;
    }
  }
}

/* Content styling */
ion-content {
  --background: #f5f5f5;
}

/* Offline status banner */
.offline-status-banner {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  border-radius: 20px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1001;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  font-size: 14px;
  font-weight: 500;
  animation: slideDown 0.3s ease-out;

  ion-icon {
    font-size: 18px;
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Map container */
#map {
  width: 100%;
  height: 100%;
}

/* Travel mode segment */
.mode-segment {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 10px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  padding: 4px;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  ion-segment-button {
    --background: transparent;
    --background-checked: var(--ion-color-light);
    --indicator-color: transparent;
    --border-radius: 16px;
    min-height: 40px;

    .segment-icon {
      width: 24px;
      height: 24px;
      display: block;
      margin: 0 auto 4px;
    }

    .segment-label {
      font-size: 12px;
      font-weight: 500;
    }
  }
}

/* Route summary card */
.route-summary-card {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 70px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  z-index: 1000;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateX(-50%) translateY(-2px);
  }

  &:active {
    transform: translateX(-50%) translateY(0);
  }

  ion-icon {
    font-size: 24px;
  }

  .summary-text {
    line-height: 1.3;

    strong {
      font-size: 16px;
    }

    .travel-mode {
      font-size: 12px;
      opacity: 0.8;
      margin-top: 2px;
    }
  }

  .expand-icon {
    font-size: 18px;
    margin-left: 8px;
    color: var(--ion-color-medium);
  }
}

/* FAB button label */
.fab-label {
  position: absolute;
  right: 80px;
  bottom: 30px;
  background: rgba(255, 255, 255, 0.95);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  color: var(--ion-color-primary);
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-weight: 500;
}

/* GPS toggle button */
ion-fab-button[activated] {
  --background: var(--ion-color-primary);
  --color: white;
}

/* GPS status indicator */
.gps-status {
  position: absolute;
  top: 10px;
  left: 70px; /* Moved to the right to make room for the download button */
  background: var(--ion-color-medium);
  border-radius: 20px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-size: 14px;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: var(--ion-color-medium-shade);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }

  &.active {
    color: white;
    background: var(--ion-color-success);

    &:hover {
      background: var(--ion-color-success-shade);
    }

    ion-icon {
      animation: pulse 1.5s infinite;
      color: white;
    }
  }

  ion-icon {
    font-size: 18px;
  }

  &::after {
    content: "?";
    display: inline-block;
    width: 16px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    background: var(--ion-color-medium);
    color: white;
    border-radius: 50%;
    font-size: 12px;
    margin-left: 6px;
    opacity: 0.7;
  }
}

/* Disaster type indicator */
.disaster-type-indicator {
  position: absolute;
  top: 10px;
  right: 80px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-size: 14px;
  font-weight: 500;
  color: var(--ion-color-dark);

  ion-icon {
    font-size: 18px;
    color: var(--ion-color-primary);
  }
}

/* Location request container */
.location-request-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  padding: 20px;
  text-align: center;
  max-width: 300px;
  width: 90%;
  z-index: 1001; /* Higher than other elements */
  animation: fadeIn 0.5s ease-out;

  ion-button {
    margin: 10px 0;
    --border-radius: 10px;
    --box-shadow: 0 4px 8px rgba(var(--ion-color-primary-rgb), 0.3);
    font-weight: 600;
    height: 48px;

    &:active {
      --box-shadow: 0 2px 4px rgba(var(--ion-color-primary-rgb), 0.2);
      transform: translateY(2px);
    }
  }

  .location-help-text {
    margin: 10px 0 0;
    font-size: 14px;
    color: var(--ion-color-medium);
    line-height: 1.4;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -40%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}

/* Default map message */
.map-default-message {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 12px 16px;
  text-align: center;
  max-width: 300px;
  z-index: 1000;

  ion-icon {
    font-size: 24px;
    color: var(--ion-color-primary);
    margin-bottom: 8px;
  }

  p {
    margin: 0 0 5px;
    font-weight: 500;
    font-size: 16px;
    color: var(--ion-color-dark);
  }

  small {
    color: var(--ion-color-medium);
    font-size: 13px;
    display: block;
    line-height: 1.4;
  }
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

/* Pulsating marker animation */
@keyframes pulsate {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.4;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.8;
  }
}

.marker-pulse-container {
  position: relative;
}

.marker-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 50px;
  height: 50px;
  margin-top: -25px;
  margin-left: -25px;
  border-radius: 50%;
  z-index: 100;
  pointer-events: none;
  animation: pulsate 1.5s ease-out infinite;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

/* Style for popup button */
:host ::ng-deep .popup-button {
  background-color: var(--ion-color-primary);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
  margin-top: 8px;
  transition: background-color 0.2s;
}

:host ::ng-deep .popup-button:hover {
  background-color: var(--ion-color-primary-shade);
}

:host ::ng-deep .evacuation-popup {
  h3 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
  }

  p {
    margin: 4px 0;
    font-size: 14px;
  }
}

/* Modal styles */
.evacuation-details-modal {
  --border-radius: 16px 16px 0 0;
  --backdrop-opacity: 0.4;
}