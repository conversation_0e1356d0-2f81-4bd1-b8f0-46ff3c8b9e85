<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Barangay extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'psgc_code',
        'description',
        'status',
        'contact_number',
        'email',
        'address',
        'coordinates',
        'population',
        'area',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'coordinates' => 'array',
        'status' => 'boolean',
    ];

    // Relationship with users (assuming users belong to a barangay)
    public function users()
    {
        return $this->hasMany(User::class);
    }

    // Relationship with evacuation centers
    public function evacuationCenters()
    {
        return $this->hasMany(EvacuationCenter::class);
    }

    // Relationship with creator
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Relationship with updater
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
} 