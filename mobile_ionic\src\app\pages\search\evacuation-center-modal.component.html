<div class="modal-container">
  <!-- Close button -->
  <div class="close-button" (click)="dismiss()">
    <ion-icon name="close-circle" color="danger"></ion-icon>
  </div>

  <!-- Center name -->
  <h2 class="center-name">{{ center.name }}</h2>

  <!-- Status badge -->
  <div class="status-badge" *ngIf="center.status">
    <ion-chip [color]="center.status === 'Full' ? 'danger' : 'success'">
      <ion-icon [name]="center.status === 'Full' ? 'warning-outline' : 'checkmark-circle-outline'"></ion-icon>
      <ion-label>{{ center.status }}</ion-label>
    </ion-chip>
  </div>

  <!-- Contact info -->
  <div class="info-section">
    <div class="info-label">Contact Number</div>
    <div class="info-value contact">
      <ion-icon name="call-outline"></ion-icon>
      <span>{{ center.contact || 'No contact available' }}</span>
    </div>
  </div>

  <!-- Address info -->
  <div class="info-section">
    <div class="info-label">Address</div>
    <div class="info-value address">
      <ion-icon name="location-outline"></ion-icon>
      <span>{{ center.address }}</span>
    </div>
  </div>

  <!-- Full center warning -->
  <div *ngIf="isCenterFull()" class="full-warning">
    <ion-icon name="warning-outline" color="danger"></ion-icon>
    <span>This evacuation center is currently at full capacity.</span>
  </div>

  <!-- Get Directions button -->
  <div class="directions-button">
    <ion-button expand="block"
                [color]="isCenterFull() ? 'danger' : 'primary'"
                [disabled]="isCenterFull()"
                (click)="getDirections()">
      <ion-icon [name]="isCenterFull() ? 'ban' : 'navigate'" slot="start"></ion-icon>
      {{ isCenterFull() ? 'Unavailable - Center Full' : 'Get Directions' }}
    </ion-button>
  </div>
</div>
