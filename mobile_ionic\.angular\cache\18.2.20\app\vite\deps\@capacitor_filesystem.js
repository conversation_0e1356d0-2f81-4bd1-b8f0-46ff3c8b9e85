import {
  Directory,
  Encoding,
  FilesystemDirectory,
  FilesystemEncoding
} from "./chunk-E6UG7NSK.js";
import {
  y
} from "./chunk-LVBQUHDE.js";
import {
  registerPlugin
} from "./chunk-MCFBZ5YE.js";
import "./chunk-EAE2VPRF.js";

// node_modules/@capacitor/filesystem/dist/esm/index.js
var Filesystem = registerPlugin("Filesystem", {
  web: () => import("./web-OTHR3NCA.js").then((m) => new m.FilesystemWeb())
});
y();
export {
  Directory,
  Encoding,
  Filesystem,
  FilesystemDirectory,
  FilesystemEncoding
};
//# sourceMappingURL=@capacitor_filesystem.js.map
