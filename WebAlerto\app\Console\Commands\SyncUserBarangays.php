<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\BarangayService;

class SyncUserBarangays extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:sync-barangays {--dry-run : Show what would be changed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync user barangay data with PSGC-connected barangay database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔄 Syncing user barangays with PSGC data...');

        $barangayService = app(BarangayService::class);

        if ($this->option('dry-run')) {
            $this->warn('🔍 DRY RUN MODE - No changes will be made');
        }

        // Get sync results
        if ($this->option('dry-run')) {
            // For dry run, we'll simulate the sync
            $validBarangays = $barangayService->getActiveBarangays();
            $users = \App\Models\User::whereNotIn('role', ['super_admin'])->get();

            $wouldUpdate = 0;
            $issues = [];

            foreach ($users as $user) {
                if (!in_array($user->barangay, $validBarangays)) {
                    $closestMatch = $this->findClosestMatch($user->barangay, $validBarangays);

                    if ($closestMatch) {
                        $this->line("Would update: {$user->email} from '{$user->barangay}' to '{$closestMatch}'");
                        $wouldUpdate++;
                    } else {
                        $issues[] = [
                            'email' => $user->email,
                            'invalid_barangay' => $user->barangay
                        ];
                        $this->error("Issue: {$user->email} has invalid barangay '{$user->barangay}'");
                    }
                }
            }

            $this->info("📊 Dry run results:");
            $this->info("   - Would update: {$wouldUpdate} users");
            $this->info("   - Issues found: " . count($issues));

        } else {
            $results = $barangayService->syncUserBarangays();

            $this->info("✅ Sync completed:");
            $this->info("   - Updated: {$results['updated']} users");
            $this->info("   - Issues: " . count($results['issues']));

            if (!empty($results['issues'])) {
                $this->warn("\n⚠️  Users with unresolvable barangay issues:");
                foreach ($results['issues'] as $issue) {
                    $this->line("   - {$issue['email']}: '{$issue['invalid_barangay']}'");
                }
            }
        }

        return 0;
    }

    /**
     * Find closest matching barangay (for dry run)
     */
    protected function findClosestMatch(string $userBarangay, array $validBarangays)
    {
        $userBarangay = strtolower(trim($userBarangay));

        foreach ($validBarangays as $validBarangay) {
            $validLower = strtolower($validBarangay);

            if ($userBarangay === $validLower) {
                return $validBarangay;
            }

            if (levenshtein($userBarangay, $validLower) <= 2) {
                return $validBarangay;
            }

            if (str_contains($validLower, $userBarangay) || str_contains($userBarangay, $validLower)) {
                return $validBarangay;
            }
        }

        return null;
    }
}
