.container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.header-section {
  text-align: center;
  margin-bottom: 30px;

  h1 {
    color: var(--ion-color-primary);
    margin-bottom: 10px;
  }

  p {
    color: var(--ion-color-medium);
    font-size: 16px;
  }
}

.controls-section {
  margin-bottom: 30px;

  ion-button {
    margin-bottom: 10px;
  }
}

.results-section {
  margin-bottom: 30px;

  h2 {
    color: var(--ion-color-primary);
    margin-bottom: 15px;
  }
}

.result-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.result-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  background: var(--ion-color-light);
  border: 2px solid transparent;

  &.success {
    background: var(--ion-color-success-tint);
    border-color: var(--ion-color-success);
    color: var(--ion-color-success-shade);

    ion-icon {
      color: var(--ion-color-success);
    }
  }

  &.error {
    background: var(--ion-color-danger-tint);
    border-color: var(--ion-color-danger);
    color: var(--ion-color-danger-shade);

    ion-icon {
      color: var(--ion-color-danger);
    }
  }

  ion-icon {
    font-size: 24px;
    margin-right: 10px;
  }

  span {
    font-weight: 500;
  }
}

.logs-section {
  h2 {
    color: var(--ion-color-primary);
    margin-bottom: 15px;
  }
}

.logs-container {
  background: var(--ion-color-dark);
  color: var(--ion-color-light);
  border-radius: 8px;
  padding: 15px;
  max-height: 400px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-entry {
  margin-bottom: 5px;
  word-wrap: break-word;

  &:last-child {
    margin-bottom: 0;
  }
}

.loading-section {
  text-align: center;
  padding: 40px 20px;

  ion-spinner {
    margin-bottom: 20px;
  }

  p {
    color: var(--ion-color-medium);
    font-size: 16px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .result-grid {
    grid-template-columns: 1fr;
  }

  .logs-container {
    font-size: 11px;
    max-height: 300px;
  }
}
