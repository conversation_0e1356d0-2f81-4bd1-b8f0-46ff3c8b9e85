<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== INVITATION/NOTIFICATION FLOW DEBUG ===\n\n";

// Check invitations
echo "1. CURRENT INVITATIONS:\n";
$invitations = \App\Models\Invitation::all();
if ($invitations->count() > 0) {
    foreach ($invitations as $inv) {
        echo "- ID: {$inv->id}, Email: {$inv->email}, Status: {$inv->status}, Created: {$inv->created_at}\n";
        echo "  Token: {$inv->token}, Expires: {$inv->expires_at}, Role: {$inv->role}, Barangay: {$inv->barangay}\n";
    }
} else {
    echo "No invitations found in database.\n";
}

echo "\n2. CURRENT USERS:\n";
$users = \App\Models\User::all();
if ($users->count() > 0) {
    foreach ($users as $user) {
        echo "- ID: {$user->id}, Email: {$user->email}, Role: {$user->role}, Status: {$user->status}\n";
        echo "  Name: {$user->first_name} {$user->last_name}, Barangay: {$user->barangay}\n";
    }
} else {
    echo "No users found in database.\n";
}

echo "\n3. EMAIL CONFIGURATION:\n";
echo "MAIL_MAILER: " . config('mail.default') . "\n";
echo "MAIL_HOST: " . config('mail.mailers.smtp.host') . "\n";
echo "MAIL_PORT: " . config('mail.mailers.smtp.port') . "\n";
echo "MAIL_USERNAME: " . config('mail.mailers.smtp.username') . "\n";
echo "MAIL_ENCRYPTION: " . config('mail.mailers.smtp.encryption') . "\n";

echo "\n4. RECENT LOG ENTRIES (last 10):\n";
$logFile = storage_path('logs/laravel.log');
if (file_exists($logFile)) {
    $lines = file($logFile);
    $recentLines = array_slice($lines, -10);
    foreach ($recentLines as $line) {
        echo trim($line) . "\n";
    }
} else {
    echo "No log file found.\n";
}

echo "\n=== DEBUG COMPLETE ===\n"; 