<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== API Connection Test ===\n\n";

// Test API endpoints that mobile app uses
$endpoints = [
    'Health Check' => 'http://***************:8000/up',
    'API Base' => 'http://***************:8000/api',
    'Device Tokens' => 'http://***************:8000/api/device-tokens',
    'Register Token' => 'http://***************:8000/api/register-token',
    'Notifications' => 'http://***************:8000/api/notifications'
];

foreach ($endpoints as $name => $url) {
    echo "Testing $name: $url\n";
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'method' => 'GET',
            'header' => [
                'Accept: application/json',
                'Content-Type: application/json'
            ]
        ]
    ]);
    
    try {
        $response = @file_get_contents($url, false, $context);
        if ($response !== false) {
            echo "   ✅ Accessible\n";
        } else {
            echo "   ❌ Not accessible\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Error: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

echo "=== Mobile App Debugging Steps ===\n\n";

echo "🔧 If mobile app still can't register token:\n\n";

echo "1. Check mobile app logs in Android Studio:\n";
echo "   - Open Android Studio\n";
echo "   - Go to View → Tool Windows → Logcat\n";
echo "   - Filter by 'FCM' or 'Firebase' or 'WebAlerto'\n";
echo "   - Look for registration errors\n\n";

echo "2. Verify network connectivity:\n";
echo "   - Make sure your phone and computer are on the same network\n";
echo "   - Try accessing http://***************:8000 in phone browser\n";
echo "   - Check if firewall is blocking the connection\n\n";

echo "3. Force token registration:\n";
echo "   - Close the mobile app completely\n";
echo "   - Clear app data (Settings → Apps → WebAlerto → Storage → Clear Data)\n";
echo "   - Restart the app\n";
echo "   - Watch for FCM registration in logs\n\n";

echo "4. Check google-services.json:\n";
echo "   - Verify the file was properly copied to android/app/\n";
echo "   - Make sure package name matches: com.webalerto.app\n";
echo "   - Rebuild: ionic capacitor sync android\n\n";

echo "5. Test manual token registration:\n";
echo "   - I can create a test script to manually register a token\n";
echo "   - This will help verify if the backend FCM sending works\n\n";

echo "=== Next Actions ===\n\n";

echo "📱 Immediate steps:\n";
echo "1. Check Android Studio Logcat for FCM errors\n";
echo "2. Try accessing http://***************:8000 in phone browser\n";
echo "3. Clear app data and restart the mobile app\n";
echo "4. Run this command again to check for new tokens:\n";
echo "   D:\\AlertoCapstone\\php\\php.exe check-tokens-simple.php\n\n";

echo "🚨 If still no tokens after these steps:\n";
echo "   The issue is likely in the mobile app FCM configuration\n";
echo "   We may need to check the mobile app code or Firebase setup\n\n";

echo "=== Test Complete ===\n";
