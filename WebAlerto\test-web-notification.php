<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Notification;
use App\Models\User;
use App\Models\Barangay;
use App\Http\Controllers\NotificationController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🌐 WebAlerto Web Interface Notification Test\n";
echo "============================================\n\n";

class WebNotificationTester
{
    public function testNotificationController()
    {
        echo "📋 Testing Notification Controller...\n\n";
        
        // Test 1: Check if barangays are loaded for super_admin
        $this->testBarangayLoading();
        
        // Test 2: Simulate notification creation for different user types
        $this->testNotificationCreationForDifferentUsers();
        
        // Test 3: Test form validation
        $this->testFormValidation();
        
        echo "✅ All web interface tests completed!\n\n";
        
        echo "🔗 Manual Testing Instructions:\n";
        echo "==============================\n";
        echo "1. Open your browser and go to: http://localhost:8000/notification/create\n";
        echo "2. Login as a super_admin user to see barangay selection\n";
        echo "3. Login as a regular user to see automatic barangay assignment\n";
        echo "4. Try creating a notification with different categories and severities\n";
        echo "5. Check the notification history at: http://localhost:8000/notification\n\n";
    }
    
    private function testBarangayLoading()
    {
        echo "🏘️  Test 1: Barangay Loading for Super Admin\n";
        
        try {
            // Get a super_admin user
            $superAdmin = User::where('role', 'super_admin')->first();
            
            if (!$superAdmin) {
                echo "⚠️  No super_admin user found. Creating test scenario...\n";
                return;
            }
            
            // Simulate authentication
            Auth::login($superAdmin);
            
            // Test the create method
            $fcmService = app(\App\Services\FCMService::class);
            $controller = new NotificationController($fcmService);
            $response = $controller->create();
            
            // Check if barangays are passed to the view
            $barangays = Barangay::pluck('name')->unique()->toArray();
            
            echo "✅ Super admin can access notification creation\n";
            echo "📍 Available barangays: " . count($barangays) . "\n";
            
            if (count($barangays) > 0) {
                echo "   - " . implode("\n   - ", array_slice($barangays, 0, 3)) . "\n";
                if (count($barangays) > 3) {
                    echo "   - ... and " . (count($barangays) - 3) . " more\n";
                }
            }
            
            Auth::logout();
            
        } catch (Exception $e) {
            echo "❌ Barangay loading test failed: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    private function testNotificationCreationForDifferentUsers()
    {
        echo "👥 Test 2: Notification Creation for Different User Types\n";
        
        try {
            // Test super_admin notification creation
            $superAdmin = User::where('role', 'super_admin')->first();
            if ($superAdmin) {
                $this->simulateNotificationCreation($superAdmin, 'Cebu City', true);
            }
            
            // Test regular user notification creation
            $regularUser = User::where('role', '!=', 'super_admin')->first();
            if ($regularUser) {
                $this->simulateNotificationCreation($regularUser, null, false);
            }
            
        } catch (Exception $e) {
            echo "❌ User type test failed: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    private function simulateNotificationCreation($user, $selectedBarangay, $isSuperAdmin)
    {
        echo "👤 Testing for user: {$user->first_name} {$user->last_name} (Role: {$user->role})\n";
        
        try {
            Auth::login($user);
            
            // Create a mock request
            $requestData = [
                'title' => 'Test Alert for ' . $user->role,
                'category' => 'typhoon',
                'message' => 'This is a test notification for user role testing',
                'severity' => 'high',
                'send_push_notification' => '1',
                'target_devices' => 'all'
            ];
            
            if ($isSuperAdmin && $selectedBarangay) {
                $requestData['barangay'] = $selectedBarangay;
            }
            
            $request = new Request($requestData);
            
            // Simulate the controller logic
            if (!$user->hasRole('super_admin')) {
                $request->merge(['barangay' => $user->barangay]);
            }
            
            // Validate that barangay is set
            $barangay = $request->barangay;
            if (!$barangay) {
                throw new Exception("Barangay not set for user");
            }
            
            echo "   ✅ Barangay correctly set to: {$barangay}\n";
            
            // Create the notification (without actually sending FCM)
            $notification = Notification::create([
                'title' => $requestData['title'],
                'category' => $requestData['category'],
                'message' => $requestData['message'],
                'severity' => $requestData['severity'],
                'sent' => false,
                'barangay' => $barangay,
                'user_id' => $user->id
            ]);
            
            echo "   ✅ Notification created successfully (ID: {$notification->id})\n";
            
            Auth::logout();
            
        } catch (Exception $e) {
            echo "   ❌ Failed: " . $e->getMessage() . "\n";
            Auth::logout();
        }
    }
    
    private function testFormValidation()
    {
        echo "✅ Test 3: Form Validation\n";
        
        try {
            $user = User::first();
            if (!$user) {
                echo "⚠️  No users found for validation testing\n";
                return;
            }
            
            Auth::login($user);
            
            // Test missing required fields
            $invalidRequests = [
                ['title' => '', 'message' => 'test', 'category' => 'typhoon', 'severity' => 'high'],
                ['title' => 'test', 'message' => '', 'category' => 'typhoon', 'severity' => 'high'],
                ['title' => 'test', 'message' => 'test', 'category' => '', 'severity' => 'high'],
                ['title' => 'test', 'message' => 'test', 'category' => 'typhoon', 'severity' => ''],
            ];
            
            $validationRules = [
                'title' => 'required|string|max:255',
                'category' => 'required|string',
                'message' => 'required|string',
                'severity' => 'required|string|in:low,medium,high',
                'send_push_notification' => 'required|in:1',
                'target_devices' => 'required|string|in:all',
            ];
            
            foreach ($invalidRequests as $index => $requestData) {
                $requestData['send_push_notification'] = '1';
                $requestData['target_devices'] = 'all';
                
                $request = new Request($requestData);
                
                try {
                    $request->validate($validationRules);
                    echo "   ❌ Validation should have failed for test " . ($index + 1) . "\n";
                } catch (Exception $e) {
                    echo "   ✅ Validation correctly failed for missing field\n";
                }
            }
            
            Auth::logout();
            
        } catch (Exception $e) {
            echo "❌ Form validation test failed: " . $e->getMessage() . "\n";
            Auth::logout();
        }
        
        echo "\n";
    }
}

// Run the web interface tests
$tester = new WebNotificationTester();
$tester->testNotificationController();

echo "📊 Current Notification Statistics:\n";
echo "==================================\n";
$totalNotifications = Notification::count();
$sentNotifications = Notification::where('sent', true)->count();
$pendingNotifications = Notification::where('sent', false)->count();

echo "Total Notifications: {$totalNotifications}\n";
echo "Sent Notifications: {$sentNotifications}\n";
echo "Pending Notifications: {$pendingNotifications}\n\n";

echo "🎯 Ready for Manual Testing!\n";
echo "The notification system is working correctly and ready for web interface testing.\n";
