<?php
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\LogoutController;
use App\Http\Controllers\Auth\ChangePasswordController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\EvacuationManagementController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\AdminInvitationController;
use App\Http\Controllers\EmailNotificationController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\MappingSystemController;
use App\Http\Controllers\SuperAdminController;
use App\Http\Middleware\CheckRole;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\SystemAdminController;

Route::get('/', function () {
    return view('landing_page');
});

// Public routes
Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [LoginController::class, 'login']);
Route::post('/logout', [LogoutController::class, 'logout'])->name('logout');


Route::get('/logout', [LogoutController::class, 'logout'])->name('logout');
// Password Change Routes
Route::get('/change-password', [ChangePasswordController::class, 'showChangePasswordForm'])->name('password.change.form');
Route::post('/change-password', [ChangePasswordController::class, 'changePassword'])->name('password.change');

// Admin Invitation Routes (Public)
Route::get('/admin/invitation/accept', [AdminInvitationController::class, 'showAcceptForm'])->name('admin.invitation.accept');
Route::post('/admin/invitation/accept', [AdminInvitationController::class, 'acceptInvitation'])->name('admin.invitation.accept.post');

// Protected routes
Route::middleware(['auth'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('components.dashboard');

    // Geolocation Test Page
    Route::get('/test-geolocation', function () {
        return view('test-geolocation');
    })->name('test.geolocation');
    
    // Barangay Statistics (Super Admin only)
    Route::middleware([CheckRole::class . ':super_admin'])->group(function () {
        Route::get('/barangay-statistics', [AdminController::class, 'barangayStatistics'])->name('admin.barangay-statistics');
    });

    // System Administrator routes
    Route::prefix('system-admin')->name('system-admin.')->middleware(['auth', CheckRole::class . ':system_admin'])->group(function () {
        Route::get('/dashboard', [SystemAdminController::class, 'dashboard'])->name('dashboard');
        Route::get('/user-management', [SystemAdminController::class, 'userManagement'])->name('user-management');
        Route::get('/mobile-users', [SystemAdminController::class, 'mobileUsers'])->name('mobile-users');
        Route::post('/create-user', [SystemAdminController::class, 'createUser'])->name('create-user');
        Route::post('/check-email', [SystemAdminController::class, 'checkEmailAvailability'])->name('check-email');
        Route::post('/get-positions', [SystemAdminController::class, 'getPositionsForRole'])->name('get-positions');
        Route::get('/positions-for-role', [SystemAdminController::class, 'getPositionsForRole'])->name('positions-for-role');
        Route::get('/system-logs', [SystemAdminController::class, 'systemLogs'])->name('system-logs');

        // User management actions
        Route::get('/users/{id}/view', [SystemAdminController::class, 'viewUser'])->name('users.view');
        Route::post('/users/{id}/toggle-status', [SystemAdminController::class, 'toggleUserStatus'])->name('users.toggle-status');
        Route::delete('/users/{id}/delete', [SystemAdminController::class, 'deleteUser'])->name('users.delete');
        Route::put('/users/{id}/edit', [SystemAdminController::class, 'editUser'])->name('users.edit');

        // Request management - REMOVED
        // Route::get('/request-management', [SystemAdminController::class, 'requestManagement'])->name('request-management');
        // Route::get('/requests/{id}/view', [SystemAdminController::class, 'viewRequest'])->name('requests.view');
        // Route::post('/requests/{id}/review', [SystemAdminController::class, 'reviewRequest'])->name('requests.review');

        // AJAX endpoints for filtering
        Route::get('/get-barangays-by-city', [SystemAdminController::class, 'getBarangaysByCity'])->name('get-barangays-by-city');
    });

    // PSGC API endpoints for location data
    Route::prefix('api/psgc')->middleware(['auth'])->group(function () {
        Route::get('/cities', [SystemAdminController::class, 'getCities'])->name('api.psgc.cities');
        Route::get('/barangays/{cityCode}', [SystemAdminController::class, 'getBarangays'])->name('api.psgc.barangays');
    });

    // Evacuation Management
    Route::prefix('evacuation')->group(function () {
        Route::get('/', [EvacuationManagementController::class, 'showDashboard'])->name('components.evacuation_management.evacuation-dashboard');
        Route::get('/add', [EvacuationManagementController::class, 'showAddForm'])->name('components.evacuation_management.add-evacuation-center');
        Route::post('/store', [EvacuationManagementController::class, 'store'])->name('components.evacuation_management.store');
        Route::get('/{id}/edit', [EvacuationManagementController::class, 'showEditForm'])->name('components.evacuation_management.edit-evacuation-center');
        Route::put('/{id}', [EvacuationManagementController::class, 'update'])->name('components.evacuation_management.update');
        Route::patch('/{id}/status', [EvacuationManagementController::class, 'updateStatus'])->name('components.evacuation_management.update-status');
        Route::delete('/{id}', [EvacuationManagementController::class, 'destroy'])->name('components.evacuation_management.destroy');
        Route::get('/{id}', [EvacuationManagementController::class, 'show'])->name('components.evacuation_management.view-map');
    });


    // Centers List
    Route::get('/centers/{type?}', [EvacuationManagementController::class, 'centersList'])
        ->name('components.evacuation_management.centers-list')
        ->where('type', 'all|active|inactive|maintenance')
        ->defaults('type', 'all');

    // User Management (Barangay Users for barangay staff, Mobile Users for super admin)
    Route::prefix('user-management')->group(function () {
        Route::get('/', [UserController::class, 'index'])->name('components.user-management');
        Route::get('/search', [UserController::class, 'search'])->name('components.user-management.search');
        Route::get('/{id}', [UserController::class, 'view'])->name('components.user.view');
        Route::patch('/{id}/verify', [UserController::class, 'verify'])->name('components.user.verify');
        Route::patch('/{id}/deactivate', [UserController::class, 'deactivate'])->name('users.deactivate');
        Route::delete('/{id}', [UserController::class, 'destroy'])->name('users.destroy');
        Route::get('/{id}/edit', [UserController::class, 'edit'])->name('components.user-management.edit');
        Route::put('/{id}', [UserController::class, 'update'])->name('components.user-management.update');

        // User Registration (SuperAdmin only)
        Route::post('/register-user', [UserController::class, 'registerUser'])
            ->middleware([CheckRole::class . ':super_admin'])
            ->name('user-management.register');

        // Chairman User Management Requests
        Route::prefix('chairman-requests')->group(function () {
            Route::get('/', [\App\Http\Controllers\UserManagementRequestController::class, 'index'])
                ->name('user-management.chairman-requests');
            Route::post('/', [\App\Http\Controllers\UserManagementRequestController::class, 'store'])
                ->name('user-management.chairman-requests.store');
            Route::patch('/{id}/cancel', [\App\Http\Controllers\UserManagementRequestController::class, 'cancel'])
                ->name('user-management.chairman-requests.cancel');
        });

        // Chairman Requests View (for chairmen to see their own requests)
        Route::prefix('requests')->group(function () {
            Route::get('/', [\App\Http\Controllers\ChairmanRequestController::class, 'index'])
                ->name('chairman.requests');
            Route::post('/mark-viewed', [\App\Http\Controllers\ChairmanRequestController::class, 'markAsViewed'])
                ->name('chairman.requests.mark-viewed');
            Route::get('/notification-count', [\App\Http\Controllers\ChairmanRequestController::class, 'getNotificationCount'])
                ->name('chairman.requests.notification-count');
        });

    });

    // Profile Management Routes
    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/edit', [UserController::class, 'editProfile'])->name('edit');
        Route::put('/update', [UserController::class, 'updateProfile'])->name('update');
    });

    // Notifications
    Route::prefix('notification')->group(function () {
        Route::get('/', [NotificationController::class, 'index'])->name('components.notification.index');
        Route::get('/create', [NotificationController::class, 'create'])->name('components.notification.create');
        Route::post('/store', [NotificationController::class, 'store'])->name('components.notification.store');
        Route::get('/{id}', [NotificationController::class, 'view'])->name('components.notification.view');
        Route::delete('/{id}', [NotificationController::class, 'destroy'])->name('components.notification.destroy');

    });

    // Admin Invitations (System Administrator only)
    Route::prefix('admin-invitations')->middleware([CheckRole::class . ':system_admin'])->group(function () {
        Route::get('/registration-form', [AdminInvitationController::class, 'showRegistrationForm'])->name('admin.invitations.registration-form');
        Route::post('/register-admin', [AdminInvitationController::class, 'registerAdmin'])->name('admin.invitations.register');
        Route::get('/invite-form', [AdminInvitationController::class, 'showInviteForm'])->name('admin.invitations.invite-form');
        Route::post('/send-invite', [AdminInvitationController::class, 'sendInvite'])->name('admin.invitations.send-invite');
        Route::get('/', [AdminInvitationController::class, 'index'])->name('admin.invitations.index');
        Route::post('/', [AdminInvitationController::class, 'store'])->name('admin.invitations.store');
        Route::get('/{invitation}', [AdminInvitationController::class, 'show'])->name('admin.invitations.show');
        Route::post('/{invitation}/resend', [AdminInvitationController::class, 'resend'])->name('admin.invitations.resend');
        Route::delete('/{invitation}', [AdminInvitationController::class, 'cancel'])->name('admin.invitations.cancel');
        Route::post('/cleanup-expired', [AdminInvitationController::class, 'cleanupExpired'])->name('admin.invitations.cleanup');
    });

    // Admin Registration Data (System Administrator only)
    Route::get('/admin-registration-data', [DashboardController::class, 'getAdminRegistrationData'])
        ->middleware([CheckRole::class . ':system_admin'])
        ->name('admin.registration.data');

    // Email Notifications (System Administrator only)
    Route::prefix('email-notifications')->middleware([CheckRole::class . ':system_admin'])->group(function () {

        Route::post('/notify-all-admins', [EmailNotificationController::class, 'notifyAllAdmins'])->name('email.notify.all');
        Route::post('/notify-barangay-admins', [EmailNotificationController::class, 'notifyBarangayAdmins'])->name('email.notify.barangay');
        Route::post('/notify-user', [EmailNotificationController::class, 'notifyUser'])->name('email.notify.user');
        Route::get('/admin-users', [EmailNotificationController::class, 'getAdminUsers'])->name('email.admin-users');
        Route::get('/barangays', [EmailNotificationController::class, 'getBarangays'])->name('email.barangays');
        Route::post('/test-config', [EmailNotificationController::class, 'testEmailConfig'])->name('email.test-config');
    });

    // Mapping System
    Route::prefix('mapping')->group(function () {
        Route::get('/', [MappingSystemController::class, 'index'])->name('map');
        Route::post('/store', [MappingSystemController::class, 'store'])->name('mapping.store');
        Route::post('/store-batch', [MappingSystemController::class, 'storeBatch'])->name('mapping.store-batch');
        Route::delete('/{id}', [MappingSystemController::class, 'destroy'])->name('mapping.destroy');
        Route::get('/search', [MappingSystemController::class, 'search'])->name('mapping.search');
    });

    // API Routes
    Route::get('/api/evacuation-centers/{id}', [EvacuationManagementController::class, 'getCenterDetails'])->name('api.evacuation-centers.show');

    // SuperAdmin routes
    Route::prefix('superadmin')->name('superadmin.')->middleware(['auth', CheckRole::class . ':super_admin'])->group(function () {
        Route::get('/dashboard', [SuperAdminController::class, 'dashboard'])->name('dashboard');


        // Admin User Management routes - CDRRMC can only view, not create/modify
        Route::get('/admin-users', [SuperAdminController::class, 'adminUsers'])->name('admin-users');
        // Route::get('/admin-users/create', [SuperAdminController::class, 'createAdminUser'])->name('create-admin-user');
        // Route::post('/admin-users', [SuperAdminController::class, 'storeAdminUser'])->name('store-admin-user');
        // Route::post('/check-email', [SuperAdminController::class, 'checkEmail'])->name('check-email');
        // Route::patch('/admin-users/{id}/deactivate', [SuperAdminController::class, 'deactivateAdminUser'])->name('deactivate-admin-user');
        // Route::patch('/admin-users/{id}/reactivate', [SuperAdminController::class, 'reactivateAdminUser'])->name('reactivate-admin-user');
        // Route::delete('/admin-users/{id}', [SuperAdminController::class, 'deleteAdminUser'])->name('delete-admin-user');

        // Chairman User Management Requests - REMOVED
        // Route::get('/user-management-requests', [SuperAdminController::class, 'userManagementRequests'])->name('user-management-requests');
        // Route::patch('/user-management-requests/{id}/review', [SuperAdminController::class, 'reviewUserManagementRequest'])->name('review-user-management-request');
        // Route::get('/chairman-requests-notification-count', [SuperAdminController::class, 'getChairmanRequestsNotificationCount'])->name('chairman-requests-notification-count');
    });
});