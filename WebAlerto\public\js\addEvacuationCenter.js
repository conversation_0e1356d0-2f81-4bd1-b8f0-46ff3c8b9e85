function populateLocationFields(data) {
    // Normalize and clean up the data to prevent duplicates
    const cleanData = {
        building_name: (data.building_name || '').trim(),
        street: (data.street || '').trim(),
        barangay: (data.barangay || '').trim(),
        city: (data.city || '').trim(),
        province: (data.province || '').trim(),
        postal_code: (data.postal_code || '').trim()
    };

    // Helper function to check if a value is a substring of another (case-insensitive)
    const isSubstringOf = (value1, value2) => {
        if (!value1 || !value2) return false;
        const v1 = value1.toLowerCase().trim();
        const v2 = value2.toLowerCase().trim();
        return v1.includes(v2) || v2.includes(v1);
    };

    // Helper function to check if a value is already represented in existing parts
    const isAlreadyRepresented = (value, existingParts) => {
        if (!value) return true;
        return existingParts.some(part => isSubstringOf(value, part));
    };

    // Build address parts intelligently to avoid duplication
    let addressParts = [];

    // Start with the most specific information
    if (cleanData.building_name) {
        addressParts.push(cleanData.building_name);
    }

    // Add street only if it's not already represented in building name
    if (cleanData.street && !isAlreadyRepresented(cleanData.street, addressParts)) {
        addressParts.push(cleanData.street);
    }

    // Add barangay only if it's not already represented
    if (cleanData.barangay && !isAlreadyRepresented(cleanData.barangay, addressParts)) {
        addressParts.push(cleanData.barangay);
    }

    // Add city only if it's not already represented
    if (cleanData.city && !isAlreadyRepresented(cleanData.city, addressParts)) {
        addressParts.push(cleanData.city);
    }

    // Add province only if it's not already represented
    if (cleanData.province && !isAlreadyRepresented(cleanData.province, addressParts)) {
        addressParts.push(cleanData.province);
    }

    // Create a clean, concise address display
    const displayAddress = addressParts.length > 0
        ? addressParts.join(', ')
        : data.full_address || `Lat: ${document.getElementById('latitude').value}, Lng: ${document.getElementById('longitude').value}`;

    // Update the display
    document.getElementById('selectedAddress').textContent = displayAddress;

    // Store building name in a hidden field for form submission
    const buildingNameField = document.getElementById('building_name');
    if (buildingNameField) {
        buildingNameField.value = data.building_name || '';
    }

    // Update form fields with original data (not cleaned, to preserve exact values)
    document.getElementById('street_name').value = data.street || '';
    document.getElementById('barangay').value = data.barangay || '';
    document.getElementById('city').value = data.city || '';
    document.getElementById('province').value = data.province || '';
    document.getElementById('postal_code').value = data.postal_code || '';

    const provinceWarning = document.getElementById('provinceWarning');
    if (provinceWarning) {
        provinceWarning.classList.toggle('hidden', !!data.province);
    }
}