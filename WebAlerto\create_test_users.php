<?php

require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Models\Evacuation;
use Illuminate\Support\Facades\Hash;

try {
    echo "Creating test users and evacuation centers for Dalaguete...\n\n";

    // Create CDRRMC user for Dalaguete
    $cdrrmc = User::updateOrCreate(
        ['email' => '<EMAIL>'],
        [
            'first_name' => 'CDRRMC',
            'middle_name' => '',
            'last_name' => 'Dalaguete',
            'password' => Hash::make('password123'),
            'role' => 'super_admin',
            'position' => 'CDRRMC Chairperson',
            'city' => '072222000', // Dalaguete PSGC code
            'barangay' => null,
            'status' => 'Active',
            'email_verified_at' => now()
        ]
    );

    echo "✅ CDRRMC user: " . $cdrrmc->email . "\n";

    // Create some BDRRMC users for Dalaguete barangays
    $dalagueteBarangays = [
        'Ablayan',
        'Babayongan',
        'Balud',
        'Banhigan',
        'Bulak'
    ];

    foreach ($dalagueteBarangays as $index => $barangay) {
        $bdrrmc = User::updateOrCreate(
            ['email' => strtolower($barangay) . '@dalaguete.com'],
            [
                'first_name' => 'BDRRMC',
                'middle_name' => '',
                'last_name' => $barangay,
                'password' => Hash::make('password123'),
                'role' => 'admin',
                'position' => 'BDRRMC Coordinator',
                'city' => '072222000', // Dalaguete PSGC code
                'barangay' => $barangay,
                'status' => 'Active',
                'email_verified_at' => now()
            ]
        );

        echo "✅ BDRRMC user: " . $bdrrmc->email . " (Barangay: $barangay)\n";

        // Create evacuation center for this barangay
        $center = Evacuation::updateOrCreate(
            ['name' => $barangay . ' Evacuation Center'],
            [
                'building_name' => $barangay . ' Community Center',
                'street_name' => 'Main Street',
                'barangay' => $barangay,
                'city' => '072222000',
                'province' => 'Cebu',
                'latitude' => 9.5 + ($index * 0.01), // Sample coordinates
                'longitude' => 123.3 + ($index * 0.01),
                'capacity' => 100 + ($index * 50),
                'status' => 'Active',
                'disaster_type' => ['Flood', 'Typhoon'],
                'contact' => '09' . str_pad($index + 1, 9, '0', STR_PAD_LEFT),
                'created_by' => $bdrrmc->id
            ]
        );

        echo "   📍 Created evacuation center: " . $center->name . "\n";
    }

    echo "\n🎉 Test users and evacuation centers created successfully!\n";
    echo "📧 CDRRMC Login: <EMAIL> / password123\n";
    echo "📧 BDRRMC Login: <EMAIL> / password123 (and others)\n";
    echo "🏢 Created " . count($dalagueteBarangays) . " evacuation centers\n";

} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "📋 Stack trace: " . $e->getTraceAsString() . "\n";
}
