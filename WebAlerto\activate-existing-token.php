<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\DeviceToken;
use App\Models\Notification;

echo "=== Activate Existing Token Test ===\n\n";

try {
    // Find the inactive token
    $inactiveToken = DeviceToken::where('is_active', false)->first();
    
    if (!$inactiveToken) {
        echo "❌ No inactive token found\n";
        exit(1);
    }
    
    echo "📱 Found inactive token:\n";
    echo "   ID: {$inactiveToken->id}\n";
    echo "   Device Type: {$inactiveToken->device_type}\n";
    echo "   Created: {$inactiveToken->created_at}\n";
    echo "   Token: " . substr($inactiveToken->token, 0, 20) . "...\n\n";
    
    // Activate the token
    $inactiveToken->is_active = true;
    $inactiveToken->last_used_at = now();
    $inactiveToken->save();
    
    echo "✅ Token activated successfully!\n\n";
    
    // Check active tokens now
    $activeTokens = DeviceToken::where('is_active', true)->count();
    echo "📊 Active tokens: $activeTokens\n\n";
    
    // Check pending notifications
    $pendingNotifications = Notification::where('sent', false)->count();
    echo "📧 Pending notifications: $pendingNotifications\n\n";
    
    if ($pendingNotifications > 0) {
        echo "🚀 Ready to send notifications!\n";
        echo "   Run: D:\\AlertoCapstone\\php\\php.exe send-pending-notifications.php\n\n";
    }
    
    echo "=== Test Complete ===\n";
    echo "Now check your mobile device for notifications!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
