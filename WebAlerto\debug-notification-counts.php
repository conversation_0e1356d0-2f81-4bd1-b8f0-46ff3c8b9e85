<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\DeviceToken;
use App\Models\Notification;

echo "=== Database Investigation ===\n\n";

try {
    // Check notifications
    echo "📧 NOTIFICATION COUNTS:\n";
    $totalNotifications = Notification::count();
    $sentNotifications = Notification::where('sent', true)->count();
    $pendingNotifications = Notification::where('sent', false)->count();
    
    echo "   Total: $totalNotifications\n";
    echo "   Sent: $sentNotifications\n";
    echo "   Pending: $pendingNotifications\n\n";
    
    // Show recent pending notifications
    echo "📋 PENDING NOTIFICATIONS (Last 15):\n";
    $pending = Notification::where('sent', false)
        ->orderBy('created_at', 'desc')
        ->limit(15)
        ->get(['id', 'title', 'category', 'priority_level', 'created_at', 'sent']);
    
    foreach ($pending as $notification) {
        $status = $notification->sent ? 'Sent' : 'Pending';
        echo "   ID: {$notification->id} | {$notification->title} | {$notification->category} | {$notification->priority_level} | {$notification->created_at} | $status\n";
    }
    
    echo "\n📱 DEVICE TOKEN COUNTS:\n";
    $totalTokens = DeviceToken::count();
    $activeTokens = DeviceToken::where('is_active', true)->count();
    $inactiveTokens = DeviceToken::where('is_active', false)->count();
    
    echo "   Total: $totalTokens\n";
    echo "   Active: $activeTokens\n";
    echo "   Inactive: $inactiveTokens\n\n";
    
    // Show all tokens
    echo "🔍 ALL DEVICE TOKENS:\n";
    $allTokens = DeviceToken::orderBy('created_at', 'desc')->get();
    
    foreach ($allTokens as $token) {
        $status = $token->is_active ? '✅ Active' : '❌ Inactive';
        $userId = $token->user_id ?? 'null';
        $tokenPreview = substr($token->token, 0, 30) . '...';
        echo "   ID: {$token->id} | $status | User: $userId | {$token->device_type} | Created: {$token->created_at}\n";
        echo "      Token: $tokenPreview\n";
    }
    
    echo "\n🔧 MOBILE APP CONNECTIVITY TEST:\n";
    
    // Test if mobile app can reach the API
    $endpoints = [
        'device-token' => 'http://192.168.112.210:8000/api/device-token',
        'notifications' => 'http://192.168.112.210:8000/api/notifications',
        'health' => 'http://192.168.112.210:8000/up'
    ];
    
    foreach ($endpoints as $name => $url) {
        echo "Testing $name endpoint: ";
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 3,
                'method' => 'GET'
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        if ($response !== false) {
            echo "✅ Accessible\n";
        } else {
            echo "❌ Not accessible\n";
        }
    }
    
    echo "\n📊 SUMMARY:\n";
    echo "   - Database shows: $pendingNotifications pending notifications\n";
    echo "   - Admin dashboard shows: 13 pending notifications\n";
    echo "   - Active tokens: $activeTokens\n";
    echo "   - Backend server: Running\n\n";
    
    if ($pendingNotifications != 13) {
        echo "⚠️  DISCREPANCY FOUND!\n";
        echo "   Database count ($pendingNotifications) != Dashboard count (13)\n";
        echo "   This suggests a filtering issue in the admin interface\n\n";
    }
    
    if ($activeTokens == 0) {
        echo "🚨 NO ACTIVE TOKENS!\n";
        echo "   Mobile app FCM registration is failing\n";
        echo "   Need to check mobile app logs and connectivity\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "=== Investigation Complete ===\n";
