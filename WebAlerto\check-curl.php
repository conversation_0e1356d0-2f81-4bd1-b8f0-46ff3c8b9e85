<?php
// Check cURL configuration
echo "Checking cURL configuration...\n";

if (!function_exists('curl_version')) {
    echo "cURL is not installed or not enabled in PHP.\n";
    exit(1);
}

$curlInfo = curl_version();
echo "cURL Version: " . $curlInfo['version'] . "\n";
echo "SSL Version: " . $curlInfo['ssl_version'] . "\n";
echo "SSL Support: " . ($curlInfo['features'] & CURL_VERSION_SSL ? 'Yes' : 'No') . "\n";

// Check if we can access Google's OAuth endpoint
echo "\nTesting connection to Google OAuth endpoint...\n";
$ch = curl_init('https://oauth2.googleapis.com/token');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_NOBODY, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

// Important: Set these options to verify SSL certificates
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);

// Execute the request
$response = curl_exec($ch);
$error = curl_error($ch);
$info = curl_getinfo($ch);
curl_close($ch);

if ($response === false) {
    echo "Error connecting to Google OAuth endpoint: " . $error . "\n";
    
    // Try with SSL verification disabled (for testing only)
    echo "\nTrying again with SSL verification disabled (NOT RECOMMENDED FOR PRODUCTION)...\n";
    $ch = curl_init('https://oauth2.googleapis.com/token');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    
    $response = curl_exec($ch);
    $error = curl_error($ch);
    $info = curl_getinfo($ch);
    curl_close($ch);
    
    if ($response === false) {
        echo "Still failed with SSL verification disabled: " . $error . "\n";
    } else {
        echo "Connection successful with SSL verification disabled.\n";
        echo "HTTP Status Code: " . $info['http_code'] . "\n";
        echo "This indicates an SSL certificate verification issue.\n";
    }
} else {
    echo "Connection successful!\n";
    echo "HTTP Status Code: " . $info['http_code'] . "\n";
}

// Check if cacert.pem exists
echo "\nChecking for cacert.pem...\n";
$phpInfo = phpinfo(INFO_GENERAL);
preg_match('/Loaded Configuration File => (.*)/', $phpInfo, $matches);
$phpIniPath = isset($matches[1]) ? $matches[1] : 'Unknown';
echo "PHP INI Path: " . $phpIniPath . "\n";

// Try to find curl.cainfo setting
preg_match('/curl\.cainfo => (.*)/', $phpInfo, $matches);
$curlCaInfo = isset($matches[1]) ? $matches[1] : 'Not set';
echo "curl.cainfo: " . $curlCaInfo . "\n";

// Suggest solutions
echo "\nPossible solutions:\n";
echo "1. Download the latest cacert.pem from https://curl.se/ca/cacert.pem\n";
echo "2. Save it to a location on your server\n";
echo "3. Update your php.ini with:\n";
echo "   curl.cainfo = \"path/to/cacert.pem\"\n";
echo "   openssl.cafile = \"path/to/cacert.pem\"\n";
echo "4. Restart your web server\n";