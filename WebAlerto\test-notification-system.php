<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\DeviceToken;
use App\Models\Notification;
use App\Models\User;
use App\Services\FCMService;
use Illuminate\Support\Facades\Log;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🚀 WebAlerto Notification System Test Suite\n";
echo "==========================================\n\n";

class NotificationTester
{
    private $fcmService;
    private $testResults = [];
    
    public function __construct()
    {
        $this->fcmService = app(FCMService::class);
    }
    
    public function runAllTests()
    {
        echo "Starting comprehensive notification system tests...\n\n";
        
        $this->testDatabaseConnection();
        $this->testNotificationModel();
        $this->testBarangayHandling();
        $this->testDeviceTokens();
        $this->testFCMService();
        $this->testNotificationCreation();
        $this->testUserRoleBasedNotifications();
        
        $this->displayResults();
    }
    
    private function testDatabaseConnection()
    {
        echo "📋 Test 1: Database Connection\n";
        try {
            $count = Notification::count();
            echo "✅ Database connected successfully. Found {$count} notifications.\n";
            $this->testResults['database'] = true;
        } catch (Exception $e) {
            echo "❌ Database connection failed: " . $e->getMessage() . "\n";
            $this->testResults['database'] = false;
        }
        echo "\n";
    }
    
    private function testNotificationModel()
    {
        echo "📋 Test 2: Notification Model Structure\n";
        try {
            // Test creating a notification with all required fields
            $notification = new Notification([
                'title' => 'Test Notification',
                'message' => 'Test message',
                'category' => 'typhoon',
                'severity' => 'high',
                'barangay' => 'Test Barangay',
                'user_id' => 1,
                'sent' => false
            ]);
            
            // Check if all required fields are fillable
            $fillable = $notification->getFillable();
            $requiredFields = ['title', 'message', 'category', 'severity', 'barangay', 'user_id', 'sent'];
            
            foreach ($requiredFields as $field) {
                if (!in_array($field, $fillable)) {
                    throw new Exception("Field '{$field}' is not fillable");
                }
            }
            
            // Check that affected_areas is NOT in fillable (should be removed)
            if (in_array('affected_areas', $fillable)) {
                throw new Exception("affected_areas field should have been removed");
            }
            
            echo "✅ Notification model structure is correct\n";
            echo "✅ affected_areas field successfully removed\n";
            $this->testResults['model'] = true;
        } catch (Exception $e) {
            echo "❌ Notification model test failed: " . $e->getMessage() . "\n";
            $this->testResults['model'] = false;
        }
        echo "\n";
    }
    
    private function testBarangayHandling()
    {
        echo "📋 Test 3: Barangay Field Handling\n";
        try {
            // Test creating notification with barangay
            $notification = Notification::create([
                'title' => 'Barangay Test',
                'message' => 'Testing barangay field',
                'category' => 'test',
                'severity' => 'low',
                'barangay' => 'Test Barangay',
                'user_id' => 1,
                'sent' => false
            ]);
            
            if ($notification->barangay !== 'Test Barangay') {
                throw new Exception("Barangay field not saved correctly");
            }
            
            echo "✅ Barangay field saves correctly\n";
            echo "✅ No more 'barangay cannot be null' errors\n";
            
            // Clean up test notification
            $notification->delete();
            
            $this->testResults['barangay'] = true;
        } catch (Exception $e) {
            echo "❌ Barangay handling test failed: " . $e->getMessage() . "\n";
            $this->testResults['barangay'] = false;
        }
        echo "\n";
    }
    
    private function testDeviceTokens()
    {
        echo "📋 Test 4: Device Token Management\n";
        try {
            $activeTokens = DeviceToken::where('is_active', true)->count();
            $totalTokens = DeviceToken::count();
            
            echo "📱 Total device tokens: {$totalTokens}\n";
            echo "📱 Active device tokens: {$activeTokens}\n";
            
            if ($activeTokens > 0) {
                echo "✅ Device tokens available for testing\n";
                $this->testResults['tokens'] = true;
            } else {
                echo "⚠️  No active device tokens found. FCM sending will be limited.\n";
                $this->testResults['tokens'] = false;
            }
        } catch (Exception $e) {
            echo "❌ Device token test failed: " . $e->getMessage() . "\n";
            $this->testResults['tokens'] = false;
        }
        echo "\n";
    }
    
    private function testFCMService()
    {
        echo "📋 Test 5: FCM Service Initialization\n";
        try {
            // Test FCM service initialization
            $testNotification = new Notification([
                'title' => 'FCM Test',
                'message' => 'Testing FCM service',
                'category' => 'test',
                'severity' => 'low',
                'barangay' => 'Test Area',
                'sent' => false
            ]);
            
            // This should not throw an exception even with empty token array
            $result = $this->fcmService->sendNotification($testNotification, []);
            
            echo "✅ FCM Service initializes without errors\n";
            $this->testResults['fcm_service'] = true;
        } catch (Exception $e) {
            echo "❌ FCM Service test failed: " . $e->getMessage() . "\n";
            $this->testResults['fcm_service'] = false;
        }
        echo "\n";
    }
    
    private function testNotificationCreation()
    {
        echo "📋 Test 6: End-to-End Notification Creation\n";
        try {
            // Create a complete notification
            $notification = Notification::create([
                'title' => 'System Test Alert',
                'message' => 'This is a comprehensive system test notification created at ' . now()->format('Y-m-d H:i:s'),
                'category' => 'typhoon',
                'severity' => 'medium',
                'barangay' => 'System Test Barangay',
                'user_id' => 1,
                'sent' => false
            ]);
            
            echo "✅ Notification created successfully (ID: {$notification->id})\n";
            echo "📝 Title: {$notification->title}\n";
            echo "📝 Category: {$notification->category}\n";
            echo "📝 Severity: {$notification->severity}\n";
            echo "📝 Barangay: {$notification->barangay}\n";
            echo "📝 Sent: " . ($notification->sent ? 'Yes' : 'No') . "\n";
            
            $this->testResults['creation'] = true;
            
            // Keep this notification for manual verification
            echo "💾 Test notification saved for manual verification\n";
            
        } catch (Exception $e) {
            echo "❌ Notification creation test failed: " . $e->getMessage() . "\n";
            $this->testResults['creation'] = false;
        }
        echo "\n";
    }
    
    private function testUserRoleBasedNotifications()
    {
        echo "📋 Test 7: User Role-Based Notification Logic\n";
        try {
            // Test different user scenarios
            $users = User::limit(3)->get();
            
            if ($users->count() > 0) {
                foreach ($users as $user) {
                    echo "👤 Testing user: {$user->first_name} {$user->last_name} (Role: {$user->role})\n";
                    
                    if ($user->hasRole('super_admin')) {
                        echo "   ✅ Super admin can select any barangay\n";
                    } else {
                        echo "   ✅ Regular user restricted to barangay: {$user->barangay}\n";
                    }
                }
                $this->testResults['user_roles'] = true;
            } else {
                echo "⚠️  No users found in database\n";
                $this->testResults['user_roles'] = false;
            }
        } catch (Exception $e) {
            echo "❌ User role test failed: " . $e->getMessage() . "\n";
            $this->testResults['user_roles'] = false;
        }
        echo "\n";
    }
    
    private function displayResults()
    {
        echo "🏁 Test Results Summary\n";
        echo "======================\n";
        
        $passed = 0;
        $total = count($this->testResults);
        
        foreach ($this->testResults as $test => $result) {
            $status = $result ? "✅ PASS" : "❌ FAIL";
            echo sprintf("%-20s: %s\n", ucfirst(str_replace('_', ' ', $test)), $status);
            if ($result) $passed++;
        }
        
        echo "\n";
        echo "Overall Result: {$passed}/{$total} tests passed\n";
        
        if ($passed === $total) {
            echo "🎉 All tests passed! Notification system is working correctly.\n";
        } else {
            echo "⚠️  Some tests failed. Please review the issues above.\n";
        }
        
        echo "\n";
        echo "Next Steps:\n";
        echo "- Test the web interface at /notification/create\n";
        echo "- Register a real FCM token for mobile testing\n";
        echo "- Send a test notification through the web interface\n";
    }
}

// Run the tests
$tester = new NotificationTester();
$tester->runAllTests();

echo "\n";
