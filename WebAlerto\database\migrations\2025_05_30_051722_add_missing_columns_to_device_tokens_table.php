<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('device_tokens', function (Blueprint $table) {
            $table->string('project_id')->nullable()->after('device_name');
            $table->timestamp('last_used_at')->nullable()->after('is_active');
            $table->timestamp('deactivated_at')->nullable()->after('last_used_at');

            // Make user_id nullable since some tokens might not be associated with users initially
            $table->foreignId('user_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('device_tokens', function (Blueprint $table) {
            $table->dropColumn(['project_id', 'last_used_at', 'deactivated_at']);
        });
    }
};
