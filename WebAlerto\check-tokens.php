<?php
require __DIR__.'/vendor/autoload.php';

// Get the application instance
$app = require_once __DIR__.'/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// Check device tokens
$tokens = App\Models\DeviceToken::all();
echo "Found " . $tokens->count() . " device tokens:\n";

foreach ($tokens as $token) {
    echo "ID: " . $token->id . "\n";
    echo "Token: " . substr($token->token, 0, 20) . "...\n";
    echo "User ID: " . ($token->user_id ?? 'null') . "\n";
    echo "Device Type: " . $token->device_type . "\n";
    echo "Is Active: " . ($token->is_active ? 'Yes' : 'No') . "\n";
    echo "Created At: " . $token->created_at . "\n";
    echo "-------------------\n";
}

// Check notifications
$notifications = App\Models\Notification::all();
echo "\nFound " . $notifications->count() . " notifications:\n";

foreach ($notifications as $notification) {
    echo "ID: " . $notification->id . "\n";
    echo "Title: " . $notification->title . "\n";
    echo "Message: " . $notification->message . "\n";
    echo "Category: " . $notification->category . "\n";
    echo "Sent: " . ($notification->sent ? 'Yes' : 'No') . "\n";
    echo "Created At: " . $notification->created_at . "\n";
    echo "-------------------\n";
}