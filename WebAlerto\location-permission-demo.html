<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Location Permission Modal - Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
    </style>
</head>
<body class="flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full">
        <div class="text-center mb-6">
            <div class="text-4xl mb-4">🗺️</div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">Location Permission Demo</h1>
            <p class="text-gray-600">Test the enhanced location permission modal for routing features</p>
        </div>
        
        <div class="space-y-4">
            <button onclick="testPermissionDenied()" class="w-full bg-red-500 hover:bg-red-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200">
                <i class="fas fa-ban mr-2"></i>
                Test Permission Denied
            </button>
            
            <button onclick="testLocationUnavailable()" class="w-full bg-orange-500 hover:bg-orange-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200">
                <i class="fas fa-satellite-dish mr-2"></i>
                Test Location Unavailable
            </button>
            
            <button onclick="testTimeout()" class="w-full bg-yellow-500 hover:bg-yellow-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200">
                <i class="fas fa-clock mr-2"></i>
                Test Timeout
            </button>
            
            <button onclick="testNotSupported()" class="w-full bg-gray-500 hover:bg-gray-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200">
                <i class="fas fa-times-circle mr-2"></i>
                Test Not Supported
            </button>
            
            <button onclick="testActualLocation()" class="w-full bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200">
                <i class="fas fa-location-arrow mr-2"></i>
                Test Real Location Request
            </button>
        </div>
        
        <div class="mt-6 p-4 bg-blue-50 rounded-lg">
            <p class="text-sm text-blue-800">
                <i class="fas fa-info-circle mr-2"></i>
                This demo shows how the enhanced modal explains routing features and guides users to enable location access.
            </p>
        </div>
    </div>

    <script>
        // Test functions for different error scenarios
        function testPermissionDenied() {
            showLocationPermissionModal({ code: 1, message: 'User denied the request for Geolocation.' });
        }
        
        function testLocationUnavailable() {
            showLocationPermissionModal({ code: 2, message: 'Location information is unavailable.' });
        }
        
        function testTimeout() {
            showLocationPermissionModal({ code: 3, message: 'The request to get user location timed out.' });
        }
        
        function testNotSupported() {
            showLocationPermissionModal({ code: 0, message: 'Geolocation is not supported by this browser.' });
        }
        
        function testActualLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        alert(`Location found! Latitude: ${position.coords.latitude}, Longitude: ${position.coords.longitude}`);
                    },
                    function(error) {
                        showLocationPermissionModal(error);
                    },
                    {
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 300000
                    }
                );
            } else {
                showLocationPermissionModal({ code: 0, message: 'Geolocation is not supported by this browser.' });
            }
        }

        // Enhanced Location Permission Modal
        function showLocationPermissionModal(error) {
            // Remove any existing modal
            const existingModal = document.getElementById('locationPermissionModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Determine error message and icon based on error type
            let title, message, icon, iconColor;
            
            if (error.code === 1) {
                title = 'Location Permission Required';
                message = 'To enable routing features and show directions to evacuation centers, we need access to your location. Please allow location access when prompted by your browser.';
                icon = '📍';
                iconColor = 'text-blue-500';
            } else if (error.code === 2) {
                title = 'Location Unavailable';
                message = 'Your location is currently unavailable. Please check your device\'s location settings and ensure GPS is enabled to use routing features.';
                icon = '🛰️';
                iconColor = 'text-orange-500';
            } else if (error.code === 3) {
                title = 'Location Request Timeout';
                message = 'The location request timed out. Please try again to enable routing features and get directions to evacuation centers.';
                icon = '⏱️';
                iconColor = 'text-yellow-500';
            } else {
                title = 'Location Not Supported';
                message = 'Your browser doesn\'t support location services. Routing features will not be available. Please try using a modern browser like Chrome, Firefox, or Safari.';
                icon = '🚫';
                iconColor = 'text-red-500';
            }

            // Create modal HTML
            const modalHTML = `
                <div id="locationPermissionModal" class="fixed inset-0 z-50 flex items-center justify-center p-4" style="background-color: rgba(0, 0, 0, 0.5);">
                    <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all">
                        <!-- Header -->
                        <div class="p-6 text-center border-b border-gray-100">
                            <div class="text-4xl mb-3">${icon}</div>
                            <h3 class="text-xl font-bold text-gray-900 mb-2">${title}</h3>
                            <p class="text-gray-600 text-sm leading-relaxed">${message}</p>
                        </div>
                        
                        <!-- Features Section -->
                        <div class="p-6 bg-gradient-to-br from-blue-50 to-indigo-50">
                            <h4 class="font-semibold text-gray-800 mb-3 flex items-center">
                                <span class="text-blue-500 mr-2">🗺️</span>
                                Routing Features Include:
                            </h4>
                            <ul class="space-y-2 text-sm text-gray-700">
                                <li class="flex items-center">
                                    <span class="text-green-500 mr-2">✓</span>
                                    Turn-by-turn directions to evacuation centers
                                </li>
                                <li class="flex items-center">
                                    <span class="text-green-500 mr-2">✓</span>
                                    Multiple transport modes (walking, driving, cycling)
                                </li>
                                <li class="flex items-center">
                                    <span class="text-green-500 mr-2">✓</span>
                                    Real-time distance and travel time estimates
                                </li>
                                <li class="flex items-center">
                                    <span class="text-green-500 mr-2">✓</span>
                                    Find nearest evacuation centers to your location
                                </li>
                            </ul>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="p-6 flex justify-center">
                            <button onclick="closeLocationModal()" class="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg">
                                OK
                            </button>
                        </div>
                        
                        <!-- Help Text -->
                        <div class="px-6 pb-6">
                            <div class="bg-amber-50 border border-amber-200 rounded-lg p-3">
                                <p class="text-xs text-amber-800 flex items-start">
                                    <span class="text-amber-500 mr-2 mt-0.5">💡</span>
                                    <span>
                                        <strong>Need help?</strong> Look for a location icon in your browser's address bar and click "Allow" when prompted.
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Add modal to page
            document.body.insertAdjacentHTML('beforeend', modalHTML);
            
            // Add animation
            setTimeout(() => {
                const modal = document.getElementById('locationPermissionModal');
                if (modal) {
                    modal.querySelector('.bg-white').style.transform = 'scale(1)';
                }
            }, 10);
        }



        // Close location modal
        function closeLocationModal() {
            const modal = document.getElementById('locationPermissionModal');
            if (modal) {
                modal.style.opacity = '0';
                setTimeout(() => {
                    modal.remove();
                }, 200);
            }
        }
    </script>
</body>
</html>
