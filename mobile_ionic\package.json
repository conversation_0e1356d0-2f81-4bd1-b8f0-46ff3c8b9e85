{"name": "alerto", "version": "0.0.1", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "^18.2.0", "@angular/common": "^18.2.0", "@angular/compiler": "^18.2.0", "@angular/core": "^18.2.0", "@angular/forms": "^18.2.0", "@angular/platform-browser": "^18.2.0", "@angular/platform-browser-dynamic": "^18.2.0", "@angular/router": "^18.2.0", "@awesome-cordova-plugins/core": "^6.16.0", "@awesome-cordova-plugins/fcm": "^6.16.0", "@capacitor-firebase/messaging": "^7.2.0", "@capacitor/android": "7.2.0", "@capacitor/app": "7.0.1", "@capacitor/core": "7.2.0", "@capacitor/filesystem": "^7.1.1", "@capacitor/geolocation": "^7.1.1", "@capacitor/haptics": "7.0.1", "@capacitor/ios": "7.2.0", "@capacitor/keyboard": "7.0.1", "@capacitor/local-notifications": "^7.0.1", "@capacitor/motion": "^7.0.0", "@capacitor/network": "^7.0.1", "@capacitor/screen-orientation": "^7.0.1", "@capacitor/status-bar": "7.0.1", "@ionic/angular": "^8.0.0", "@ionic/storage-angular": "^4.0.0", "@types/leaflet": "^1.9.17", "cordova-plugin-device": "^3.0.0", "firebase": "^11.7.1", "html2canvas": "^1.4.1", "ionicons": "^7.0.0", "leaflet": "^1.9.4", "openrouteservice-js": "^0.4.1", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.0", "@angular-eslint/builder": "^18.0.0", "@angular-eslint/eslint-plugin": "^18.0.0", "@angular-eslint/eslint-plugin-template": "^18.0.0", "@angular-eslint/schematics": "^18.0.0", "@angular-eslint/template-parser": "^18.0.0", "@angular/cli": "^18.2.0", "@angular/compiler-cli": "^18.2.0", "@angular/language-service": "^18.2.0", "@capacitor/cli": "7.2.0", "@ionic/angular-toolkit": "^12.0.0", "@types/cordova": "^11.0.3", "@types/jasmine": "~5.1.0", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "eslint": "^9.16.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsdoc": "^48.2.1", "eslint-plugin-prefer-arrow": "1.2.2", "jasmine-core": "~5.1.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.5.0"}, "description": "Alerto - Evacuation Center Mapping System"}