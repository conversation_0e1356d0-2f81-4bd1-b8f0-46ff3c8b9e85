<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\DeviceToken;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Clearing Invalid FCM Tokens ===\n\n";

try {
    // Get all active tokens
    $activeTokens = DeviceToken::where('is_active', true)->get();
    echo "Found {$activeTokens->count()} active tokens\n\n";

    $invalidCount = 0;
    $validCount = 0;

    foreach ($activeTokens as $token) {
        echo "Checking token ID {$token->id}:\n";
        echo "  Token: " . substr($token->token, 0, 50) . "...\n";
        echo "  Device Type: {$token->device_type}\n";
        echo "  User ID: " . ($token->user_id ?? 'Anonymous') . "\n";
        echo "  Created: {$token->created_at}\n";

        // Check if token looks like a valid FCM token
        // Valid FCM tokens are typically 152+ characters long and contain specific patterns
        if (strlen($token->token) < 100 || 
            strpos($token->token, 'test_') === 0 || 
            !preg_match('/^[A-Za-z0-9_-]+$/', $token->token)) {
            
            echo "  ❌ Token appears invalid - deactivating\n";
            $token->is_active = false;
            $token->save();
            $invalidCount++;
        } else {
            echo "  ✅ Token appears valid\n";
            $validCount++;
        }
        echo "\n";
    }

    echo "=== Summary ===\n";
    echo "Valid tokens: $validCount\n";
    echo "Invalid tokens deactivated: $invalidCount\n";

    if ($invalidCount > 0) {
        echo "\n✅ Invalid tokens have been deactivated.\n";
        echo "Please ensure the mobile app is running and registers a new valid FCM token.\n";
    }

    if ($validCount === 0) {
        echo "\n⚠️  No valid tokens found. To fix this:\n";
        echo "1. Open the mobile app\n";
        echo "2. Ensure it has internet connection\n";
        echo "3. Check that FCM is properly initialized\n";
        echo "4. The app should automatically register a new token\n";
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n=== Complete ===\n";
