export class MapZones {
    constructor(mapManager, severityManager) {
        this.mapManager = mapManager;
        this.severityManager = severityManager;
        this.zones = new Map();
        this.marker = null;
    }

    createZone(lat, lon, radius, severity) {
        const map = this.mapManager.getMap();
        const colors = this.severityManager.getSeverityColor(severity);

        const zone = L.circle([lat, lon], {
            radius,
            color: colors.color,
            fillColor: colors.fillColor,
            fillOpacity: 0.3,
            weight: 2
        }).addTo(map);

        this.zones.set(uniqueKey,layer);
        return zone;
    }

    addAffectedArea(geojson, name, severity) {
        const map = this.mapManager.getMap();
        const colors = this.severityManager.getSeverityColor(severity);

        const layer = L.geoJSON(geojson, {
            style: {
                color: colors.color,
                fillColor: colors.fillColor,
                fillOpacity: 0.3,
                weight: 2
            }
        }).addTo(map);

        this.zones.set(name, layer);
        return layer;
    }

    updateSeverity(severity) {
        this.zones.forEach((zone, name) => {
            this.severityManager.updateLayerStyle(zone, severity);
        });
    }
}

export class SeverityManager {
    constructor() {
        this.severityColors = {
            high: { color: '#ff0000', fillColor: '#ff6666' },
            medium: { color: '#eab308', fillColor: '#fde047' },
            low: { color: '#22c55e', fillColor: '#bbf7d0' }
        };
    }

    getSeverityColor(severity) {
        return this.severityColors[severity] || this.severityColors.high;
    }

    updateLayerStyle(layer, severity) {
        const colors = this.getSeverityColor(severity);
        layer.setStyle({
            color: colors.color,
            fillColor: colors.fillColor
        });
    }
}
