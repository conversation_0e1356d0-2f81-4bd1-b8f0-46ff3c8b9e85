<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('invitations', function (Blueprint $table) {
            $table->string('position')->nullable()->change();
        });
    }

    public function down()
    {
        Schema::table('invitations', function (Blueprint $table) {
            $table->string('position')->nullable(false)->change();
        });
    }
}; 