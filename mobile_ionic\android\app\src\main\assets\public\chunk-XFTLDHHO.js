import{f as r,g as i}from"./chunk-P4NBILQA.js";import{c as a,d as m}from"./chunk-ZOYALB5L.js";import{b as s}from"./chunk-UYQ7EZNZ.js";import"./chunk-BAKMWPBW.js";import{h as n}from"./chunk-B7O3QC5Z.js";var y=()=>{let o=window;o.addEventListener("statusTap",()=>{a(()=>{let c=o.innerWidth,d=o.innerHeight,e=document.elementFromPoint(c/2,d/2);if(!e)return;let t=r(e);t&&new Promise(p=>s(t,p)).then(()=>{m(()=>n(void 0,null,function*(){t.style.setProperty("--overflow","hidden"),yield i(t,300),t.style.removeProperty("--overflow")}))})})})};export{y as startStatusTap};
