
<!-- Header -->
<ion-header [translucent]="true">
  <ion-toolbar color="secondary">
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <div id="all-maps" style="height: 100%; width: 100%;"></div>

  <!-- Map Controls - Three buttons on the right side -->
  <div class="map-controls">
    <!-- Filter Pins Button -->
    <ion-button
      fill="clear"
      class="control-btn"
      (click)="toggleFilterPanel()">
      <img src="assets/allPin.png" alt="Filter Pins" class="control-icon">
    </ion-button>

    <!-- Show All Centers Button -->
    <ion-button
      fill="clear"
      class="control-btn"
      (click)="showAllCenters()">
      <img src="assets/ListOfEvacuationArea.png" alt="All Centers" class="control-icon">
    </ion-button>

    <!-- Download Button -->
    <ion-button
      fill="clear"
      class="control-btn"
      (click)="downloadMap()">
      <img src="assets/downloadForSeeWholeMap.png" alt="Download" class="control-icon">
    </ion-button>
  </div>

  <!-- Real-time Navigation Component -->
  <app-real-time-navigation
    *ngIf="navigationDestination"
    [destination]="navigationDestination"
    [travelMode]="selectedTransportMode === 'walking' ? 'foot-walking' :
                  selectedTransportMode === 'cycling' ? 'cycling-regular' : 'driving-car'"
    [autoStart]="isRealTimeNavigationActive"
    (routeUpdated)="onNavigationRouteUpdated($event)"
    (navigationStopped)="onNavigationStopped()">
  </app-real-time-navigation>

  <!-- All Centers Panel (slides in from right) -->
  <div class="all-centers-panel" [class.show]="showAllCentersPanel">
    <div class="panel-content">
      <div class="panel-header">
        <ion-button fill="clear" class="back-btn" (click)="closeAllCentersPanel()">
          <img src="assets/backIcon.png" alt="Back" class="back-icon">
        </ion-button>
        <div class="header-info">
          <h3>🗺️ All Evacuation Centers</h3>
          <p>{{ centerCounts.total }} centers available</p>
        </div>
      </div>

      <!-- Disaster Type Counts -->
      <div class="disaster-counts">
        <div class="count-row">
          <span class="disaster-icon">🟠</span>
          <span class="disaster-label">Earthquake:</span>
          <span class="disaster-count">{{ centerCounts.earthquake }}</span>
        </div>

        <div class="count-row">
          <span class="disaster-icon">🟢</span>
          <span class="disaster-label">Typhoon:</span>
          <span class="disaster-count">{{ centerCounts.typhoon }}</span>
        </div>

        <div class="count-row">
          <span class="disaster-icon">🔵</span>
          <span class="disaster-label">Flood:</span>
          <span class="disaster-count">{{ centerCounts.flood }}</span>
        </div>

        <div class="count-row">
          <span class="disaster-icon">🔴</span>
          <span class="disaster-label">Fire:</span>
          <span class="disaster-count">{{ centerCounts.fire }}</span>
        </div>

        <div class="count-row">
          <span class="disaster-icon">🟤</span>
          <span class="disaster-label">Landslide:</span>
          <span class="disaster-count">{{ centerCounts.landslide }}</span>
        </div>

        <div class="count-row">
          <span class="disaster-icon">🟣</span>
          <span class="disaster-label">Others:</span>
          <span class="disaster-count">{{ centerCounts.others }}</span>
        </div>

        <div class="count-row">
          <span class="disaster-icon">🔘</span>
          <span class="disaster-label">Multiple:</span>
          <span class="disaster-count">{{ centerCounts.multiple }}</span>
        </div>
      </div>

      <!-- Centers List -->
      <div class="centers-list">
        <div class="center-item"
             *ngFor="let center of evacuationCenters; let i = index"
             (click)="selectCenterFromList(center)">
          <div class="center-info">
            <h4>{{ center.name }}</h4>
            <p class="address">{{ center.address }}</p>
            <div class="center-details">
              <span class="distance" *ngIf="userLocation">
                📍 {{ calculateDistanceInKm(center) }} km away
              </span>
              <span class="capacity">👥 {{ center.capacity || 'N/A' }} capacity</span>
            </div>
          </div>
          <div class="center-actions">
            <ion-icon name="chevron-forward-outline"></ion-icon>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Overlay to close all centers panel -->
  <div class="all-centers-overlay"
       [class.show]="showAllCentersPanel"
       (click)="closeAllCentersPanel()">
  </div>

  <!-- Filter Panel (slides from right) -->
  <div class="filter-panel" [class.show]="showFilterPanel">
    <div class="panel-content">
      <div class="panel-header">
        <ion-button fill="clear" class="back-btn" (click)="closeFilterPanel()">
          <img src="assets/backIcon.png" alt="Back" class="back-icon">
        </ion-button>
        <div class="header-info">
          <h3>🎯 Filter Pins</h3>
          <p>Show/hide pins by disaster type</p>
        </div>
      </div>

      <!-- Filter Options -->
      <div class="filter-options">
        <!-- Show All Button -->
        <div class="filter-option"
             [class.active]="currentFilter === 'all'"
             (click)="applyFilter('all')">
          <div class="filter-icon">
            <img src="assets/allPin.png" alt="All" class="disaster-type-icon">
          </div>
          <div class="filter-info">
            <span class="filter-label">Show All Pins</span>
            <span class="filter-count">{{ centerCounts.total }} centers</span>
          </div>
        </div>

        <!-- Earthquake Filter -->
        <div class="filter-option"
             [class.active]="currentFilter === 'Earthquake'"
             (click)="applyFilter('Earthquake')">
          <div class="filter-icon">
            <img src="assets/forEarthquake.png" alt="Earthquake" class="disaster-type-icon">
          </div>
          <div class="filter-info">
            <span class="filter-label">Earthquake Centers</span>
            <span class="filter-count">{{ centerCounts.earthquake }} centers</span>
          </div>
        </div>

        <!-- Typhoon Filter -->
        <div class="filter-option"
             [class.active]="currentFilter === 'Typhoon'"
             (click)="applyFilter('Typhoon')">
          <div class="filter-icon">
            <img src="assets/forTyphoon.png" alt="Typhoon" class="disaster-type-icon">
          </div>
          <div class="filter-info">
            <span class="filter-label">Typhoon Centers</span>
            <span class="filter-count">{{ centerCounts.typhoon }} centers</span>
          </div>
        </div>

        <!-- Flood Filter -->
        <div class="filter-option"
             [class.active]="currentFilter === 'Flood'"
             (click)="applyFilter('Flood')">
          <div class="filter-icon">
            <img src="assets/forFlood.png" alt="Flood" class="disaster-type-icon">
          </div>
          <div class="filter-info">
            <span class="filter-label">Flood Centers</span>
            <span class="filter-count">{{ centerCounts.flood }} centers</span>
          </div>
        </div>

        <!-- Fire Filter -->
        <div class="filter-option"
             [class.active]="currentFilter === 'Fire'"
             (click)="applyFilter('Fire')">
          <div class="filter-icon">
            <img src="assets/forFire.png" alt="Fire" class="disaster-type-icon">
          </div>
          <div class="filter-info">
            <span class="filter-label">Fire Centers</span>
            <span class="filter-count">{{ centerCounts.fire }} centers</span>
          </div>
        </div>

        <!-- Landslide Filter -->
        <div class="filter-option"
             [class.active]="currentFilter === 'Landslide'"
             (click)="applyFilter('Landslide')">
          <div class="filter-icon">
            <img src="assets/forLandslide.png" alt="Landslide" class="disaster-type-icon">
          </div>
          <div class="filter-info">
            <span class="filter-label">Landslide Centers</span>
            <span class="filter-count">{{ centerCounts.landslide }} centers</span>
          </div>
        </div>

        <!-- Others Filter -->
        <div class="filter-option"
             [class.active]="currentFilter === 'Others'"
             (click)="applyFilter('Others')">
          <div class="filter-icon">
            <img src="assets/forOthers.png" alt="Others" class="disaster-type-icon">
          </div>
          <div class="filter-info">
            <span class="filter-label">Other Disasters</span>
            <span class="filter-count">{{ centerCounts.others }} centers</span>
          </div>
        </div>

        <!-- Multiple Filter -->
        <div class="filter-option"
             [class.active]="currentFilter === 'Multiple'"
             (click)="applyFilter('Multiple')">
          <div class="filter-icon">
            <img src="assets/forMultiple.png" alt="Multiple" class="disaster-type-icon">
          </div>
          <div class="filter-info">
            <span class="filter-label">Multiple Types</span>
            <span class="filter-count">{{ centerCounts.multiple }} centers</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Overlay to close filter panel -->
  <div class="filter-overlay"
       [class.show]="showFilterPanel"
       (click)="closeFilterPanel()">
  </div>

  <!-- Navigation Panel (slides from right) -->
  <div class="navigation-panel" [class.show]="selectedCenter">
    <div class="panel-content">
      <!-- Panel Header -->
      <div class="panel-header">
        <div class="center-info" *ngIf="selectedCenter">
          <h3>{{ selectedCenter.name }}</h3>
          <p>{{ selectedCenter.address }}</p>
        </div>
        <ion-button fill="clear" size="small" (click)="closeNavigationPanel()">
          <ion-icon name="close-outline"></ion-icon>
        </ion-button>
      </div>

      <!-- Transportation Options -->
      <div class="transport-options" *ngIf="selectedCenter">
        <div class="option-header">
          <ion-icon name="navigate-outline"></ion-icon>
          <span>Choose Transportation</span>
        </div>

        <div class="transport-buttons">
          <button class="transport-btn"
                  [class.active]="selectedTransportMode === 'walking'"
                  (click)="selectTransportMode('walking')">
            <ion-icon name="walk-outline"></ion-icon>
            <span>Walk</span>
            <div class="route-info" *ngIf="routeInfo && selectedTransportMode === 'walking'">
              <span class="time">{{ formatTime(routeInfo.walking?.duration) }}</span>
              <span class="distance">{{ formatDistance(routeInfo.walking?.distance) }}</span>
            </div>
          </button>

          <button class="transport-btn"
                  [class.active]="selectedTransportMode === 'cycling'"
                  (click)="selectTransportMode('cycling')">
            <ion-icon name="bicycle-outline"></ion-icon>
            <span>Cycle</span>
            <div class="route-info" *ngIf="routeInfo && selectedTransportMode === 'cycling'">
              <span class="time">{{ formatTime(routeInfo.cycling?.duration) }}</span>
              <span class="distance">{{ formatDistance(routeInfo.cycling?.distance) }}</span>
            </div>
          </button>

          <button class="transport-btn"
                  [class.active]="selectedTransportMode === 'driving'"
                  (click)="selectTransportMode('driving')">
            <ion-icon name="car-outline"></ion-icon>
            <span>Drive</span>
            <div class="route-info" *ngIf="routeInfo && selectedTransportMode === 'driving'">
              <span class="time">{{ formatTime(routeInfo.driving?.duration) }}</span>
              <span class="distance">{{ formatDistance(routeInfo.driving?.distance) }}</span>
            </div>
          </button>
        </div>

        <!-- Start Navigation Button -->
        <button class="start-navigation-btn"
                *ngIf="selectedTransportMode && routeInfo"
                (click)="startNavigation()">
          <ion-icon name="navigate"></ion-icon>
          <span>Start Navigation</span>
        </button>
      </div>
    </div>
  </div>


</ion-content>
