<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;

class SystemLogsTest extends TestCase
{
    use RefreshDatabase;

    protected $systemAdmin;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a system administrator for testing
        $this->systemAdmin = User::create([
            'first_name' => 'System',
            'last_name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'system_admin',
            'position' => 'Technical Administrator',
            'status' => 'Active',
        ]);
    }

    /**
     * Test system logs page loads for system admin
     */
    public function test_system_logs_page_loads_for_system_admin()
    {
        $response = $this->actingAs($this->systemAdmin)
            ->get('/system-admin/system-logs');

        $response->assertStatus(200);
        $response->assertViewIs('components.system-admin.system-logs');
        $response->assertViewHas(['paginatedLogs', 'pagination', 'stats']);
    }

    /**
     * Test system logs page denies access to non-system admin
     */
    public function test_system_logs_denies_access_to_non_system_admin()
    {
        $superAdmin = User::create([
            'first_name' => 'Super',
            'last_name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'super_admin',
            'position' => 'CDRRMC Director',
            'city' => 'Cebu City',
            'status' => 'Active',
        ]);

        $response = $this->actingAs($superAdmin)
            ->get('/system-admin/system-logs');

        $response->assertStatus(403);
    }

    /**
     * Test system logs with level filter
     */
    public function test_system_logs_with_level_filter()
    {
        $response = $this->actingAs($this->systemAdmin)
            ->get('/system-admin/system-logs?level=error');

        $response->assertStatus(200);
        $response->assertViewHas('level', 'error');
    }

    /**
     * Test system logs with date filter
     */
    public function test_system_logs_with_date_filter()
    {
        $date = now()->format('Y-m-d');
        
        $response = $this->actingAs($this->systemAdmin)
            ->get("/system-admin/system-logs?date={$date}");

        $response->assertStatus(200);
        $response->assertViewHas('date', $date);
    }

    /**
     * Test system logs with search filter
     */
    public function test_system_logs_with_search_filter()
    {
        $search = 'test search term';
        
        $response = $this->actingAs($this->systemAdmin)
            ->get("/system-admin/system-logs?search=" . urlencode($search));

        $response->assertStatus(200);
        $response->assertViewHas('search', $search);
    }

    /**
     * Test system logs handles missing log files gracefully
     */
    public function test_system_logs_handles_missing_log_files()
    {
        // Test with a date that definitely won't have a log file
        $futureDate = now()->addDays(30)->format('Y-m-d');
        
        $response = $this->actingAs($this->systemAdmin)
            ->get("/system-admin/system-logs?date={$futureDate}");

        $response->assertStatus(200);
        $response->assertViewHas('paginatedLogs', []);
    }

    /**
     * Test log parsing functionality
     */
    public function test_log_parsing_functionality()
    {
        // Create a temporary log file for testing
        $logContent = "[2025-07-02 10:00:00] local.ERROR: Test error message\n";
        $logContent .= "Stack trace:\n";
        $logContent .= "#0 /path/to/file.php(123): TestClass->testMethod()\n";
        $logContent .= "[2025-07-02 10:01:00] local.INFO: Test info message\n";
        $logContent .= "[2025-07-02 10:02:00] local.WARNING: Test warning message\n";

        $tempLogFile = storage_path('logs/test-laravel.log');
        File::put($tempLogFile, $logContent);

        try {
            $response = $this->actingAs($this->systemAdmin)
                ->get('/system-admin/system-logs');

            $response->assertStatus(200);
            
            // Clean up
            File::delete($tempLogFile);
        } catch (\Exception $e) {
            // Clean up even if test fails
            if (File::exists($tempLogFile)) {
                File::delete($tempLogFile);
            }
            throw $e;
        }
    }

    /**
     * Test system logs statistics
     */
    public function test_system_logs_statistics()
    {
        $response = $this->actingAs($this->systemAdmin)
            ->get('/system-admin/system-logs');

        $response->assertStatus(200);
        
        $stats = $response->viewData('stats');
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('error', $stats);
        $this->assertArrayHasKey('warning', $stats);
        $this->assertArrayHasKey('info', $stats);
        $this->assertArrayHasKey('debug', $stats);
        $this->assertArrayHasKey('total', $stats);
    }

    /**
     * Test system logs pagination
     */
    public function test_system_logs_pagination()
    {
        $response = $this->actingAs($this->systemAdmin)
            ->get('/system-admin/system-logs?page=1');

        $response->assertStatus(200);
        
        $pagination = $response->viewData('pagination');
        $this->assertIsArray($pagination);
        $this->assertArrayHasKey('current_page', $pagination);
        $this->assertArrayHasKey('total_pages', $pagination);
        $this->assertArrayHasKey('total_logs', $pagination);
        $this->assertEquals(1, $pagination['current_page']);
    }

    /**
     * Test system logs error handling
     */
    public function test_system_logs_error_handling()
    {
        // Test that the page loads even when there are issues reading logs
        $response = $this->actingAs($this->systemAdmin)
            ->get('/system-admin/system-logs');

        $response->assertStatus(200);
        
        // Should have default empty values when errors occur
        $paginatedLogs = $response->viewData('paginatedLogs');
        $this->assertIsArray($paginatedLogs);
    }

    /**
     * Test system logs view structure
     */
    public function test_system_logs_view_structure()
    {
        $response = $this->actingAs($this->systemAdmin)
            ->get('/system-admin/system-logs');

        $response->assertStatus(200);
        $response->assertSee('System Logs');
        $response->assertSee('Monitor system activity');
        $response->assertSee('Total Logs');
        $response->assertSee('Errors');
        $response->assertSee('Warnings');
        $response->assertSee('Search logs...');
        $response->assertSee('All Levels');
    }
}
