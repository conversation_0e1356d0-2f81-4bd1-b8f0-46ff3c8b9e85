<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AppNotification extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'title',
        'message',
        'data',
        'read',
        'reactions',
        'sent_at'
    ];

    protected $casts = [
        'data' => 'array',
        'read' => 'boolean',
        'sent_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Get the user that owns the notification
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for unread notifications
     */
    public function scopeUnread($query)
    {
        return $query->where('read', false);
    }

    /**
     * Scope for read notifications
     */
    public function scopeRead($query)
    {
        return $query->where('read', true);
    }

    /**
     * Scope for specific user
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where(function ($q) use ($userId) {
            $q->where('user_id', $userId)
              ->orWhereNull('user_id'); // Include broadcast notifications
        });
    }

    /**
     * Scope for specific type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Mark notification as read
     */
    public function markAsRead()
    {
        $this->update(['read' => true]);
    }

    /**
     * Mark notification as unread
     */
    public function markAsUnread()
    {
        $this->update(['read' => false]);
    }

    /**
     * Increment reactions count
     */
    public function addReaction()
    {
        $this->increment('reactions');
    }

    /**
     * Create evacuation center notification
     */
    public static function createEvacuationCenterNotification($evacuationCenter, $userId = null)
    {
        return self::create([
            'user_id' => $userId,
            'type' => 'evacuation_center_added',
            'title' => 'New Evacuation Center Added',
            'message' => "A new evacuation center '{$evacuationCenter->name}' has been added in {$evacuationCenter->city}, {$evacuationCenter->province} for {$evacuationCenter->disaster_type} emergencies.",
            'data' => [
                'evacuation_center_id' => $evacuationCenter->id,
                'evacuation_center_name' => $evacuationCenter->name,
                'disaster_type' => $evacuationCenter->disaster_type,
                'location' => [
                    'city' => $evacuationCenter->city,
                    'province' => $evacuationCenter->province,
                    'latitude' => $evacuationCenter->latitude,
                    'longitude' => $evacuationCenter->longitude
                ]
            ],
            'sent_at' => now()
        ]);
    }

    /**
     * Create emergency alert notification
     */
    public static function createEmergencyNotification($title, $message, $category, $userId = null)
    {
        return self::create([
            'user_id' => $userId,
            'type' => 'emergency_alert',
            'title' => $title,
            'message' => $message,
            'data' => [
                'category' => $category,
                'severity' => 'high'
            ],
            'sent_at' => now()
        ]);
    }

    /**
     * Create system update notification
     */
    public static function createSystemNotification($title, $message, $userId = null)
    {
        return self::create([
            'user_id' => $userId,
            'type' => 'system_update',
            'title' => $title,
            'message' => $message,
            'sent_at' => now()
        ]);
    }

    /**
     * Get formatted time ago
     */
    public function getTimeAgoAttribute()
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Check if notification is recent (within last 24 hours)
     */
    public function getIsRecentAttribute()
    {
        return $this->created_at->isAfter(now()->subDay());
    }
}