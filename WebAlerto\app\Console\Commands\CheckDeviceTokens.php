<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\DeviceToken;

class CheckDeviceTokens extends Command
{
    protected $signature = 'tokens:check';
    protected $description = 'Check device tokens status';

    public function handle()
    {
        $tokens = DeviceToken::all();
        
        $this->info("📱 Device Tokens Status:");
        $this->info("Total tokens: " . $tokens->count());
        
        if ($tokens->count() > 0) {
            $this->table(
                ['ID', 'Device Type', 'Project ID', 'Active', 'User ID', 'Created'],
                $tokens->map(function ($token) {
                    return [
                        $token->id,
                        $token->device_type,
                        $token->project_id,
                        $token->is_active ? '✅ Yes' : '❌ No',
                        $token->user_id ?? 'None',
                        $token->created_at->format('Y-m-d H:i')
                    ];
                })
            );
        } else {
            $this->warn("❌ No device tokens found!");
            $this->info("💡 Make sure your mobile app is registering tokens properly.");
        }
    }
}
