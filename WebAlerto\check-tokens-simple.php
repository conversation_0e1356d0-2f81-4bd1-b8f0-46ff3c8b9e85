<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\DeviceToken;
use App\Models\Notification;

echo "=== Quick Token Check ===\n\n";

try {
    // Check device tokens
    $totalTokens = DeviceToken::count();
    $activeTokens = DeviceToken::where('is_active', true)->count();
    $inactiveTokens = DeviceToken::where('is_active', false)->count();
    
    echo "📱 Device Tokens:\n";
    echo "   Total: $totalTokens\n";
    echo "   Active: $activeTokens\n";
    echo "   Inactive: $inactiveTokens\n\n";
    
    // Show recent tokens
    $recentTokens = DeviceToken::orderBy('created_at', 'desc')->limit(3)->get();
    
    if ($recentTokens->count() > 0) {
        echo "🕒 Recent Tokens:\n";
        foreach ($recentTokens as $token) {
            $status = $token->is_active ? '✅ Active' : '❌ Inactive';
            $deviceType = $token->device_type ?? 'unknown';
            $created = $token->created_at->format('Y-m-d H:i:s');
            echo "   - $status | $deviceType | Created: $created\n";
        }
    } else {
        echo "❌ No tokens found in database\n";
    }
    
    echo "\n📧 Notifications:\n";
    $totalNotifications = Notification::count();
    $sentNotifications = Notification::where('sent', true)->count();
    $pendingNotifications = Notification::where('sent', false)->count();
    
    echo "   Total: $totalNotifications\n";
    echo "   Sent: $sentNotifications\n";
    echo "   Pending: $pendingNotifications\n\n";
    
    if ($activeTokens == 0) {
        echo "🚨 NO ACTIVE TOKENS FOUND!\n";
        echo "   This means the mobile app hasn't registered yet.\n\n";
        
        echo "🔧 Next Steps:\n";
        echo "   1. Make sure mobile app is running on your device\n";
        echo "   2. Check if app can reach: http://192.168.112.210:8000/api\n";
        echo "   3. Look for FCM registration errors in mobile app logs\n";
        echo "   4. Verify google-services.json is correct\n\n";
    } else {
        echo "✅ TOKENS FOUND! Ready to send notifications.\n\n";
        
        if ($pendingNotifications > 0) {
            echo "📤 You can send pending notifications now:\n";
            echo "   D:\\AlertoCapstone\\php\\php.exe send-pending-notifications.php\n\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error checking tokens: " . $e->getMessage() . "\n";
}

echo "=== Check Complete ===\n";
