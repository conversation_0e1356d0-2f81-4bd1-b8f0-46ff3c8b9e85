<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Password;
use App\Models\User;

class ChangePasswordController extends Controller
{
    public function showChangePasswordForm()
    {
        return view('auth.change-password');
    }

    public function changePassword(Request $request)
    {
        $request->validate([
            'email' => 'required|email|exists:users,email',
            'current_password' => ['required', function ($attribute, $value, $fail) use ($request) {
                $user = User::where('email', $request->email)->first();
                if (!$user || !Hash::check($value, $user->password)) {
                    $fail('The current password is incorrect.');
                }
            }],
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        $user = User::where('email', $request->email)->first();
        $user->password = Hash::make($request->password);
        $user->save();

        if (Auth::check()) {
            return redirect()->back()->with('success', 'Password changed successfully!');
        } else {
            return redirect()->route('login')->with('success', 'Password changed successfully! Please login with your new password.');
        }
    }

    /**
     * Show admin password change form (for new admin users with token)
     */
    public function showAdminPasswordChangeForm(Request $request)
    {
        $token = $request->get('token');
        $email = $request->get('email');

        if (!$token || !$email) {
            return redirect()->route('login')->with('error', 'Invalid password reset link.');
        }

        // Verify token exists and is valid
        $resetRecord = DB::table('password_reset_tokens')
            ->where('email', $email)
            ->first();

        if (!$resetRecord || !Hash::check($token, $resetRecord->token)) {
            return redirect()->route('login')->with('error', 'Invalid or expired password reset token.');
        }

        // Check if token is not older than 24 hours
        if (now()->diffInHours($resetRecord->created_at) > 24) {
            return redirect()->route('login')->with('error', 'Password reset token has expired.');
        }

        return view('auth.admin-password-change', compact('token', 'email'));
    }

    /**
     * Change admin password using token
     */
    public function changeAdminPassword(Request $request)
    {
        $request->validate([
            'token' => 'required',
            'email' => 'required|email|exists:users,email',
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        // Verify token
        $resetRecord = DB::table('password_reset_tokens')
            ->where('email', $request->email)
            ->first();

        if (!$resetRecord || !Hash::check($request->token, $resetRecord->token)) {
            return redirect()->route('login')->with('error', 'Invalid or expired password reset token.');
        }

        // Check if token is not older than 24 hours
        if (now()->diffInHours($resetRecord->created_at) > 24) {
            return redirect()->route('login')->with('error', 'Password reset token has expired.');
        }

        // Update user password
        $user = User::where('email', $request->email)->first();
        $user->password = Hash::make($request->password);
        $user->save();

        // Delete the used token
        DB::table('password_reset_tokens')->where('email', $request->email)->delete();

        // Log the password change
        \App\Models\SystemLog::log('admin_password_changed', $user, $user, 'Admin password changed via email token');

        return redirect()->route('login')->with('success', 'Password changed successfully! You can now login with your new password.');
    }
}