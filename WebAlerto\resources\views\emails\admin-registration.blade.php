<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to WebAlerto</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background-color: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #0ea5e9;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #64748b;
            font-size: 16px;
        }
        .content {
            margin-bottom: 30px;
        }
        .credentials-box {
            background-color: #f1f5f9;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .credential-item {
            margin: 10px 0;
            font-size: 16px;
        }
        .credential-label {
            font-weight: bold;
            color: #475569;
        }
        .credential-value {
            color: #1e293b;
            font-family: 'Courier New', monospace;
            background-color: white;
            padding: 5px 10px;
            border-radius: 4px;
            border: 1px solid #cbd5e1;
            display: inline-block;
            margin-left: 10px;
        }
        .login-button {
            display: inline-block;
            background-color: #0ea5e9;
            color: white !important;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            margin: 20px 0;
            text-align: center;
            border: 2px solid #0ea5e9;
        }
        .login-button:hover {
            background-color: #0284c7;
            border-color: #0284c7;
        }
        .warning {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        .warning-icon {
            color: #f59e0b;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            text-align: center;
            color: #64748b;
            font-size: 14px;
        }
        .contact-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8fafc;
            border-radius: 6px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🚨 Alerto</div>
            <div class="subtitle">Emergency Alert & Management System</div>
        </div>

        <div class="content">
            <h2 style="color: #1e293b; margin-bottom: 20px;">WebAlerto Admin Account Setup</h2>

            <p>Dear <strong>{{ $user->first_name }} {{ $user->middle_name ? $user->middle_name . ' ' : '' }}{{ $user->last_name }}</strong>,</p>

            <p>Your administrator account for WebAlerto Emergency Management System has been created by the system administrator. You have been granted access to manage emergency alerts and evacuation resources{{ $user->barangay ? ' for ' . $user->barangay . ' Barangay' : '' }}{{ $user->city ? ' in ' . $user->city : '' }}.</p>

            <p style="color: #059669; font-weight: 500;">This is an official system notification from your organization's emergency management team.</p>

            <div class="credentials-box">
                <h3 style="margin-top: 0; color: #374151;">Your Login Credentials</h3>
                <div class="credential-item">
                    <span class="credential-label">Email:</span>
                    <span class="credential-value">{{ $user->email }}</span>
                </div>
                <div class="credential-item">
                    <span class="credential-label">Temporary Password:</span>
                    <span class="credential-value">{{ $temporaryPassword }}</span>
                </div>
                <div class="credential-item">
                    <span class="credential-label">Role:</span>
                    <span class="credential-value">{{ $user->getRoleDisplayName() }}</span>
                </div>
                <div class="credential-item">
                    <span class="credential-label">Position:</span>
                    <span class="credential-value">{{ $user->position }}</span>
                </div>
                @if($user->city)
                <div class="credential-item">
                    <span class="credential-label">City:</span>
                    <span class="credential-value">{{ $user->city }}</span>
                </div>
                @endif
                @if($user->barangay)
                <div class="credential-item">
                    <span class="credential-label">Barangay:</span>
                    <span class="credential-value">{{ $user->barangay }}</span>
                </div>
                @endif
            </div>

            <div class="warning">
                <p><span class="warning-icon">⚠️ Important Security Notice:</span></p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>This is a temporary password. Please change it immediately after your first login.</li>
                    <li>Do not share these credentials with anyone.</li>
                    <li>Always log out when finished using the system.</li>
                </ul>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ $loginUrl }}" class="login-button">Access Admin Dashboard</a>
            </div>

            <div class="contact-info">
                <h4 style="margin-top: 0; color: #374151;">Need Help?</h4>
                <p style="margin: 5px 0;">If you have any questions or need assistance, please contact the system administrator.</p>
                <p style="margin: 5px 0;"><strong>System:</strong> WebAlerto Emergency Management</p>
                <p style="margin: 5px 0;"><strong>Environment:</strong> {{ config('app.env') === 'production' ? 'Production' : 'Development' }}</p>
            </div>
        </div>

        <div class="footer">
            <p>This email was sent automatically by the WebAlerto system.</p>
            <p>© {{ date('Y') }} WebAlerto Emergency Alert & Management System</p>
        </div>
    </div>
</body>
</html>
