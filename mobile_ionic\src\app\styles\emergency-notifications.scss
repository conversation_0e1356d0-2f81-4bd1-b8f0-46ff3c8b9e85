/* Emergency Notification Modal Styles */

.emergency-notification {
  --backdrop-opacity: 0.9;
  
  .alert-wrapper {
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border: 3px solid;
    animation: emergencyPulse 2s infinite;
  }

  .alert-head {
    padding: 20px 20px 10px 20px;
    text-align: center;
  }

  .alert-title {
    font-size: 1.4rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 8px;
  }

  .alert-sub-title {
    font-size: 1rem;
    font-weight: 600;
    opacity: 0.9;
    margin-bottom: 0;
  }

  .alert-message {
    font-size: 1.1rem;
    line-height: 1.5;
    padding: 10px 20px 20px 20px;
    text-align: center;
  }

  .alert-button-group {
    padding: 0 20px 20px 20px;
    display: flex;
    gap: 12px;
  }

  .alert-button {
    flex: 1;
    font-weight: 600;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 1rem;
  }

  .alert-button-primary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.5);
  }

  .alert-button-secondary {
    background: rgba(0, 0, 0, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
  }
}

/* Disaster-specific styles */
.earthquake-alert {
  .alert-wrapper {
    background: linear-gradient(135deg, #FF8C00, #FFA500);
    border-color: #FF6B00;
    color: white;
  }
}

.flood-alert {
  .alert-wrapper {
    background: linear-gradient(135deg, #0066CC, #4A90E2);
    border-color: #0052A3;
    color: white;
  }
}

.typhoon-alert {
  .alert-wrapper {
    background: linear-gradient(135deg, #228B22, #32CD32);
    border-color: #1F7A1F;
    color: white;
  }
}

.fire-alert {
  .alert-wrapper {
    background: linear-gradient(135deg, #DC143C, #FF4500);
    border-color: #B91C3C;
    color: white;
  }
}

.general-alert {
  .alert-wrapper {
    background: linear-gradient(135deg, #666666, #888888);
    border-color: #555555;
    color: white;
  }
}

/* Emergency pulse animation */
@keyframes emergencyPulse {
  0% {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 8px 32px rgba(255, 0, 0, 0.4), 0 0 20px rgba(255, 0, 0, 0.3);
    transform: scale(1.02);
  }
  100% {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transform: scale(1);
  }
}

/* Toast notification styles for fallback */
.emergency-toast {
  --background: linear-gradient(135deg, #FF8C00, #FFA500);
  --color: white;
  --border-radius: 12px;
  --box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  
  .toast-wrapper {
    border: 2px solid rgba(255, 255, 255, 0.3);
  }

  .toast-header {
    font-weight: 700;
    font-size: 1.1rem;
  }

  .toast-message {
    font-size: 1rem;
    line-height: 1.4;
  }

  .toast-button {
    --color: white;
    font-weight: 600;
  }
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .emergency-notification {
    .alert-wrapper {
      margin: 20px;
      max-width: calc(100vw - 40px);
    }

    .alert-title {
      font-size: 1.2rem;
    }

    .alert-message {
      font-size: 1rem;
    }

    .alert-button {
      font-size: 0.9rem;
      padding: 10px 12px;
    }
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .emergency-notification .alert-wrapper {
    border-width: 4px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .emergency-notification .alert-wrapper {
    animation: none;
  }
  
  @keyframes emergencyPulse {
    0%, 100% {
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      transform: scale(1);
    }
  }
}
