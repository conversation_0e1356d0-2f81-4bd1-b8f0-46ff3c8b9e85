@extends('layout.app')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-sky-100 py-8">
    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Back Button and Title -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6 mb-8">
            <div class="flex items-center gap-4">
                <a href="{{ route('components.user-management') }}"
                   class="p-2 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg text-white hover:from-sky-600 hover:to-blue-700 transition-all">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                </a>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Edit Mobile User</h1>
                    <p class="text-gray-600 mt-1">Update mobile user information and settings</p>
                </div>
            </div>
        </div>

        <form action="{{ route('components.user-management.update', $mobileUser->id) }}" method="POST" id="mobileUserForm">
            @csrf
            @method('PUT')

            <!-- Personal Information Section -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-8 mb-8">
                <div class="max-w-3xl mx-auto">
                    <div class="flex items-center gap-6 mb-8">
                        <div class="p-3 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg text-white w-12 h-12 flex items-center justify-center text-xl font-semibold">1</div>
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900">Personal Information</h2>
                            <p class="text-gray-600 mt-1">Update the mobile user's personal details</p>
                        </div>
                    </div>

                    <div class="space-y-6">
                        <!-- Full Name -->
                        <div class="bg-sky-50/50 backdrop-blur-sm rounded-xl border border-sky-200 p-6">
                            <label for="full_name" class="block text-lg font-semibold text-sky-600 mb-3">Full Name</label>
                            <input type="text" name="full_name" id="full_name" placeholder="Enter full name" value="{{ old('full_name', $mobileUser->full_name) }}"
                                   class="w-full rounded-xl border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-base py-3 px-4 bg-white/90">
                            @error('full_name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Mobile Number -->
                        <div class="bg-sky-50/50 backdrop-blur-sm rounded-xl border border-sky-200 p-6">
                            <label for="mobile_number" class="block text-lg font-semibold text-sky-600 mb-3">Mobile Number</label>
                            <input type="text" name="mobile_number" id="mobile_number" placeholder="Enter mobile number" value="{{ old('mobile_number', $mobileUser->mobile_number) }}"
                                   class="w-full rounded-xl border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-base py-3 px-4 bg-white/90">
                            @error('mobile_number')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Age and Gender -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="bg-sky-50/50 backdrop-blur-sm rounded-xl border border-sky-200 p-6">
                                <label for="age" class="block text-lg font-semibold text-sky-600 mb-3">Age</label>
                                <input type="number" name="age" id="age" placeholder="Enter age" value="{{ old('age', $mobileUser->age) }}" min="1" max="120"
                                       class="w-full rounded-xl border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-base py-3 px-4 bg-white/90">
                                @error('age')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="bg-sky-50/50 backdrop-blur-sm rounded-xl border border-sky-200 p-6">
                                <label for="gender" class="block text-lg font-semibold text-sky-600 mb-3">Gender</label>
                                <select name="gender" id="gender"
                                        class="w-full rounded-xl border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-base py-3 px-4 bg-white/90">
                                    <option value="">Select gender</option>
                                    <option value="Male" {{ old('gender', $mobileUser->gender) == 'Male' ? 'selected' : '' }}>Male</option>
                                    <option value="Female" {{ old('gender', $mobileUser->gender) == 'Female' ? 'selected' : '' }}>Female</option>
                                    <option value="Other" {{ old('gender', $mobileUser->gender) == 'Other' ? 'selected' : '' }}>Other</option>
                                </select>
                                @error('gender')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Address -->
                        <div class="bg-sky-50/50 backdrop-blur-sm rounded-xl border border-sky-200 p-6">
                            <label for="address" class="block text-lg font-semibold text-sky-600 mb-3">Address</label>
                            <textarea name="address" id="address" rows="3" placeholder="Enter complete address"
                                      class="w-full rounded-xl border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-base py-3 px-4 bg-white/90">{{ old('address', $mobileUser->address) }}</textarea>
                            @error('address')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Settings Section -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-8">
                <div class="max-w-3xl mx-auto">
                    <div class="flex items-center gap-6 mb-8">
                        <div class="p-3 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg text-white w-12 h-12 flex items-center justify-center text-xl font-semibold">2</div>
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900">Account Settings</h2>
                            <p class="text-gray-600 mt-1">Configure mobile user location and status</p>
                        </div>
                    </div>

                    <div class="space-y-6">
                        <!-- Barangay -->
                        @if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('super_admin') || (auth()->user()->hasRole('chairman') && auth()->user()->barangay == $mobileUser->barangay))
                        <div class="bg-sky-50/50 backdrop-blur-sm rounded-xl border border-sky-200 p-6">
                            <label for="barangay" class="block text-lg font-semibold text-sky-600 mb-3">Barangay</label>
                            <select name="barangay" id="barangay"
                                    class="w-full rounded-xl border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-base py-3 px-4 bg-white/90">
                                <option value="">Select barangay</option>
                                @foreach($barangays as $barangay)
                                    <option value="{{ $barangay }}" {{ old('barangay', $mobileUser->barangay) == $barangay ? 'selected' : '' }}>
                                        {{ $barangay }}
                                    </option>
                                @endforeach
                            </select>
                            @error('barangay')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        @else
                        <div class="bg-sky-50/50 backdrop-blur-sm rounded-xl border border-sky-200 p-6">
                            <label for="barangay" class="block text-lg font-semibold text-sky-600 mb-3">Barangay</label>
                            <input type="text" value="{{ $mobileUser->barangay }}" readonly
                                   class="w-full rounded-xl border-2 border-gray-300 shadow-sm text-base py-3 px-4 bg-gray-100 text-gray-700 cursor-not-allowed">
                            <input type="hidden" name="barangay" value="{{ $mobileUser->barangay }}">
                        </div>
                        @endif

                        <!-- Status -->
                        <div class="bg-sky-50/50 backdrop-blur-sm rounded-xl border border-sky-200 p-6">
                            <label for="status" class="block text-lg font-semibold text-sky-600 mb-3">Status</label>
                            <select name="status" id="status"
                                    class="w-full rounded-xl border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-base py-3 px-4 bg-white/90">
                                <option value="">Select status</option>
                                <option value="Active" {{ old('status', $mobileUser->status) == 'Active' ? 'selected' : '' }}>Active</option>
                                <option value="Inactive" {{ old('status', $mobileUser->status) == 'Inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                            @error('status')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Buttons -->
                        <div class="flex justify-end gap-4 mt-8">
                            <a href="{{ route('components.user-management') }}"
                               class="inline-flex items-center gap-2 px-6 py-3 rounded-xl font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 transition-colors">
                                <i class="fas fa-times"></i>
                                Cancel
                            </a>
                            <button type="submit"
                                    class="inline-flex items-center gap-2 px-6 py-3 rounded-xl font-medium text-white bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 shadow-lg transition-all duration-200 transform hover:scale-105">
                                <i class="fas fa-save"></i>
                                Save Changes
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection