@extends('layout.app')

@section('content')
<style>
    /* Custom responsive utilities */
    @media (max-width: 475px) {
        .xs\:hidden { display: none !important; }
        .xs\:inline { display: inline !important; }
    }

    /* Line clamp utility for mobile cards */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* Optimized mobile button sizing */
    @media (max-width: 768px) {
        /* Primary action buttons */
        .btn-primary-mobile {
            min-height: 40px;
            max-width: 280px;
            margin: 0 auto;
        }

        /* Secondary action buttons in cards */
        .btn-secondary-mobile {
            min-height: 32px;
            padding: 6px 12px;
        }

        /* Ensure proper touch targets without being too large */
        button, a {
            min-height: 36px;
        }

        /* Compact button spacing */
        .mobile-button-group {
            gap: 8px;
        }
    }
</style>
<div class="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-sky-100 py-8">
    <div class="max-w-[1920px] mx-auto px-6 sm:px-8 lg:px-12">
        <!-- Header Section -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-4 sm:p-6 mb-8">
            <div class="flex flex-col justify-center">
                <h1 class="text-base sm:text-2xl font-bold text-black-600">Notification Management Dashboard</h1>
                <p class="text-xs sm:text-base text-gray-600 mt-1">Send and manage emergency notifications</p>
            </div>
        </div>

        <!-- Stats Grid -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-6 mb-4 md:mb-6">
            <!-- Total Notifications -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border border-sky-200 p-3 md:p-6 transform transition-all hover:scale-105">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-xs md:text-sm font-medium text-gray-600">Total Alerts</p>
                        <h3 class="text-xl md:text-3xl font-bold text-gray-900 mt-1 md:mt-2">{{ $notifications->total() }}</h3>
                    </div>
                    <div class="p-2 md:p-4 bg-gradient-to-br from-sky-500 to-blue-600 rounded-lg shadow-lg">
                        <i class="fas fa-bell text-white text-lg md:text-2xl"></i>
                    </div>
                </div>
            </div>

            <!-- Sent Notifications -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border border-green-200 p-3 md:p-6 transform transition-all hover:scale-105">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-xs md:text-sm font-medium text-gray-600">Sent Alerts</p>
                        <h3 class="text-xl md:text-3xl font-bold text-green-600 mt-1 md:mt-2">{{ $notifications->where('sent', true)->count() }}</h3>
                    </div>
                    <div class="p-2 md:p-4 bg-gradient-to-br from-emerald-500 to-green-600 rounded-lg shadow-lg">
                        <i class="fas fa-check-circle text-white text-lg md:text-2xl"></i>
                    </div>
                </div>
            </div>

            <!-- Pending Notifications -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border border-orange-200 p-3 md:p-6 transform transition-all hover:scale-105">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-xs md:text-sm font-medium text-gray-600">Pending</p>
                        <h3 class="text-xl md:text-3xl font-bold text-orange-600 mt-1 md:mt-2">{{ $notifications->where('sent', false)->count() }}</h3>
                    </div>
                    <div class="p-2 md:p-4 bg-gradient-to-br from-amber-500 to-orange-600 rounded-lg shadow-lg">
                        <i class="fas fa-clock text-white text-lg md:text-2xl"></i>
                    </div>
                </div>
            </div>

            <!-- This Month -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border border-purple-200 p-3 md:p-6 transform transition-all hover:scale-105">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-xs md:text-sm font-medium text-gray-600">This Month</p>
                        <h3 class="text-xl md:text-3xl font-bold text-purple-600 mt-1 md:mt-2">{{ $notifications->where('created_at', '>=', now()->startOfMonth())->count() }}</h3>
                    </div>
                    <div class="p-2 md:p-4 bg-gradient-to-br from-violet-500 to-purple-600 rounded-lg shadow-lg">
                        <i class="fas fa-calendar text-white text-lg md:text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons Section -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
            <div class="flex flex-wrap gap-3">
                @if(!auth()->user()->hasRole('system_admin'))
                    <a href="{{ route('components.notification.create') }}"
                       class="inline-flex items-center justify-center gap-2 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white px-6 py-3 rounded-lg font-semibold shadow-lg transition-all duration-200 transform hover:scale-105">
                        <i class="fas fa-plus"></i>
                        <span>Create Notification</span>
                    </a>
                @endif
            </div>
        </div>

    <!-- Search and Filter Section -->
    @if(auth()->user()->hasRole('super_admin') || auth()->user()->hasRole('system_admin'))
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border border-sky-200 p-6 mb-6">
            <!-- Header -->
            <div class="flex items-center gap-2 sm:gap-3 mb-4 sm:mb-6">
                <div class="p-2 sm:p-3 bg-gradient-to-br from-sky-500 to-blue-600 rounded-lg shadow-lg">
                    <i class="fas fa-search text-white text-lg sm:text-xl"></i>
                </div>
                <div>
                    <h2 class="text-lg sm:text-xl font-bold text-gray-900">Find Notifications</h2>
                    <p class="text-xs sm:text-sm text-gray-600 mt-0.5 sm:mt-1">Search and filter emergency alerts</p>
                </div>
            </div>

            <!-- Search Bar - Top, Full Width -->
            <div class="mb-4 sm:mb-6">
                <div class="relative">
                    <input type="text" id="searchInput"
                           class="w-full rounded-lg border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 bg-white/90 py-3 sm:py-4 px-3 sm:px-4 text-sm sm:text-base text-gray-700 font-medium transition-all duration-200 hover:border-blue-400 pl-10 sm:pl-12"
                           placeholder="Search by title, message, or barangay..." value="{{ $searchQuery ?? '' }}">
                    <div class="absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400 text-sm sm:text-base"></i>
                    </div>
                </div>
            </div>

            <!-- Filter Controls -->
            <form action="{{ route('components.notification.index') }}" method="GET">
                <input type="hidden" name="search" value="{{ $searchQuery ?? '' }}">
                @if(auth()->user()->hasRole('system_admin'))
                <div class="grid grid-cols-1 gap-4 mb-4">
                    <!-- Barangay Filter (Only for System Admin) -->
                    <div class="relative">
                        <label for="barangay" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-map-marker-alt mr-1"></i>Filter by Barangay
                        </label>
                        <select name="barangay" id="barangay" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" onchange="this.form.submit()">
                            <option value="">All Barangays</option>
                            @foreach($barangays as $barangay)
                                <option value="{{ $barangay }}" {{ ($selectedBarangay ?? '') == $barangay ? 'selected' : '' }}>
                                    {{ $barangay }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>


                @else
                <div class="grid grid-cols-1 gap-4">
                    <!-- Barangay Filter (Only for CDRRMC) -->
                    <div class="relative">
                        <label for="barangay" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-map-marker-alt mr-1"></i>Filter by Barangay
                        </label>
                        <select name="barangay" id="barangay" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" onchange="this.form.submit()">
                            <option value="">All Barangays</option>
                            @foreach($barangays as $barangay)
                                <option value="{{ $barangay }}" {{ ($selectedBarangay ?? '') == $barangay ? 'selected' : '' }}>
                                    {{ $barangay }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                @endif
            </form>
        </div>

        <!-- JavaScript for real-time search -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const searchInput = document.getElementById('searchInput');
                if (searchInput) {
                    let searchTimeout;
                    searchInput.addEventListener('input', function() {
                        clearTimeout(searchTimeout);
                        const query = this.value.trim();

                        searchTimeout = setTimeout(() => {
                            const url = new URL(window.location.href);
                            if (query) {
                                url.searchParams.set('search', query);
                            } else {
                                url.searchParams.delete('search');
                            }
                            window.location.href = url.toString();
                        }, 500);
                    });
                }
            });
        </script>
    </div>
    @else
    <!-- BDRRMC users see only their barangay notifications - no filter needed -->
        <div class="bg-blue-50/80 backdrop-blur-sm border border-blue-200 rounded-xl shadow-lg p-6 mb-6">
            <div class="flex items-center gap-3">
                <div class="p-2 bg-blue-500 rounded-lg">
                    <i class="fas fa-map-marker-alt text-white"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-blue-900">Barangay Notifications</h3>
                    <p class="text-sm text-blue-700">Showing notifications for: <strong>{{ auth()->user()->barangay }}</strong></p>
                </div>
            </div>
        </div>
    @endif

    <!-- Dashboard Cards Section -->
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-6 mb-6">
            <!-- Statistics Chart Card - Spans 2 columns for more space -->
            <div class="xl:col-span-2">
                <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border border-sky-200 p-6 h-full">
                    <div class="flex items-center gap-3 mb-6">
                        <div class="p-3 bg-gradient-to-br from-sky-500 to-blue-600 rounded-lg shadow-lg">
                            <i class="fas fa-chart-bar text-white text-lg"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900">Disaster Notifications Analytics</h3>
                            <p class="text-sm text-gray-600">Monthly breakdown by disaster type</p>
                        </div>
                    </div>
                    <div class="relative h-96">
                        <canvas id="notifChart" class="w-full h-full"></canvas>
                    </div>
                </div>
            </div>

            <!-- Recent Alerts Card -->
            <div class="xl:col-span-1">
                <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border border-sky-200 p-6 h-full">
                    <div class="flex items-center gap-3 mb-6">
                        <div class="p-3 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg shadow-lg">
                            <i class="fas fa-exclamation-triangle text-white text-lg"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900">Recent Alerts</h3>
                            <p class="text-sm text-gray-600">Latest notifications</p>
                        </div>
                    </div>
                    <div class="space-y-3 max-h-80 overflow-y-auto">
                        @if(count($recentAlerts) > 0)
                            @foreach($recentAlerts as $alert)
                                <div class="group p-4 rounded-xl border-2 transition-all duration-200 hover:shadow-lg transform hover:scale-[1.02]
                                    @if($alert->type == 'Flood') bg-blue-50/80 border-blue-200 hover:bg-blue-100/80 hover:border-blue-300
                                    @elseif($alert->type == 'Typhoon') bg-green-50/80 border-green-200 hover:bg-green-100/80 hover:border-green-300
                                    @elseif($alert->type == 'Earthquake') bg-amber-50/80 border-amber-200 hover:bg-amber-100/80 hover:border-amber-300
                                    @elseif($alert->type == 'Fire') bg-red-50/80 border-red-200 hover:bg-red-100/80 hover:border-red-300
                                    @else bg-gray-50/80 border-gray-200 hover:bg-gray-100/80 hover:border-gray-300 @endif">
                                    <div class="flex items-start gap-3">
                                        <div class="flex-shrink-0 p-2 rounded-lg
                                            @if($alert->type == 'Flood') bg-blue-500 text-white
                                            @elseif($alert->type == 'Typhoon') bg-green-500 text-white
                                            @elseif($alert->type == 'Earthquake') bg-amber-500 text-white
                                            @elseif($alert->type == 'Fire') bg-red-500 text-white
                                            @else bg-gray-500 text-white @endif">
                                            @if($alert->type == 'Flood') <i class="fas fa-water text-sm"></i>
                                            @elseif($alert->type == 'Typhoon') <i class="fas fa-wind text-sm"></i>
                                            @elseif($alert->type == 'Earthquake') <i class="fas fa-house-crack text-sm"></i>
                                            @elseif($alert->type == 'Fire') <i class="fas fa-fire text-sm"></i>
                                            @else <i class="fas fa-bell text-sm"></i> @endif
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <p class="font-semibold text-gray-900 text-sm leading-tight mb-2">{{ $alert->message }}</p>
                                            <div class="flex items-center justify-between">
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold
                                                    @if($alert->type == 'Flood') bg-blue-100 text-blue-800
                                                    @elseif($alert->type == 'Typhoon') bg-green-100 text-green-800
                                                    @elseif($alert->type == 'Earthquake') bg-amber-100 text-amber-800
                                                    @elseif($alert->type == 'Fire') bg-red-100 text-red-800
                                                    @else bg-gray-100 text-gray-800 @endif">
                                                    {{ $alert->type }}
                                                </span>
                                                <span class="text-xs text-gray-500 font-medium">{{ $alert->time_ago }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="text-center py-8">
                                <div class="p-4 bg-gray-100 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                                    <i class="fas fa-bell-slash text-gray-400 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-semibold text-gray-600 mb-2">No Recent Alerts</h4>
                                <p class="text-gray-500 text-sm">All systems are running smoothly</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Notification History Section -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border border-sky-200 overflow-hidden">
            <!-- Section Header -->
            <div class="bg-gradient-to-r from-sky-600 to-blue-600 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <i class="fas fa-history text-white text-xl"></i>
                        <h2 class="text-xl font-bold text-white">Notification History</h2>
                    </div>
                    <div class="text-white/80 text-sm">
                        Total: {{ $notifications->total() ?? 0 }} notifications
                    </div>
                </div>
            </div>

            @if($notifications->isEmpty())
                <div class="p-8 sm:p-12 text-center">
                    <div class="text-gray-400 text-4xl sm:text-6xl mb-3 sm:mb-4">📋</div>
                    <h3 class="text-lg sm:text-xl font-semibold text-gray-600 mb-2">No notifications found</h3>
                    <p class="text-sm sm:text-base text-gray-500">
                        @if(auth()->user()->hasRole('system_admin'))
                            No notifications found in the system
                        @else
                            Use the "Create Notification" button above to get started
                        @endif
                    </p>
                </div>
            @else
                <!-- Desktop Table View (hidden on mobile) -->
                <div class="hidden lg:block overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="bg-blue-500 text-white text-sm leading-normal">
                                <th class="py-3 px-6 text-left font-semibold">Title</th>
                                <th class="py-3 px-6 text-left font-semibold">Category</th>
                                <th class="py-3 px-6 text-left font-semibold">Message</th>
                                <th class="py-3 px-6 text-center font-semibold">Severity</th>
                                <th class="py-3 px-6 text-center font-semibold">Created</th>
                                <th class="py-3 px-6 text-center font-semibold">Status</th>
                                <th class="py-3 px-6 text-center font-semibold">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="text-gray-600 text-sm">
                            @foreach($notifications as $notification)
                            <tr class="border-b border-sky-100 hover:bg-sky-50/50 transition-colors">
                                <td class="py-3 px-6">
                                    <span class="font-medium text-gray-900">{{ $notification->title }}</span>
                                </td>
                                <td class="py-3 px-6 text-left">
                                    @php
                                        $categoryConfig = [
                                            'typhoon' => ['bg-green-100 text-green-800', 'fas fa-wind', 'Typhoon'],
                                            'flood' => ['bg-blue-100 text-blue-800', 'fas fa-water', 'Flood'],
                                            'fire' => ['bg-red-100 text-red-800', 'fas fa-fire', 'Fire'],
                                            'earthquake' => ['bg-orange-100 text-orange-800', 'fas fa-house-crack', 'Earthquake'],
                                            'landslide' => ['bg-amber-100 text-amber-800', 'fas fa-mountain', 'Landslide'],
                                            'others' => ['bg-purple-100 text-purple-800', 'fas fa-cog', 'Others'],
                                            'evacuation' => ['bg-purple-100 text-purple-800', 'fas fa-people-arrows', 'Evacuation'],
                                        ];

                                        $category = strtolower($notification->category);
                                        $displayText = ucfirst($notification->category);

                                        // Handle custom "others" categories
                                        if (strpos($category, 'others:') === 0) {
                                            $customType = trim(str_replace('others:', '', $notification->category));
                                            $config = ['bg-purple-100 text-purple-800', 'fas fa-cog', $customType];
                                        } else {
                                            $config = $categoryConfig[$category] ?? ['bg-gray-100 text-gray-800', 'fas fa-circle', $displayText];
                                        }
                                    @endphp
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium {{ $config[0] }}">
                                        <i class="{{ $config[1] }} text-xs mr-1"></i>
                                        {{ $config[2] }}
                                    </span>
                                </td>
                                <td class="py-3 px-6 text-left">
                                    <div class="text-gray-600 max-w-xs truncate" title="{{ $notification->message }}">
                                        {{ Str::limit($notification->message, 50) }}
                                    </div>
                                </td>
                                <td class="py-3 px-6 text-center">
                                    @php
                                        $severityConfig = [
                                            'low' => ['bg-green-100 text-green-800', 'fas fa-info-circle', 'Low'],
                                            'medium' => ['bg-yellow-100 text-yellow-800', 'fas fa-exclamation-triangle', 'Medium'],
                                            'high' => ['bg-red-100 text-red-800', 'fas fa-exclamation-circle', 'High'],
                                            'critical' => ['bg-pink-100 text-pink-800', 'fas fa-skull-crossbones', 'Critical'],
                                        ];
                                        $severity = strtolower($notification->severity);
                                        $config = $severityConfig[$severity] ?? ['bg-gray-100 text-gray-800', 'fas fa-question', 'Unknown'];
                                    @endphp
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold {{ $config[0] }}">
                                        <i class="{{ $config[1] }} text-xs mr-1"></i>
                                        {{ $config[2] }}
                                    </span>
                                </td>
                                <td class="py-3 px-6 text-center">
                                    <div class="text-sm">{{ $notification->created_at->format('M j, Y') }}</div>
                                    <div class="text-xs text-gray-400">{{ $notification->created_at->format('g:i A') }}</div>
                                </td>
                                <td class="py-3 px-6 text-center">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold
                                        {{ $notification->sent ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                        <i class="fas {{ $notification->sent ? 'fa-check' : 'fa-clock' }} text-xs mr-1"></i>
                                        {{ $notification->sent ? 'Sent' : 'Pending' }}
                                    </span>
                                </td>
                                <td class="py-3 px-6 text-center">
                                    <div class="flex justify-center gap-2">
                                        <!-- View Button -->
                                        <button
                                            class="inline-flex items-center gap-1 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white px-3 py-1.5 rounded-lg text-xs font-medium shadow-lg transition-all duration-200"
                                            onclick="openModal({{ $notification->id }}, '{{ addslashes($notification->title) }}', '{{ addslashes($notification->category) }}', '{{ addslashes($notification->message) }}', '{{ $notification->sent ? 'Sent' : 'Pending' }}')">
                                            <i class="fas fa-eye text-xs"></i>
                                            View
                                        </button>


                                        <!-- Delete Button -->
                                        @if((Auth::user()->hasRole('super_admin') || $notification->user_id === Auth::user()->id) && !Auth::user()->hasRole('system_admin'))
                                            <button onclick="openDeleteModal({{ $notification->id }}, '{{ addslashes($notification->title) }}')"
                                                    class="inline-flex items-center gap-1 bg-gradient-to-r from-red-600 to-rose-600 hover:from-red-700 hover:to-rose-700 text-white px-3 py-1.5 rounded-lg text-xs font-medium shadow-lg transition-all duration-200">
                                                <i class="fas fa-trash text-xs"></i>
                                                Delete
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Mobile Card View (visible on mobile and tablet) -->
                <div class="lg:hidden space-y-3 sm:space-y-4">
                    @foreach($notifications as $notification)
                        @php
                            $categoryConfig = [
                                'typhoon' => ['bg-green-100 text-green-800', 'fas fa-wind', 'Typhoon'],
                                'flood' => ['bg-blue-100 text-blue-800', 'fas fa-water', 'Flood'],
                                'fire' => ['bg-red-100 text-red-800', 'fas fa-fire', 'Fire'],
                                'earthquake' => ['bg-orange-100 text-orange-800', 'fas fa-house-crack', 'Earthquake'],
                                'landslide' => ['bg-amber-100 text-amber-800', 'fas fa-mountain', 'Landslide'],
                                'others' => ['bg-purple-100 text-purple-800', 'fas fa-cog', 'Others'],
                                'evacuation' => ['bg-purple-100 text-purple-800', 'fas fa-people-arrows', 'Evacuation'],
                            ];

                            $category = strtolower($notification->category);
                            $displayText = ucfirst($notification->category);

                            // Handle custom "others" categories
                            if (strpos($category, 'others:') === 0) {
                                $customType = trim(str_replace('others:', '', $notification->category));
                                $config = ['bg-purple-100 text-purple-800', 'fas fa-cog', $customType];
                            } else {
                                $config = $categoryConfig[$category] ?? ['bg-gray-100 text-gray-800', 'fas fa-circle', $displayText];
                            }

                            $severityConfig = [
                                'low' => ['bg-green-100 text-green-800', 'Low'],
                                'medium' => ['bg-yellow-100 text-yellow-800', 'Medium'],
                                'high' => ['bg-red-100 text-red-800', 'High'],
                            ];
                            $severityStyle = $severityConfig[strtolower($notification->severity)] ?? ['bg-gray-100 text-gray-800', ucfirst($notification->severity)];
                        @endphp

                        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border border-sky-200 p-4 hover:shadow-2xl transition-all duration-200 transform hover:scale-[1.02]">
                            <!-- Card Header -->
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex-1 min-w-0">
                                    <h3 class="font-semibold text-gray-900 text-sm sm:text-base truncate">{{ $notification->title }}</h3>
                                    <div class="flex items-center gap-2 mt-1">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $config[0] }}">
                                            <i class="{{ $config[1] }} text-xs mr-1"></i>
                                            {{ $config[2] }}
                                        </span>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $severityStyle[0] }}">
                                            {{ $severityStyle[1] }}
                                        </span>
                                    </div>
                                </div>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold ml-2
                                    {{ $notification->sent ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                    <i class="fas {{ $notification->sent ? 'fa-check' : 'fa-clock' }} text-xs mr-1"></i>
                                    {{ $notification->sent ? 'Sent' : 'Pending' }}
                                </span>
                            </div>

                            <!-- Card Content -->
                            <div class="mb-3">
                                <p class="text-gray-600 text-sm line-clamp-2">{{ strlen($notification->message) > 100 ? substr($notification->message, 0, 100) . '...' : $notification->message }}</p>
                            </div>

                            <!-- Card Footer -->
                            <div class="flex items-center justify-between pt-3 border-t border-gray-100">
                                <div class="text-xs text-gray-500">
                                    <div>{{ $notification->created_at->format('M j, Y') }}</div>
                                    <div>{{ $notification->created_at->format('g:i A') }}</div>
                                </div>
                                <div class="flex gap-1.5 mobile-button-group">
                                    <!-- View Button -->
                                    <button
                                        class="btn-secondary-mobile inline-flex items-center gap-1 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white px-3 py-1.5 rounded-md text-xs font-medium shadow-sm transition-all duration-200"
                                        onclick="openModal({{ $notification->id }}, '{{ addslashes($notification->title) }}', '{{ addslashes($notification->category) }}', '{{ addslashes($notification->message) }}', '{{ $notification->sent ? 'Sent' : 'Pending' }}')">
                                        <i class="fas fa-eye text-xs"></i>
                                        <span>View</span>
                                    </button>

                                    <!-- Delete Button -->
                                    @if((Auth::user()->hasRole('super_admin') || $notification->user_id === Auth::user()->id) && !Auth::user()->hasRole('system_admin'))
                                        <button onclick="openDeleteModal({{ $notification->id }}, '{{ addslashes($notification->title) }}')"
                                                class="btn-secondary-mobile inline-flex items-center gap-1 bg-gradient-to-r from-red-600 to-rose-600 hover:from-red-700 hover:to-rose-700 text-white px-3 py-1.5 rounded-md text-xs font-medium shadow-sm transition-all duration-200">
                                            <i class="fas fa-trash text-xs"></i>
                                            <span>Delete</span>
                                        </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="bg-sky-50/80 px-6 py-4 border-t border-sky-200">
                    {{ $notifications->links('pagination::tailwind') }}
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Enhanced Modal -->
<div id="notificationModal" class="fixed inset-0 z-50 bg-gray-600 bg-opacity-50 backdrop-blur-sm flex items-center justify-center hidden p-4 overflow-y-auto" onclick="closeModal()">
    <div class="relative mx-auto w-full max-w-2xl bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border border-sky-200 overflow-hidden" onclick="event.stopPropagation()">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-sky-600 to-blue-600 px-4 sm:px-6 py-3 sm:py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-2 sm:gap-3 min-w-0">
                    <div class="p-1.5 sm:p-2 bg-white/20 rounded-lg">
                        <i class="fas fa-bell text-white text-sm sm:text-base"></i>
                    </div>
                    <h2 id="modalTitle" class="text-lg sm:text-xl font-bold text-white truncate"></h2>
                </div>
                <button onclick="closeModal()" class="text-white/80 hover:text-white transition-colors p-1">
                    <i class="fas fa-times text-lg sm:text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <div class="p-4 sm:p-6 space-y-4 sm:space-y-6">
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                <div class="space-y-3 sm:space-y-4">
                    <div class="flex items-center gap-2 sm:gap-3 p-3 sm:p-4 bg-sky-50 rounded-lg sm:rounded-xl">
                        <div class="p-1.5 sm:p-2 bg-sky-100 rounded-lg">
                            <i class="fas fa-tag text-sky-600 text-sm sm:text-base"></i>
                        </div>
                        <div class="min-w-0 flex-1">
                            <div class="text-xs sm:text-sm font-medium text-gray-600">Category</div>
                            <div id="modalCategory" class="text-base sm:text-lg font-semibold text-gray-900 flex items-center gap-2"></div>
                        </div>
                    </div>

                    <div class="flex items-center gap-2 sm:gap-3 p-3 sm:p-4 bg-green-50 rounded-lg sm:rounded-xl">
                        <div class="p-1.5 sm:p-2 bg-green-100 rounded-lg">
                            <i class="fas fa-check-circle text-green-600 text-sm sm:text-base"></i>
                        </div>
                        <div class="min-w-0 flex-1">
                            <div class="text-xs sm:text-sm font-medium text-gray-600">Status</div>
                            <div id="modalStatus" class="text-base sm:text-lg font-semibold text-gray-900"></div>
                        </div>
                    </div>
                </div>

                <div class="sm:col-span-1">
                    <div class="p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl h-full">
                        <div class="flex items-start gap-2 sm:gap-3">
                            <div class="p-1.5 sm:p-2 bg-gray-100 rounded-lg">
                                <i class="fas fa-message text-gray-600 text-sm sm:text-base"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="text-xs sm:text-sm font-medium text-gray-600 mb-2">Message</div>
                                <div id="modalMessage" class="text-sm sm:text-base text-gray-900 leading-relaxed break-words"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal Footer -->
        <div class="bg-gray-50 px-4 sm:px-6 py-3 sm:py-4 flex justify-end">
            <button onclick="closeModal()"
                    class="inline-flex items-center gap-2 bg-gray-600 hover:bg-gray-700 text-white px-4 sm:px-6 py-2 rounded-lg font-medium transition-all duration-200 text-sm sm:text-base">
                <i class="fas fa-times text-sm sm:text-base"></i>
                Close
            </button>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 z-50 bg-black/60 backdrop-blur-sm flex items-center justify-center hidden p-3 sm:p-4">
    <div class="bg-white rounded-lg sm:rounded-2xl w-full max-w-md shadow-2xl transform transition-all scale-95 overflow-hidden">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-red-600 to-rose-600 px-4 sm:px-6 py-3 sm:py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-2 sm:gap-3">
                    <div class="p-1.5 sm:p-2 bg-white/20 rounded-lg">
                        <i class="fas fa-trash text-white text-sm sm:text-base"></i>
                    </div>
                    <h2 class="text-xl font-bold text-white">Delete Notification</h2>
                </div>
                <button onclick="closeDeleteModal()" class="text-white/80 hover:text-white transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <div class="p-6">
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                    <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Are you sure?</h3>
                <p class="text-sm text-gray-500 mb-4">
                    You are about to delete the notification "<span id="deleteNotificationTitle" class="font-medium"></span>".
                    This action cannot be undone.
                </p>
            </div>
        </div>

        <!-- Modal Footer -->
        <div class="bg-gray-50 px-6 py-4 flex justify-end gap-3">
            <button onclick="closeDeleteModal()"
                    class="inline-flex items-center gap-2 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200">
                <i class="fas fa-times"></i>
                Cancel
            </button>
            <form id="deleteForm" method="POST" class="inline-block">
                @csrf
                @method('DELETE')
                <button type="submit"
                        class="inline-flex items-center gap-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200">
                    <i class="fas fa-trash"></i>
                    Delete
                </button>
            </form>
        </div>
    </div>
</div>

<script>
    function openModal(id, title, category, message, status) {
        // Category configuration matching the table
        const categoryConfig = {
            'typhoon': ['fas fa-wind', 'Typhoon'],
            'flood': ['fas fa-water', 'Flood'],
            'fire': ['fas fa-fire', 'Fire'],
            'earthquake': ['fas fa-house-crack', 'Earthquake'],
            'landslide': ['fas fa-mountain', 'Landslide'],
            'others': ['fas fa-cog', 'Others'],
        };

        let config;
        const lowerCategory = category.toLowerCase();

        // Handle custom "others" categories
        if (lowerCategory.startsWith('others:')) {
            const customType = category.replace(/^others:/i, '').trim();
            config = ['fas fa-cog', customType];
        } else {
            config = categoryConfig[lowerCategory] || ['fas fa-circle', category];
        }

        document.getElementById('modalTitle').textContent = title;
        document.getElementById('modalCategory').innerHTML = `<i class="${config[0]} text-sm mr-1"></i>${config[1]}`;
        document.getElementById('modalMessage').textContent = message;
        document.getElementById('modalStatus').textContent = status;
        document.getElementById('notificationModal').classList.remove('hidden');
    }

    function closeModal() {
        document.getElementById('notificationModal').classList.add('hidden');
    }

    function openDeleteModal(id, title) {
        document.getElementById('deleteNotificationTitle').textContent = title;
        document.getElementById('deleteForm').action = `/notification/${id}`;
        document.getElementById('deleteModal').classList.remove('hidden');
    }

    function closeDeleteModal() {
        document.getElementById('deleteModal').classList.add('hidden');
    }
</script>
<script>
    window.monthlyCategoryCounts = {!! $monthlyCategoryCounts->toJson() !!};
</script>
<script src="{{ asset('js/notification.js') }}"></script>
@endsection

@push('scripts')
<script>
    // The standardized filter function in app.js will handle the filter changes automatically
    // No need for custom event listeners here

    // City-Barangay filter interaction for System Admin
    @if(auth()->user()->hasRole('system_admin'))
    document.addEventListener('DOMContentLoaded', function() {
        const citySelect = document.getElementById('city');
        const barangaySelect = document.getElementById('barangay');

        if (citySelect && barangaySelect) {
            citySelect.addEventListener('change', function() {
                loadBarangaysForNotifications();
            });
        }
    });

    async function loadBarangaysForNotifications() {
        const citySelect = document.getElementById('city');
        const barangaySelect = document.getElementById('barangay');

        // Clear barangay options
        barangaySelect.innerHTML = '<option value="">All Barangays</option>';
        barangaySelect.disabled = true;

        if (!citySelect.value) {
            return;
        }

        try {
            const response = await fetch(`{{ route('system-admin.get-barangays-by-city') }}?city=${encodeURIComponent(citySelect.value)}`, {
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Accept': 'application/json'
                }
            });

            const result = await response.json();

            if (result.barangays && Array.isArray(result.barangays)) {
                result.barangays.forEach(barangay => {
                    const option = document.createElement('option');
                    option.value = barangay;
                    option.textContent = barangay;
                    barangaySelect.appendChild(option);
                });
                barangaySelect.disabled = false;
            }
        } catch (error) {
            console.error('Failed to load barangays:', error);
        }
    }
    @endif
</script>
@endpush
