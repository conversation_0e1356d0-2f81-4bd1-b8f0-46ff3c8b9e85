# PSGC API Integration for Barangay Data

This document explains how the WebAlerto system integrates with the Philippine Standard Geographic Code (PSGC) API to fetch authentic and updated barangay data for Cebu City.

## Overview

The system uses the PSGC API to fetch real barangay data instead of hardcoded values, ensuring that the barangay information is always up-to-date and authentic according to the Philippine Statistics Authority.

## Components

### 1. PSGCService (`app/Services/PSGCService.php`)

The main service class that handles:
- API communication with PSGC endpoints
- Data transformation and caching
- Fallback data when API is unavailable
- Error handling and logging

**Key Methods:**
- `getCebuCityBarangays()` - Fetches all barangays for Cebu City
- `clearCache()` - Clears cached barangay data
- `makeApiRequest()` - Handles API requests with proper error handling

### 2. Database Migration

**File:** `database/migrations/xxxx_add_psgc_code_to_barangays_table.php`

Adds the `psgc_code` field to store official PSGC codes from the Philippine Statistics Authority.

### 3. Updated Seeder (`database/seeders/BarangaySeeder.php`)

The seeder now:
- Uses PSGCService to fetch real data
- Falls back to complete official list if API fails
- Provides detailed progress feedback
- Handles errors gracefully

### 4. Artisan Commands

#### Refresh Barangays Command
```bash
php artisan barangays:refresh-from-psgc [--force]
```

**Features:**
- Fetches latest data from PSGC API
- Shows progress bar during seeding
- Clears cache for fresh data
- Requires confirmation unless `--force` flag is used

#### Test PSGC API Command
```bash
php artisan test:psgc-api
```

**Features:**
- Tests various PSGC API endpoints
- Shows response structure and data
- Helps debug API connectivity issues

## Data Source

### Primary Source: PSGC API
- **Base URL:** `https://psgc.gitlab.io/api`
- **Cebu City Code:** `072217000`
- **Endpoints Tried:**
  - `/cities-municipalities/072217000/barangays`
  - `/cities/072217000/barangays`
  - `/municipalities/072217000/barangays`

### Fallback Source: Official PSA Data
When the API is unavailable, the system uses a complete list of all 80 official barangays in Cebu City with their PSGC codes, sourced from:
- Philippine Statistics Authority (PSA)
- Wikipedia's verified list
- Official government records

## Features

### 1. Caching
- **Cache Key:** `psgc_cebu_city_barangays`
- **Duration:** 1 hour (3600 seconds)
- **Purpose:** Reduces API calls and improves performance

### 2. Error Handling
- Graceful fallback to official data
- Comprehensive logging
- SSL verification with fallback
- Timeout handling (10 seconds)

### 3. Data Transformation
- Converts API response to application format
- Generates consistent barangay codes
- Adds descriptive information
- Maintains PSGC code references

## Complete Barangay List

The system includes all 80 official barangays of Cebu City:

**North District (46 barangays):**
Adlaon, Agsungot, Apas, Bacayan, Banilad, Binaliw, Busay, Cambinocot, Capitol Site, Carreta, Cogon Ramos, Day-as, Ermita, Guba, Hipodromo, Kalubihan, Kamagayan, Kamputhaw, Kasambagan, Lahug, Lorega San Miguel, Lusaran, Luz, Mabini, Mabolo, Malubog, Pahina Central, Pari-an, Paril, Pit-os, Pulangbato, Sambag I, Sambag II, San Antonio, San Jose, San Roque, Santa Cruz, Santo Niño, Sirao, T. Padilla, Talamban, Taptap, Tejero, Tinago, Zapatera

**South District (34 barangays):**
Babag, Basak Pardo, Basak San Nicolas, Bonbon, Budlaan, Buhisan, Bulacao, Buot, Calamba, Cogon Pardo, Duljo Fatima, Guadalupe, Inayawan, Kalunasan, Kinasang-an Pardo, Labangon, Mambaling, Pahina San Nicolas, Pamutan, Pasil, Poblacion Pardo, Pung-ol Sibugay, Punta Princesa, Quiot Pardo, San Nicolas Proper, Sapangdaku, Sawang Calero, Sinsin, Suba, Sudlon I, Sudlon II, Tabunan, Tagba-o, Tisa, To-ong

## Usage Examples

### Refresh Barangay Data
```bash
# Interactive refresh (asks for confirmation)
php artisan barangays:refresh-from-psgc

# Force refresh without confirmation
php artisan barangays:refresh-from-psgc --force
```

### Seed Database
```bash
# Run the updated seeder
php artisan db:seed --class=BarangaySeeder
```

### Clear Cache
```php
use App\Services\PSGCService;

$psgcService = new PSGCService();
$psgcService->clearCache();
```

## Benefits

1. **Authenticity:** Uses official government data sources
2. **Currency:** Always up-to-date with latest changes
3. **Reliability:** Robust fallback system ensures data availability
4. **Performance:** Caching reduces API calls
5. **Maintainability:** Centralized service for all PSGC operations
6. **Transparency:** Comprehensive logging for debugging

## Troubleshooting

### API Connection Issues
1. Check internet connectivity
2. Verify PSGC API status
3. Review logs for specific errors
4. Use test command to diagnose issues

### Cache Issues
```bash
# Clear application cache
php artisan cache:clear

# Clear PSGC-specific cache
php artisan tinker
>>> (new App\Services\PSGCService())->clearCache()
```

### Data Inconsistencies
1. Run refresh command with `--force` flag
2. Check logs for API response issues
3. Verify fallback data is complete
4. Compare with official PSA records

## RBAC Integration

### Complete PSGC-RBAC Integration

All Role-Based Access Control (RBAC) functionalities now use the PSGC-connected barangay data:

#### 1. **BarangayService** (`app/Services/BarangayService.php`)
Centralized service for all barangay-related RBAC operations:
- `getActiveBarangays()` - Get all active barangays from PSGC data
- `getAccessibleBarangays(User $user)` - Get barangays accessible to specific user
- `canUserAccessBarangay(User $user, string $barangay)` - Check user access
- `applyBarangayFilter($query, User $user, ...)` - Apply RBAC filters to queries
- `syncUserBarangays()` - Sync user data with PSGC barangays

#### 2. **Custom Validation Rule** (`app/Rules/ValidBarangay.php`)
Validates barangay names against PSGC-connected database:
```php
'barangay' => ['required', 'string', 'max:255', new \App\Rules\ValidBarangay()]
```

#### 3. **Updated Controllers**
ALL controllers now use BarangayService for complete PSGC integration:
- **DashboardController**: Uses `applyBarangayFilter()` for data filtering and BarangayService for dropdowns
- **SuperAdminController**: Complete PSGC integration for all admin user management features
- **EvacuationManagementController**: Uses `ValidBarangay` rule and BarangayService for all dropdowns and filters
- **UserController**: Uses `ValidBarangay` rule for user management and BarangayService for filters
- **AdminController**: Uses BarangayService for barangay statistics
- **AdminInvitationController**: Uses BarangayService for registration forms
- **NotificationController**: Uses PSGC-connected barangay lists for all filters
- **EmailNotificationController**: Uses BarangayService for targeting and API endpoints
- **MappingSystemController**: Uses BarangayService for map filters and dropdowns

#### 4. **User Model Enhancement**
Updated `getAccessibleBarangays()` method to use BarangayService:
```php
public function getAccessibleBarangays()
{
    $barangayService = app(\App\Services\BarangayService::class);
    return $barangayService->getAccessibleBarangays($this);
}
```

### RBAC Commands

#### Sync User Barangays
```bash
# Check what would be updated (dry run)
php artisan users:sync-barangays --dry-run

# Actually sync user barangays with PSGC data
php artisan users:sync-barangays
```

#### Test Integration
```bash
# Test PSGC integration and RBAC functionality
php artisan test:psgc-integration
```

### RBAC Features

1. **Automatic Validation**: All barangay inputs are validated against PSGC data
2. **Centralized Access Control**: Single service manages all RBAC operations
3. **Data Consistency**: User barangays are synced with official PSGC data
4. **Fallback Support**: Graceful degradation when API is unavailable
5. **Cache Optimization**: Efficient caching for performance
6. **Complete System Integration**: ALL features and filters use PSGC data
7. **Form Validation**: Custom ValidBarangay rule ensures data integrity
8. **Dropdown Population**: All barangay dropdowns use PSGC-connected data
9. **Filter Consistency**: All barangay filters across the system use PSGC data
10. **API Integration**: All API endpoints return PSGC-connected barangay data

### Complete System Coverage

**ALL barangay filters and features now use PSGC data:**

#### **Frontend Components:**
- ✅ Dashboard barangay filter
- ✅ Notification management barangay filter
- ✅ Evacuation management barangay filters
- ✅ User management barangay filters
- ✅ Mapping system barangay filters
- ✅ Superadmin barangay filters
- ✅ All dropdown selections

#### **Backend Controllers:**
- ✅ All controller methods use BarangayService
- ✅ All validation uses ValidBarangay rule
- ✅ All API endpoints return PSGC data
- ✅ All filtering logic uses PSGC-connected data

#### **Data Sources:**
- ✅ Primary: PSGC-connected database
- ✅ Fallback: Direct PSGC API calls
- ✅ Cache: 30-minute efficient caching
- ✅ Validation: Real-time PSGC validation

## Future Enhancements

1. Support for other cities/municipalities
2. Real-time data synchronization
3. API rate limiting and queuing
4. Data validation and verification
5. Historical data tracking
6. Integration with other government APIs
7. Advanced RBAC features (district-level access, multi-barangay users)
8. Automated user barangay correction suggestions
9. Multi-language support for barangay names
10. Integration with other PSA datasets
