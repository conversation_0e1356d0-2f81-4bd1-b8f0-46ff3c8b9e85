import{b as U,c as I}from"./chunk-74T6JBPL.js";import{a as P}from"./chunk-DUSKIWGF.js";import{a as S}from"./chunk-3J7GGTVR.js";import{a as C,b as F}from"./chunk-ICWJVXBH.js";import{dc as E,fc as $,i as T,l as L}from"./chunk-6NKRJMOW.js";import{g as D,h as d}from"./chunk-B7O3QC5Z.js";var b=F("Filesystem",{web:()=>import("./chunk-A4BPP7KE.js").then(u=>new u.FilesystemWeb)});P();var M=D(U()),f=D(I());var H=(()=>{class u{constructor(o,n){this.toastCtrl=o,this.loadingCtrl=n}downloadMapWithRoutes(o,n,t,a=!0){return d(this,null,function*(){let i=yield this.loadingCtrl.create({message:`Capturing ${t} map with routes...`,spinner:"crescent"});yield i.present();try{let e;try{console.log("Attempting composite canvas approach...");let l=yield this.extractMapData(n);e=yield this.createCompositeMapCanvas(o,n,l)}catch(l){console.warn("Composite approach failed, trying fallback:",l),e=yield this.captureWithEnhancedHtml2Canvas(o)}let s=yield this.saveToDevice(e,t);yield i.dismiss(),yield(yield this.toastCtrl.create({message:`\u{1F4F1} ${t} map with routes saved to device gallery!`,duration:4e3,color:"success",buttons:[{text:"View",handler:()=>{this.openDeviceGallery()}}]})).present()}catch(e){yield i.dismiss(),console.error("Enhanced download error:",e),yield(yield this.toastCtrl.create({message:"Failed to save map to device. Please try again.",duration:3e3,color:"danger"})).present()}})}extractMapData(o){return d(this,null,function*(){let n=[],t=[];return console.log("\u{1F50D} Extracting map data for download..."),o.eachLayer(a=>{if(a instanceof f.Marker){let i=a.getLatLng(),e=a.options.icon;console.log("\u{1F4CD} Found marker:",{position:i,icon:e,iconOptions:e?.options});let s=[40,40];e?.options?.iconSize&&(Array.isArray(e.options.iconSize)?s=e.options.iconSize:e.options.iconSize instanceof f.Point&&(s=[e.options.iconSize.x,e.options.iconSize.y]));let r="assets/Location.png";if(e)if(e.options&&"iconUrl"in e.options&&e.options.iconUrl)r=e.options.iconUrl,console.log("\u2705 Found iconUrl from L.Icon:",r);else if(e.options&&"html"in e.options&&e.options.html){let g=e.options.html,p="";typeof g=="string"?p=g:g instanceof HTMLElement&&(p=g.outerHTML);let w=p.match(/src="([^"]+)"/);w&&w[1]?(r=w[1],console.log("\u2705 Found iconUrl from DivIcon HTML:",r)):console.log("\u26A0\uFE0F DivIcon found but no image src, using default")}else e._url?(r=e._url,console.log("\u2705 Found iconUrl from _url property:",r)):console.log("\u26A0\uFE0F Icon found but no iconUrl detected, using default:",r);let l,c=a.getPopup();if(c){let g=c.getContent();typeof g=="string"?l=g:g instanceof HTMLElement&&(l=g.innerHTML)}let m={lat:i.lat,lng:i.lng,iconUrl:r,iconSize:s,popupContent:l};console.log("\u{1F4CC} Adding marker to download:",m),n.push(m)}else if(a instanceof f.Polyline){let i=a.getLatLngs();t.push({coordinates:i.map(e=>[e.lat,e.lng]),color:a.options.color||"#3388ff",weight:a.options.weight||3}),console.log("\u{1F6E3}\uFE0F Found route with",i.length,"points")}}),console.log(`\u{1F4CA} Extraction complete: ${n.length} markers, ${t.length} routes`),{markers:n,routes:t,bounds:o.getBounds()}})}createCompositeMapCanvas(o,n,t){return d(this,null,function*(){let a=document.getElementById(o);if(!a)throw new Error("Map element not found");console.log("Creating composite map canvas...");let i=yield this.captureBaseMap(a),e=document.createElement("canvas"),s=e.getContext("2d");if(!s)throw new Error("Could not get canvas context");let r=a.getBoundingClientRect();return e.width=r.width*2,e.height=r.height*2,s.scale(2,2),s.drawImage(i,0,0,r.width,r.height),yield this.drawRoutesOnCanvas(s,n,t.routes,r),yield this.drawMarkersOnCanvas(s,n,t.markers,r),e})}captureBaseMap(o){return d(this,null,function*(){return yield(0,M.default)(o,{useCORS:!0,allowTaint:!0,foreignObjectRendering:!1,scrollX:0,scrollY:0,scale:1,backgroundColor:"#ffffff",logging:!1,imageTimeout:1e4,ignoreElements:n=>n.classList.contains("leaflet-control-zoom")||n.classList.contains("leaflet-control-attribution")||n.classList.contains("leaflet-marker-pane")||n.classList.contains("leaflet-overlay-pane")||n.classList.contains("leaflet-shadow-pane")})})}drawRoutesOnCanvas(o,n,t,a){return d(this,null,function*(){t.forEach(i=>{i.coordinates.length<2||(o.strokeStyle=i.color,o.lineWidth=i.weight,o.lineCap="round",o.lineJoin="round",o.beginPath(),i.coordinates.forEach((e,s)=>{let r=n.latLngToContainerPoint([e[0],e[1]]);s===0?o.moveTo(r.x,r.y):o.lineTo(r.x,r.y)}),o.stroke())})})}drawMarkersOnCanvas(o,n,t,a){return d(this,null,function*(){console.log(`\u{1F3A8} Drawing ${t.length} markers on canvas...`);for(let i=0;i<t.length;i++){let e=t[i];try{let s=n.latLngToContainerPoint([e.lat,e.lng]);console.log(`\u{1F4CD} Drawing marker ${i+1}/${t.length} at point:`,s,"iconUrl:",e.iconUrl);let r=yield this.loadImage(e.iconUrl),[l,c]=e.iconSize;console.log(`\u2705 Loaded marker image: ${l}x${c}`),o.drawImage(r,s.x-l/2,s.y-c,l,c),console.log(`\u2705 Drew marker ${i+1} successfully`)}catch(s){console.warn(`\u274C Failed to load marker image ${i+1}:`,e.iconUrl,s);let r=n.latLngToContainerPoint([e.lat,e.lng]),l="#ff0000";e.iconUrl.includes("Earthquake")?l="#ff9500":e.iconUrl.includes("Flood")?l="#3dc2ff":e.iconUrl.includes("Typhoon")?l="#2dd36f":e.iconUrl.includes("Fire")?l="#ef4444":e.iconUrl.includes("Landslide")?l="#8b5a2b":e.iconUrl.includes("Others")&&(l="#9333ea"),o.fillStyle=l,o.strokeStyle="#ffffff",o.lineWidth=2,o.beginPath(),o.arc(r.x,r.y,12,0,2*Math.PI),o.fill(),o.stroke(),console.log(`\u2705 Drew fallback marker ${i+1} with color ${l}`)}}console.log("\u{1F3A8} Completed drawing all markers on canvas")})}loadImage(o){return new Promise((n,t)=>{let a=new Image;a.crossOrigin="anonymous",a.onload=()=>{console.log(`\u2705 Successfully loaded image: ${o}`),n(a)},a.onerror=e=>{console.error(`\u274C Failed to load image: ${o}`,e),t(new Error(`Failed to load image: ${o}`))};let i=setTimeout(()=>{console.error(`\u23F0 Image load timeout: ${o}`),t(new Error(`Image load timeout: ${o}`))},1e4);a.onload=()=>{clearTimeout(i),console.log(`\u2705 Successfully loaded image: ${o}`),n(a)},console.log(`\u{1F504} Loading image: ${o}`),a.src=o})}captureWithEnhancedHtml2Canvas(o){return d(this,null,function*(){let n=document.getElementById(o);if(!n)throw new Error("Map element not found");return console.log("Using enhanced html2canvas fallback..."),yield this.waitForMapRender(),yield(0,M.default)(n,{useCORS:!0,allowTaint:!0,foreignObjectRendering:!0,scrollX:0,scrollY:0,windowWidth:window.innerWidth,windowHeight:window.innerHeight,scale:2,backgroundColor:"#ffffff",logging:!1,imageTimeout:15e3,removeContainer:!1,ignoreElements:t=>t.classList.contains("leaflet-control-zoom")||t.classList.contains("leaflet-control-attribution")})})}waitForMapRender(){return d(this,null,function*(){return new Promise(o=>{setTimeout(()=>{requestAnimationFrame(()=>{setTimeout(o,500)})},1e3)})})}createOfflineMapCanvas(o,n,t=800,a=600){return d(this,null,function*(){let i=document.createElement("canvas"),e=i.getContext("2d");if(!e)throw new Error("Could not get canvas context");i.width=t,i.height=a,e.fillStyle="#f0f0f0",e.fillRect(0,0,t,a);try{let s=o.getBounds(),r=o.getZoom(),l=o.getCenter();yield this.drawCachedTiles(e,s,r,t,a);let c=document.createElement("div");c.style.width=`${t}px`,c.style.height=`${a}px`,c.style.position="absolute",c.style.top="-9999px",document.body.appendChild(c);let m=f.map(c).setView([l.lat,l.lng],r);yield this.drawRoutesOnCanvas(e,m,n.routes,{width:t,height:a}),yield this.drawMarkersOnCanvas(e,m,n.markers,{width:t,height:a}),m.remove(),document.body.removeChild(c)}catch(s){console.warn("Error creating offline map canvas:",s),e.fillStyle="#666",e.font="16px Arial",e.textAlign="center",e.fillText("Map data unavailable",t/2,a/2)}return i})}drawCachedTiles(o,n,t,a,i){return d(this,null,function*(){let s=n.getNorthWest(),r=n.getSouthEast(),l=this.latLngToTile(s.lat,s.lng,t),c=this.latLngToTile(r.lat,r.lng,t),m=Math.floor(l.x),g=Math.ceil(c.x),p=Math.floor(l.y),w=Math.ceil(c.y);for(let y=m;y<=g;y++)for(let v=p;v<=w;v++)try{let h=(y-l.x)*256,k=(v-l.y)*256;o.fillStyle="#e8f4f8",o.fillRect(h,k,256,256),o.strokeStyle="#b0d4e3",o.strokeRect(h,k,256,256),o.fillStyle="#666",o.font="10px Arial",o.textAlign="center",o.fillText("Map Area",h+256/2,k+256/2)}catch(h){console.warn(`Failed to draw tile ${t}/${y}/${v}:`,h)}})}latLngToTile(o,n,t){let a=Math.pow(2,t),i=(n+180)/360*a,e=(1-Math.log(Math.tan(o*Math.PI/180)+1/Math.cos(o*Math.PI/180))/Math.PI)/2*a;return{x:i,y:e}}loadImageFromBase64(o){return new Promise((n,t)=>{let a=new Image;a.onload=()=>n(a),a.onerror=t,a.src=`data:image/png;base64,${o}`})}saveToDevice(o,n){return d(this,null,function*(){let t=o.toDataURL("image/png",1),a=t.split(",")[1],e=new Date().toISOString().replace(/[:.]/g,"-").substring(0,19),s=`${n.toLowerCase()}-evacuation-map-${e}.png`;try{return C.isNativePlatform()?yield this.saveToNativeDevice(a,s):this.fallbackBrowserDownload(t,s),s}catch(r){return console.error("Download error:",r),this.fallbackBrowserDownload(t,s),s}})}saveToNativeDevice(o,n){return d(this,null,function*(){try{let t=yield b.writeFile({path:n,data:o,directory:S.Documents,recursive:!0});if(console.log("File saved to:",t.uri),this.isAndroid())try{yield b.writeFile({path:`Download/${n}`,data:o,directory:S.ExternalStorage,recursive:!0}),console.log("File also saved to Downloads folder")}catch(a){console.log("Could not save to Downloads folder:",a)}}catch(t){throw console.error("Native file save error:",t),t}})}fallbackBrowserDownload(o,n){let t=document.createElement("a");t.href=o,t.download=n,document.body.appendChild(t),t.click(),document.body.removeChild(t),console.log("Used fallback browser download")}isAndroid(){return/Android/i.test(navigator.userAgent)}isIOS(){return/iPad|iPhone|iPod/.test(navigator.userAgent)}openDeviceGallery(){return d(this,null,function*(){try{if(C.isNativePlatform())if(this.isAndroid())try{window.open("content://com.android.externalstorage.documents/document/primary%3ADownload","_system")}catch{window.open("content://com.android.externalstorage.documents/","_system")}else this.isIOS()&&window.open("shareddocuments://","_system");else yield(yield this.toastCtrl.create({message:"File downloaded to your browser's download folder",duration:3e3,color:"primary"})).present()}catch(o){console.error("Error opening gallery:",o),yield(yield this.toastCtrl.create({message:"File saved successfully! Check your device's file manager.",duration:3e3,color:"success"})).present()}})}getDownloadStats(){return d(this,null,function*(){try{if(C.isNativePlatform()){let n=(yield b.readdir({path:"",directory:S.Documents})).files.filter(t=>t.name.includes("evacuation-map")&&t.name.endsWith(".png"));return{totalDownloads:n.length,lastDownload:n.length>0?n[n.length-1].name:"None"}}else return{totalDownloads:0,lastDownload:"Check browser downloads"}}catch(o){return console.error("Error getting download stats:",o),{totalDownloads:0,lastDownload:"None"}}})}static{this.\u0275fac=function(n){return new(n||u)(L($),L(E))}}static{this.\u0275prov=T({token:u,factory:u.\u0275fac,providedIn:"root"})}}return u})();export{H as a};
