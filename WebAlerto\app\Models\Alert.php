<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Alert extends Model
{
    use HasFactory;

    protected $table = 'alerts';

    protected $primaryKey = 'alert_id';

    protected $fillable = [
        'title',
        'description',
        'created_by',
    ];

    public $timestamps = false; // Disable automatic timestamps since `created_at` is manually handled
}