@extends('layout.app')

@section('title', 'Evacuation Centers Map')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-sky-100 py-8">
    <!-- Main Container -->
    <div class="max-w-[1920px] mx-auto px-6 sm:px-8 lg:px-12">
        <!-- Center Search & Filters Form -->
        <form method="GET" action="{{ route('map') }}" class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-3 mb-6">
            @if(auth()->user()->hasRole('system_admin'))
            <div class="grid grid-cols-1 md:grid-cols-6 gap-2 items-end">
                <!-- Status Filter -->
                <div>
                    <label class="block text-xs font-semibold text-gray-700 mb-1">
                        <i class="fas fa-filter text-sky-600 mr-1"></i>Status
                    </label>
                    <select id="status-filter" name="status" class="w-full px-2 py-2 border border-sky-200 rounded-lg focus:ring-1 focus:ring-sky-500 focus:border-sky-500 transition-all bg-white/80 backdrop-blur-sm text-sm" onchange="this.form.submit()">
                        <option value="All" {{ ($statusFilter ?? 'All') === 'All' ? 'selected' : '' }}>All Status</option>
                        <option value="Active" {{ ($statusFilter ?? '') === 'Active' ? 'selected' : '' }}>Active</option>
                        <option value="Inactive" {{ ($statusFilter ?? '') === 'Inactive' ? 'selected' : '' }}>Inactive</option>
                        <option value="Under Maintenance" {{ ($statusFilter ?? '') === 'Under Maintenance' ? 'selected' : '' }}>Under Maintenance</option>
                    </select>
                </div>
                <!-- Disaster Type Filter -->
                <div>
                    <label class="block text-xs font-semibold text-gray-700 mb-1">
                        <i class="fas fa-exclamation-triangle text-sky-600 mr-1"></i>Disaster
                    </label>
                    <select name="disaster_type" id="disaster-filter" class="w-full px-2 py-2 border border-sky-200 rounded-lg focus:ring-1 focus:ring-sky-500 focus:border-sky-500 transition-all bg-white/80 backdrop-blur-sm text-sm" onchange="this.form.submit()">
                        <option value="All" {{ ($disasterFilter ?? 'All') === 'All' ? 'selected' : '' }}>All Disasters</option>
                        <option value="Typhoon" {{ ($disasterFilter ?? '') === 'Typhoon' ? 'selected' : '' }}>Typhoon</option>
                        <option value="Flood" {{ ($disasterFilter ?? '') === 'Flood' ? 'selected' : '' }}>Flood</option>
                        <option value="Fire" {{ ($disasterFilter ?? '') === 'Fire' ? 'selected' : '' }}>Fire</option>
                        <option value="Earthquake" {{ ($disasterFilter ?? '') === 'Earthquake' ? 'selected' : '' }}>Earthquake</option>
                        <option value="Landslide" {{ ($disasterFilter ?? '') === 'Landslide' ? 'selected' : '' }}>Landslide</option>
                        <option value="Others" {{ ($disasterFilter ?? '') === 'Others' ? 'selected' : '' }}>Others</option>
                        <option value="Multi-disaster" {{ ($disasterFilter ?? '') === 'Multi-disaster' ? 'selected' : '' }}>Multi-disaster</option>
                    </select>
                </div>
                <!-- City Filter (Only for System Admin) -->
                <div>
                    <label class="block text-xs font-semibold text-gray-700 mb-1">
                        <i class="fas fa-city text-sky-600 mr-1"></i>City
                    </label>
                    <select name="city" id="city" class="w-full px-2 py-2 border border-sky-200 rounded-lg focus:ring-1 focus:ring-sky-500 focus:border-sky-500 transition-all bg-white/80 backdrop-blur-sm text-sm" onchange="this.form.submit()">
                        <option value="">All Cities</option>
                        @foreach($cities as $city)
                            <option value="{{ $city }}" {{ $selectedCity == $city ? 'selected' : '' }}>{{ $city }}</option>
                        @endforeach
                    </select>
                </div>
                <!-- Barangay Filter -->
                <div>
                    <label class="block text-xs font-semibold text-gray-700 mb-1">
                        <i class="fas fa-map text-sky-600 mr-1"></i>Barangay
                    </label>
                    <select name="barangay" id="barangay" class="w-full px-2 py-2 border border-sky-200 rounded-lg focus:ring-1 focus:ring-sky-500 focus:border-sky-500 transition-all bg-white/80 backdrop-blur-sm text-sm" {{ !$selectedCity ? 'disabled' : '' }} onchange="this.form.submit()">
                        <option value="All" {{ ($selectedBarangay ?? 'All') === 'All' ? 'selected' : '' }}>All Barangays</option>
                        @foreach($barangays as $barangay)
                            <option value="{{ $barangay }}" {{ $selectedBarangay == $barangay ? 'selected' : '' }}>{{ $barangay }}</option>
                        @endforeach
                    </select>
                </div>
            @else
            <div class="grid grid-cols-1 md:grid-cols-5 gap-2 items-end">
                <!-- Status Filter -->
                <div>
                    <label class="block text-xs font-semibold text-gray-700 mb-1">
                        <i class="fas fa-filter text-sky-600 mr-1"></i>Status
                    </label>
                    <select id="status-filter" name="status" class="w-full px-2 py-2 border border-sky-200 rounded-lg focus:ring-1 focus:ring-sky-500 focus:border-sky-500 transition-all bg-white/80 backdrop-blur-sm text-sm">
                        <option value="All" {{ ($statusFilter ?? 'All') === 'All' ? 'selected' : '' }}>All Status</option>
                        <option value="Active" {{ ($statusFilter ?? '') === 'Active' ? 'selected' : '' }}>Active</option>
                        <option value="Inactive" {{ ($statusFilter ?? '') === 'Inactive' ? 'selected' : '' }}>Inactive</option>
                        <option value="Under Maintenance" {{ ($statusFilter ?? '') === 'Under Maintenance' ? 'selected' : '' }}>Under Maintenance</option>
                    </select>
                </div>
                <!-- Disaster Type Filter -->
                <div>
                    <label class="block text-xs font-semibold text-gray-700 mb-1">
                        <i class="fas fa-exclamation-triangle text-sky-600 mr-1"></i>Disaster
                    </label>
                    <select name="disaster_type" id="disaster-filter" class="w-full px-2 py-2 border border-sky-200 rounded-lg focus:ring-1 focus:ring-sky-500 focus:border-sky-500 transition-all bg-white/80 backdrop-blur-sm text-sm">
                        <option value="All" {{ ($disasterFilter ?? 'All') === 'All' ? 'selected' : '' }}>All Disasters</option>
                        <option value="Typhoon" {{ ($disasterFilter ?? '') === 'Typhoon' ? 'selected' : '' }}>Typhoon</option>
                        <option value="Flood" {{ ($disasterFilter ?? '') === 'Flood' ? 'selected' : '' }}>Flood</option>
                        <option value="Fire" {{ ($disasterFilter ?? '') === 'Fire' ? 'selected' : '' }}>Fire</option>
                        <option value="Earthquake" {{ ($disasterFilter ?? '') === 'Earthquake' ? 'selected' : '' }}>Earthquake</option>
                        <option value="Landslide" {{ ($disasterFilter ?? '') === 'Landslide' ? 'selected' : '' }}>Landslide</option>
                        <option value="Others" {{ ($disasterFilter ?? '') === 'Others' ? 'selected' : '' }}>Others</option>
                        <option value="Multi-disaster" {{ ($disasterFilter ?? '') === 'Multi-disaster' ? 'selected' : '' }}>Multi-disaster</option>
                    </select>
                </div>
                <!-- Barangay Filter (Only for CDRRMC) -->
                @if(auth()->user()->hasRole('super_admin'))
                <div>
                    <label class="block text-xs font-semibold text-gray-700 mb-1">
                        <i class="fas fa-map text-sky-600 mr-1"></i>Barangay
                    </label>
                    <select name="barangay" id="barangay" class="w-full px-2 py-2 border border-sky-200 rounded-lg focus:ring-1 focus:ring-sky-500 focus:border-sky-500 transition-all bg-white/80 backdrop-blur-sm text-sm" onchange="this.form.submit()">
                        <option value="All" {{ ($selectedBarangay ?? 'All') === 'All' ? 'selected' : '' }}>All Barangays</option>
                        @foreach($barangays as $barangay)
                            <option value="{{ $barangay }}" {{ $selectedBarangay == $barangay ? 'selected' : '' }}>{{ $barangay }}</option>
                        @endforeach
                    </select>
                </div>
                @endif
            @endif
                <!-- Filter Centers + Search Button (side by side, rightmost) -->
                <div class="col-span-1 md:col-span-2">
                    <div class="w-full">
                        <label class="block text-xs font-semibold text-gray-700 mb-1">
                            <i class="fas fa-search text-sky-600 mr-1"></i>Search Centers
                        </label>
                        <div class="relative">
                        <input id="search" type="text" name="search" value="{{ $searchQuery ?? '' }}" placeholder="Search by name, address..." class="w-full pl-8 pr-2 py-2 border border-sky-200 rounded-lg focus:ring-1 focus:ring-sky-500 focus:border-sky-500 transition-all bg-white/80 backdrop-blur-sm text-sm">
                           
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <!-- Map Section (bigger) -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-4 mb-8 map-section-container">
            <!-- Map Controls Header -->
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-800">Evacuation Centers Map</h3>
                <div class="flex items-center gap-3">
                    <!-- Transport Mode Selector -->
                    <div class="flex items-center gap-1 bg-white/90 rounded-lg p-1 border border-sky-200">
                        <button onclick="setTransportMode('driving')" id="transport-driving" class="transport-mode-btn active px-3 py-2 text-sm rounded bg-blue-500 text-white hover:bg-blue-600 transition-all">
                            <i class="fas fa-car mr-1"></i>Drive
                        </button>
                        <button onclick="setTransportMode('walking')" id="transport-walking" class="transport-mode-btn px-3 py-2 text-sm rounded bg-gray-300 text-gray-700 hover:bg-gray-400 transition-all">
                            <i class="fas fa-walking mr-1"></i>Walk
                        </button>
                        <button onclick="setTransportMode('cycling')" id="transport-cycling" class="transport-mode-btn px-3 py-2 text-sm rounded bg-gray-300 text-gray-700 hover:bg-gray-400 transition-all">
                            <i class="fas fa-bicycle mr-1"></i>Bike
                        </button>
                    </div>

                    <button id="reset-map" class="inline-flex items-center px-3 py-2 bg-sky-500 text-white rounded-lg hover:bg-sky-600 transition-colors text-sm shadow-md">
                        <i class="fas fa-undo-alt mr-2"></i>Reset Map
                    </button>
                </div>
            </div>
            
            <!-- Legend Overlay -->
            <div class="absolute top-20 left-4 z-20 bg-white/95 backdrop-blur-sm rounded-lg shadow-lg border border-sky-200 p-3 legend-overlay" style="max-width: 200px;">
                <h5 class="text-xs font-bold text-gray-800 mb-2 text-center">Legend</h5>
                <div class="grid grid-cols-1 gap-y-2">
                    <div class="flex items-center gap-2">
                        <img src="/image/pins/forTyphoon.png" alt="Typhoon" class="w-4 h-4 object-contain">
                        <span class="text-xs text-gray-700">Typhoon</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <img src="/image/pins/forFlood.png" alt="Flood" class="w-4 h-4 object-contain">
                        <span class="text-xs text-gray-700">Flood</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <img src="/image/pins/forFire.png" alt="Fire" class="w-4 h-4 object-contain">
                        <span class="text-xs text-gray-700">Fire</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <img src="/image/pins/forEarthquake.png" alt="Earthquake" class="w-4 h-4 object-contain">
                        <span class="text-xs text-gray-700">Earthquake</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <img src="/image/pins/forLandslide.png" alt="Landslide" class="w-4 h-4 object-contain">
                        <span class="text-xs text-gray-700">Landslide</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <img src="/image/pins/forOthers.png" alt="Others" class="w-4 h-4 object-contain">
                        <span class="text-xs text-gray-700">Others</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <img src="/image/pins/forMultiple.png" alt="Multi-disaster" class="w-4 h-4 object-contain">
                        <span class="text-xs text-gray-700">Multi-disaster</span>
                    </div>
                </div>
            </div>
            <div id="map" class="w-full rounded-xl overflow-hidden shadow-md border-2 border-sky-200 bg-white relative" style="z-index: 1; height: 650px; min-height: 650px;"></div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
      integrity="sha256-o9N1j+n1D2dLkUobZLCOoMOGMNF9OL8W8BO6FhJdguI=" crossorigin=""/>
<link rel="stylesheet" href="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.css" />
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    body {
        font-family: 'Poppins', sans-serif;
    }
    #map {
        height: 500px !important;
        width: 100% !important;
        z-index: 1;
        position: relative;
        overflow: hidden;
        min-height: 500px;
        flex: 1;
    }
    .leaflet-container {
        position: relative !important;
        outline: none;
        overflow: hidden !important;
        background: white !important;
        width: 100% !important;
        height: 100% !important;
    }
    /* Map Section Container */
    .map-section-container {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
    }
    .leaflet-popup-content {
        margin: 0;
        padding: 1rem;
        font-family: 'Poppins', sans-serif;
    }
    .leaflet-popup-content-wrapper {
        border-radius: 1rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border: 1px solid #e0f2fe;
        background: white;
    }
    .leaflet-popup-tip {
        background: white;
    }
    .leaflet-popup-content h3 {
        font-weight: 600;
        color: #111827;
        margin-bottom: 0.75rem;
        font-size: 1rem;
    }
    .leaflet-popup-content p {
        color: #4b5563;
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
    }
    .leaflet-popup-content .actions {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e0f2fe;
    }
    .leaflet-popup-content .actions button:hover {
        background-color: #fee2e2;
        border-color: #fecaca;
    }
    .leaflet-popup-content .actions a:hover {
        background-color: #dbeafe;
        border-color: #bfdbfe;
    }

    /* Location search results styling */
    .search-result-item {
        transition: background-color 0.2s;
    }

    .search-result-item:hover {
        background-color: #f0f9ff;
    }

    .location-search-marker {
        z-index: 1000;
    }

    .location-search-marker .animate-pulse {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.1);
            opacity: 0.8;
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    /* Search results container */
    #mapSearchResults {
        max-width: 100%;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
    }

    /* Search input focus styling */
    #mapLocationSearch:focus {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        border-color: #3b82f6;
    }

    /* Reset button styling */
    #reset-map {
        transition: all 0.2s ease-in-out;
    }

    #reset-map:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
    }

    #reset-map:active {
        transform: translateY(0);
    }

    /* Map controls header styling */
    .map-section-container h3 {
        color: #1e293b;
        font-weight: 600;
    }

    /* Legend Overlay Styling */
    .map-section-container .legend-overlay {
        position: absolute;
        top: 5rem;
        left: 1rem;
        z-index: 20;
        background: rgba(255,255,255,0.95);
        backdrop-filter: blur(4px);
        border-radius: 0.5rem;
        box-shadow: 0 4px 12px rgba(14, 165, 233, 0.15);
        border: 1px solid #bae6fd;
        padding: 0.75rem;
        max-width: 200px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }

    /* Popup routing controls styling */
    .routing-controls button {
        transition: all 0.2s ease-in-out;
    }

    .routing-controls button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .routing-controls button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
    }

    /* Route info styling */
    .route-info-section {
        transition: all 0.3s ease-in-out;
    }

    /* Transport mode button styles */
    .transport-mode-btn {
        transition: all 0.2s ease;
        border: 1px solid transparent;
        font-weight: 500;
    }

    .transport-mode-btn.active {
        background-color: #3b82f6 !important;
        color: white !important;
        border-color: #2563eb;
        box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
    }

    .transport-mode-btn:not(.active) {
        background-color: #f3f4f6;
        color: #6b7280;
        border-color: #d1d5db;
    }

    .transport-mode-btn:not(.active):hover {
        background-color: #e5e7eb;
        color: #374151;
        border-color: #9ca3af;
    }
</style>
@endsection

@section('scripts')
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
        integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
        crossorigin=""></script>
<script src="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.js"></script>
<script src="{{ asset('js/map.js') }}"></script>
<script>
    // All routing functions are now in map.js

    // Initialize map with data from Laravel
    document.addEventListener('DOMContentLoaded', function() {
        const centers = @json($centers);
        const isAdmin = {{ auth()->user()->hasRole('super_admin') || auth()->user()->hasRole('admin') ? 'true' : 'false' }};

        // Make centers globally available
        window.centers = centers;

        // Initialize map functionality
        if (typeof initializeMap === 'function') {
            initializeMap(centers, isAdmin);
        }

        // Initialize location search functionality
        if (typeof initializeLocationSearch === 'function') {
            initializeLocationSearch();
        }

        // Initialize barangay filter functionality
        if (typeof initializeBarangayFilter === 'function') {
            initializeBarangayFilter();
        }

        // City-Barangay filter interaction for System Admin
        @if(auth()->user()->hasRole('system_admin'))
        const citySelect = document.getElementById('city');
        const barangaySelect = document.getElementById('barangay');

        if (citySelect && barangaySelect) {
            citySelect.addEventListener('change', function() {
                loadBarangaysForMap();
            });
        }

        async function loadBarangaysForMap() {
            // Clear barangay options
            barangaySelect.innerHTML = '<option value="All">All Barangays</option>';
            barangaySelect.disabled = true;

            if (!citySelect.value) {
                return;
            }

            try {
                const response = await fetch(`{{ route('system-admin.get-barangays-by-city') }}?city=${encodeURIComponent(citySelect.value)}`, {
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Accept': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.barangays && Array.isArray(result.barangays)) {
                    result.barangays.forEach(barangay => {
                        const option = document.createElement('option');
                        option.value = barangay;
                        option.textContent = barangay;
                        barangaySelect.appendChild(option);
                    });
                    barangaySelect.disabled = false;
                }
            } catch (error) {
                console.error('Failed to load barangays:', error);
            }
        }
        @endif
    });
</script>
@endsection