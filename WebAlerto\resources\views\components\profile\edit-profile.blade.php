@extends('layout.app')

@section('title', 'Edit Profile')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-sky-50 to-sky-100 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 mb-8">
            <div class="p-6 md:p-8">
                <div class="flex items-center gap-4">
                    <div class="h-16 w-16 rounded-full bg-gradient-to-br from-sky-400 to-blue-500 flex items-center justify-center">
                        <i class="fas fa-user-edit text-white text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Edit Profile</h1>
                        <p class="text-gray-600 mt-1">Update your personal information and account details</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success Message -->
        @if(session('success'))
            <div class="bg-green-50 border border-green-200 rounded-xl p-4 mb-6">
                <div class="flex items-center gap-3">
                    <i class="fas fa-check-circle text-green-600"></i>
                    <span class="text-green-800 font-medium">{{ session('success') }}</span>
                </div>
            </div>
        @endif

        <!-- Profile Edit Form -->
        <form action="{{ route('profile.update') }}" method="POST" class="space-y-8">
            @csrf
            @method('PUT')

            <!-- Personal Information Section -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6 md:p-8">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">Personal Information</h2>
                    <p class="text-gray-600">Update your basic personal details</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Title -->
                    <div class="md:col-span-1">
                        <label for="title" class="block text-sm font-semibold text-gray-700 mb-2">
                            <i class="fas fa-crown text-sky-600 mr-2"></i>Title (Optional)
                        </label>
                        <input type="text" 
                               name="title" 
                               id="title" 
                               value="{{ old('title', $user->title) }}"
                               placeholder="e.g., Dr., Engr., Hon."
                               class="w-full rounded-xl border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-base py-3 px-4 bg-white/90 @error('title') border-red-300 @enderror">
                        @error('title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- First Name -->
                    <div class="md:col-span-1">
                        <label for="first_name" class="block text-sm font-semibold text-gray-700 mb-2">
                            <i class="fas fa-user text-sky-600 mr-2"></i>First Name *
                        </label>
                        <input type="text" 
                               name="first_name" 
                               id="first_name" 
                               value="{{ old('first_name', $user->first_name) }}"
                               required
                               class="w-full rounded-xl border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-base py-3 px-4 bg-white/90 @error('first_name') border-red-300 @enderror">
                        @error('first_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Middle Name -->
                    <div class="md:col-span-1">
                        <label for="middle_name" class="block text-sm font-semibold text-gray-700 mb-2">
                            <i class="fas fa-user text-sky-600 mr-2"></i>Middle Name (Optional)
                        </label>
                        <input type="text" 
                               name="middle_name" 
                               id="middle_name" 
                               value="{{ old('middle_name', $user->middle_name) }}"
                               class="w-full rounded-xl border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-base py-3 px-4 bg-white/90 @error('middle_name') border-red-300 @enderror">
                        @error('middle_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Last Name -->
                    <div class="md:col-span-1">
                        <label for="last_name" class="block text-sm font-semibold text-gray-700 mb-2">
                            <i class="fas fa-user text-sky-600 mr-2"></i>Last Name *
                        </label>
                        <input type="text" 
                               name="last_name" 
                               id="last_name" 
                               value="{{ old('last_name', $user->last_name) }}"
                               required
                               class="w-full rounded-xl border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-base py-3 px-4 bg-white/90 @error('last_name') border-red-300 @enderror">
                        @error('last_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Suffix -->
                    <div class="md:col-span-1">
                        <label for="suffix" class="block text-sm font-semibold text-gray-700 mb-2">
                            <i class="fas fa-tag text-sky-600 mr-2"></i>Suffix (Optional)
                        </label>
                        <input type="text" 
                               name="suffix" 
                               id="suffix" 
                               value="{{ old('suffix', $user->suffix) }}"
                               placeholder="e.g., Jr., Sr., III"
                               class="w-full rounded-xl border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-base py-3 px-4 bg-white/90 @error('suffix') border-red-300 @enderror">
                        @error('suffix')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Email -->
                    <div class="md:col-span-1">
                        <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">
                            <i class="fas fa-envelope text-sky-600 mr-2"></i>Email Address *
                        </label>
                        <input type="email" 
                               name="email" 
                               id="email" 
                               value="{{ old('email', $user->email) }}"
                               required
                               class="w-full rounded-xl border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-base py-3 px-4 bg-white/90 @error('email') border-red-300 @enderror">
                        @error('email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Professional Information Section -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6 md:p-8">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">Professional Information</h2>
                    <p class="text-gray-600">Update your position and role details</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Position -->
                    <div class="md:col-span-1">
                        <label for="position" class="block text-sm font-semibold text-gray-700 mb-2">
                            <i class="fas fa-briefcase text-sky-600 mr-2"></i>Position
                        </label>
                        <input type="text" 
                               name="position" 
                               id="position" 
                               value="{{ old('position', $user->position) }}"
                               placeholder="e.g., Barangay BDRRMO Chairman"
                               class="w-full rounded-xl border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-base py-3 px-4 bg-white/90 @error('position') border-red-300 @enderror">
                        @error('position')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Role (Read-only) -->
                    <div class="md:col-span-1">
                        <label for="role" class="block text-sm font-semibold text-gray-700 mb-2">
                            <i class="fas fa-shield-alt text-sky-600 mr-2"></i>Role
                        </label>
                        <input type="text" 
                               value="{{ ucfirst(str_replace('_', ' ', $user->role)) }}"
                               readonly
                               class="w-full rounded-xl border-2 border-gray-200 shadow-sm text-base py-3 px-4 bg-gray-100 text-gray-600 cursor-not-allowed">
                        <p class="mt-1 text-xs text-gray-500">Role cannot be changed. Contact your administrator if needed.</p>
                    </div>

                    <!-- Barangay (Read-only) -->
                    <div class="md:col-span-1">
                        <label for="barangay" class="block text-sm font-semibold text-gray-700 mb-2">
                            <i class="fas fa-map-marker-alt text-sky-600 mr-2"></i>Barangay
                        </label>
                        <input type="text" 
                               value="{{ $user->barangay }}"
                               readonly
                               class="w-full rounded-xl border-2 border-gray-200 shadow-sm text-base py-3 px-4 bg-gray-100 text-gray-600 cursor-not-allowed">
                        <p class="mt-1 text-xs text-gray-500">Barangay assignment cannot be changed.</p>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end gap-4">
                <a href="{{ route('components.dashboard') }}" 
                   class="inline-flex items-center gap-2 px-6 py-3 rounded-xl font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 transition-colors">
                    <i class="fas fa-times"></i>
                    Cancel
                </a>
                <button type="submit" 
                        class="inline-flex items-center gap-2 px-6 py-3 rounded-xl font-medium text-white bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 shadow-lg transition-all duration-200 transform hover:scale-105">
                    <i class="fas fa-save"></i>
                    Save Changes
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Form validation and user experience enhancements
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const submitButton = form.querySelector('button[type="submit"]');
    
    // Add loading state to submit button
    form.addEventListener('submit', function() {
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';
    });
    
    // Auto-capitalize first letter of names
    const nameFields = ['first_name', 'last_name', 'middle_name'];
    nameFields.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (field) {
            field.addEventListener('input', function() {
                this.value = this.value.charAt(0).toUpperCase() + this.value.slice(1);
            });
        }
    });
});
</script>
@endsection
