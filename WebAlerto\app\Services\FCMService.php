<?php

namespace App\Services;

use App\Contracts\FCMServiceInterface;
use App\Exceptions\FCMException;
use App\Models\DeviceToken;
use App\Models\Notification;
use Illuminate\Support\Facades\Log;
use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Message;
use Kreait\Firebase\Messaging\Notification as FirebaseNotification;
use Kreait\Firebase\Messaging\AndroidConfig;
use Kreait\Firebase\Messaging\ApnsConfig;
use Kreait\Firebase\Messaging\WebPushConfig;
use Kreait\Firebase\Messaging\MessageTarget;
use Kreait\Firebase\Messaging\MulticastSendReport;
use Kreait\Firebase\Exception\MessagingException;
use Kreait\Firebase\Http\HttpClientOptions;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;

class FCMService implements FCMServiceInterface
{
    protected $batchSize;
    protected $messaging;
    protected $rateLimiter;
    protected $maxNotificationsPerMinute = 200; // Increased for testing multiple notifications
    protected $notificationExpiry = 3600; // 1 hour
    protected $maxRetries;
    protected $retryDelay;
    protected $curlOptions = [];

    /**
     * Constructor
     *
     * Initializes the FCM service with configuration from config files
     */
    public function __construct()
    {
        $this->batchSize = config('firebase.batch_size', 500);
        $this->rateLimiter = RateLimiter::class;
        $this->maxRetries = config('fcm.retry.max_attempts', 3);
        $this->retryDelay = config('fcm.retry.delay', 1000);

        // Set CA certificate path for cURL
        $this->configureCACertificate();

        try {
            $this->initializeFirebase();
        } catch (FCMException $e) {
            // Log the error but don't re-throw
            // This allows the application to continue running even if FCM is not available
            // The error will be handled when trying to send notifications
            Log::error('FCM initialization failed in constructor', [
                'error' => $e->getMessage()
            ]);

            // Set messaging to null to indicate initialization failed
            $this->messaging = null;
        }
    }

    /**
     * Configure CA certificate for cURL
     */
    protected function configureCACertificate()
    {
        // Try multiple certificate paths
        $certPaths = [
            storage_path('certs/cacert.pem'),
            storage_path('cacert.pem'),
            base_path('cacert.pem'),
            // Windows default paths
            'C:\Windows\System32\cacert.pem',
            'C:\php\extras\ssl\cacert.pem',
            // PHP installation paths
            'D:\AlertoCapstone\php\extras\ssl\cacert.pem',
            'D:\AlertoCapstone\php\cacert.pem'
        ];

        $currentCertPath = ini_get('curl.cainfo');

        // If already set and file exists, keep it
        if ($currentCertPath && file_exists($currentCertPath)) {
            Log::info('Using existing CA certificate', ['path' => $currentCertPath]);
            $this->setSSLConfiguration($currentCertPath);
            return;
        }

        // Try to find a valid certificate file
        foreach ($certPaths as $certPath) {
            if (file_exists($certPath)) {
                $this->setSSLConfiguration($certPath);
                Log::info('Set CA certificate path for cURL and SSL context', ['path' => $certPath]);
                return;
            }
        }

        // If no certificate found, try to download it
        $this->downloadCACertificate();
    }

    /**
     * Set SSL configuration for all HTTP requests
     */
    protected function setSSLConfiguration($certPath)
    {
        // Set PHP ini settings
        ini_set('curl.cainfo', $certPath);
        ini_set('openssl.cafile', $certPath);

        // Set environment variables for cURL
        putenv('CURL_CA_BUNDLE=' . $certPath);
        putenv('SSL_CERT_FILE=' . $certPath);

        // Set default context options for all HTTP requests
        $contextOptions = [
            'ssl' => [
                'cafile' => $certPath,
                'verify_peer' => true,
                'verify_peer_name' => true,
                'allow_self_signed' => false,
                'verify_depth' => 7,
                'ciphers' => 'HIGH:!SSLv2:!SSLv3:!TLSv1:!TLSv1.1'
            ],
            'http' => [
                'timeout' => 30,
                'user_agent' => 'WebAlerto/1.0 (Laravel FCM Service)'
            ]
        ];

        stream_context_set_default($contextOptions);

        // Set global cURL options for the Firebase SDK
        $this->setGlobalCurlOptions($certPath);
    }

    /**
     * Set global cURL options for Firebase SDK
     */
    protected function setGlobalCurlOptions($certPath)
    {
        // Set default cURL options that will be used by the Firebase SDK
        $curlDefaults = [
            CURLOPT_CAINFO => $certPath,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_USERAGENT => 'WebAlerto/1.0 (Laravel FCM Service)',
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3
        ];

        // Store these options for later use
        $this->curlOptions = $curlDefaults;
    }

    /**
     * Download CA certificate bundle
     */
    protected function downloadCACertificate()
    {
        $certDir = storage_path('certs');
        $certPath = $certDir . '/cacert.pem';

        // Create directory if it doesn't exist
        if (!is_dir($certDir)) {
            mkdir($certDir, 0755, true);
        }

        try {
            // Download the latest CA bundle from curl.se
            $certUrl = 'https://curl.se/ca/cacert.pem';

            // Use file_get_contents with context to bypass SSL verification for this download
            $context = stream_context_create([
                'http' => [
                    'timeout' => 30,
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                ],
                'ssl' => [
                    'verify_peer' => false,
                    'verify_peer_name' => false
                ]
            ]);

            $certContent = file_get_contents($certUrl, false, $context);

            if ($certContent !== false) {
                file_put_contents($certPath, $certContent);

                // Set multiple certificate configurations
                ini_set('curl.cainfo', $certPath);
                ini_set('openssl.cafile', $certPath);

                // Also set environment variables for cURL
                putenv('CURL_CA_BUNDLE=' . $certPath);
                putenv('SSL_CERT_FILE=' . $certPath);

                // Set default context options for all HTTP requests
                $contextOptions = [
                    'ssl' => [
                        'cafile' => $certPath,
                        'verify_peer' => true,
                        'verify_peer_name' => true,
                    ],
                    'http' => [
                        'timeout' => 30,
                    ]
                ];
                stream_context_set_default($contextOptions);

                Log::info('Downloaded and set CA certificate', ['path' => $certPath]);
            } else {
                Log::warning('Failed to download CA certificate');
                // Disable SSL verification as fallback (not recommended for production)
                $this->disableSSLVerification();
            }
        } catch (\Exception $e) {
            Log::error('Error downloading CA certificate', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            // Disable SSL verification as fallback
            $this->disableSSLVerification();
        }
    }

    /**
     * Create HTTP client with proper SSL configuration
     */
    protected function createHttpClientWithSSL()
    {
        try {
            // Check if GuzzleHttp is available
            if (!class_exists('\GuzzleHttp\Client')) {
                Log::info('GuzzleHttp not available, using default HTTP client');
                return null;
            }

            $certPath = storage_path('certs/cacert.pem');

            // If certificate exists, use it
            if (file_exists($certPath)) {
                $config = [
                    'verify' => $certPath,
                    'timeout' => 30,
                    'connect_timeout' => 10,
                    'headers' => [
                        'User-Agent' => 'WebAlerto/1.0 (Laravel FCM Service)'
                    ]
                ];

                Log::info('Creating HTTP client with SSL certificate', ['cert_path' => $certPath]);
                return new \GuzzleHttp\Client($config);
            } else {
                // Disable SSL verification as fallback
                Log::warning('SSL certificate not found, disabling SSL verification for HTTP client');
                $config = [
                    'verify' => false,
                    'timeout' => 30,
                    'connect_timeout' => 10,
                    'headers' => [
                        'User-Agent' => 'WebAlerto/1.0 (Laravel FCM Service)'
                    ]
                ];

                return new \GuzzleHttp\Client($config);
            }
        } catch (\Exception $e) {
            Log::error('Error creating HTTP client', [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Disable SSL verification as a last resort
     */
    protected function disableSSLVerification()
    {
        Log::warning('Disabling SSL verification for FCM - this is not recommended for production');

        // Set PHP ini settings to disable SSL verification
        ini_set('curl.cainfo', '');
        ini_set('openssl.cafile', '');

        // Set environment variables
        putenv('CURL_CA_BUNDLE=');
        putenv('SSL_CERT_FILE=');

        // Set default context options to disable SSL verification
        $contextOptions = [
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true,
            ],
            'http' => [
                'timeout' => 30,
                'user_agent' => 'WebAlerto/1.0 (Laravel FCM Service)'
            ]
        ];

        stream_context_set_default($contextOptions);

        // Set cURL options to disable SSL verification
        $curlOptions = [
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
        ];

        // Store these options for later use in HTTP requests
        $this->curlOptions = $curlOptions;
    }

    /**
     * Initialize Firebase Messaging
     *
     * @throws FCMException If Firebase service account file is missing or invalid
     * @return bool True if initialization was successful
     */
    protected function initializeFirebase()
    {
        $credentialsPath = config('firebase.projects.app.credentials', 'firebase-service-account.json');

        // Handle different path formats
        if (str_starts_with($credentialsPath, 'storage/')) {
            // If path starts with 'storage/', use it as relative to base path
            $serviceAccount = base_path($credentialsPath);
        } elseif (basename($credentialsPath) === $credentialsPath) {
            // If it's just a filename, prepend storage path
            $serviceAccount = storage_path($credentialsPath);
        } else {
            // Use absolute path as-is
            $serviceAccount = $credentialsPath;
        }

        // Check if service account file exists
        if (!file_exists($serviceAccount)) {
            $errorMessage = 'Firebase service account file not found at: ' . $serviceAccount;
            Log::error($errorMessage);
            throw new FCMException($errorMessage);
        }

        // Ensure SSL configuration is properly set
        $this->configureCACertificate();

        // For testing purposes, disable SSL verification
        $this->disableSSLVerification();

        try {
            // Parse the service account JSON file
            $serviceAccountData = json_decode(file_get_contents($serviceAccount), true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $errorMessage = 'Firebase service account file is not valid JSON: ' . json_last_error_msg();
                Log::error($errorMessage, ['path' => $serviceAccount]);
                throw new FCMException($errorMessage);
            }

            // Validate required fields
            $requiredFields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email'];
            foreach ($requiredFields as $field) {
                if (!isset($serviceAccountData[$field]) || empty($serviceAccountData[$field])) {
                    $errorMessage = "Firebase service account file is missing required field: {$field}";
                    Log::error($errorMessage, ['path' => $serviceAccount]);
                    throw new FCMException($errorMessage);
                }
            }

            // Validate private key format
            if (!str_contains($serviceAccountData['private_key'], '-----BEGIN PRIVATE KEY-----')) {
                $errorMessage = 'Firebase service account file has invalid private key format';
                Log::error($errorMessage, ['path' => $serviceAccount]);
                throw new FCMException($errorMessage);
            }

            // Initialize Firebase Messaging
            $factory = (new Factory)->withServiceAccount($serviceAccount);
            $this->messaging = $factory->createMessaging();

            Log::info('Firebase Messaging initialized successfully', [
                'project_id' => $serviceAccountData['project_id']
            ]);

            return true;
        } catch (FCMException $e) {
            // Re-throw FCM exceptions
            throw $e;
        } catch (\Exception $e) {
            // Wrap other exceptions in FCMException
            $errorMessage = 'Failed to initialize Firebase: ' . $e->getMessage();
            Log::error($errorMessage, [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new FCMException($errorMessage, 0, $e);
        }
    }

    public function send(string $token, array $notification): string
    {
        try {
            $this->validateNotification($notification);

            $message = $this->buildMessage($token, $notification);

            Log::channel(config('fcm.logging.channel'))
                ->info('Sending FCM notification', [
                    'token' => $token,
                    'notification' => $notification
                ]);

            $attempt = 0;
            do {
                try {
                    $messageId = $this->messaging->send($message);
                    $this->logSuccess($token, $messageId);
                    return $messageId;
                } catch (MessagingException $e) {
                    $attempt++;
                    if ($attempt >= $this->maxRetries) {
                        throw $e;
                    }
                    usleep($this->retryDelay * 1000);
                }
            } while ($attempt < $this->maxRetries);
        } catch (MessagingException $e) {
            $this->logError($token, $e);
            throw new FCMException(
                "Failed to send notification: {$e->getMessage()}",
                $e->getCode(),
                $e,
                ['token' => $token, 'notification' => $notification]
            );
        }
    }

    public function sendMulticast(array $tokens, array $notification, bool $detailed = false): array
    {
        if (empty($tokens)) {
            throw new FCMException('No tokens provided for multicast message');
        }

        if (count($tokens) > config('fcm.max_tokens', 500)) {
            throw new FCMException('Too many tokens provided for multicast message. Maximum is 500.');
        }

        try {
            $this->validateNotification($notification);

            $message = $this->buildMessage(null, $notification);

            Log::channel(config('fcm.logging.channel'))
                ->info('Sending FCM multicast notification', [
                    'token_count' => count($tokens),
                    'notification' => $notification
                ]);

            $attempt = 0;
            do {
                try {
                    $report = $this->messaging->sendMulticast($message, $tokens);
                    return $this->processMulticastReport($report, $detailed);
                } catch (MessagingException $e) {
                    $attempt++;
                    if ($attempt >= $this->maxRetries) {
                        throw $e;
                    }
                    usleep($this->retryDelay * 1000);
                }
            } while ($attempt < $this->maxRetries);
        } catch (MessagingException $e) {
            $this->logError('multicast', $e);
            throw new FCMException(
                "Failed to send multicast notification: {$e->getMessage()}",
                $e->getCode(),
                $e,
                ['tokens' => $tokens, 'notification' => $notification]
            );
        }
    }

    public function validateToken(string $token): bool
    {
        $cacheKey = "fcm_token_validation:{$token}";

        return Cache::remember($cacheKey, now()->addHours(24), function () use ($token) {
            try {
                $this->messaging->validateRegistrationTokens([$token]);
                return true;
            } catch (MessagingException $e) {
                return false;
            }
        });
    }

    public function getDeliveryStatus(string $messageId): array
    {
        try {
            $status = $this->messaging->getMessageStatus($messageId);
            return [
                'message_id' => $messageId,
                'status' => $status->getStatus(),
                'delivered_at' => $status->getDeliveredAt(),
                'error' => $status->getError()
            ];
        } catch (MessagingException $e) {
            throw new FCMException(
                "Failed to get message status: {$e->getMessage()}",
                $e->getCode(),
                $e,
                ['message_id' => $messageId]
            );
        }
    }

    protected function buildMessage(?string $token, array $notification): Message
    {
        $message = $token
            ? CloudMessage::withTarget(MessageTarget::TOKEN, $token)
            : CloudMessage::new();

        $message->withNotification(FirebaseNotification::create(
            $notification['title'],
            $notification['message']
        ));

        if (isset($notification['data'])) {
            $message->withData($notification['data']);
        }

        if (isset($notification['android'])) {
            $message->withAndroidConfig(AndroidConfig::fromArray($notification['android']));
        }

        if (isset($notification['apns'])) {
            $message->withApnsConfig(ApnsConfig::fromArray($notification['apns']));
        }

        if (isset($notification['webpush'])) {
            $message->withWebPushConfig(WebPushConfig::fromArray($notification['webpush']));
        }

        return $message;
    }

    protected function validateNotification(array $notification): void
    {
        $required = ['title', 'message'];
        $missing = array_diff($required, array_keys($notification));

        if (!empty($missing)) {
            throw new FCMException(
                'Missing required notification fields: ' . implode(', ', $missing)
            );
        }
    }

    protected function processMulticastReport(MulticastSendReport $report, bool $detailed): array
    {
        $result = [
            'success_count' => $report->successes()->count(),
            'failure_count' => $report->failures()->count(),
        ];

        if ($detailed) {
            $result['results'] = $report->getItems()->map(function ($item) {
                return [
                    'status' => $item->isSuccess() ? 'sent' : 'failed',
                    'error' => $item->isSuccess() ? null : $item->error()->getMessage()
                ];
            })->toArray();
        }

        return $result;
    }

    protected function logSuccess(string $token, string $messageId): void
    {
        Log::channel(config('fcm.logging.channel'))
            ->info('FCM notification sent successfully', [
                'token' => $token,
                'message_id' => $messageId
            ]);
    }

    protected function logError(string $token, \Exception $e): void
    {
        Log::channel(config('fcm.logging.channel'))
            ->error('FCM notification failed', [
                'token' => $token,
                'error' => $e->getMessage(),
                'code' => $e->getCode()
            ]);
    }

    /**
     * Send notification to a specific token
     *
     * @param string $token The FCM token
     * @param array|Notification $notification The notification data or model
     * @return array Result of the send operation
     */
    public function sendToToken(string $token, $notification): array
    {
        try {
            // Check if messaging is initialized
            if (!$this->messaging) {
                Log::error('Firebase messaging not initialized');
                return [
                    'success' => false,
                    'error' => 'Firebase messaging not initialized',
                    'message_id' => null
                ];
            }

            // Convert notification to array if it's a model
            if ($notification instanceof Notification) {
                $notificationData = [
                    'title' => $notification->title,
                    'message' => $notification->message,
                    'category' => $notification->category ?? 'general',
                    'severity' => $notification->severity ?? 'medium',
                    'barangay' => $notification->barangay ?? 'All Areas',
                    'notification_id' => $notification->id,
                    'time' => $notification->created_at ? $notification->created_at->toIso8601String() : now()->toIso8601String()
                ];
            } else {
                $notificationData = $notification;
            }

            // Validate required fields
            if (empty($notificationData['title']) || empty($notificationData['message'])) {
                return [
                    'success' => false,
                    'error' => 'Title and message are required',
                    'message_id' => null
                ];
            }

            // Create FCM message
            $message = CloudMessage::withTarget('token', $token)
                ->withNotification(
                    FirebaseNotification::create(
                        $notificationData['title'],
                        $notificationData['message']
                    )
                )
                ->withData($notificationData);

            // Send the message
            $messageId = $this->messaging->send($message);

            Log::info('FCM notification sent successfully to single token', [
                'token_hash' => hash('sha256', $token),
                'message_id' => $messageId,
                'title' => $notificationData['title']
            ]);

            return [
                'success' => true,
                'error' => null,
                'message_id' => $messageId
            ];

        } catch (MessagingException $e) {
            $errorMessage = $e->getMessage();
            Log::error('FCM notification failure', [
                'token_hash' => hash('sha256', $token),
                'error' => $errorMessage,
                'code' => $e->getCode()
            ]);

            return [
                'success' => false,
                'error' => $errorMessage,
                'message_id' => null
            ];
        } catch (\Exception $e) {
            $errorMessage = 'Unexpected error: ' . $e->getMessage();
            Log::error('FCM unexpected error', [
                'token_hash' => hash('sha256', $token),
                'error' => $errorMessage,
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $errorMessage,
                'message_id' => null
            ];
        }
    }

    public function sendNotification(Notification $notification, array $tokens = null)
    {
        // Check rate limit
        if (!$this->checkRateLimit()) {
            Log::warning('Rate limit exceeded for FCM notifications');
            return [
                'success' => false,
                'message' => 'Rate limit exceeded. Please try again later.',
                'success_count' => 0,
                'failure_count' => 0,
                'invalid_tokens' => 0
            ];
        }

        if ($tokens === null) {
            $tokens = $this->getActiveTokens();
        }

        if (empty($tokens)) {
            Log::info('No active device tokens found');
            return [
                'success' => false,
                'message' => 'No active device tokens found',
                'success_count' => 0,
                'failure_count' => 0,
                'invalid_tokens' => 0
            ];
        }

        // Check if Firebase Messaging is initialized
        if (!$this->messaging) {
            // Try to initialize Firebase again
            try {
                $this->initializeFirebase();
            } catch (FCMException $e) {
                // If initialization fails again, handle the error
                return $this->handleMessagingError();
            }

            // If we still don't have messaging after re-initialization, handle the error
            if (!$this->messaging) {
                return $this->handleMessagingError();
            }
        }

        // Create the notification payload with customization
        $fcmNotification = $this->createNotificationPayload($notification);
        $data = $this->createDataPayload($notification);

        // Send to all tokens in batches
        $successCount = 0;
        $failureCount = 0;
        $invalidTokens = [];
        $errors = [];

        // Process tokens in chunks
        foreach (array_chunk($tokens, $this->batchSize) as $tokenBatch) {
            $result = $this->sendBatchNotifications($tokenBatch, $fcmNotification, $data);
            $successCount += $result['success_count'];
            $failureCount += $result['failure_count'];
            $invalidTokens = array_merge($invalidTokens, $result['invalid_tokens']);

            if (!empty($result['error'])) {
                $errors[] = $result['error'];
            }
        }

        // Handle results
        if ($successCount === 0) {
            // If no notifications were sent successfully, don't mark as sent
            if (!empty($errors)) {
                return $this->handleSendError($errors, $failureCount, $invalidTokens);
            } else {
                // No errors but also no successes - likely all tokens were invalid
                Log::warning('No notifications sent successfully', [
                    'notification_id' => $notification->id,
                    'failure_count' => $failureCount,
                    'invalid_tokens' => count($invalidTokens)
                ]);
                return [
                    'success' => false,
                    'message' => 'No notifications were delivered successfully',
                    'success_count' => 0,
                    'failure_count' => $failureCount,
                    'invalid_tokens' => count($invalidTokens)
                ];
            }
        }

        // Clean up invalid tokens
        if (!empty($invalidTokens)) {
            $this->deactivateInvalidTokens($invalidTokens);
        }

        // Mark notification as sent ONLY if at least one was successful
        $notification->sent = true;
        $notification->save();

        // Log success with detailed information
        Log::info('Notification marked as sent successfully', [
            'notification_id' => $notification->id,
            'title' => $notification->title,
            'success_count' => $successCount,
            'failure_count' => $failureCount,
            'invalid_tokens' => count($invalidTokens),
            'total_attempted' => $successCount + $failureCount + count($invalidTokens)
        ]);

        $this->logNotificationSuccess($notification, $successCount, $failureCount, $invalidTokens);

        return [
            'success' => true,
            'message' => "Notification sent to {$successCount} devices" . ($failureCount > 0 ? " ({$failureCount} failures)" : ""),
            'success_count' => $successCount,
            'failure_count' => $failureCount,
            'invalid_tokens' => count($invalidTokens)
        ];
    }

    protected function checkRateLimit()
    {
        $key = 'fcm_notifications_' . date('Y-m-d_H');
        $maxAttempts = $this->maxNotificationsPerMinute;
        $decaySeconds = 60; // 1 minute decay time

        return !RateLimiter::tooManyAttempts($key, $maxAttempts) && RateLimiter::hit($key, $decaySeconds);
    }

    /**
     * Create the notification payload for FCM
     *
     * @param Notification $notification The notification to create a payload for
     * @return array The notification payload
     */
    protected function createNotificationPayload(Notification $notification)
    {
        // Create rich notification title and body
        $richTitle = $this->createRichTitle($notification->title, $notification->category, $notification->severity);
        $richBody = $this->createRichBody($notification->message, null, $notification->barangay, $notification->severity);

        // Rich notification payload
        $payload = [
            'title' => $richTitle,
            'body' => $richBody,
            'sound' => $this->getNotificationSound($notification->severity),
            'priority' => $this->getNotificationPriority($notification->severity)
        ];

        // Add category-specific customizations
        if ($notification->category) {
            // Add color based on disaster type
            $payload['color'] = $this->getDisasterColor($notification->category);

            // Simple tag to prevent notification replacement
            $payload['tag'] = strtolower($notification->category) . '_' . $notification->id;
        }

        return $payload;
    }

    /**
     * Create rich notification title with category and severity
     *
     * @param string $originalTitle
     * @param string $category
     * @param string $severity
     * @return string
     */
    protected function createRichTitle($originalTitle, $category, $severity)
    {
        $categoryEmoji = $this->getCategoryEmoji($category);
        $severityText = $this->getSeverityText($severity);

        return "{$categoryEmoji} {$severityText}: {$originalTitle}";
    }

    /**
     * Create rich notification body with severity and affected areas information
     *
     * @param string $originalBody
     * @param string|array|null $affectedAreas
     * @param string|null $barangay
     * @param string|null $severity
     * @return string
     */
    protected function createRichBody($originalBody, $affectedAreas, $barangay, $severity = null)
    {
        $richBody = $originalBody;

        // Add severity/priority level information only
        if ($severity) {
            $priorityIcon = $this->getPriorityIcon($severity);
            $priorityText = $this->getPriorityDescription($severity);
            $richBody .= "\n{$priorityIcon} Priority: {$priorityText}";
        }

        return $richBody;
    }

    /**
     * Get category emoji
     *
     * @param string $category
     * @return string
     */
    protected function getCategoryEmoji($category)
    {
        switch (strtolower($category)) {
            case 'earthquake': return '🌍';
            case 'typhoon': return '🌪️';
            case 'flood': return '🌊';
            case 'fire': return '🔥';
            case 'emergency': return '🚨';
            case 'evacuation': return '🏃‍♂️';
            case 'general': return '📢';
            case 'announcement': return '📣';
            default: return '⚠️';
        }
    }

    /**
     * Get severity text
     *
     * @param string $severity
     * @return string
     */
    protected function getSeverityText($severity)
    {
        switch (strtolower($severity)) {
            case 'high': return 'URGENT';
            case 'medium': return 'ALERT';
            case 'low': return 'INFO';
            default: return 'NOTICE';
        }
    }

    /**
     * Get priority icon based on severity
     *
     * @param string $severity
     * @return string
     */
    protected function getPriorityIcon($severity)
    {
        switch (strtolower($severity)) {
            case 'high': return '🔴';
            case 'medium': return '🟡';
            case 'low': return '🟢';
            default: return '🔵';
        }
    }

    /**
     * Get priority description based on severity
     *
     * @param string $severity
     * @return string
     */
    protected function getPriorityDescription($severity)
    {
        switch (strtolower($severity)) {
            case 'high': return 'Critical - Immediate Action Required';
            case 'medium': return 'High - Action Required Soon';
            case 'low': return 'Normal - For Your Information';
            default: return 'Standard - General Notice';
        }
    }

    /**
     * Format affected areas for display in notifications
     *
     * @param string|array|null $affectedAreas
     * @return string|null
     */
    protected function formatAffectedAreas($affectedAreas)
    {
        if (!$affectedAreas) {
            return null;
        }

        try {
            // Handle both string (JSON) and array formats
            if (is_string($affectedAreas)) {
                $areas = json_decode($affectedAreas, true);
            } elseif (is_array($affectedAreas)) {
                $areas = $affectedAreas;
            } else {
                return null;
            }

            if (!is_array($areas) || empty($areas)) {
                return null;
            }

            // Extract area names - handle both old and new data structures
            $areaNames = [];
            foreach ($areas as $area) {
                if (is_array($area)) {
                    // New structure: {name: "Cebu City", geojson: {...}}
                    if (isset($area['name'])) {
                        $areaNames[] = $area['name'];
                    }
                    // Old structure: {type: "Polygon", coordinates: [...]} - skip these
                    elseif (isset($area['type']) && $area['type'] === 'Polygon') {
                        // Skip polygon data without names
                        continue;
                    }
                } elseif (is_string($area)) {
                    // Direct string area name
                    $areaNames[] = $area;
                }
            }

            if (empty($areaNames)) {
                return null;
            }

            // Format the area names
            if (count($areaNames) === 1) {
                return $areaNames[0];
            } elseif (count($areaNames) === 2) {
                return implode(' and ', $areaNames);
            } else {
                // For 3 or more areas: "Area1, Area2, and Area3"
                $lastArea = array_pop($areaNames);
                return implode(', ', $areaNames) . ', and ' . $lastArea;
            }

        } catch (\Exception $e) {
            \Log::warning('Error formatting affected areas', [
                'affected_areas' => $affectedAreas,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }



    /**
     * Create the data payload for FCM
     *
     * @param Notification $notification The notification to create a data payload for
     * @return array The data payload
     */
    protected function createDataPayload(Notification $notification)
    {
        // Base data payload - keep it minimal to avoid "message too big" errors
        $payload = [
            'title' => $notification->title,  // Original title for modal
            'body' => $notification->message, // Original message for modal
            'category' => $notification->category,
            'severity' => $notification->severity,
            'notification_id' => (string)$notification->id,
            'time' => now()->format('Y-m-d H:i:s')
        ];

        // Only add barangay if it's not the default
        if ($notification->barangay && $notification->barangay !== 'All Areas') {
            $payload['barangay'] = $notification->barangay;
        }



        // Add minimal action data based on notification type
        if ($notification->category) {
            switch (strtolower($notification->category)) {
                case 'earthquake':
                case 'flood':
                case 'typhoon':
                    $payload['action'] = 'evacuation';
                    break;
                default:
                    $payload['action'] = 'general';
                    break;
            }
        }

        return $payload;
    }

    /**
     * Get the appropriate icon for a notification category
     *
     * @param string $category The notification category
     * @return string The icon name
     */
    protected function getCategoryIcon($category)
    {
        switch (strtolower($category)) {
            case 'earthquake':
                return 'earthquake_icon';
            case 'flood':
                return 'flood_icon';
            case 'typhoon':
                return 'typhoon_icon';
            default:
                $icon = '🗺️'; // Placeholder, replace with actual icon
                break;
        }
        return $icon;
    }

    /**
     * Get the appropriate color for a disaster category
     *
     * @param string $category The notification category
     * @return string The color in hex format
     */
    protected function getDisasterColor($category)
    {
        switch (strtolower($category)) {
            case 'earthquake':
                return '#FFA500'; // Orange for earthquake
            case 'flood':
                return '#0066CC'; // Blue for flood
            case 'typhoon':
                return '#008000'; // Green for typhoon
            case 'fire':
                return '#FF0000'; // Red for fire
            default:
                return '#03B2DD'; // Default app color
        }
    }

    /**
     * Get the appropriate color for a notification severity
     *
     * @param string $severity The notification severity
     * @return string The color in hex format
     */
    protected function getSeverityColor($severity)
    {
        switch (strtolower($severity)) {
            case 'high':
                return '#FF0000'; // Red
            case 'medium':
                return '#FFA500'; // Orange
            default:
                return '#0000FF'; // Blue
        }
    }

    protected function getNotificationSound($severity)
    {
        switch ($severity) {
            case 'high':
                return 'emergency.mp3';
            case 'medium':
                return 'alert.mp3';
            default:
                return 'default.mp3';
        }
    }

    protected function getNotificationPriority($severity)
    {
        switch ($severity) {
            case 'high':
                return 'high';
            case 'medium':
                return 'normal';
            default:
                return 'low';
        }
    }

    protected function handleMessagingError()
    {
        $credentialsPath = config('firebase.projects.app.credentials', 'firebase-service-account.json');

        // Handle different path formats (same logic as initializeFirebase)
        if (str_starts_with($credentialsPath, 'storage/')) {
            $serviceAccount = base_path($credentialsPath);
        } elseif (basename($credentialsPath) === $credentialsPath) {
            $serviceAccount = storage_path($credentialsPath);
        } else {
            $serviceAccount = $credentialsPath;
        }

        $errorMessage = '';

        if (!file_exists($serviceAccount)) {
            $errorMessage = 'Firebase service account file not found. Please check that the file exists at: ' . $serviceAccount;
        } else {
            $serviceAccountData = json_decode(file_get_contents($serviceAccount), true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $errorMessage = 'Firebase service account file is not valid JSON. Error: ' . json_last_error_msg();
            } else if (isset($serviceAccountData['private_key_id']) && $serviceAccountData['private_key_id'] === '1234567890abcdef1234567890abcdef12345678') {
                $errorMessage = 'Firebase service account file contains placeholder values. Please replace with actual service account credentials.';
            } else {
                $errorMessage = 'Firebase messaging not initialized. Please check your Firebase configuration.';
            }
        }

        Log::error($errorMessage);
        return [
            'success' => false,
            'message' => $errorMessage,
            'success_count' => 0,
            'failure_count' => 0,
            'invalid_tokens' => 0
        ];
    }

    protected function handleSendError($errors, $failureCount, $invalidTokens)
    {
        $errorMessage = 'Failed to send notifications: ' . implode('; ', array_unique($errors));
        Log::error($errorMessage);

        return [
            'success' => false,
            'message' => $errorMessage,
            'success_count' => 0,
            'failure_count' => $failureCount,
            'invalid_tokens' => count($invalidTokens)
        ];
    }

    protected function logNotificationSuccess($notification, $successCount, $failureCount, $invalidTokens)
    {
        Log::info('Notification process completed', [
            'id' => $notification->id,
            'sent' => $notification->sent,
            'success_count' => $successCount,
            'failure_count' => $failureCount,
            'invalid_tokens' => count($invalidTokens)
        ]);
    }

    protected function sendBatchNotifications(array $tokenBatch, array $fcmNotification, array $data)
    {
        $successCount = 0;
        $failureCount = 0;
        $invalidTokens = [];

        try {
            $messages = [];

            foreach ($tokenBatch as $token) {
                if ($this->isValidTokenFormat($token)) {
                    // Create message with both notification and data payload for better delivery
                    $notification = FirebaseNotification::create(
                        $fcmNotification['title'],
                        $fcmNotification['body']
                    );

                    // Only add image URL if it's not null
                    if (!empty($fcmNotification['icon'])) {
                        $notification = $notification->withImageUrl($fcmNotification['icon']);
                    }

                    $message = CloudMessage::withTarget('token', $token)
                        ->withNotification($notification)
                        ->withData($data)
                        ->withAndroidConfig(
                            AndroidConfig::fromArray([
                                'priority' => $this->getAndroidPriority($data),
                                'notification' => [
                                    'title' => $fcmNotification['title'],
                                    'body' => $fcmNotification['body'],
                                    'icon' => $fcmNotification['icon'] ?? 'ic_notification',
                                    'color' => $fcmNotification['color'] ?? '#03B2DD',
                                    'sound' => $this->getEmergencySound($data),
                                    'channel_id' => $this->getNotificationChannel($data),
                                    'visibility' => $this->getNotificationVisibility($data),
                                    'importance' => $this->getNotificationImportance($data)
                                ]
                            ])
                        );

                    $messages[] = $message;
                } else {
                    $invalidTokens[] = $token;
                    Log::warning('Invalid token format', ['token' => $this->maskToken($token)]);
                }
            }

            if (empty($messages)) {
                return [
                    'success_count' => 0,
                    'failure_count' => 0,
                    'invalid_tokens' => $invalidTokens
                ];
            }

            // Use direct HTTP implementation to bypass Firebase SDK SSL issues
            return $this->sendBatchNotificationsDirectHTTP($tokenBatch, $fcmNotification, $data);

            return [
                'success_count' => $successCount,
                'failure_count' => $failureCount,
                'invalid_tokens' => $invalidTokens
            ];
        } catch (\Exception $e) {
            Log::error('Failed to send batch of FCM notifications', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'batch_size' => count($tokenBatch)
            ]);

            return [
                'success_count' => $successCount,
                'failure_count' => $failureCount + count($tokenBatch),
                'invalid_tokens' => $invalidTokens,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Send batch notifications using direct HTTP implementation
     * This bypasses Firebase SDK SSL issues
     */
    protected function sendBatchNotificationsDirectHTTP(array $tokenBatch, array $fcmNotification, array $data)
    {
        $successCount = 0;
        $failureCount = 0;
        $invalidTokens = [];

        try {
            // Get Firebase access token
            $accessToken = $this->getFirebaseAccessToken();
            if (!$accessToken) {
                throw new \Exception('Failed to get Firebase access token');
            }

            $serviceAccount = json_decode(file_get_contents(storage_path('firebase-service-account.json')), true);
            $projectId = $serviceAccount['project_id'];

            foreach ($tokenBatch as $token) {
                if (!$this->isValidTokenFormat($token)) {
                    $invalidTokens[] = $token;
                    Log::warning('Invalid token format', ['token' => $this->maskToken($token)]);
                    continue;
                }

                // Create FCM message payload
                $message = [
                    'message' => [
                        'token' => $token,
                        'notification' => [
                            'title' => $fcmNotification['title'],
                            'body' => $fcmNotification['body']
                        ],
                        'data' => array_map('strval', $data), // FCM requires all data values to be strings
                        'android' => [
                            'priority' => $this->getAndroidPriority($data),
                            'notification' => [
                                'channel_id' => $this->getNotificationChannel($data),
                                'sound' => $this->getEmergencySound($data),
                                'icon' => $fcmNotification['icon'] ?? 'ic_notification',
                                'color' => $fcmNotification['color'] ?? '#03B2DD'
                            ]
                        ]
                    ]
                ];

                // Send HTTP request to FCM
                $context = stream_context_create([
                    'http' => [
                        'method' => 'POST',
                        'header' => [
                            'Authorization: Bearer ' . $accessToken,
                            'Content-Type: application/json'
                        ],
                        'content' => json_encode($message),
                        'timeout' => 30,
                        'ignore_errors' => true
                    ],
                    'ssl' => [
                        'verify_peer' => false,
                        'verify_peer_name' => false
                    ]
                ]);

                $fcmUrl = "https://fcm.googleapis.com/v1/projects/{$projectId}/messages:send";
                $response = file_get_contents($fcmUrl, false, $context);

                // Get HTTP response code
                $httpCode = null;
                if (isset($http_response_header)) {
                    foreach ($http_response_header as $header) {
                        if (preg_match('/^HTTP\/\d\.\d\s+(\d+)/', $header, $matches)) {
                            $httpCode = (int)$matches[1];
                            break;
                        }
                    }
                }

                if ($httpCode === 200) {
                    $result = json_decode($response, true);
                    if (isset($result['name'])) {
                        $successCount++;
                        Log::info('FCM notification sent successfully via direct HTTP', [
                            'token_hash' => hash('sha256', $token),
                            'message_id' => $result['name']
                        ]);
                    }
                } else {
                    $failureCount++;
                    $errorResponse = json_decode($response, true);
                    $errorMessage = $errorResponse['error']['message'] ?? 'Unknown error';

                    Log::error('FCM notification failure via direct HTTP', [
                        'token_hash' => hash('sha256', $token),
                        'error' => $errorMessage,
                        'http_code' => $httpCode
                    ]);

                    // Check if token is invalid
                    if (strpos($errorMessage, 'not found') !== false ||
                        strpos($errorMessage, 'UNREGISTERED') !== false ||
                        strpos($errorMessage, 'invalid') !== false) {
                        $invalidTokens[] = $token;
                    }
                }
            }

            return [
                'success_count' => $successCount,
                'failure_count' => $failureCount,
                'invalid_tokens' => $invalidTokens
            ];

        } catch (\Exception $e) {
            Log::error('Failed to send batch notifications via direct HTTP', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'batch_size' => count($tokenBatch)
            ]);

            return [
                'success_count' => $successCount,
                'failure_count' => $failureCount + count($tokenBatch),
                'invalid_tokens' => $invalidTokens,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get Firebase access token using service account
     */
    protected function getFirebaseAccessToken()
    {
        try {
            $serviceAccount = json_decode(file_get_contents(storage_path('firebase-service-account.json')), true);

            // Create JWT token
            $header = json_encode(['typ' => 'JWT', 'alg' => 'RS256']);
            $now = time();
            $payload = json_encode([
                'iss' => $serviceAccount['client_email'],
                'scope' => 'https://www.googleapis.com/auth/firebase.messaging',
                'aud' => 'https://oauth2.googleapis.com/token',
                'exp' => $now + 3600,
                'iat' => $now
            ]);

            $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
            $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));

            $signature = '';
            openssl_sign($base64Header . "." . $base64Payload, $signature, $serviceAccount['private_key'], 'SHA256');
            $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

            $jwt = $base64Header . "." . $base64Payload . "." . $base64Signature;

            // Get access token
            $tokenData = [
                'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                'assertion' => $jwt
            ];

            $context = stream_context_create([
                'http' => [
                    'method' => 'POST',
                    'header' => 'Content-Type: application/x-www-form-urlencoded',
                    'content' => http_build_query($tokenData),
                    'timeout' => 30
                ],
                'ssl' => [
                    'verify_peer' => false,
                    'verify_peer_name' => false
                ]
            ]);

            $response = file_get_contents('https://oauth2.googleapis.com/token', false, $context);
            if ($response === false) {
                return null;
            }

            $tokenResponse = json_decode($response, true);
            return $tokenResponse['access_token'] ?? null;

        } catch (\Exception $e) {
            Log::error('Failed to get Firebase access token', [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    protected function getActiveTokens()
    {
        return DeviceToken::where('is_active', true)->pluck('token')->toArray();
    }

    protected function deactivateInvalidTokens(array $tokens)
    {
        $count = DeviceToken::whereIn('token', $tokens)->update(['is_active' => false]);
        Log::info('Deactivated invalid tokens', ['count' => $count]);
        return $count;
    }

    protected function isValidTokenFormat(string $token)
    {
        return preg_match('/^[a-zA-Z0-9:_\-]+$/', $token) && strlen($token) > 20;
    }

    protected function extractErrorCode(string $errorMessage)
    {
        if (preg_match('/error(?:\s+code)?[:\s]+([a-z0-9_-]+)/i', $errorMessage, $matches)) {
            return $matches[1];
        }
        return '';
    }

    protected function maskToken(string $token)
    {
        if (strlen($token) > 10) {
            return substr($token, 0, 5) . '...' . substr($token, -5);
        }
        return substr($token, 0, 3) . '...';
    }

    /**
     * Get Android priority based on notification data
     */
    private function getAndroidPriority(array $data): string
    {
        $severity = $data['severity'] ?? 'medium';
        $category = strtolower($data['category'] ?? '');

        // Emergency categories get high priority
        $emergencyCategories = ['earthquake', 'flood', 'typhoon', 'fire', 'landslide'];

        if (in_array($category, $emergencyCategories) ||
            in_array($severity, ['high', 'critical'])) {
            return 'high';
        }

        return 'normal';
    }

    /**
     * Get emergency sound based on notification data
     */
    private function getEmergencySound(array $data): string
    {
        $severity = $data['severity'] ?? 'medium';
        $category = strtolower($data['category'] ?? '');

        // Emergency categories get special sounds
        $emergencyCategories = ['earthquake', 'flood', 'typhoon', 'fire', 'landslide'];

        if (in_array($category, $emergencyCategories)) {
            return "emergency_{$category}.mp3";
        }

        if ($severity === 'critical') {
            return 'emergency_critical.mp3';
        }

        if ($severity === 'high') {
            return 'emergency_alert.mp3';
        }

        return 'default';
    }

    /**
     * Get notification channel based on notification data
     */
    private function getNotificationChannel(array $data): string
    {
        $severity = $data['severity'] ?? 'medium';
        $category = strtolower($data['category'] ?? '');

        // Emergency categories get emergency channel
        $emergencyCategories = ['earthquake', 'flood', 'typhoon', 'fire', 'landslide'];

        if (in_array($category, $emergencyCategories) ||
            in_array($severity, ['high', 'critical'])) {
            return 'emergency_alerts';
        }

        return 'alerto_notifications';
    }

    /**
     * Get notification visibility (for lock screen display)
     */
    private function getNotificationVisibility(array $data): string
    {
        $severity = $data['severity'] ?? 'medium';
        $category = strtolower($data['category'] ?? '');

        // Emergency notifications should show on lock screen
        $emergencyCategories = ['earthquake', 'flood', 'typhoon', 'fire', 'landslide'];

        if (in_array($category, $emergencyCategories) ||
            in_array($severity, ['high', 'critical'])) {
            return 'public'; // Shows on lock screen
        }

        return 'private'; // Requires unlock to view
    }

    /**
     * Get notification importance level
     */
    private function getNotificationImportance(array $data): string
    {
        $severity = $data['severity'] ?? 'medium';
        $category = strtolower($data['category'] ?? '');

        // Emergency categories get maximum importance
        $emergencyCategories = ['earthquake', 'flood', 'typhoon', 'fire', 'landslide'];

        if (in_array($category, $emergencyCategories) || $severity === 'critical') {
            return 'max'; // Bypasses Do Not Disturb
        }

        if ($severity === 'high') {
            return 'high';
        }

        return 'default';
    }
}