{"version": 3, "sources": ["../../../../../../node_modules/ionicons/dist/esm-es5/index-b72adede.js", "../../../../../../node_modules/ionicons/dist/esm-es5/utils-2c56d1c8.js"], "sourcesContent": ["var __extends = this && this.__extends || function () {\n  var e = function (t, n) {\n    e = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (e, t) {\n      e.__proto__ = t;\n    } || function (e, t) {\n      for (var n in t) if (Object.prototype.hasOwnProperty.call(t, n)) e[n] = t[n];\n    };\n    return e(t, n);\n  };\n  return function (t, n) {\n    if (typeof n !== \"function\" && n !== null) throw new TypeError(\"Class extends value \" + String(n) + \" is not a constructor or null\");\n    e(t, n);\n    function r() {\n      this.constructor = t;\n    }\n    t.prototype = n === null ? Object.create(n) : (r.prototype = n.prototype, new r());\n  };\n}();\nvar __awaiter = this && this.__awaiter || function (e, t, n, r) {\n  function a(e) {\n    return e instanceof n ? e : new n(function (t) {\n      t(e);\n    });\n  }\n  return new (n || (n = Promise))(function (n, o) {\n    function s(e) {\n      try {\n        l(r.next(e));\n      } catch (e) {\n        o(e);\n      }\n    }\n    function i(e) {\n      try {\n        l(r[\"throw\"](e));\n      } catch (e) {\n        o(e);\n      }\n    }\n    function l(e) {\n      e.done ? n(e.value) : a(e.value).then(s, i);\n    }\n    l((r = r.apply(e, t || [])).next());\n  });\n};\nvar __generator = this && this.__generator || function (e, t) {\n  var n = {\n      label: 0,\n      sent: function () {\n        if (o[0] & 1) throw o[1];\n        return o[1];\n      },\n      trys: [],\n      ops: []\n    },\n    r,\n    a,\n    o,\n    s;\n  return s = {\n    next: i(0),\n    throw: i(1),\n    return: i(2)\n  }, typeof Symbol === \"function\" && (s[Symbol.iterator] = function () {\n    return this;\n  }), s;\n  function i(e) {\n    return function (t) {\n      return l([e, t]);\n    };\n  }\n  function l(i) {\n    if (r) throw new TypeError(\"Generator is already executing.\");\n    while (s && (s = 0, i[0] && (n = 0)), n) try {\n      if (r = 1, a && (o = i[0] & 2 ? a[\"return\"] : i[0] ? a[\"throw\"] || ((o = a[\"return\"]) && o.call(a), 0) : a.next) && !(o = o.call(a, i[1])).done) return o;\n      if (a = 0, o) i = [i[0] & 2, o.value];\n      switch (i[0]) {\n        case 0:\n        case 1:\n          o = i;\n          break;\n        case 4:\n          n.label++;\n          return {\n            value: i[1],\n            done: false\n          };\n        case 5:\n          n.label++;\n          a = i[1];\n          i = [0];\n          continue;\n        case 7:\n          i = n.ops.pop();\n          n.trys.pop();\n          continue;\n        default:\n          if (!(o = n.trys, o = o.length > 0 && o[o.length - 1]) && (i[0] === 6 || i[0] === 2)) {\n            n = 0;\n            continue;\n          }\n          if (i[0] === 3 && (!o || i[1] > o[0] && i[1] < o[3])) {\n            n.label = i[1];\n            break;\n          }\n          if (i[0] === 6 && n.label < o[1]) {\n            n.label = o[1];\n            o = i;\n            break;\n          }\n          if (o && n.label < o[2]) {\n            n.label = o[2];\n            n.ops.push(i);\n            break;\n          }\n          if (o[2]) n.ops.pop();\n          n.trys.pop();\n          continue;\n      }\n      i = t.call(e, n);\n    } catch (e) {\n      i = [6, e];\n      a = 0;\n    } finally {\n      r = o = 0;\n    }\n    if (i[0] & 5) throw i[1];\n    return {\n      value: i[0] ? i[1] : void 0,\n      done: true\n    };\n  }\n};\nvar __spreadArray = this && this.__spreadArray || function (e, t, n) {\n  if (n || arguments.length === 2) for (var r = 0, a = t.length, o; r < a; r++) {\n    if (o || !(r in t)) {\n      if (!o) o = Array.prototype.slice.call(t, 0, r);\n      o[r] = t[r];\n    }\n  }\n  return e.concat(o || Array.prototype.slice.call(t));\n};\nvar NAMESPACE = \"ionicons\";\nvar scopeId;\nvar hostTagName;\nvar isSvgMode = false;\nvar queuePending = false;\nvar getAssetPath = function (e) {\n  var t = new URL(e, plt.$resourcesUrl$);\n  return t.origin !== win.location.origin ? t.href : t.pathname;\n};\nvar setAssetPath = function (e) {\n  return plt.$resourcesUrl$ = e;\n};\nvar createTime = function (e, t) {\n  if (t === void 0) {\n    t = \"\";\n  }\n  {\n    return function () {\n      return;\n    };\n  }\n};\nvar uniqueTime = function (e, t) {\n  {\n    return function () {\n      return;\n    };\n  }\n};\nvar HYDRATED_CSS = \"{visibility:hidden}.hydrated{visibility:inherit}\";\nvar XLINK_NS = \"http://www.w3.org/1999/xlink\";\nvar EMPTY_OBJ = {};\nvar isDef = function (e) {\n  return e != null;\n};\nvar isComplexType = function (e) {\n  e = typeof e;\n  return e === \"object\" || e === \"function\";\n};\nfunction queryNonceMetaTagContent(e) {\n  var t, n, r;\n  return (r = (n = (t = e.head) === null || t === void 0 ? void 0 : t.querySelector('meta[name=\"csp-nonce\"]')) === null || n === void 0 ? void 0 : n.getAttribute(\"content\")) !== null && r !== void 0 ? r : undefined;\n}\nvar h = function (e, t) {\n  var n = [];\n  for (var r = 2; r < arguments.length; r++) {\n    n[r - 2] = arguments[r];\n  }\n  var a = null;\n  var o = null;\n  var s = false;\n  var i = false;\n  var l = [];\n  var c = function (t) {\n    for (var n = 0; n < t.length; n++) {\n      a = t[n];\n      if (Array.isArray(a)) {\n        c(a);\n      } else if (a != null && typeof a !== \"boolean\") {\n        if (s = typeof e !== \"function\" && !isComplexType(a)) {\n          a = String(a);\n        }\n        if (s && i) {\n          l[l.length - 1].$text$ += a;\n        } else {\n          l.push(s ? newVNode(null, a) : a);\n        }\n        i = s;\n      }\n    }\n  };\n  c(n);\n  if (t) {\n    if (t.key) {\n      o = t.key;\n    }\n    {\n      var u = t.className || t.class;\n      if (u) {\n        t.class = typeof u !== \"object\" ? u : Object.keys(u).filter(function (e) {\n          return u[e];\n        }).join(\" \");\n      }\n    }\n  }\n  var f = newVNode(e, null);\n  f.$attrs$ = t;\n  if (l.length > 0) {\n    f.$children$ = l;\n  }\n  {\n    f.$key$ = o;\n  }\n  return f;\n};\nvar newVNode = function (e, t) {\n  var n = {\n    $flags$: 0,\n    $tag$: e,\n    $text$: t,\n    $elm$: null,\n    $children$: null\n  };\n  {\n    n.$attrs$ = null;\n  }\n  {\n    n.$key$ = null;\n  }\n  return n;\n};\nvar Host = {};\nvar isHost = function (e) {\n  return e && e.$tag$ === Host;\n};\nvar parsePropertyValue = function (e, t) {\n  if (e != null && !isComplexType(e)) {\n    if (t & 4) {\n      return e === \"false\" ? false : e === \"\" || !!e;\n    }\n    if (t & 1) {\n      return String(e);\n    }\n    return e;\n  }\n  return e;\n};\nvar getElement = function (e) {\n  return getHostRef(e).$hostElement$;\n};\nvar emitEvent = function (e, t, n) {\n  var r = plt.ce(t, n);\n  e.dispatchEvent(r);\n  return r;\n};\nvar rootAppliedStyles = new WeakMap();\nvar registerStyle = function (e, t, n) {\n  var r = styles.get(e);\n  if (supportsConstructableStylesheets && n) {\n    r = r || new CSSStyleSheet();\n    if (typeof r === \"string\") {\n      r = t;\n    } else {\n      r.replaceSync(t);\n    }\n  } else {\n    r = t;\n  }\n  styles.set(e, r);\n};\nvar addStyle = function (e, t, n) {\n  var r;\n  var a = getScopeId(t);\n  var o = styles.get(a);\n  e = e.nodeType === 11 ? e : doc;\n  if (o) {\n    if (typeof o === \"string\") {\n      e = e.head || e;\n      var s = rootAppliedStyles.get(e);\n      var i = void 0;\n      if (!s) {\n        rootAppliedStyles.set(e, s = new Set());\n      }\n      if (!s.has(a)) {\n        {\n          i = doc.createElement(\"style\");\n          i.innerHTML = o;\n          var l = (r = plt.$nonce$) !== null && r !== void 0 ? r : queryNonceMetaTagContent(doc);\n          if (l != null) {\n            i.setAttribute(\"nonce\", l);\n          }\n          e.insertBefore(i, e.querySelector(\"link\"));\n        }\n        if (s) {\n          s.add(a);\n        }\n      }\n    } else if (!e.adoptedStyleSheets.includes(o)) {\n      e.adoptedStyleSheets = __spreadArray(__spreadArray([], e.adoptedStyleSheets, true), [o], false);\n    }\n  }\n  return a;\n};\nvar attachStyles = function (e) {\n  var t = e.$cmpMeta$;\n  var n = e.$hostElement$;\n  var r = t.$flags$;\n  var a = createTime(\"attachStyles\", t.$tagName$);\n  var o = addStyle(n.shadowRoot ? n.shadowRoot : n.getRootNode(), t);\n  if (r & 10) {\n    n[\"s-sc\"] = o;\n    n.classList.add(o + \"-h\");\n  }\n  a();\n};\nvar getScopeId = function (e, t) {\n  return \"sc-\" + e.$tagName$;\n};\nvar setAccessor = function (e, t, n, r, a, o) {\n  if (n !== r) {\n    var s = isMemberInElement(e, t);\n    var i = t.toLowerCase();\n    if (t === \"class\") {\n      var l = e.classList;\n      var c = parseClassList(n);\n      var u = parseClassList(r);\n      l.remove.apply(l, c.filter(function (e) {\n        return e && !u.includes(e);\n      }));\n      l.add.apply(l, u.filter(function (e) {\n        return e && !c.includes(e);\n      }));\n    } else if (t === \"style\") {\n      {\n        for (var f in n) {\n          if (!r || r[f] == null) {\n            if (f.includes(\"-\")) {\n              e.style.removeProperty(f);\n            } else {\n              e.style[f] = \"\";\n            }\n          }\n        }\n      }\n      for (var f in r) {\n        if (!n || r[f] !== n[f]) {\n          if (f.includes(\"-\")) {\n            e.style.setProperty(f, r[f]);\n          } else {\n            e.style[f] = r[f];\n          }\n        }\n      }\n    } else if (t === \"key\") ;else if (t === \"ref\") {\n      if (r) {\n        r(e);\n      }\n    } else if (!s && t[0] === \"o\" && t[1] === \"n\") {\n      if (t[2] === \"-\") {\n        t = t.slice(3);\n      } else if (isMemberInElement(win, i)) {\n        t = i.slice(2);\n      } else {\n        t = i[2] + t.slice(3);\n      }\n      if (n) {\n        plt.rel(e, t, n, false);\n      }\n      if (r) {\n        plt.ael(e, t, r, false);\n      }\n    } else {\n      var $ = isComplexType(r);\n      if ((s || $ && r !== null) && !a) {\n        try {\n          if (!e.tagName.includes(\"-\")) {\n            var d = r == null ? \"\" : r;\n            if (t === \"list\") {\n              s = false;\n            } else if (n == null || e[t] != d) {\n              e[t] = d;\n            }\n          } else {\n            e[t] = r;\n          }\n        } catch (e) {}\n      }\n      var v = false;\n      {\n        if (i !== (i = i.replace(/^xlink\\:?/, \"\"))) {\n          t = i;\n          v = true;\n        }\n      }\n      if (r == null || r === false) {\n        if (r !== false || e.getAttribute(t) === \"\") {\n          if (v) {\n            e.removeAttributeNS(XLINK_NS, t);\n          } else {\n            e.removeAttribute(t);\n          }\n        }\n      } else if ((!s || o & 4 || a) && !$) {\n        r = r === true ? \"\" : r;\n        if (v) {\n          e.setAttributeNS(XLINK_NS, t, r);\n        } else {\n          e.setAttribute(t, r);\n        }\n      }\n    }\n  }\n};\nvar parseClassListRegex = /\\s/;\nvar parseClassList = function (e) {\n  return !e ? [] : e.split(parseClassListRegex);\n};\nvar updateElement = function (e, t, n, r) {\n  var a = t.$elm$.nodeType === 11 && t.$elm$.host ? t.$elm$.host : t.$elm$;\n  var o = e && e.$attrs$ || EMPTY_OBJ;\n  var s = t.$attrs$ || EMPTY_OBJ;\n  {\n    for (r in o) {\n      if (!(r in s)) {\n        setAccessor(a, r, o[r], undefined, n, t.$flags$);\n      }\n    }\n  }\n  for (r in s) {\n    setAccessor(a, r, o[r], s[r], n, t.$flags$);\n  }\n};\nvar createElm = function (e, t, n, r) {\n  var a = t.$children$[n];\n  var o = 0;\n  var s;\n  var i;\n  if (a.$text$ !== null) {\n    s = a.$elm$ = doc.createTextNode(a.$text$);\n  } else {\n    s = a.$elm$ = doc.createElement(a.$tag$);\n    {\n      updateElement(null, a, isSvgMode);\n    }\n    if (isDef(scopeId) && s[\"s-si\"] !== scopeId) {\n      s.classList.add(s[\"s-si\"] = scopeId);\n    }\n    if (a.$children$) {\n      for (o = 0; o < a.$children$.length; ++o) {\n        i = createElm(e, a, o);\n        if (i) {\n          s.appendChild(i);\n        }\n      }\n    }\n  }\n  return s;\n};\nvar addVnodes = function (e, t, n, r, a, o) {\n  var s = e;\n  var i;\n  if (s.shadowRoot && s.tagName === hostTagName) {\n    s = s.shadowRoot;\n  }\n  for (; a <= o; ++a) {\n    if (r[a]) {\n      i = createElm(null, n, a);\n      if (i) {\n        r[a].$elm$ = i;\n        s.insertBefore(i, t);\n      }\n    }\n  }\n};\nvar removeVnodes = function (e, t, n) {\n  for (var r = t; r <= n; ++r) {\n    var a = e[r];\n    if (a) {\n      var o = a.$elm$;\n      nullifyVNodeRefs(a);\n      if (o) {\n        o.remove();\n      }\n    }\n  }\n};\nvar updateChildren = function (e, t, n, r) {\n  var a = 0;\n  var o = 0;\n  var s = 0;\n  var i = 0;\n  var l = t.length - 1;\n  var c = t[0];\n  var u = t[l];\n  var f = r.length - 1;\n  var $ = r[0];\n  var d = r[f];\n  var v;\n  var p;\n  while (a <= l && o <= f) {\n    if (c == null) {\n      c = t[++a];\n    } else if (u == null) {\n      u = t[--l];\n    } else if ($ == null) {\n      $ = r[++o];\n    } else if (d == null) {\n      d = r[--f];\n    } else if (isSameVnode(c, $)) {\n      patch(c, $);\n      c = t[++a];\n      $ = r[++o];\n    } else if (isSameVnode(u, d)) {\n      patch(u, d);\n      u = t[--l];\n      d = r[--f];\n    } else if (isSameVnode(c, d)) {\n      patch(c, d);\n      e.insertBefore(c.$elm$, u.$elm$.nextSibling);\n      c = t[++a];\n      d = r[--f];\n    } else if (isSameVnode(u, $)) {\n      patch(u, $);\n      e.insertBefore(u.$elm$, c.$elm$);\n      u = t[--l];\n      $ = r[++o];\n    } else {\n      s = -1;\n      {\n        for (i = a; i <= l; ++i) {\n          if (t[i] && t[i].$key$ !== null && t[i].$key$ === $.$key$) {\n            s = i;\n            break;\n          }\n        }\n      }\n      if (s >= 0) {\n        p = t[s];\n        if (p.$tag$ !== $.$tag$) {\n          v = createElm(t && t[o], n, s);\n        } else {\n          patch(p, $);\n          t[s] = undefined;\n          v = p.$elm$;\n        }\n        $ = r[++o];\n      } else {\n        v = createElm(t && t[o], n, o);\n        $ = r[++o];\n      }\n      if (v) {\n        {\n          c.$elm$.parentNode.insertBefore(v, c.$elm$);\n        }\n      }\n    }\n  }\n  if (a > l) {\n    addVnodes(e, r[f + 1] == null ? null : r[f + 1].$elm$, n, r, o, f);\n  } else if (o > f) {\n    removeVnodes(t, a, l);\n  }\n};\nvar isSameVnode = function (e, t) {\n  if (e.$tag$ === t.$tag$) {\n    {\n      return e.$key$ === t.$key$;\n    }\n  }\n  return false;\n};\nvar patch = function (e, t) {\n  var n = t.$elm$ = e.$elm$;\n  var r = e.$children$;\n  var a = t.$children$;\n  var o = t.$text$;\n  if (o === null) {\n    {\n      {\n        updateElement(e, t, isSvgMode);\n      }\n    }\n    if (r !== null && a !== null) {\n      updateChildren(n, r, t, a);\n    } else if (a !== null) {\n      if (e.$text$ !== null) {\n        n.textContent = \"\";\n      }\n      addVnodes(n, null, t, a, 0, a.length - 1);\n    } else if (r !== null) {\n      removeVnodes(r, 0, r.length - 1);\n    }\n  } else if (e.$text$ !== o) {\n    n.data = o;\n  }\n};\nvar nullifyVNodeRefs = function (e) {\n  {\n    e.$attrs$ && e.$attrs$.ref && e.$attrs$.ref(null);\n    e.$children$ && e.$children$.map(nullifyVNodeRefs);\n  }\n};\nvar renderVdom = function (e, t, n) {\n  if (n === void 0) {\n    n = false;\n  }\n  var r = e.$hostElement$;\n  var a = e.$cmpMeta$;\n  var o = e.$vnode$ || newVNode(null, null);\n  var s = isHost(t) ? t : h(null, null, t);\n  hostTagName = r.tagName;\n  if (a.$attrsToReflect$) {\n    s.$attrs$ = s.$attrs$ || {};\n    a.$attrsToReflect$.map(function (e) {\n      var t = e[0],\n        n = e[1];\n      return s.$attrs$[n] = r[t];\n    });\n  }\n  if (n && s.$attrs$) {\n    for (var i = 0, l = Object.keys(s.$attrs$); i < l.length; i++) {\n      var c = l[i];\n      if (r.hasAttribute(c) && ![\"key\", \"ref\", \"style\", \"class\"].includes(c)) {\n        s.$attrs$[c] = r[c];\n      }\n    }\n  }\n  s.$tag$ = null;\n  s.$flags$ |= 4;\n  e.$vnode$ = s;\n  s.$elm$ = o.$elm$ = r.shadowRoot || r;\n  {\n    scopeId = r[\"s-sc\"];\n  }\n  patch(o, s);\n};\nvar attachToAncestor = function (e, t) {\n  if (t && !e.$onRenderResolve$ && t[\"s-p\"]) {\n    t[\"s-p\"].push(new Promise(function (t) {\n      return e.$onRenderResolve$ = t;\n    }));\n  }\n};\nvar scheduleUpdate = function (e, t) {\n  {\n    e.$flags$ |= 16;\n  }\n  if (e.$flags$ & 4) {\n    e.$flags$ |= 512;\n    return;\n  }\n  attachToAncestor(e, e.$ancestorComponent$);\n  var n = function () {\n    return dispatchHooks(e, t);\n  };\n  return writeTask(n);\n};\nvar dispatchHooks = function (e, t) {\n  var n = createTime(\"scheduleUpdate\", e.$cmpMeta$.$tagName$);\n  var r = e.$lazyInstance$;\n  var a;\n  if (t) {\n    {\n      a = safeCall(r, \"componentWillLoad\");\n    }\n  }\n  n();\n  return enqueue(a, function () {\n    return updateComponent(e, r, t);\n  });\n};\nvar enqueue = function (e, t) {\n  return isPromisey(e) ? e.then(t) : t();\n};\nvar isPromisey = function (e) {\n  return e instanceof Promise || e && e.then && typeof e.then === \"function\";\n};\nvar updateComponent = function (e, t, n) {\n  return __awaiter(void 0, void 0, void 0, function () {\n    var r, a, o, s, i, l, c;\n    return __generator(this, function (u) {\n      a = e.$hostElement$;\n      o = createTime(\"update\", e.$cmpMeta$.$tagName$);\n      s = a[\"s-rc\"];\n      if (n) {\n        attachStyles(e);\n      }\n      i = createTime(\"render\", e.$cmpMeta$.$tagName$);\n      {\n        callRender(e, t, a, n);\n      }\n      if (s) {\n        s.map(function (e) {\n          return e();\n        });\n        a[\"s-rc\"] = undefined;\n      }\n      i();\n      o();\n      {\n        l = (r = a[\"s-p\"]) !== null && r !== void 0 ? r : [];\n        c = function () {\n          return postUpdateComponent(e);\n        };\n        if (l.length === 0) {\n          c();\n        } else {\n          Promise.all(l).then(c);\n          e.$flags$ |= 4;\n          l.length = 0;\n        }\n      }\n      return [2];\n    });\n  });\n};\nvar callRender = function (e, t, n, r) {\n  try {\n    t = t.render();\n    {\n      e.$flags$ &= ~16;\n    }\n    {\n      e.$flags$ |= 2;\n    }\n    {\n      {\n        {\n          renderVdom(e, t, r);\n        }\n      }\n    }\n  } catch (t) {\n    consoleError(t, e.$hostElement$);\n  }\n  return null;\n};\nvar postUpdateComponent = function (e) {\n  var t = e.$cmpMeta$.$tagName$;\n  var n = e.$hostElement$;\n  var r = createTime(\"postUpdate\", t);\n  var a = e.$lazyInstance$;\n  var o = e.$ancestorComponent$;\n  if (!(e.$flags$ & 64)) {\n    e.$flags$ |= 64;\n    {\n      addHydratedFlag(n);\n    }\n    {\n      safeCall(a, \"componentDidLoad\");\n    }\n    r();\n    {\n      e.$onReadyResolve$(n);\n      if (!o) {\n        appDidLoad();\n      }\n    }\n  } else {\n    r();\n  }\n  {\n    if (e.$onRenderResolve$) {\n      e.$onRenderResolve$();\n      e.$onRenderResolve$ = undefined;\n    }\n    if (e.$flags$ & 512) {\n      nextTick(function () {\n        return scheduleUpdate(e, false);\n      });\n    }\n    e.$flags$ &= ~(4 | 512);\n  }\n};\nvar appDidLoad = function (e) {\n  {\n    addHydratedFlag(doc.documentElement);\n  }\n  nextTick(function () {\n    return emitEvent(win, \"appload\", {\n      detail: {\n        namespace: NAMESPACE\n      }\n    });\n  });\n};\nvar safeCall = function (e, t, n) {\n  if (e && e[t]) {\n    try {\n      return e[t](n);\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  return undefined;\n};\nvar addHydratedFlag = function (e) {\n  return e.classList.add(\"hydrated\");\n};\nvar getValue = function (e, t) {\n  return getHostRef(e).$instanceValues$.get(t);\n};\nvar setValue = function (e, t, n, r) {\n  var a = getHostRef(e);\n  var o = a.$hostElement$;\n  var s = a.$instanceValues$.get(t);\n  var i = a.$flags$;\n  var l = a.$lazyInstance$;\n  n = parsePropertyValue(n, r.$members$[t][0]);\n  var c = Number.isNaN(s) && Number.isNaN(n);\n  var u = n !== s && !c;\n  if ((!(i & 8) || s === undefined) && u) {\n    a.$instanceValues$.set(t, n);\n    if (l) {\n      if (r.$watchers$ && i & 128) {\n        var f = r.$watchers$[t];\n        if (f) {\n          f.map(function (e) {\n            try {\n              l[e](n, s, t);\n            } catch (e) {\n              consoleError(e, o);\n            }\n          });\n        }\n      }\n      if ((i & (2 | 16)) === 2) {\n        scheduleUpdate(a, false);\n      }\n    }\n  }\n};\nvar proxyComponent = function (e, t, n) {\n  if (t.$members$) {\n    if (e.watchers) {\n      t.$watchers$ = e.watchers;\n    }\n    var r = Object.entries(t.$members$);\n    var a = e.prototype;\n    r.map(function (e) {\n      var r = e[0],\n        o = e[1][0];\n      if (o & 31 || n & 2 && o & 32) {\n        Object.defineProperty(a, r, {\n          get: function () {\n            return getValue(this, r);\n          },\n          set: function (e) {\n            setValue(this, r, e, t);\n          },\n          configurable: true,\n          enumerable: true\n        });\n      }\n    });\n    if (n & 1) {\n      var o = new Map();\n      a.attributeChangedCallback = function (e, t, n) {\n        var r = this;\n        plt.jmp(function () {\n          var t = o.get(e);\n          if (r.hasOwnProperty(t)) {\n            n = r[t];\n            delete r[t];\n          } else if (a.hasOwnProperty(t) && typeof r[t] === \"number\" && r[t] == n) {\n            return;\n          }\n          r[t] = n === null && typeof r[t] === \"boolean\" ? false : n;\n        });\n      };\n      e.observedAttributes = r.filter(function (e) {\n        var t = e[0],\n          n = e[1];\n        return n[0] & 15;\n      }).map(function (e) {\n        var n = e[0],\n          r = e[1];\n        var a = r[1] || n;\n        o.set(a, n);\n        if (r[0] & 512) {\n          t.$attrsToReflect$.push([n, a]);\n        }\n        return a;\n      });\n    }\n  }\n  return e;\n};\nvar initializeComponent = function (e, t, n, r, a) {\n  return __awaiter(void 0, void 0, void 0, function () {\n    var e, r, o, s, i, l, c;\n    return __generator(this, function (u) {\n      switch (u.label) {\n        case 0:\n          if (!((t.$flags$ & 32) === 0)) return [3, 3];\n          t.$flags$ |= 32;\n          a = loadModule(n);\n          if (!a.then) return [3, 2];\n          e = uniqueTime();\n          return [4, a];\n        case 1:\n          a = u.sent();\n          e();\n          u.label = 2;\n        case 2:\n          if (!a.isProxied) {\n            {\n              n.$watchers$ = a.watchers;\n            }\n            proxyComponent(a, n, 2);\n            a.isProxied = true;\n          }\n          r = createTime(\"createInstance\", n.$tagName$);\n          {\n            t.$flags$ |= 8;\n          }\n          try {\n            new a(t);\n          } catch (e) {\n            consoleError(e);\n          }\n          {\n            t.$flags$ &= ~8;\n          }\n          {\n            t.$flags$ |= 128;\n          }\n          r();\n          fireConnectedCallback(t.$lazyInstance$);\n          if (a.style) {\n            o = a.style;\n            s = getScopeId(n);\n            if (!styles.has(s)) {\n              i = createTime(\"registerStyles\", n.$tagName$);\n              registerStyle(s, o, !!(n.$flags$ & 1));\n              i();\n            }\n          }\n          u.label = 3;\n        case 3:\n          l = t.$ancestorComponent$;\n          c = function () {\n            return scheduleUpdate(t, true);\n          };\n          if (l && l[\"s-rc\"]) {\n            l[\"s-rc\"].push(c);\n          } else {\n            c();\n          }\n          return [2];\n      }\n    });\n  });\n};\nvar fireConnectedCallback = function (e) {\n  {\n    safeCall(e, \"connectedCallback\");\n  }\n};\nvar connectedCallback = function (e) {\n  if ((plt.$flags$ & 1) === 0) {\n    var t = getHostRef(e);\n    var n = t.$cmpMeta$;\n    var r = createTime(\"connectedCallback\", n.$tagName$);\n    if (!(t.$flags$ & 1)) {\n      t.$flags$ |= 1;\n      {\n        var a = e;\n        while (a = a.parentNode || a.host) {\n          if (a[\"s-p\"]) {\n            attachToAncestor(t, t.$ancestorComponent$ = a);\n            break;\n          }\n        }\n      }\n      if (n.$members$) {\n        Object.entries(n.$members$).map(function (t) {\n          var n = t[0],\n            r = t[1][0];\n          if (r & 31 && e.hasOwnProperty(n)) {\n            var a = e[n];\n            delete e[n];\n            e[n] = a;\n          }\n        });\n      }\n      {\n        initializeComponent(e, t, n);\n      }\n    } else {\n      if (t === null || t === void 0 ? void 0 : t.$lazyInstance$) {\n        fireConnectedCallback(t.$lazyInstance$);\n      } else if (t === null || t === void 0 ? void 0 : t.$onReadyPromise$) {\n        t.$onReadyPromise$.then(function () {\n          return fireConnectedCallback(t.$lazyInstance$);\n        });\n      }\n    }\n    r();\n  }\n};\nvar disconnectInstance = function (e) {\n  {\n    safeCall(e, \"disconnectedCallback\");\n  }\n};\nvar disconnectedCallback = function (e) {\n  return __awaiter(void 0, void 0, void 0, function () {\n    var t;\n    return __generator(this, function (n) {\n      if ((plt.$flags$ & 1) === 0) {\n        t = getHostRef(e);\n        if (t === null || t === void 0 ? void 0 : t.$lazyInstance$) {\n          disconnectInstance(t.$lazyInstance$);\n        } else if (t === null || t === void 0 ? void 0 : t.$onReadyPromise$) {\n          t.$onReadyPromise$.then(function () {\n            return disconnectInstance(t.$lazyInstance$);\n          });\n        }\n      }\n      return [2];\n    });\n  });\n};\nvar bootstrapLazy = function (e, t) {\n  if (t === void 0) {\n    t = {};\n  }\n  var n;\n  var r = createTime();\n  var a = [];\n  var o = t.exclude || [];\n  var s = win.customElements;\n  var i = doc.head;\n  var l = i.querySelector(\"meta[charset]\");\n  var c = doc.createElement(\"style\");\n  var u = [];\n  var f;\n  var $ = true;\n  Object.assign(plt, t);\n  plt.$resourcesUrl$ = new URL(t.resourcesUrl || \"./\", doc.baseURI).href;\n  e.map(function (e) {\n    e[1].map(function (t) {\n      var n = {\n        $flags$: t[0],\n        $tagName$: t[1],\n        $members$: t[2],\n        $listeners$: t[3]\n      };\n      {\n        n.$members$ = t[2];\n      }\n      {\n        n.$attrsToReflect$ = [];\n      }\n      {\n        n.$watchers$ = {};\n      }\n      var r = n.$tagName$;\n      var i = function (e) {\n        __extends(t, e);\n        function t(t) {\n          var r = e.call(this, t) || this;\n          t = r;\n          registerHost(t, n);\n          if (n.$flags$ & 1) {\n            {\n              {\n                t.attachShadow({\n                  mode: \"open\"\n                });\n              }\n            }\n          }\n          return r;\n        }\n        t.prototype.connectedCallback = function () {\n          var e = this;\n          if (f) {\n            clearTimeout(f);\n            f = null;\n          }\n          if ($) {\n            u.push(this);\n          } else {\n            plt.jmp(function () {\n              return connectedCallback(e);\n            });\n          }\n        };\n        t.prototype.disconnectedCallback = function () {\n          var e = this;\n          plt.jmp(function () {\n            return disconnectedCallback(e);\n          });\n        };\n        t.prototype.componentOnReady = function () {\n          return getHostRef(this).$onReadyPromise$;\n        };\n        return t;\n      }(HTMLElement);\n      n.$lazyBundleId$ = e[0];\n      if (!o.includes(r) && !s.get(r)) {\n        a.push(r);\n        s.define(r, proxyComponent(i, n, 1));\n      }\n    });\n  });\n  {\n    c.innerHTML = a + HYDRATED_CSS;\n    c.setAttribute(\"data-styles\", \"\");\n    var d = (n = plt.$nonce$) !== null && n !== void 0 ? n : queryNonceMetaTagContent(doc);\n    if (d != null) {\n      c.setAttribute(\"nonce\", d);\n    }\n    i.insertBefore(c, l ? l.nextSibling : i.firstChild);\n  }\n  $ = false;\n  if (u.length) {\n    u.map(function (e) {\n      return e.connectedCallback();\n    });\n  } else {\n    {\n      plt.jmp(function () {\n        return f = setTimeout(appDidLoad, 30);\n      });\n    }\n  }\n  r();\n};\nvar setNonce = function (e) {\n  return plt.$nonce$ = e;\n};\nvar hostRefs = new WeakMap();\nvar getHostRef = function (e) {\n  return hostRefs.get(e);\n};\nvar registerInstance = function (e, t) {\n  return hostRefs.set(t.$lazyInstance$ = e, t);\n};\nvar registerHost = function (e, t) {\n  var n = {\n    $flags$: 0,\n    $hostElement$: e,\n    $cmpMeta$: t,\n    $instanceValues$: new Map()\n  };\n  {\n    n.$onReadyPromise$ = new Promise(function (e) {\n      return n.$onReadyResolve$ = e;\n    });\n    e[\"s-p\"] = [];\n    e[\"s-rc\"] = [];\n  }\n  return hostRefs.set(e, n);\n};\nvar isMemberInElement = function (e, t) {\n  return t in e;\n};\nvar consoleError = function (e, t) {\n  return (0, console.error)(e, t);\n};\nvar cmpModules = new Map();\nvar loadModule = function (e, t, n) {\n  var r = e.$tagName$.replace(/-/g, \"_\");\n  var a = e.$lazyBundleId$;\n  var o = cmpModules.get(a);\n  if (o) {\n    return o[r];\n  }\n  /*!__STENCIL_STATIC_IMPORT_SWITCH__*/\n  return import(\"./\".concat(a, \".entry.js\").concat(\"\")).then(function (e) {\n    {\n      cmpModules.set(a, e);\n    }\n    return e[r];\n  }, consoleError);\n};\nvar styles = new Map();\nvar win = typeof window !== \"undefined\" ? window : {};\nvar doc = win.document || {\n  head: {}\n};\nvar plt = {\n  $flags$: 0,\n  $resourcesUrl$: \"\",\n  jmp: function (e) {\n    return e();\n  },\n  raf: function (e) {\n    return requestAnimationFrame(e);\n  },\n  ael: function (e, t, n, r) {\n    return e.addEventListener(t, n, r);\n  },\n  rel: function (e, t, n, r) {\n    return e.removeEventListener(t, n, r);\n  },\n  ce: function (e, t) {\n    return new CustomEvent(e, t);\n  }\n};\nvar promiseResolve = function (e) {\n  return Promise.resolve(e);\n};\nvar supportsConstructableStylesheets = function () {\n  try {\n    new CSSStyleSheet();\n    return typeof new CSSStyleSheet().replaceSync === \"function\";\n  } catch (e) {}\n  return false;\n}();\nvar queueDomReads = [];\nvar queueDomWrites = [];\nvar queueTask = function (e, t) {\n  return function (n) {\n    e.push(n);\n    if (!queuePending) {\n      queuePending = true;\n      if (t && plt.$flags$ & 4) {\n        nextTick(flush);\n      } else {\n        plt.raf(flush);\n      }\n    }\n  };\n};\nvar consume = function (e) {\n  for (var t = 0; t < e.length; t++) {\n    try {\n      e[t](performance.now());\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  e.length = 0;\n};\nvar flush = function () {\n  consume(queueDomReads);\n  {\n    consume(queueDomWrites);\n    if (queuePending = queueDomReads.length > 0) {\n      plt.raf(flush);\n    }\n  }\n};\nvar nextTick = function (e) {\n  return promiseResolve().then(e);\n};\nvar writeTask = queueTask(queueDomWrites, true);\nexport { Host as H, setAssetPath as a, bootstrapLazy as b, getElement as c, getAssetPath as g, h, promiseResolve as p, registerInstance as r, setNonce as s };", "import { g as getAssetPath } from \"./index-b72adede.js\";\nvar CACHED_MAP;\nvar getIconMap = function () {\n  if (typeof window === \"undefined\") {\n    return new Map();\n  } else {\n    if (!CACHED_MAP) {\n      var t = window;\n      t.Ionicons = t.Ionicons || {};\n      CACHED_MAP = t.Ionicons.map = t.Ionicons.map || new Map();\n    }\n    return CACHED_MAP;\n  }\n};\nvar addIcons = function (t) {\n  Object.keys(t).forEach(function (e) {\n    addToIconMap(e, t[e]);\n    var r = e.replace(/([a-z0-9]|(?=[A-Z]))([A-Z0-9])/g, \"$1-$2\").toLowerCase();\n    if (e !== r) {\n      addToIconMap(r, t[e]);\n    }\n  });\n};\nvar addToIconMap = function (t, e) {\n  var r = getIconMap();\n  var n = r.get(t);\n  if (n === undefined) {\n    r.set(t, e);\n  } else if (n !== e) {\n    console.warn('[Ionicons Warning]: Multiple icons were mapped to name \"'.concat(t, '\". Ensure that multiple icons are not mapped to the same icon name.'));\n  }\n};\nvar getUrl = function (t) {\n  var e = getSrc(t.src);\n  if (e) {\n    return e;\n  }\n  e = getName(t.name, t.icon, t.mode, t.ios, t.md);\n  if (e) {\n    return getNamedUrl(e, t);\n  }\n  if (t.icon) {\n    e = getSrc(t.icon);\n    if (e) {\n      return e;\n    }\n    e = getSrc(t.icon[t.mode]);\n    if (e) {\n      return e;\n    }\n  }\n  return null;\n};\nvar getNamedUrl = function (t, e) {\n  var r = getIconMap().get(t);\n  if (r) {\n    return r;\n  }\n  try {\n    return getAssetPath(\"svg/\".concat(t, \".svg\"));\n  } catch (r) {\n    console.warn('[Ionicons Warning]: Could not load icon with name \"'.concat(t, '\". Ensure that the icon is registered using addIcons or that the icon SVG data is passed directly to the icon component.'), e);\n  }\n};\nvar getName = function (t, e, r, n, i) {\n  r = (r && toLower(r)) === \"ios\" ? \"ios\" : \"md\";\n  if (n && r === \"ios\") {\n    t = toLower(n);\n  } else if (i && r === \"md\") {\n    t = toLower(i);\n  } else {\n    if (!t && e && !isSrc(e)) {\n      t = e;\n    }\n    if (isStr(t)) {\n      t = toLower(t);\n    }\n  }\n  if (!isStr(t) || t.trim() === \"\") {\n    return null;\n  }\n  var o = t.replace(/[a-z]|-|\\d/gi, \"\");\n  if (o !== \"\") {\n    return null;\n  }\n  return t;\n};\nvar getSrc = function (t) {\n  if (isStr(t)) {\n    t = t.trim();\n    if (isSrc(t)) {\n      return t;\n    }\n  }\n  return null;\n};\nvar isSrc = function (t) {\n  return t.length > 0 && /(\\/|\\.)/.test(t);\n};\nvar isStr = function (t) {\n  return typeof t === \"string\";\n};\nvar toLower = function (t) {\n  return t.toLowerCase();\n};\nvar inheritAttributes = function (t, e) {\n  if (e === void 0) {\n    e = [];\n  }\n  var r = {};\n  e.forEach(function (e) {\n    if (t.hasAttribute(e)) {\n      var n = t.getAttribute(e);\n      if (n !== null) {\n        r[e] = t.getAttribute(e);\n      }\n      t.removeAttribute(e);\n    }\n  });\n  return r;\n};\nvar isRTL = function (t) {\n  if (t) {\n    if (t.dir !== \"\") {\n      return t.dir.toLowerCase() === \"rtl\";\n    }\n  }\n  return (document === null || document === void 0 ? void 0 : document.dir.toLowerCase()) === \"rtl\";\n};\nexport { addIcons as a, inheritAttributes as b, getName as c, isRTL as d, getUrl as g, isStr as i };"], "mappings": ";;;AAoJA,IAAI,eAAe;AAKnB,IAAI,eAAe,SAAU,GAAG;AAC9B,SAAO,IAAI,iBAAiB;AAC9B;AAqgCA,IAAI,eAAe,SAAU,GAAG,GAAG;AACjC,UAAQ,GAAG,QAAQ,OAAO,GAAG,CAAC;AAChC;AAkBA,IAAI,MAAM,OAAO,WAAW,cAAc,SAAS,CAAC;AACpD,IAAI,MAAM,IAAI,YAAY;AAAA,EACxB,MAAM,CAAC;AACT;AACA,IAAI,MAAM;AAAA,EACR,SAAS;AAAA,EACT,gBAAgB;AAAA,EAChB,KAAK,SAAU,GAAG;AAChB,WAAO,EAAE;AAAA,EACX;AAAA,EACA,KAAK,SAAU,GAAG;AAChB,WAAO,sBAAsB,CAAC;AAAA,EAChC;AAAA,EACA,KAAK,SAAU,GAAG,GAAG,GAAG,GAAG;AACzB,WAAO,EAAE,iBAAiB,GAAG,GAAG,CAAC;AAAA,EACnC;AAAA,EACA,KAAK,SAAU,GAAG,GAAG,GAAG,GAAG;AACzB,WAAO,EAAE,oBAAoB,GAAG,GAAG,CAAC;AAAA,EACtC;AAAA,EACA,IAAI,SAAU,GAAG,GAAG;AAClB,WAAO,IAAI,YAAY,GAAG,CAAC;AAAA,EAC7B;AACF;AACA,IAAI,iBAAiB,SAAU,GAAG;AAChC,SAAO,QAAQ,QAAQ,CAAC;AAC1B;AACA,IAAI,mCAAmC,WAAY;AACjD,MAAI;AACF,QAAI,cAAc;AAClB,WAAO,OAAO,IAAI,cAAc,EAAE,gBAAgB;AAAA,EACpD,SAAS,GAAG;AAAA,EAAC;AACb,SAAO;AACT,EAAE;AACF,IAAI,gBAAgB,CAAC;AACrB,IAAI,iBAAiB,CAAC;AACtB,IAAI,YAAY,SAAU,GAAG,GAAG;AAC9B,SAAO,SAAU,GAAG;AAClB,MAAE,KAAK,CAAC;AACR,QAAI,CAAC,cAAc;AACjB,qBAAe;AACf,UAAI,KAAK,IAAI,UAAU,GAAG;AACxB,iBAAS,KAAK;AAAA,MAChB,OAAO;AACL,YAAI,IAAI,KAAK;AAAA,MACf;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,UAAU,SAAU,GAAG;AACzB,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,QAAI;AACF,QAAE,CAAC,EAAE,YAAY,IAAI,CAAC;AAAA,IACxB,SAASA,IAAG;AACV,mBAAaA,EAAC;AAAA,IAChB;AAAA,EACF;AACA,IAAE,SAAS;AACb;AACA,IAAI,QAAQ,WAAY;AACtB,UAAQ,aAAa;AACrB;AACE,YAAQ,cAAc;AACtB,QAAI,eAAe,cAAc,SAAS,GAAG;AAC3C,UAAI,IAAI,KAAK;AAAA,IACf;AAAA,EACF;AACF;AACA,IAAI,WAAW,SAAU,GAAG;AAC1B,SAAO,eAAe,EAAE,KAAK,CAAC;AAChC;AACA,IAAI,YAAY,UAAU,gBAAgB,IAAI;;;ACzvC9C,IAAI;AACJ,IAAI,aAAa,WAAY;AAC3B,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO,oBAAI,IAAI;AAAA,EACjB,OAAO;AACL,QAAI,CAAC,YAAY;AACf,UAAI,IAAI;AACR,QAAE,WAAW,EAAE,YAAY,CAAC;AAC5B,mBAAa,EAAE,SAAS,MAAM,EAAE,SAAS,OAAO,oBAAI,IAAI;AAAA,IAC1D;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAI,WAAW,SAAU,GAAG;AAC1B,SAAO,KAAK,CAAC,EAAE,QAAQ,SAAU,GAAG;AAClC,iBAAa,GAAG,EAAE,CAAC,CAAC;AACpB,QAAI,IAAI,EAAE,QAAQ,mCAAmC,OAAO,EAAE,YAAY;AAC1E,QAAI,MAAM,GAAG;AACX,mBAAa,GAAG,EAAE,CAAC,CAAC;AAAA,IACtB;AAAA,EACF,CAAC;AACH;AACA,IAAI,eAAe,SAAU,GAAG,GAAG;AACjC,MAAI,IAAI,WAAW;AACnB,MAAI,IAAI,EAAE,IAAI,CAAC;AACf,MAAI,MAAM,QAAW;AACnB,MAAE,IAAI,GAAG,CAAC;AAAA,EACZ,WAAW,MAAM,GAAG;AAClB,YAAQ,KAAK,2DAA2D,OAAO,GAAG,qEAAqE,CAAC;AAAA,EAC1J;AACF;", "names": ["e"]}