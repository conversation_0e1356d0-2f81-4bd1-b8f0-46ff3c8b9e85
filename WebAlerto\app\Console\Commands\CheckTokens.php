<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\DeviceToken;
use App\Models\User;

class CheckTokens extends Command
{
    protected $signature = 'fcm:check-tokens';
    protected $description = 'Check FCM device tokens in database';

    public function handle()
    {
        $this->info('🔍 Checking FCM Device Tokens...');
        
        $totalTokens = DeviceToken::count();
        $activeTokens = DeviceToken::where('is_active', true)->count();
        $inactiveTokens = DeviceToken::where('is_active', false)->count();
        
        $this->info("📊 Total tokens: {$totalTokens}");
        $this->info("✅ Active tokens: {$activeTokens}");
        $this->info("❌ Inactive tokens: {$inactiveTokens}");
        
        if ($totalTokens > 0) {
            $this->info("\n📱 Token Details:");
            DeviceToken::all()->each(function($token) {
                $user = $token->user_id ? User::find($token->user_id) : null;
                $userName = $user ? $user->name : 'No user';
                
                $this->line(sprintf(
                    "ID: %d | Active: %s | User: %s | Token: %s...",
                    $token->id,
                    $token->is_active ? 'Yes' : 'No',
                    $userName,
                    substr($token->token, 0, 20)
                ));
            });
        } else {
            $this->warn('⚠️  No device tokens found in database.');
            $this->info('💡 Make sure the mobile app is registering tokens properly.');
        }
        
        return 0;
    }
}
