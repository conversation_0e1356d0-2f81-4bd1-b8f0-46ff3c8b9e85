ion-header {
  ion-toolbar {
    --background: transparent;
    --border-color: transparent;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;

  ion-spinner {
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
  }

  p {
    color: var(--ion-color-medium);
  }
}

.details-container {
  padding-bottom: 20px;
}



.center-name {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: var(--ion-color-dark);
}

.center-type {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.info-section {
  margin-bottom: 16px;

  ion-item {
    --padding-start: 0;
    --inner-padding-end: 0;
    --background: transparent;

    ion-icon {
      font-size: 24px;
    }

    h2 {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    p {
      font-size: 14px;
      color: var(--ion-color-medium);
    }
  }
}

.travel-section {
  margin-top: 24px;

  h2 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--ion-color-dark);
  }

  .travel-cards {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .travel-card {
      margin: 0;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      &:active {
        transform: scale(0.98);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      ion-card-header {
        display: flex;
        align-items: center;
        padding: 12px 16px 0;

        .travel-icon {
          font-size: 28px;
          margin-right: 12px;
        }

        ion-card-title {
          font-size: 18px;
          font-weight: 600;
        }
      }

      ion-card-content {
        padding: 12px 16px 16px;

        .travel-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 12px;

          .travel-time, .travel-distance {
            display: flex;
            align-items: center;

            ion-icon {
              font-size: 18px;
              margin-right: 6px;
              color: var(--ion-color-medium);
            }

            span {
              font-size: 15px;
              font-weight: 500;
              color: var(--ion-color-dark);
            }
          }
        }

        ion-button {
          margin-top: 8px;
          font-weight: 500;
        }
      }
    }
  }
}

ion-footer {
  ion-toolbar {
    --background: transparent;
    --border-color: transparent;
    padding: 0 16px 16px;
  }
}

.full-center-message {
  margin-bottom: 20px;

  .full-header {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  ion-card-content {
    p {
      margin-bottom: 16px;
      color: var(--ion-color-danger);
      font-weight: 500;
    }
  }
}
