<?php

namespace App\Services;

class OrganizationalPositionService
{
    /**
     * Get all legitimate positions for CDRRMC and BDRRMC
     */
    public static function getAllPositions()
    {
        return [
            'system_admin' => self::getSystemAdminPositions(),
            'super_admin' => self::getCDRRMCPositions(),
            'admin' => self::getBDRRMCPositions(),
        ];
    }

    /**
     * Get positions for System Administrators
     */
    public static function getSystemAdminPositions()
    {
        return [
            'Technical Administrator' => 'Technical Administrator',
            'System Administrator' => 'System Administrator',
            'IT Manager' => 'IT Manager',
            'Database Administrator' => 'Database Administrator',
            'System Analyst' => 'System Analyst',
        ];
    }

    /**
     * Get positions for CDRRMC (City Disaster Risk Reduction and Management Council)
     */
    public static function getCDRRMCPositions()
    {
        return [
            // Executive Level
            'CDRRMC Chairperson' => 'CDRRMC Chairperson',
            'CDRRMC Vice-Chairperson' => 'CDRRMC Vice-Chairperson',
            'CDRRMC Executive Director' => 'CDRRMC Executive Director',
            
            // Department Heads
            'City Mayor' => 'City Mayor',
            'Vice Mayor' => 'Vice Mayor',
            'City Administrator' => 'City Administrator',
            'City Planning and Development Coordinator' => 'City Planning and Development Coordinator',
            'City Health Officer' => 'City Health Officer',
            'City Engineer' => 'City Engineer',
            'City Social Welfare and Development Officer' => 'City Social Welfare and Development Officer',
            'City Environment and Natural Resources Officer' => 'City Environment and Natural Resources Officer',
            'City Agriculture Officer' => 'City Agriculture Officer',
            'City Budget Officer' => 'City Budget Officer',
            'City Accountant' => 'City Accountant',
            'City Treasurer' => 'City Treasurer',
            'City Legal Officer' => 'City Legal Officer',
            'City Information Officer' => 'City Information Officer',
            
            // Emergency Services
            'Fire Chief' => 'Fire Chief',
            'Police Chief' => 'Police Chief',
            'Emergency Medical Services Director' => 'Emergency Medical Services Director',
            'Rescue Operations Chief' => 'Rescue Operations Chief',
            
            // CDRRMC Staff
            'CDRRMC Operations Manager' => 'CDRRMC Operations Manager',
            'CDRRMC Planning Officer' => 'CDRRMC Planning Officer',
            'CDRRMC Information Officer' => 'CDRRMC Information Officer',
            'CDRRMC Training Officer' => 'CDRRMC Training Officer',
            'CDRRMC Logistics Officer' => 'CDRRMC Logistics Officer',
            'CDRRMC Communications Officer' => 'CDRRMC Communications Officer',
            'CDRRMC Monitoring and Evaluation Officer' => 'CDRRMC Monitoring and Evaluation Officer',
            'CDRRMC Administrative Officer' => 'CDRRMC Administrative Officer',
            'CDRRMC Finance Officer' => 'CDRRMC Finance Officer',
            
            // Technical Staff
            'Senior Disaster Risk Reduction Officer' => 'Senior Disaster Risk Reduction Officer',
            'Disaster Risk Reduction Officer' => 'Disaster Risk Reduction Officer',
            'Emergency Response Coordinator' => 'Emergency Response Coordinator',
            'Risk Assessment Specialist' => 'Risk Assessment Specialist',
            'Early Warning Systems Specialist' => 'Early Warning Systems Specialist',
            'GIS Specialist' => 'GIS Specialist',
            'Data Analyst' => 'Data Analyst',
            'Research Officer' => 'Research Officer',
            
            // Support Staff
            'Administrative Assistant' => 'Administrative Assistant',
            'Executive Secretary' => 'Executive Secretary',
            'Records Officer' => 'Records Officer',
            'IT Support Specialist' => 'IT Support Specialist',
        ];
    }

    /**
     * Get positions for BDRRMC (Barangay Disaster Risk Reduction and Management Council)
     */
    public static function getBDRRMCPositions()
    {
        return [
            // Executive Level
            'BDRRMC Chairperson' => 'BDRRMC Chairperson',
            'BDRRMC Vice-Chairperson' => 'BDRRMC Vice-Chairperson',
            'Barangay Captain' => 'Barangay Captain',
            'Barangay Kagawad' => 'Barangay Kagawad',
            
            // BDRRMC Officers
            'BDRRMC Secretary' => 'BDRRMC Secretary',
            'BDRRMC Treasurer' => 'BDRRMC Treasurer',
            'BDRRMC Operations Officer' => 'BDRRMC Operations Officer',
            'BDRRMC Planning Officer' => 'BDRRMC Planning Officer',
            'BDRRMC Information Officer' => 'BDRRMC Information Officer',
            'BDRRMC Training Officer' => 'BDRRMC Training Officer',
            'BDRRMC Logistics Officer' => 'BDRRMC Logistics Officer',
            'BDRRMC Communications Officer' => 'BDRRMC Communications Officer',
            
            // Emergency Response Teams
            'Emergency Response Team Leader' => 'Emergency Response Team Leader',
            'Search and Rescue Team Leader' => 'Search and Rescue Team Leader',
            'Medical Response Team Leader' => 'Medical Response Team Leader',
            'Evacuation Team Leader' => 'Evacuation Team Leader',
            'Fire Response Team Leader' => 'Fire Response Team Leader',
            'Traffic Management Team Leader' => 'Traffic Management Team Leader',
            'Security Team Leader' => 'Security Team Leader',
            
            // Specialized Roles
            'Barangay Health Worker' => 'Barangay Health Worker',
            'Barangay Nutrition Scholar' => 'Barangay Nutrition Scholar',
            'Barangay Tanod' => 'Barangay Tanod',
            'Barangay Environmental Officer' => 'Barangay Environmental Officer',
            'Barangay Social Worker' => 'Barangay Social Worker',
            'Barangay Youth Leader' => 'Barangay Youth Leader',
            'Senior Citizen Representative' => 'Senior Citizen Representative',
            'Women\'s Representative' => 'Women\'s Representative',
            'PWD Representative' => 'PWD Representative',
            
            // Technical and Support Staff
            'Disaster Risk Reduction Officer' => 'Disaster Risk Reduction Officer',
            'Emergency Response Officer' => 'Emergency Response Officer',
            'Community Preparedness Officer' => 'Community Preparedness Officer',
            'Risk Assessment Officer' => 'Risk Assessment Officer',
            'Early Warning Officer' => 'Early Warning Officer',
            'Damage Assessment Officer' => 'Damage Assessment Officer',
            'Relief Operations Officer' => 'Relief Operations Officer',
            'Rehabilitation Officer' => 'Rehabilitation Officer',
            
            // Administrative Support
            'Administrative Assistant' => 'Administrative Assistant',
            'Records Keeper' => 'Records Keeper',
            'Data Encoder' => 'Data Encoder',
            'Communications Operator' => 'Communications Operator',
            'Equipment Custodian' => 'Equipment Custodian',
            
            // Volunteer Positions
            'Volunteer Coordinator' => 'Volunteer Coordinator',
            'Community Volunteer' => 'Community Volunteer',
            'Emergency Response Volunteer' => 'Emergency Response Volunteer',
            'First Aid Volunteer' => 'First Aid Volunteer',
            'Evacuation Assistant' => 'Evacuation Assistant',
            
            // Education and Training
            'Training Coordinator' => 'Training Coordinator',
            'Community Educator' => 'Community Educator',
            'Safety Officer' => 'Safety Officer',
            'Preparedness Advocate' => 'Preparedness Advocate',
        ];
    }

    /**
     * Get positions for a specific role
     */
    public static function getPositionsForRole($role)
    {
        $allPositions = self::getAllPositions();
        return $allPositions[$role] ?? [];
    }

    /**
     * Get all positions as a flat array for validation
     */
    public static function getAllPositionsFlat()
    {
        $allPositions = self::getAllPositions();
        $flatPositions = [];
        
        foreach ($allPositions as $rolePositions) {
            $flatPositions = array_merge($flatPositions, array_keys($rolePositions));
        }
        
        return array_unique($flatPositions);
    }

    /**
     * Validate if a position is legitimate for a given role
     */
    public static function isValidPositionForRole($position, $role)
    {
        $rolePositions = self::getPositionsForRole($role);
        return array_key_exists($position, $rolePositions);
    }

    /**
     * Get position categories for better organization
     */
    public static function getPositionCategories($role)
    {
        switch ($role) {
            case 'super_admin':
                return [
                    'Executive Level' => [
                        'CDRRMC Chairperson', 'CDRRMC Vice-Chairperson', 'CDRRMC Executive Director',
                        'City Mayor', 'Vice Mayor', 'City Administrator'
                    ],
                    'Department Heads' => [
                        'City Planning and Development Coordinator', 'City Health Officer', 'City Engineer',
                        'City Social Welfare and Development Officer', 'City Environment and Natural Resources Officer',
                        'City Agriculture Officer', 'City Budget Officer', 'City Accountant', 'City Treasurer',
                        'City Legal Officer', 'City Information Officer'
                    ],
                    'Emergency Services' => [
                        'Fire Chief', 'Police Chief', 'Emergency Medical Services Director', 'Rescue Operations Chief'
                    ],
                    'CDRRMC Staff' => [
                        'CDRRMC Operations Manager', 'CDRRMC Planning Officer', 'CDRRMC Information Officer',
                        'CDRRMC Training Officer', 'CDRRMC Logistics Officer', 'CDRRMC Communications Officer',
                        'CDRRMC Monitoring and Evaluation Officer', 'CDRRMC Administrative Officer', 'CDRRMC Finance Officer'
                    ],
                    'Technical Staff' => [
                        'Senior Disaster Risk Reduction Officer', 'Disaster Risk Reduction Officer',
                        'Emergency Response Coordinator', 'Risk Assessment Specialist', 'Early Warning Systems Specialist',
                        'GIS Specialist', 'Data Analyst', 'Research Officer'
                    ],
                    'Support Staff' => [
                        'Administrative Assistant', 'Executive Secretary', 'Records Officer', 'IT Support Specialist'
                    ]
                ];
                
            case 'admin':
                return [
                    'Executive Level' => [
                        'BDRRMC Chairperson', 'BDRRMC Vice-Chairperson', 'Barangay Captain', 'Barangay Kagawad'
                    ],
                    'BDRRMC Officers' => [
                        'BDRRMC Secretary', 'BDRRMC Treasurer', 'BDRRMC Operations Officer', 'BDRRMC Planning Officer',
                        'BDRRMC Information Officer', 'BDRRMC Training Officer', 'BDRRMC Logistics Officer', 'BDRRMC Communications Officer'
                    ],
                    'Emergency Response Teams' => [
                        'Emergency Response Team Leader', 'Search and Rescue Team Leader', 'Medical Response Team Leader',
                        'Evacuation Team Leader', 'Fire Response Team Leader', 'Traffic Management Team Leader', 'Security Team Leader'
                    ],
                    'Specialized Roles' => [
                        'Barangay Health Worker', 'Barangay Nutrition Scholar', 'Barangay Tanod', 'Barangay Environmental Officer',
                        'Barangay Social Worker', 'Barangay Youth Leader', 'Senior Citizen Representative', 'Women\'s Representative', 'PWD Representative'
                    ],
                    'Technical Staff' => [
                        'Disaster Risk Reduction Officer', 'Emergency Response Officer', 'Community Preparedness Officer',
                        'Risk Assessment Officer', 'Early Warning Officer', 'Damage Assessment Officer', 'Relief Operations Officer', 'Rehabilitation Officer'
                    ],
                    'Support Staff' => [
                        'Administrative Assistant', 'Records Keeper', 'Data Encoder', 'Communications Operator', 'Equipment Custodian'
                    ],
                    'Volunteers' => [
                        'Volunteer Coordinator', 'Community Volunteer', 'Emergency Response Volunteer', 'First Aid Volunteer', 'Evacuation Assistant'
                    ],
                    'Education & Training' => [
                        'Training Coordinator', 'Community Educator', 'Safety Officer', 'Preparedness Advocate'
                    ]
                ];
                
            default:
                return [];
        }
    }
}
