<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\DeviceToken;
use App\Models\User;

class CreateTestToken extends Command
{
    protected $signature = 'fcm:create-test-token {token} {--user-id=1}';
    protected $description = 'Create a test FCM device token for testing';

    public function handle()
    {
        $token = $this->argument('token');
        $userId = $this->option('user-id');
        
        // Check if user exists
        $user = User::find($userId);
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return 1;
        }
        
        // Create or update the device token
        $deviceToken = DeviceToken::updateOrCreate(
            ['token' => $token],
            [
                'user_id' => $userId,
                'device_type' => 'android',
                'is_active' => true,
                'last_used_at' => now()
            ]
        );
        
        $this->info("✅ Test token created/updated successfully!");
        $this->info("📱 Token ID: {$deviceToken->id}");
        $this->info("👤 User: {$user->name}");
        $this->info("🔑 Token: " . substr($token, 0, 20) . "...");
        
        return 0;
    }
}
