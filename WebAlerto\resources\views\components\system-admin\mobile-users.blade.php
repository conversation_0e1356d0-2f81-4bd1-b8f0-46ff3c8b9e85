@extends('layout.app')

@section('title', 'Mobile Users Management')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-slate-100 py-8">
    <div class="max-w-7xl mx-auto px-6 sm:px-8 lg:px-12">
        
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center gap-4 mb-4">
                <div class="p-3 bg-gradient-to-br from-slate-600 to-slate-700 rounded-xl shadow-lg">
                    <i class="fas fa-mobile-alt text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Mobile Users Management</h1>
                    <p class="text-gray-600 mt-1">View registered device tokens from mobile application</p>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border border-slate-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Devices</p>
                        <h3 class="text-2xl font-bold text-gray-900 mt-1">{{ $stats['total_users'] }}</h3>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg shadow-lg">
                        <i class="fas fa-mobile-alt text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border border-slate-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Active Devices</p>
                        <h3 class="text-2xl font-bold text-green-600 mt-1">{{ $stats['active_users'] }}</h3>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-lg shadow-lg">
                        <i class="fas fa-wifi text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border border-slate-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Inactive Devices</p>
                        <h3 class="text-2xl font-bold text-red-600 mt-1">{{ $stats['inactive_users'] }}</h3>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-red-500 to-red-600 rounded-lg shadow-lg">
                        <i class="fas fa-mobile-alt text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border border-slate-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Recent (30 days)</p>
                        <h3 class="text-2xl font-bold text-purple-600 mt-1">{{ $stats['recent_registrations'] }}</h3>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg shadow-lg">
                        <i class="fas fa-plus-circle text-white text-xl"></i>
                    </div>
                </div>
            </div>
        </div>



        <!-- Mobile Users Table -->
        <div class="bg-white/90 backdrop-blur-sm rounded-xl shadow-xl border border-slate-200 overflow-hidden">
            <div class="p-6 border-b border-slate-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <div class="p-3 bg-gradient-to-br from-slate-500 to-slate-600 rounded-lg shadow-lg">
                            <i class="fas fa-table text-white text-xl"></i>
                        </div>
                        <div>
                            <h2 class="text-xl font-bold text-gray-900">Registered Device Tokens</h2>
                            <p class="text-sm text-gray-600 mt-1">{{ $mobileUsers->total() }} devices found</p>
                        </div>
                    </div>
                </div>
            </div>

            @if($mobileUsers->count() > 0)
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-slate-50">
                        <tr>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">Device Info</th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">Device Token</th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">User Account</th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">Device Type</th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">Registered</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-slate-100">
                        @foreach($mobileUsers as $deviceToken)
                        <tr class="hover:bg-slate-50/50 transition-colors">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-mobile-alt text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">Device #{{ $deviceToken->id }}</div>
                                        <div class="text-sm text-gray-500">{{ $deviceToken->device_name ?: 'Unknown Device' }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900 font-mono bg-gray-50 p-2 rounded border">
                                    {{ substr($deviceToken->token, 0, 30) }}...
                                </div>
                                <div class="text-xs text-gray-500 mt-1">
                                    <i class="fas fa-key text-gray-400 mr-1"></i>
                                    FCM Token
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                @if($deviceToken->user)
                                    <div class="text-sm text-gray-900">
                                        <i class="fas fa-user text-gray-400 mr-1"></i>
                                        {{ trim($deviceToken->user->first_name . ' ' . $deviceToken->user->last_name) }}
                                    </div>
                                    <div class="text-xs text-blue-600 mt-1">
                                        <i class="fas fa-envelope text-blue-400 mr-1"></i>
                                        {{ $deviceToken->user->email }}
                                    </div>
                                @else
                                    <div class="text-sm text-gray-500">
                                        <i class="fas fa-user-slash text-gray-400 mr-1"></i>
                                        No linked user
                                    </div>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">
                                    <i class="fab fa-android text-green-500 mr-1"></i>
                                    {{ ucfirst($deviceToken->device_type ?: 'Unknown') }}
                                </div>
                                <div class="text-sm text-gray-500">Mobile Device</div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    {{ $deviceToken->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $deviceToken->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">{{ $deviceToken->created_at->format('M d, Y') }}</div>
                                <div class="text-sm text-gray-500">{{ $deviceToken->created_at->format('h:i A') }}</div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="px-6 py-4 border-t border-slate-200">
                {{ $mobileUsers->links() }}
            </div>
            @else
            <div class="p-12 text-center">
                <div class="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-mobile-alt text-slate-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No device tokens found</h3>
                <p class="text-gray-500">No mobile devices have registered with the system yet.</p>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
