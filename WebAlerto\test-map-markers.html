<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Map Markers Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.js"></script>
    <style>
        #map { height: 400px; }
    </style>
</head>
<body class="bg-gray-100 p-4">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold mb-4">Map Markers Loading Test</h1>
            
            <!-- Test Controls -->
            <div class="mb-4 space-x-2">
                <button onclick="testInitialLoad()" class="bg-blue-500 text-white px-4 py-2 rounded">Test Initial Load</button>
                <button onclick="testAddMarkers()" class="bg-green-500 text-white px-4 py-2 rounded">Test Add Markers</button>
                <button onclick="clearMarkers()" class="bg-red-500 text-white px-4 py-2 rounded">Clear Markers</button>
                <button onclick="showLogs()" class="bg-gray-500 text-white px-4 py-2 rounded">Show Logs</button>
            </div>

            <!-- Status -->
            <div id="status" class="mb-4 p-3 bg-blue-50 rounded">
                <strong>Status:</strong> <span id="statusText">Ready to test</span>
            </div>

            <!-- Map -->
            <div id="map" class="border rounded"></div>

            <!-- Logs -->
            <div id="logs" class="mt-4 p-3 bg-gray-50 rounded max-h-40 overflow-y-auto hidden">
                <h3 class="font-bold mb-2">Console Logs:</h3>
                <div id="logContent"></div>
            </div>
        </div>
    </div>

    <script>
        let map;
        let markers = [];
        let logs = [];
        
        // Sample evacuation centers data
        const testCenters = [
            {
                id: 1,
                name: "Cebu City Sports Complex",
                latitude: 10.3157,
                longitude: 123.8854,
                status: "Active",
                disaster_type: "Multi-disaster",
                barangay: "Kalunasan",
                address: "Cebu City Sports Complex, Kalunasan, Cebu City"
            },
            {
                id: 2,
                name: "Barangay Lahug Gymnasium",
                latitude: 10.3350,
                longitude: 123.9063,
                status: "Active",
                disaster_type: "Typhoon",
                barangay: "Lahug",
                address: "Lahug Gymnasium, Lahug, Cebu City"
            },
            {
                id: 3,
                name: "Capitol Site Elementary School",
                latitude: 10.2935,
                longitude: 123.9015,
                status: "Active",
                disaster_type: "Flood",
                barangay: "Capitol Site",
                address: "Capitol Site Elementary School, Capitol Site, Cebu City"
            }
        ];

        // Override console.log to capture logs
        const originalLog = console.log;
        console.log = function(...args) {
            logs.push(new Date().toLocaleTimeString() + ': ' + args.join(' '));
            originalLog.apply(console, args);
        };

        function updateStatus(text) {
            document.getElementById('statusText').textContent = text;
        }

        function showLogs() {
            const logsDiv = document.getElementById('logs');
            const logContent = document.getElementById('logContent');
            
            logContent.innerHTML = logs.map(log => `<div class="text-sm">${log}</div>`).join('');
            logsDiv.classList.toggle('hidden');
        }

        function testInitialLoad() {
            updateStatus('Testing initial map load...');
            console.log('=== Testing Initial Load ===');
            
            // Clear existing map
            if (map) {
                map.remove();
            }
            
            // Initialize map
            map = L.map('map').setView([10.3157, 123.8854], 13);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);
            
            console.log('Map initialized');
            
            // Test adding markers immediately
            setTimeout(() => {
                console.log('Adding markers after 100ms delay...');
                addTestMarkers();
                
                if (markers.length > 0) {
                    updateStatus(`Success! ${markers.length} markers loaded automatically`);
                    console.log('✅ Initial load test passed');
                } else {
                    updateStatus('Failed! No markers loaded automatically');
                    console.log('❌ Initial load test failed');
                }
            }, 100);
        }

        function testAddMarkers() {
            updateStatus('Testing manual marker addition...');
            console.log('=== Testing Manual Add Markers ===');
            
            if (!map) {
                updateStatus('Error: Map not initialized');
                return;
            }
            
            clearMarkers();
            addTestMarkers();
            
            if (markers.length > 0) {
                updateStatus(`Success! ${markers.length} markers added manually`);
                console.log('✅ Manual add test passed');
            } else {
                updateStatus('Failed! No markers added manually');
                console.log('❌ Manual add test failed');
            }
        }

        function addTestMarkers() {
            console.log('addTestMarkers called with', testCenters.length, 'centers');
            
            testCenters.forEach((center, index) => {
                console.log(`Adding marker ${index + 1}:`, center.name);
                
                const marker = L.marker([center.latitude, center.longitude])
                    .bindPopup(`
                        <strong>${center.name}</strong><br>
                        Status: ${center.status}<br>
                        Type: ${center.disaster_type}<br>
                        Barangay: ${center.barangay}
                    `)
                    .addTo(map);
                
                markers.push(marker);
            });
            
            console.log('Added', markers.length, 'markers to map');
            
            // Fit map to show all markers
            if (markers.length > 0) {
                const group = new L.featureGroup(markers);
                map.fitBounds(group.getBounds().pad(0.1));
            }
        }

        function clearMarkers() {
            console.log('Clearing', markers.length, 'markers');
            markers.forEach(marker => map.removeLayer(marker));
            markers = [];
            updateStatus('Markers cleared');
        }

        // Auto-run initial test when page loads
        window.onload = function() {
            console.log('Page loaded, running initial test...');
            setTimeout(testInitialLoad, 500);
        };
    </script>
</body>
</html>
