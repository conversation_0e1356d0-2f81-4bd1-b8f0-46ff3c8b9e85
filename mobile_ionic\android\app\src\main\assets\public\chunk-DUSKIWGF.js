import{b as p}from"./chunk-ICWJVXBH.js";import{h as a}from"./chunk-B7O3QC5Z.js";function d(o){o.CapacitorUtils.Synapse=new Proxy({},{get(w,n){return new Proxy({},{get(l,t){return(s,u,i)=>{let r=o.Capacitor.Plugins[n];if(r===void 0){i(new Error(`Capacitor plugin ${n} not found`));return}if(typeof r[t]!="function"){i(new Error(`Method ${t} not found in Capacitor plugin ${n}`));return}a(this,null,function*(){try{let e=yield r[t](s);u(e)}catch(e){i(e)}})}}})}})}function f(o){o.CapacitorUtils.Synapse=new Proxy({},{get(w,n){return o.cordova.plugins[n]}})}function c(o=!1){window.CapacitorUtils=window.CapacitorUtils||{},window.Capacitor!==void 0&&!o?d(window):window.cordova!==void 0&&f(window)}var m=p("Geolocation",{web:()=>import("./chunk-NJ6WMC4A.js").then(o=>new o.GeolocationWeb)});c();export{c as a,m as b};
