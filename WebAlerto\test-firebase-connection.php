<?php

require_once __DIR__ . '/vendor/autoload.php';

use <PERSON>reait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Firebase Connection Test ===\n\n";

// Test Firebase connection
try {
    $serviceAccountPath = storage_path('firebase-service-account.json');
    
    if (!file_exists($serviceAccountPath)) {
        echo "❌ Firebase service account file not found!\n";
        echo "   Expected: $serviceAccountPath\n";
        exit(1);
    }
    
    echo "✅ Firebase service account file found\n";
    
    // Load service account
    $serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);
    echo "✅ Project ID: " . $serviceAccount['project_id'] . "\n";
    echo "✅ Client Email: " . $serviceAccount['client_email'] . "\n\n";
    
    // Initialize Firebase
    $factory = (new Factory)->withServiceAccount($serviceAccountPath);
    $messaging = $factory->createMessaging();
    
    echo "✅ Firebase Messaging service initialized\n\n";
    
    // Test with a dummy token (this will fail but shows if Firebase is working)
    echo "🧪 Testing Firebase connection with dummy token...\n";
    
    $dummyToken = 'dummy_token_for_testing';
    $message = CloudMessage::withTarget('token', $dummyToken)
        ->withNotification(Notification::create('Test', 'Firebase connection test'));
    
    try {
        $result = $messaging->send($message);
        echo "✅ Firebase connection successful (unexpected success with dummy token)\n";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Invalid registration token') !== false) {
            echo "✅ Firebase connection working (expected error with dummy token)\n";
            echo "   Error: " . $e->getMessage() . "\n";
        } else {
            echo "❌ Firebase connection error: " . $e->getMessage() . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Firebase initialization failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n=== Mobile App Configuration Check ===\n\n";

// Check mobile app configuration
$mobileConfigPath = 'C:\Users\<USER>\JUNREL\mobile_ionic\src\environments\environment.ts';
if (file_exists($mobileConfigPath)) {
    echo "✅ Mobile app environment file found\n";
    
    $envContent = file_get_contents($mobileConfigPath);
    if (strpos($envContent, 'last-5acaf') !== false) {
        echo "✅ Mobile app has correct Firebase project ID\n";
    } else {
        echo "❌ Mobile app Firebase project ID mismatch\n";
    }
    
    if (strpos($envContent, '192.168.112.210:8000') !== false) {
        echo "✅ Mobile app has correct API URL\n";
    } else {
        echo "❌ Mobile app API URL mismatch\n";
    }
} else {
    echo "❌ Mobile app environment file not found\n";
}

// Check google-services.json
$googleServicesPath = 'C:\Users\<USER>\JUNREL\mobile_ionic\google-services.json';
if (file_exists($googleServicesPath)) {
    echo "✅ google-services.json found\n";
    
    $googleServices = json_decode(file_get_contents($googleServicesPath), true);
    if ($googleServices['project_info']['project_id'] === 'last-5acaf') {
        echo "✅ google-services.json has correct project ID\n";
    } else {
        echo "❌ google-services.json project ID mismatch\n";
    }
    
    $packageName = $googleServices['client'][0]['client_info']['android_client_info']['package_name'];
    echo "📱 Package name in google-services.json: $packageName\n";
    
    if ($packageName === 'com.webalerto.app') {
        echo "✅ Package name is correct\n";
    } else {
        echo "⚠️  Package name should be 'com.webalerto.app'\n";
    }
} else {
    echo "❌ google-services.json not found\n";
}

echo "\n=== Next Steps ===\n\n";

echo "🚀 To fix the notification issue:\n\n";

echo "1. Rebuild mobile app:\n";
echo "   cd C:\\Users\\<USER>\\JUNREL\\mobile_ionic\n";
echo "   ionic build\n";
echo "   ionic capacitor sync android\n";
echo "   ionic capacitor open android\n\n";

echo "2. In Android Studio:\n";
echo "   - Connect your Android device\n";
echo "   - Click Run button\n";
echo "   - Wait for app to install\n\n";

echo "3. Check token registration:\n";
echo "   D:\\AlertoCapstone\\php\\php.exe check-fcm-status.php\n\n";

echo "4. If still no tokens, check Firebase Console:\n";
echo "   - Go to Project Settings\n";
echo "   - Add new Android app with package: com.webalerto.app\n";
echo "   - Download new google-services.json\n";
echo "   - Replace the file and rebuild\n\n";

echo "=== Test Complete ===\n";
