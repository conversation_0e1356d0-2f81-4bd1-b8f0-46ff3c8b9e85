<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserManagementRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'requester_id',
        'target_user_id',
        'action_type',
        'reason',
        'status',
        'reviewed_by',
        'review_notes',
        'reviewed_at',
        'requested_first_name',
        'requested_last_name',
        'requested_email',
        'requested_position',
        'requested_barangay'
    ];

    protected $casts = [
        'reviewed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Relationships
    public function requester()
    {
        return $this->belongsTo(User::class, 'requester_id');
    }

    public function targetUser()
    {
        return $this->belongsTo(User::class, 'target_user_id');
    }

    public function reviewer()
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    public function scopeForBarangay($query, $barangay)
    {
        return $query->whereHas('requester', function($q) use ($barangay) {
            $q->where('barangay', $barangay);
        });
    }

    // Helper methods
    public function isPending()
    {
        return $this->status === 'pending';
    }

    public function isApproved()
    {
        return $this->status === 'approved';
    }

    public function isRejected()
    {
        return $this->status === 'rejected';
    }

    public function getActionTypeDisplayAttribute()
    {
        return ucfirst($this->action_type);
    }

    public function getStatusDisplayAttribute()
    {
        return ucfirst($this->status);
    }

    // Registration request specific methods
    public function isRegistrationRequest()
    {
        return $this->action_type === 'registration';
    }

    public function getRequestedFullNameAttribute()
    {
        if ($this->isRegistrationRequest()) {
            return $this->requested_first_name . ' ' . $this->requested_last_name;
        }
        return null;
    }

    public function scopeRegistrationRequests($query)
    {
        return $query->where('action_type', 'registration');
    }

    public function scopeUserManagementRequests($query)
    {
        return $query->whereIn('action_type', ['deactivate', 'delete']);
    }
}
