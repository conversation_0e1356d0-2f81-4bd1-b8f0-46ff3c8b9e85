<?php

namespace App\Exceptions;

use Exception;

class FCMException extends Exception
{
    /**
     * Create a new FCM exception instance.
     *
     * @param string $message
     * @param int $code
     * @param \Throwable|null $previous
     * @return void
     */
    public function __construct($message = 'Firebase Cloud Messaging error', $code = 0, \Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }

    /**
     * Report the exception.
     *
     * @return bool|null
     */
    public function report()
    {
        \Log::error('FCM Exception: ' . $this->getMessage(), [
            'code' => $this->getCode(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'trace' => $this->getTraceAsString()
        ]);
    }

    /**
     * Render the exception into an HTTP response.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function render($request)
    {
        if ($request->expectsJson()) {
            return response()->json([
                'success' => false,
                'message' => 'Notification service error: ' . $this->getMessage(),
                'error_code' => $this->getCode() ?: 'fcm_error'
            ], 500);
        }

        return response()->view('errors.fcm', [
            'message' => $this->getMessage(),
            'code' => $this->getCode()
        ], 500);
    }
}