<?php
namespace App\Http\Controllers;
use App\Models\Evacuation;
use App\Models\AppNotification;
use App\Models\User;
use App\Models\Barangay;
use App\Services\BarangayService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;


class EvacuationManagementController extends Controller
{
    protected $barangayService;

    public function __construct(BarangayService $barangayService)
    {
        $this->barangayService = $barangayService;
    }

    public function showDashboard(Request $request)
    {
        $user = Auth::user();
        $selectedCity = $request->input('city');
        $selectedBarangay = $request->input('barangay');
        $search = $request->input('search');
        $statusFilter = $request->input('status');

        // Get list of accessible cities and barangays for filter dropdown
        $cities = [];
        $barangays = [];

        if ($user->hasRole('super_admin')) {
            // CDRRMC: get barangays in their city/municipality
            $barangays = $this->barangayService->getAccessibleBarangays($user);
        } elseif ($user->hasRole('system_admin')) {
            // System Admin: get all cities and barangays province-wide
            $cities = $this->barangayService->getAllProvincialCities();
            if ($selectedCity) {
                $barangays = $this->barangayService->getBarangaysByCity($selectedCity);
            } else {
                $barangays = $this->barangayService->getAllProvincialBarangays();
            }
        }
        
        // Base query for evacuation centers (for the table/list, filtered)
        $query = Evacuation::query();

        // Apply role-based filtering
        if ($user->hasRole('admin')) {
            // BDRRMC users: only their barangay
            $query->where('barangay', $user->barangay);
        } elseif ($user->hasRole('super_admin')) {
            // CDRRMC users: filter by selected barangay or all barangays in their city
            if ($selectedBarangay) {
                $query->where('barangay', $selectedBarangay);
            } else {
                // Filter by barangays in their city
                $accessibleBarangays = $this->barangayService->getAccessibleBarangays($user);
                if (!empty($accessibleBarangays)) {
                    $query->whereIn('barangay', $accessibleBarangays);
                }
            }
        } elseif ($user->hasRole('system_admin')) {
            // System Admin: province-wide access, filter by city and/or barangay
            if ($selectedCity) {
                $query->where('city', $selectedCity);
            }
            if ($selectedBarangay) {
                $query->where('barangay', $selectedBarangay);
            }
        }
        
        // Apply search filter if provided
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('street_name', 'like', "%{$search}%")
                  ->orWhere('barangay', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%");
            });
        }

        // Apply status filter if provided
        if ($statusFilter) {
            $query->where('status', $statusFilter);
        }
        
        // Order by created_at in descending order (latest first)
        $query->orderBy('created_at', 'desc');
        
        $centers = $query->paginate(10);
        
        // --- UNFILTERED COUNTS FOR SUMMARY CARDS ---
        $userCentersQuery = Evacuation::query();
        if (!$user->hasRole('super_admin')) {
            $userCentersQuery->where('barangay', $user->barangay);
        }
        $totalCenters = (clone $userCentersQuery)->count();
        $activeCenters = (clone $userCentersQuery)->where('status', 'Active')->count();
        $inactiveCenters = (clone $userCentersQuery)->where('status', 'Inactive')->count();
        $maintenanceCenters = (clone $userCentersQuery)->where('status', 'Under Maintenance')->count();
        $fullCenters = (clone $userCentersQuery)->where('status', 'Full')->count();
        // --- END UNFILTERED COUNTS ---

        return view('components.evacuation_management.evacuation-dashboard',
            compact('centers', 'activeCenters', 'inactiveCenters', 'maintenanceCenters', 'fullCenters', 'totalCenters', 'cities', 'barangays', 'selectedCity', 'selectedBarangay', 'search', 'statusFilter'));

    }

    public function showAddForm()
    {
        $user = Auth::user();

        // Super admin cannot add new centers
        if ($user->hasRole('super_admin')) {
            return redirect()->route('components.evacuation_management.evacuation-dashboard')
                ->with('error', 'Super administrators cannot add new evacuation centers.');
        }

        // Get list of accessible barangays for admin users using PSGC-connected BarangayService
        $barangays = [];
        if ($user->hasRole('admin')) {
            $barangayService = app(\App\Services\BarangayService::class);
            $barangays = $barangayService->getAccessibleBarangays($user);
        }

        // Get default coordinates for user's city
        $psgcService = app(\App\Services\PSGCService::class);
        $cityCoordinates = $psgcService->getCityCoordinates($user->city ?? 'Cebu City');

        return view('components.evacuation_management.add-evacuation-center', compact('barangays', 'cityCoordinates'));
    }

    public function showEditForm($id)
    {
        $user = Auth::user();

        // Super admin cannot edit centers
        if ($user->hasRole('super_admin')) {
            return redirect()->route('components.evacuation_management.evacuation-dashboard')
                ->with('error', 'Super administrators can only view evacuation centers.');
        }

        // Admin and regular users can only edit centers in their barangay
        $evacuationCenter = Evacuation::where('barangay', $user->barangay)
            ->findOrFail($id);

        // Get default coordinates for user's city
        $psgcService = app(\App\Services\PSGCService::class);
        $cityCoordinates = $psgcService->getCityCoordinates($user->city ?? 'Cebu City');

        return view('components.evacuation_management.edit-evacuation-center', compact('evacuationCenter', 'cityCoordinates'));
    }

    public function store(Request $request)
    {
        try {
            $user = Auth::user();

            // Super admin cannot create new centers
            if ($user->hasRole('super_admin')) {
                return redirect()->route('components.evacuation_management.evacuation-dashboard')
                    ->with('error', 'Super administrators cannot add new evacuation centers.');
            }

            // Apply role-based restrictions for location validation
            if ($user->hasRole('admin')) {
                // BDRRMC users: restricted to their specific barangay
                $request->merge(['barangay' => $user->barangay]);
            }

            // Custom validation rules based on user role
            $barangayRules = ['required', 'string', 'max:255', new \App\Rules\ValidBarangay()];

            // Add text-based restrictions based on user role and credentials
            if ($user->hasRole('admin')) {
                // BDRRMC: Restrict to specific barangay (text-based validation only)
                $barangayRules[] = function ($attribute, $value, $fail) use ($user, $request) {
                    // Check barangay match
                    if (strtolower(trim($value)) !== strtolower(trim($user->barangay))) {
                        $fail("You can only add evacuation centers within your assigned barangay: {$user->barangay}.");
                        return;
                    }
                };
            } elseif ($user->hasRole('super_admin')) {
                // CDRRMC: Allow any barangay within their city/municipality
                // Text-based validation will be handled by form field validation
                // Users can select any barangay from their accessible list
            }

            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'building_name' => 'nullable|string|max:255',
                'street_name' => 'nullable|string|max:255',
                'province' => 'nullable|string|max:255',
                'city' => 'nullable|string|max:255',
                'postal_code' => 'nullable|string|max:20',
                'barangay' => $barangayRules,
                'latitude' => 'required|numeric',
                'longitude' => 'required|numeric',
                'capacity' => 'required|integer',
                'contact_numbers' => 'required|array|min:1',
                'contact_numbers.*' => 'required|string|max:255',
                'contact_networks' => 'required|array',
                'contact_networks.*' => 'required|string|in:Globe,Smart,PLDT,Sun,TM,TNT,DITO,Other',
                'disaster_type' => 'required|array',
                'disaster_type.*' => 'required|string',
                'status' => 'required|in:Active,Inactive,Under Maintenance',
                'other_disaster_type' => 'nullable|string|max:255',
            ]);

            // Handle 'Others' disaster type
            if (in_array('Others', $validated['disaster_type']) && !empty($validated['other_disaster_type'])) {
                // Find the 'Others' value and replace it with the custom text
                $otherKey = array_search('Others', $validated['disaster_type']);
                if ($otherKey !== false) {
                    $validated['disaster_type'][$otherKey] = 'Others: ' . $validated['other_disaster_type'];
                }
            }

            // Process contact information
            $contacts = [];
            if (isset($validated['contact_numbers'])) {
                for ($i = 0; $i < count($validated['contact_numbers']); $i++) {
                    $contacts[] = [
                        'number' => $validated['contact_numbers'][$i],
                        'network' => $validated['contact_networks'][$i] ?? 'Other'
                    ];
                }
            }
            $validated['contact'] = $contacts;

            // Remove contact arrays from validated data as they're now processed
            unset($validated['contact_numbers'], $validated['contact_networks']);
            unset($validated['other_disaster_type']);

            // Ensure coordinates are saved with high precision
            $validated['latitude'] = number_format((float)$validated['latitude'], 8, '.', '');
            $validated['longitude'] = number_format((float)$validated['longitude'], 8, '.', '');
            
            $center = Evacuation::create($validated);

            // Send notification to all users about new evacuation center
            $this->sendEvacuationCenterNotification($center);

            // Return JSON for AJAX requests, redirect for regular form submissions
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Evacuation center added successfully',
                    'redirect' => route('components.evacuation_management.evacuation-dashboard')
                ]);
            }

            return redirect()->route('components.evacuation_management.evacuation-dashboard')
                ->with('success', 'Evacuation center added successfully.');

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Validation error adding evacuation center: ' . $e->getMessage() . ' Errors: ' . json_encode($e->errors()));
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed.',
                    'errors' => $e->errors()
                ], 422);
            }
            return back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            \Log::error('Error adding evacuation center: ' . $e->getMessage());
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error adding evacuation center: ' . $e->getMessage()
                ], 500);
            }
            return back()->withInput()->withErrors(['error' => 'Error adding evacuation center: ' . $e->getMessage()]);
        }
    }

    public function update(Request $request, $id)
    {
        try {
            $user = Auth::user();

            // Super admin cannot update centers
            if ($user->hasRole('super_admin')) {
                return redirect()->route('components.evacuation_management.evacuation-dashboard')
                    ->with('error', 'Super administrators can only view evacuation centers.');
            }

            // Admin and regular users can only update centers in their barangay
            $center = Evacuation::where('barangay', $user->barangay)
                ->findOrFail($id);

            // Custom validation rules based on user role
            $barangayRules = ['required', 'string', 'max:255', new \App\Rules\ValidBarangay()];

            // Add text-based restrictions based on user role and credentials
            if ($user->hasRole('admin')) {
                // BDRRMC: Restrict to specific barangay (text-based validation only)
                $barangayRules[] = function ($attribute, $value, $fail) use ($user, $request) {
                    // Check barangay match
                    if (strtolower(trim($value)) !== strtolower(trim($user->barangay))) {
                        $fail("You can only update evacuation centers within your assigned barangay: {$user->barangay}.");
                        return;
                    }
                };
            } elseif ($user->hasRole('super_admin')) {
                // CDRRMC: Allow any barangay within their city/municipality
                // Text-based validation will be handled by form field validation
                // Users can select any barangay from their accessible list
            }

            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'building_name' => 'nullable|string|max:255',
                'street_name' => 'nullable|string|max:255',
                'province' => 'nullable|string|max:255',
                'city' => 'nullable|string|max:255',
                'postal_code' => 'nullable|string|max:20',
                'barangay' => $barangayRules,
                'latitude' => 'required|numeric',
                'longitude' => 'required|numeric',
                'capacity' => 'required|integer',
                'contact_numbers' => 'required|array|min:1',
                'contact_numbers.*' => 'required|string|max:255',
                'contact_networks' => 'required|array',
                'contact_networks.*' => 'required|string|in:Globe,Smart,PLDT,Sun,TM,TNT,DITO,Other',
                'disaster_type' => 'required|array',
                'disaster_type.*' => 'required|string',
                'status' => 'required|in:Active,Inactive,Under Maintenance',
                'other_disaster_type' => 'nullable|string|max:255',
            ]);

            // Handle 'Others' disaster type
            if (in_array('Others', $validated['disaster_type']) && !empty($validated['other_disaster_type'])) {
                $otherKey = array_search('Others', $validated['disaster_type']);
                if ($otherKey !== false) {
                    $validated['disaster_type'][$otherKey] = 'Others: ' . $validated['other_disaster_type'];
                }
            }

            // Process contact information
            $contacts = [];
            if (isset($validated['contact_numbers'])) {
                for ($i = 0; $i < count($validated['contact_numbers']); $i++) {
                    $contacts[] = [
                        'number' => $validated['contact_numbers'][$i],
                        'network' => $validated['contact_networks'][$i] ?? 'Other'
                    ];
                }
            }
            $validated['contact'] = $contacts;

            // Remove contact arrays from validated data as they're now processed
            unset($validated['contact_numbers'], $validated['contact_networks']);
            unset($validated['other_disaster_type']);

            // If user is not super_admin or admin, force their barangay
            if (!$user->hasRole('super_admin') && !$user->hasRole('admin')) {
                $validated['barangay'] = $user->barangay;
            }

            // Ensure coordinates are saved with high precision
            $validated['latitude'] = number_format((float)$validated['latitude'], 8, '.', '');
            $validated['longitude'] = number_format((float)$validated['longitude'], 8, '.', '');

            $center->update($validated);

            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Evacuation center updated successfully',
                    'redirect' => route('components.evacuation_management.evacuation-dashboard')
                ]);
            }

            return redirect()->route('components.evacuation_management.evacuation-dashboard')
                ->with('success', 'Evacuation center updated successfully');
                
        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Validation error updating evacuation center: ' . $e->getMessage() . ' Errors: ' . json_encode($e->errors()));
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed.',
                    'errors' => $e->errors()
                ], 422);
            }
            return back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            \Log::error('Error updating evacuation center: ' . $e->getMessage());
            
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error updating evacuation center: ' . $e->getMessage()
                ], 500);
            }
            
            return redirect()->back()
                ->withInput()
                ->withErrors(['general' => 'An error occurred while updating the evacuation center.']);
        }
    }

    public function destroy($id)
    {
        try {
            $user = Auth::user();

            // Super admin cannot delete centers
            if ($user->hasRole('super_admin')) {
                if (request()->ajax() || request()->wantsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Super administrators can only view evacuation centers.'
                    ], 403);
                }
                return redirect()->route('components.evacuation_management.evacuation-dashboard')
                    ->with('error', 'Super administrators can only view evacuation centers.');
            }

            // Admin and regular users can only delete centers in their barangay
            $center = Evacuation::where('barangay', $user->barangay)
                ->findOrFail($id);
            
            // Log the delete attempt
            \Log::info("Delete attempt for evacuation center {$id} by user {$user->email} (role: {$user->role})");
            
            $center->delete();
            
            if (request()->ajax() || request()->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Evacuation center deleted successfully'
                ]);
            }
            
            return redirect()->route('components.evacuation_management.evacuation-dashboard')
                ->with('success', 'Evacuation center deleted successfully');
        } catch (\Exception $e) {
            \Log::error("Error deleting evacuation center {$id}: " . $e->getMessage());
            return redirect()->route('components.evacuation_management.evacuation-dashboard')
                ->with('error', 'Error deleting evacuation center: ' . $e->getMessage());
        }
    }

    public function show($id)
    {
        $user = Auth::user();
        $query = Evacuation::query();
        
        // If user is not super_admin, restrict to their barangay
        if (!$user->hasRole('super_admin')) {
            $query->where('barangay', $user->barangay);
        }
        
        $center = $query->findOrFail($id);
        return view('components.evacuation_management.view-map', compact('center'));
    }

    public function centersList(Request $request, $type = 'all')
    {
        $user = Auth::user();
        $query = Evacuation::query();
        $selectedBarangay = $request->input('barangay');
        $search = $request->input('search');
        
        // Set title based on type
        $title = ucfirst($type) . ' Evacuation Centers';
        
        // Apply type filter
        if ($type === 'active') {
            $query->where('status', 'Active');
        } else if ($type === 'inactive') {
            $query->where('status', 'Inactive');
        } else if ($type === 'maintenance') {
            $query->where('status', 'Under Maintenance');
        }
        
        // Apply search filter if provided
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('street_name', 'like', "%{$search}%")
                  ->orWhere('barangay', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%");
            });
        }
        
        // Apply barangay filter if provided
        if ($selectedBarangay) {
            $query->where('barangay', $selectedBarangay);
        }
        // If no barangay filter and user is not super_admin, show only user's barangay
        else if (!$user->hasRole('super_admin')) {
            $query->where('barangay', $user->barangay);
        }
        
        // Order by created_at in descending order (latest first)
        $query->orderBy('created_at', 'desc');
        
        $centers = $query->paginate(9);
        
        // Get total counts for active and inactive centers
        // Important: Clone the query BEFORE pagination to get accurate counts
        // But AFTER applying barangay and search filters
        $baseQuery = clone $query;
        $activeCount = (clone $baseQuery)->where('status', 'Active')->count();
        
        // Create a new clone for inactive count
        $inactiveCount = (clone $baseQuery)->where('status', 'Inactive')->count();
        
        // Count for 'Under Maintenance'
        $maintenanceCount = (clone $baseQuery)->where('status', 'Under Maintenance')->count();
        
        // Define specific barangays for super_admin filter
        // Get accessible barangays for filter dropdown using PSGC-connected BarangayService
        $barangays = [];
        if ($user->hasRole('super_admin') || $user->hasRole('system_admin')) {
            $barangayService = app(\App\Services\BarangayService::class);
            $barangays = $barangayService->getAccessibleBarangays($user);
        }
        
        return view('components.evacuation_management.centers-list', 
            compact('centers', 'activeCount', 'inactiveCount', 'maintenanceCount', 'type', 'barangays', 'selectedBarangay', 'title'));
    }

    public function getCenterDetails($id)
    {
        $user = Auth::user();
        $query = Evacuation::query();

        // If user is not admin, restrict to their barangay
        if (!$user->hasRole('admin') && !$user->hasRole('super_admin')) {
            $query->where('barangay', $user->barangay);
        }

        $center = $query->findOrFail($id);
        return response()->json($center);
    }

    /**
     * API endpoint to get evacuation centers for mobile app
     */
    public function apiList(Request $request)
    {
        try {
            $query = Evacuation::query();

            // Filter by disaster type if provided - handle JSON array format
            if ($request->has('disaster_type') && $request->disaster_type) {
                $query->whereJsonContains('disaster_type', $request->disaster_type);
            }

            // Filter by barangay if provided
            if ($request->has('barangay') && $request->barangay) {
                $query->where('barangay', $request->barangay);
            }

            // Only return active centers for mobile app
            $query->where('status', 'Active');

            $centers = $query->select([
                'id',
                'name',
                'street_name',
                'barangay',
                'city',
                'province',
                'latitude',
                'longitude',
                'capacity',
                'status',
                'disaster_type',
                'contact'
            ])->get();

            // Format the response for mobile app
            $formattedCenters = $centers->map(function($center) {
                // Build address from available components
                $addressParts = array_filter([
                    $center->building_name,
                    $center->street_name,
                    $center->barangay,
                    $center->city,
                    $center->province
                ]);

                return [
                    'id' => $center->id,
                    'name' => $center->name,
                    'address' => implode(', ', $addressParts),
                    'latitude' => (float) $center->latitude,
                    'longitude' => (float) $center->longitude,
                    'capacity' => (int) $center->capacity,
                    'status' => $center->status,
                    'disaster_type' => $center->disaster_type,
                    'contact' => is_array($center->contact) ?
                        (count($center->contact) > 0 ? $center->contact[0]['number'] : '') :
                        $center->contact,
                    'contact_details' => $center->contact,
                    'barangay' => $center->barangay
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $formattedCenters,
                'count' => $formattedCenters->count()
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch evacuation centers',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update evacuation center status (Available/Full)
     */
    public function updateStatus(Request $request, $id)
    {
        try {
            $user = Auth::user();

            // Super admin cannot update centers
            if ($user->hasRole('super_admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Super administrators can only view evacuation centers.'
                ], 403);
            }

            // Admin and regular users can only update centers in their barangay
            $center = Evacuation::where('barangay', $user->barangay)
                ->findOrFail($id);

            $request->validate([
                'status' => 'required|in:Active,Full'
            ]);

            $oldStatus = $center->status;
            $newStatus = $request->status;

            // Update the status
            $center->update(['status' => $newStatus]);

            // Send notification to users if center becomes full
            if ($newStatus === 'Full' && $oldStatus !== 'Full') {
                $this->sendEvacuationCenterFullNotification($center);
            }

            return response()->json([
                'success' => true,
                'message' => $newStatus === 'Full'
                    ? 'Evacuation center marked as full and users have been notified.'
                    : 'Evacuation center marked as available.',
                'new_status' => $newStatus
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating the status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send notification to users when evacuation center becomes full
     */
    private function sendEvacuationCenterFullNotification(Evacuation $center)
    {
        try {
            // Get disaster types as a readable string
            $disasterTypes = is_array($center->disaster_type)
                ? implode(', ', $center->disaster_type)
                : $center->disaster_type;

            // Create notification for all mobile users in the same barangay
            $users = User::where('role', 'mobile_user')
                ->where('barangay', $center->barangay)
                ->get();

            foreach ($users as $user) {
                AppNotification::create([
                    'user_id' => $user->id,
                    'type' => 'evacuation_center_full',
                    'title' => 'Evacuation Center Full',
                    'message' => "The evacuation center '{$center->name}' in {$center->barangay} is now full. Please find an alternative evacuation center.",
                    'data' => json_encode([
                        'center_id' => $center->id,
                        'center_name' => $center->name,
                        'barangay' => $center->barangay,
                        'disaster_types' => $center->disaster_type,
                        'latitude' => $center->latitude,
                        'longitude' => $center->longitude,
                        'status' => 'Full'
                    ]),
                    'read' => false
                ]);
            }

            Log::info('Evacuation center full notifications sent', [
                'center_id' => $center->id,
                'center_name' => $center->name,
                'barangay' => $center->barangay,
                'users_notified' => $users->count()
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send evacuation center full notifications', [
                'center_id' => $center->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send notification to users when a new evacuation center is added
     */
    private function sendEvacuationCenterNotification(Evacuation $center)
    {
        try {
            // Get disaster types as a readable string
            $disasterTypes = is_array($center->disaster_type)
                ? implode(', ', $center->disaster_type)
                : $center->disaster_type;

            // Create notification for all users
            $users = User::where('role', '!=', 'admin')->get(); // Don't notify admins

            foreach ($users as $user) {
                AppNotification::create([
                    'user_id' => $user->id,
                    'type' => 'evacuation_center_added',
                    'title' => 'New Evacuation Center Added',
                    'message' => "A new evacuation center '{$center->name}' has been added in {$center->barangay} for {$disasterTypes} emergencies.",
                    'data' => json_encode([
                        'center_id' => $center->id,
                        'center_name' => $center->name,
                        'barangay' => $center->barangay,
                        'disaster_types' => $center->disaster_type,
                        'latitude' => $center->latitude,
                        'longitude' => $center->longitude
                    ]),
                    'read' => false
                ]);
            }

            \Log::info("Sent evacuation center notification for center: {$center->name} to " . $users->count() . " users");

        } catch (\Exception $e) {
            \Log::error("Failed to send evacuation center notification: " . $e->getMessage());
        }
    }

    /**
     * Helper method to get city name from identifier (PSGC code or name)
     */
    private function getCityNameFromIdentifier($cityIdentifier)
    {
        if (!$cityIdentifier) {
            return null;
        }

        // If it's already a city name (not numeric), return as is
        if (!is_numeric($cityIdentifier)) {
            return $cityIdentifier;
        }

        // If it's a PSGC code, convert to city name
        try {
            $psgcService = app(\App\Services\PSGCService::class);
            $cities = $psgcService->getCebuProvinceCities();

            $city = collect($cities)->firstWhere('code', $cityIdentifier);
            return $city ? $city['name'] : null;
        } catch (\Exception $e) {
            \Log::warning("Failed to convert city identifier to name: {$cityIdentifier}", [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }



public function geocode(Request $request)
{
    $address = $request->get('address');

    if (!$address) {
        return response()->json(['error' => 'Address parameter required'], 400);
    }

    try {
        $params = [
            'q' => $address,
            'countrycodes' => 'ph',
            'format' => 'json',
            'limit' => 10,
            'addressdetails' => 1,
        ];

        $url = "https://nominatim.openstreetmap.org/search?" . http_build_query($params);

        \Log::info("Geocoding request: " . $url);

        // Try with SSL verification first, fallback to no verification if it fails
        try {
            $response = Http::withHeaders([
                'User-Agent' => 'WebAlerto Laravel App'
            ])->timeout(30)->get($url);
        } catch (\Exception $sslError) {
            \Log::warning("SSL verification failed, retrying without verification: " . $sslError->getMessage());
            $response = Http::withHeaders([
                'User-Agent' => 'WebAlerto Laravel App'
            ])->withOptions([
                'verify' => false, // Disable SSL verification as fallback
                'timeout' => 30,
            ])->get($url);
        }

        if (!$response->successful()) {
            \Log::error("Nominatim API error: " . $response->status() . " - " . $response->body());
            return response()->json(['error' => 'Nominatim API error', 'status' => $response->status(), 'details' => $response->body()], 500);
        }

        $data = $response->json();

        \Log::info("Nominatim response: " . json_encode($data));

        if (empty($data)) {
            return response()->json(['features' => [], 'message' => 'No results found for: ' . $address]);
        }

        $features = array_map(function ($item) {
            return [
                'id' => 'poi.' . $item['place_id'],
                'text' => $item['display_name'],
                'place_name' => $item['display_name'],
                'center' => [(float)$item['lon'], (float)$item['lat']],
                'context' => [
                    ['id' => 'postcode.' . ($item['address']['postcode'] ?? ''), 'text' => $item['address']['postcode'] ?? ''],
                    ['id' => 'place.' . ($item['address']['city'] ?? ''), 'text' => $item['address']['city'] ?? ''],
                    ['id' => 'region.' . ($item['address']['state'] ?? ''), 'text' => $item['address']['state'] ?? ''],
                ]
            ];
        }, $data);

        return response()->json(['features' => $features]);

    } catch (\Exception $e) {
        \Log::error("Geocode error: " . $e->getMessage());
        return response()->json(['error' => 'Geocoding failed: ' . $e->getMessage()], 500);
    }
}

public function reverseGeocode(Request $request)
{
    $lat = $request->get('lat');
    $lng = $request->get('lng');

    if (!$lat || !$lng) {
        return response()->json(['error' => 'Latitude and longitude parameters required'], 400);
    }

    $params = [
        'lat' => $lat,
        'lon' => $lng,
        'format' => 'json',
        'addressdetails' => 1,
        'zoom' => 18,
    ];

    try {
        \Log::info("Reverse geocoding request: lat={$lat}, lng={$lng}");

        // Try with SSL verification first, fallback to no verification if it fails
        try {
            $response = Http::withHeaders([
                'User-Agent' => 'WebAlerto Laravel App'
            ])->timeout(30)->get("https://nominatim.openstreetmap.org/reverse", $params);
        } catch (\Exception $sslError) {
            \Log::warning("SSL verification failed for reverse geocoding, retrying without verification: " . $sslError->getMessage());
            $response = Http::withHeaders([
                'User-Agent' => 'WebAlerto Laravel App'
            ])->withOptions([
                'verify' => false, // Disable SSL verification as fallback
                'timeout' => 30,
            ])->get("https://nominatim.openstreetmap.org/reverse", $params);
        }

        if (!$response->successful()) {
            \Log::error("Reverse geocoding API error: " . $response->status() . " - " . $response->body());
            return response()->json(['error' => 'Reverse geocoding failed: ' . $response->body()], 500);
        }

        $data = $response->json();

        \Log::info("Reverse geocoding response: " . json_encode($data));

        if (!empty($data) && !isset($data['error'])) {
            $address = $data['address'];

            $buildingName = $address['school'] ?? $address['hospital'] ?? $address['public_building'] ?? $address['shop'] ?? $address['office'] ?? $address['commercial'] ?? $address['amenity'] ?? $address['tourism'] ?? '';
            $street = $address['road'] ?? $address['path'] ?? '';

            if (empty($buildingName)) {
                $fullAddressParts = array_map('trim', explode(',', $data['display_name']));
                $potentialName = $fullAddressParts[0] ?? '';
                if (!empty($potentialName) && !is_numeric($potentialName) && strcasecmp($potentialName, $street) !== 0) {
                    $buildingName = $potentialName;
                }
            }

            if (!empty($buildingName) && strcasecmp($buildingName, $street) === 0) {
                $street = '';
            }

            $province = $address['state'] ?? $address['region'] ?? $address['province'] ?? '';
            $city = $address['city'] ?? $address['town'] ?? $address['municipality'] ?? '';
            $postal_code = $address['postcode'] ?? '';
            $full_address = $data['display_name'] ?? '';

            if ($province === 'Central Visayas') {
                $province = 'Cebu';
            }

            $responseAddress = [
                'building_name' => $buildingName,
                'street' => $street,
                'barangay' => $address['suburb'] ?? $address['village'] ?? $address['city_district'] ?? $address['neighbourhood'] ?? '',
                'city' => $city,
                'province' => $province,
                'postal_code' => $postal_code,
                'full_address' => $full_address
            ];

            if ($responseAddress['province'] === 'Metro Manila' && empty($responseAddress['city'])) {
                $responseAddress['city'] = $address['county'] ?? 'Metro Manila';
            }

            return response()->json($responseAddress);
        }

        return response()->json(['error' => 'No features found', 'details' => $data['error'] ?? ''], 404);

    } catch (\Exception $e) {
        return response()->json(['error' => 'Reverse geocoding failed: ' . $e->getMessage()], 500);
    }
}
}