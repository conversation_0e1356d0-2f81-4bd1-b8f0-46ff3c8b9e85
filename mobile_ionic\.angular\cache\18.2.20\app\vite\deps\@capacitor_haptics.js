import {
  ImpactStyle,
  NotificationType
} from "./chunk-OGKSRTVA.js";
import {
  registerPlugin
} from "./chunk-MCFBZ5YE.js";
import "./chunk-EAE2VPRF.js";

// node_modules/@capacitor/haptics/dist/esm/index.js
var Haptics = registerPlugin("Haptics", {
  web: () => import("./web-OFE4VD5L.js").then((m) => new m.HapticsWeb())
});
export {
  Haptics,
  ImpactStyle,
  NotificationType
};
//# sourceMappingURL=@capacitor_haptics.js.map
