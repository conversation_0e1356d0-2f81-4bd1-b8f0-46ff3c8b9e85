.offline-status-banner {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  
  &.status-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.9), rgba(25, 135, 84, 0.9));
    color: white;
  }
  
  &.status-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.9), rgba(255, 143, 0, 0.9));
    color: #212529;
  }
  
  &.status-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.9), rgba(176, 42, 55, 0.9));
    color: white;
  }
}

.status-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  cursor: pointer;
  user-select: none;
  
  .status-icon {
    font-size: 18px;
    margin-right: 8px;
  }
  
  .status-text {
    flex: 1;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
  }
  
  .toggle-icon {
    font-size: 16px;
    margin-left: 8px;
    transition: transform 0.3s ease;
  }
}

.status-details {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.details-content {
  padding: 12px 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .detail-icon {
    font-size: 14px;
    margin-right: 8px;
    opacity: 0.8;
    width: 16px;
  }
  
  .detail-label {
    font-weight: 500;
    margin-right: 8px;
    min-width: 80px;
  }
  
  .detail-value {
    flex: 1;
    
    &.online {
      color: #28a745;
      font-weight: 500;
    }
    
    &.offline {
      color: #dc3545;
      font-weight: 500;
    }
    
    &.cached {
      color: #28a745;
    }
    
    &.not-cached {
      color: #6c757d;
    }
  }
}

.action-buttons {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: center;
  
  ion-button {
    --color: currentColor;
    font-size: 12px;
  }
}

// Animation for slide down
@keyframes slideDown {
  from {
    max-height: 0;
    opacity: 0;
  }
  to {
    max-height: 200px;
    opacity: 1;
  }
}

.status-details {
  animation: slideDown 0.3s ease-out;
}

// Responsive design
@media (max-width: 768px) {
  .status-content {
    padding: 6px 12px;
    
    .status-text {
      font-size: 13px;
    }
  }
  
  .details-content {
    padding: 10px 12px;
  }
  
  .detail-item {
    font-size: 12px;
    
    .detail-label {
      min-width: 70px;
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .offline-status-banner {
    &.status-warning {
      color: white;
    }
  }
  
  .status-details {
    background: rgba(255, 255, 255, 0.05);
  }
}
