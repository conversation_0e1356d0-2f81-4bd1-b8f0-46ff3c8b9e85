# ALERTO Email Notification System Configuration
# Copy these variables to your .env file and update with your actual values

# =============================================================================
# SMTP CONFIGURATION
# =============================================================================

# Mail Driver (use 'smtp' for production)
MAIL_MAILER=smtp

# SMTP Host (examples for common providers)
# Gmail: smtp.gmail.com
# Outlook: smtp-mail.outlook.com
# Yahoo: smtp.mail.yahoo.com
# Custom: your-smtp-server.com
MAIL_HOST=smtp.gmail.com

# SMTP Port (usually 587 for TLS, 465 for SSL)
MAIL_PORT=587

# SMTP Username (your email address)
MAIL_USERNAME=<EMAIL>

# SMTP Password (use app password for Gmail)
MAIL_PASSWORD=your-app-password

# SMTP Encryption (tls or ssl)
MAIL_ENCRYPTION=tls

# From Address (should match your SMTP username)
MAIL_FROM_ADDRESS=<EMAIL>

# From Name (displayed in email clients)
MAIL_FROM_NAME="ALERTO Disaster Management System"

# =============================================================================
# QUEUE CONFIGURATION (for reliable email delivery)
# =============================================================================

# Queue Connection (use 'database' for production)
QUEUE_CONNECTION=database

# =============================================================================
# SYSTEM ADMINISTRATOR CREDENTIALS (Development/Testing)
# =============================================================================

# System Administrator (only hardcoded account for initial setup)
SYSTEM_ADMIN_EMAIL=<EMAIL>
SYSTEM_ADMIN_PASSWORD=SysAdmin@2025

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Application URL (for invitation links)
APP_URL=http://localhost:8000

# Application Name
APP_NAME="ALERTO"

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Session Configuration
SESSION_DRIVER=file
SESSION_LIFETIME=120

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log Level (debug, info, notice, warning, error, critical, alert, emergency)
LOG_LEVEL=info

# Log Channel (single, daily, stack, syslog, errorlog)
LOG_CHANNEL=stack

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Database Connection
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=alerto_db
DB_USERNAME=root
DB_PASSWORD=

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================

# Cache Driver (file, redis, memcached)
CACHE_DRIVER=file

# =============================================================================
# ROLE-BASED ACCESS CONTROL
# =============================================================================

# Role Hierarchy:
# - super_admin: System Administrator (full system access)
# - admin: City Hall Admin (city-wide monitoring, access to all barangays)
# - chairman: BDRRMO Chairman (barangay-specific access only)
# - officer: BDRRMO Officer (barangay-specific access only)
# - assistant: BDRRMO Assistant (barangay-specific access only)

# Barangay Access:
# - City Hall: City-wide monitoring and access to all barangays
# - Lahug, Mabolo, Luz: Barangay-specific access only

# =============================================================================
# NOTES FOR SETUP
# =============================================================================

# 1. Gmail Setup Instructions:
#    - Enable 2-Factor Authentication on your Gmail account
#    - Go to Google Account settings → Security → 2-Step Verification → App passwords
#    - Generate a password for "Mail"
#    - Use the generated password in MAIL_PASSWORD

# 2. Admin Account Registration:
#    - Only System Administrator can register new admin accounts
#    - Use the registration form at /admin-invitations/registration-form
#    - All accounts (including City Hall admin) must be registered through the form
#    - No hardcoded credentials except System Administrator

# 3. For Production:
#    - Use a dedicated email service (SendGrid, Mailgun, etc.)
#    - Set up proper DNS records (SPF, DKIM, DMARC)
#    - Use environment-specific credentials
#    - Enable queue workers for reliable delivery

# 4. Security Best Practices:
#    - Never commit .env files to version control
#    - Use strong, unique passwords
#    - Regularly rotate credentials
#    - Monitor email delivery rates
#    - Set up proper backup procedures

# 5. Testing:
#    - Test email configuration: php artisan tinker
#    - Send test email: Mail::raw('Test', function($m) { $m->to('<EMAIL>')->subject('Test'); });
#    - Check logs: tail -f storage/logs/laravel.log 