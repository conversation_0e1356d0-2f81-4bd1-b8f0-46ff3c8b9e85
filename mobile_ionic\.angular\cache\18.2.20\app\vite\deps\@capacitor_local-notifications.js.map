{"version": 3, "sources": ["../../../../../../node_modules/@capacitor/local-notifications/dist/esm/definitions.js", "../../../../../../node_modules/@capacitor/local-notifications/dist/esm/index.js"], "sourcesContent": ["/// <reference types=\"@capacitor/cli\" />\n/**\n * Day of the week. Used for scheduling notifications on a particular weekday.\n */\nexport var Weekday;\n(function (Weekday) {\n  Weekday[Weekday[\"Sunday\"] = 1] = \"Sunday\";\n  Weekday[Weekday[\"Monday\"] = 2] = \"Monday\";\n  Weekday[Weekday[\"Tuesday\"] = 3] = \"Tuesday\";\n  Weekday[Weekday[\"Wednesday\"] = 4] = \"Wednesday\";\n  Weekday[Weekday[\"Thursday\"] = 5] = \"Thursday\";\n  Weekday[Weekday[\"Friday\"] = 6] = \"Friday\";\n  Weekday[Weekday[\"Saturday\"] = 7] = \"Saturday\";\n})(Weekday || (Weekday = {}));\n", "import { registerPlugin } from '@capacitor/core';\nconst LocalNotifications = registerPlugin('LocalNotifications', {\n  web: () => import('./web').then(m => new m.LocalNotificationsWeb())\n});\nexport * from './definitions';\nexport { LocalNotifications };\n"], "mappings": ";;;;;;AAIO,IAAI;AAAA,CACV,SAAUA,UAAS;AAClB,EAAAA,SAAQA,SAAQ,QAAQ,IAAI,CAAC,IAAI;AACjC,EAAAA,SAAQA,SAAQ,QAAQ,IAAI,CAAC,IAAI;AACjC,EAAAA,SAAQA,SAAQ,SAAS,IAAI,CAAC,IAAI;AAClC,EAAAA,SAAQA,SAAQ,WAAW,IAAI,CAAC,IAAI;AACpC,EAAAA,SAAQA,SAAQ,UAAU,IAAI,CAAC,IAAI;AACnC,EAAAA,SAAQA,SAAQ,QAAQ,IAAI,CAAC,IAAI;AACjC,EAAAA,SAAQA,SAAQ,UAAU,IAAI,CAAC,IAAI;AACrC,GAAG,YAAY,UAAU,CAAC,EAAE;;;ACZ5B,IAAM,qBAAqB,eAAe,sBAAsB;AAAA,EAC9D,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,sBAAsB,CAAC;AACpE,CAAC;", "names": ["Weekday"]}