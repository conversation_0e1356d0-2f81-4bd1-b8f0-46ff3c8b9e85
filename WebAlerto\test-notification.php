<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\DeviceToken;
use App\Models\Notification;
use App\Services\FCMService;
use Illuminate\Support\Facades\Log;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== FCM Notification Test ===\n\n";

// Check if we have any arguments
$args = array_slice($argv, 1);

if (empty($args)) {
    echo "Usage:\n";
    echo "  php test-notification.php register <fcm_token> [user_id]  - Register a new FCM token\n";
    echo "  php test-notification.php send <title> <message>          - Send test notification\n";
    echo "  php test-notification.php list                           - List all tokens\n";
    echo "  php test-notification.php clean                          - Remove test tokens\n";
    echo "\nExamples:\n";
    echo "  php test-notification.php register \"dGVzdC10b2tlbi0xMjM0NS1hbmRyb2lkLWRldmljZQ\" 1\n";
    echo "  php test-notification.php send \"Test Alert\" \"This is a test notification\"\n";
    exit(1);
}

$command = $args[0];

switch ($command) {
    case 'register':
        if (count($args) < 2) {
            echo "Error: FCM token is required\n";
            echo "Usage: php test-notification.php register <fcm_token> [user_id]\n";
            exit(1);
        }

        $token = $args[1];
        $userId = isset($args[2]) ? (int)$args[2] : 1; // Default to user ID 1

        echo "Registering FCM token...\n";
        echo "Token: " . substr($token, 0, 20) . "...\n";
        echo "User ID: $userId\n\n";

        try {
            $deviceToken = DeviceToken::updateOrCreate(
                ['token' => $token],
                [
                    'user_id' => $userId,
                    'device_type' => 'android',
                    'project_id' => 'last-5acaf',
                    'is_active' => true,
                    'last_used_at' => now()
                ]
            );

            echo "✅ Token registered successfully!\n";
            echo "   ID: {$deviceToken->id}\n";
            echo "   User ID: {$deviceToken->user_id}\n";
            echo "   Device Type: {$deviceToken->device_type}\n";
            echo "   Project ID: {$deviceToken->project_id}\n";
            echo "   Active: " . ($deviceToken->is_active ? 'Yes' : 'No') . "\n";

        } catch (Exception $e) {
            echo "❌ Error registering token: " . $e->getMessage() . "\n";
        }
        break;

    case 'send':
        if (count($args) < 3) {
            echo "Error: Title and message are required\n";
            echo "Usage: php test-notification.php send <title> <message>\n";
            exit(1);
        }

        $title = $args[1];
        $message = $args[2];

        echo "Sending test notification...\n";
        echo "Title: $title\n";
        echo "Message: $message\n\n";

        try {
            // Get active tokens (excluding test tokens)
            $tokens = DeviceToken::where('is_active', true)
                ->where('token', 'not like', 'test-%')
                ->pluck('token')
                ->toArray();

            if (empty($tokens)) {
                echo "❌ No active FCM tokens found!\n";
                echo "   Please register a real FCM token first.\n";
                exit(1);
            }

            echo "📱 Found " . count($tokens) . " active token(s)\n";

            // Create notification record
            $notification = Notification::create([
                'title' => $title,
                'message' => $message,
                'category' => 'test',
                'severity' => 'medium',
                'barangay' => 'Test Area',
                'sent' => false
            ]);

            echo "✅ Notification record created (ID: {$notification->id})\n";

            // Send notification
            $fcmService = app(FCMService::class);
            $result = $fcmService->sendNotification($notification, $tokens);

            if ($result['success']) {
                echo "✅ Notification sent successfully!\n";
                echo "   Success count: {$result['success_count']}\n";
                echo "   Failure count: {$result['failure_count']}\n";
                echo "   Invalid tokens: {$result['invalid_tokens']}\n";

                if ($result['failure_count'] > 0) {
                    echo "\n⚠️  Some notifications failed to send. Check Laravel logs for details.\n";
                }
            } else {
                echo "❌ Notification sending failed: {$result['message']}\n";
            }

        } catch (Exception $e) {
            echo "❌ Error sending notification: " . $e->getMessage() . "\n";
        }
        break;

    case 'list':
        echo "Listing all device tokens...\n\n";

        $tokens = DeviceToken::orderBy('created_at', 'desc')->get();

        if ($tokens->count() === 0) {
            echo "No device tokens found.\n";
        } else {
            foreach ($tokens as $token) {
                echo "ID: {$token->id}\n";
                echo "User ID: {$token->user_id}\n";
                echo "Device Type: {$token->device_type}\n";
                echo "Project ID: {$token->project_id}\n";
                echo "Active: " . ($token->is_active ? 'Yes' : 'No') . "\n";
                echo "Token: " . substr($token->token, 0, 30) . "...\n";
                echo "Created: {$token->created_at}\n";
                echo "Last Used: {$token->last_used_at}\n";
                echo "---\n";
            }
        }
        break;

    case 'clean':
        echo "Removing test tokens...\n";

        $testTokens = DeviceToken::where('token', 'like', 'test-%')->get();

        if ($testTokens->count() === 0) {
            echo "No test tokens found.\n";
        } else {
            foreach ($testTokens as $token) {
                echo "Removing test token ID: {$token->id}\n";
                $token->delete();
            }
            echo "✅ Removed {$testTokens->count()} test token(s)\n";
        }
        break;

    default:
        echo "Unknown command: $command\n";
        echo "Available commands: register, send, list, clean\n";
        exit(1);
}

echo "\n";
