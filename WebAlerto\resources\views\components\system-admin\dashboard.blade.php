@extends('layout.app')

@section('title', 'System Administrator Dashboard')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-gray-50 to-slate-100 py-8">
    <div class="max-w-[1920px] mx-auto px-6 sm:px-8 lg:px-12">
        
        <!-- Header Section -->
        <div class="bg-white/90 backdrop-blur-sm rounded-xl shadow-xl border border-slate-200 p-6 mb-6">
            <div class="flex items-center gap-3 mb-4">
                <div class="p-3 bg-gradient-to-br from-slate-600 to-gray-700 rounded-lg shadow-lg">
                    <i class="fas fa-cogs text-white text-xl"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Technical Administrator Dashboard</h1>
                    <p class="text-sm text-gray-600 mt-1">System configurations, user management, and technical maintenance</p>
                </div>
            </div>
            
            @if(isset($error))
                <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    <i class="fas fa-exclamation-triangle mr-2"></i>{{ $error }}
                </div>
            @endif
        </div>

        <!-- Filter Section -->
        <div class="bg-white/90 backdrop-blur-sm rounded-xl shadow-xl border border-slate-200 p-6 mb-6">
            <form method="GET" action="{{ route('system-admin.dashboard') }}" class="flex flex-wrap gap-4 items-end">
                <div class="flex-1 min-w-[200px]">
                    <label for="city" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-city mr-1"></i>Filter by City
                    </label>
                    <select name="city" id="city" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" onchange="this.form.submit()">
                        <option value="">All Cities</option>
                        @foreach($cities as $city)
                            <option value="{{ $city }}" {{ $selectedCity == $city ? 'selected' : '' }}>
                                {{ $city }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="flex-1 min-w-[200px]">
                    <label for="barangay" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-map-marker-alt mr-1"></i>Filter by Barangay
                    </label>
                    <select name="barangay" id="barangay" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" onchange="this.form.submit()">
                        <option value="">All Barangays</option>
                        @foreach($barangays as $barangay)
                            <option value="{{ $barangay }}" {{ $selectedBarangay == $barangay ? 'selected' : '' }}>
                                {{ $barangay }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </form>
        </div>

        <!-- System Overview Stats -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Users -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Users</p>
                        <p class="text-3xl font-bold text-gray-900">{{ number_format($stats['total_users']) }}</p>
                        <p class="text-xs text-gray-500 mt-1">All system users</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Active Users -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Active Users</p>
                        <p class="text-3xl font-bold text-green-600">{{ number_format($stats['active_users']) }}</p>
                        <p class="text-xs text-gray-500 mt-1">Currently active</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                        <i class="fas fa-user-check text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Total Notifications -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Notifications</p>
                        <p class="text-3xl font-bold text-orange-600">{{ number_format($stats['total_notifications']) }}</p>
                        <p class="text-xs text-gray-500 mt-1">System-wide alerts</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-lg">
                        <i class="fas fa-bell text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Evacuation Centers -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Evacuation Centers</p>
                        <p class="text-3xl font-bold text-purple-600">{{ number_format($stats['total_evacuation_centers']) }}</p>
                        <p class="text-xs text-gray-500 mt-1">{{ number_format($stats['active_evacuation_centers']) }} active</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
                        <i class="fas fa-building text-white text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Role Distribution and System Health -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Role Distribution -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-bold text-gray-900">User Role Distribution</h3>
                        <p class="text-sm text-gray-600">Disaster management hierarchy</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl shadow-lg">
                        <i class="fas fa-chart-pie text-white text-xl"></i>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="w-4 h-4 bg-slate-600 rounded"></div>
                            <span class="text-sm font-medium text-gray-700">Technical Administrators</span>
                        </div>
                        <span class="text-lg font-bold text-gray-900">{{ $stats['system_admins'] }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="w-4 h-4 bg-blue-500 rounded"></div>
                            <span class="text-sm font-medium text-gray-700">CDRRMC (City-Level)</span>
                        </div>
                        <span class="text-lg font-bold text-gray-900">{{ $stats['super_admins'] }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="w-4 h-4 bg-green-500 rounded"></div>
                            <span class="text-sm font-medium text-gray-700">BDRRMC (Barangay-Level)</span>
                        </div>
                        <span class="text-lg font-bold text-gray-900">{{ $stats['admins'] }}</span>
                    </div>
                </div>
            </div>

            <!-- User Statistics Chart -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-bold text-gray-900">User Statistics Overview</h3>
                        <p class="text-sm text-gray-600">System-wide user distribution</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
                        <i class="fas fa-chart-bar text-white text-xl"></i>
                    </div>
                </div>

                <div class="h-64">
                    <canvas id="userStatsChart"></canvas>
                </div>
            </div>

            <!-- System Health -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-bold text-gray-900">System Health</h3>
                        <p class="text-sm text-gray-600">Current system status</p>
                    </div>
                    <div class="p-3 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl shadow-lg">
                        <i class="fas fa-heartbeat text-white text-xl"></i>
                    </div>
                </div>
                
                @if(isset($systemHealth))
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Database Status</span>
                        <span class="px-3 py-1 rounded-full text-xs font-medium {{ $systemHealth['database_status'] === 'healthy' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ ucfirst($systemHealth['database_status']) }}
                        </span>
                    </div>
                    
                    @if(isset($systemHealth['user_activity']))
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Active Today</span>
                        <span class="text-lg font-bold text-gray-900">{{ $systemHealth['user_activity']['active_today'] }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Active This Week</span>
                        <span class="text-lg font-bold text-gray-900">{{ $systemHealth['user_activity']['active_this_week'] }}</span>
                    </div>
                    @endif
                    
                    @if(isset($systemHealth['system_load']))
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Memory Usage</span>
                        <span class="text-sm text-gray-600">{{ $systemHealth['system_load']['memory_usage'] }}</span>
                    </div>
                    @endif
                </div>
                @endif
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200 p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h3 class="text-lg font-bold text-gray-900">Quick Actions</h3>
                    <p class="text-sm text-gray-600">System administration tools</p>
                </div>
                <div class="p-3 bg-gradient-to-br from-violet-500 to-violet-600 rounded-xl shadow-lg">
                    <i class="fas fa-tools text-white text-xl"></i>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="{{ route('system-admin.user-management') }}" class="flex items-center gap-3 p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200 hover:from-blue-100 hover:to-blue-200 transition-all duration-200">
                    <div class="p-2 bg-blue-500 rounded-lg">
                        <i class="fas fa-users-cog text-white"></i>
                    </div>
                    <div>
                        <p class="font-medium text-blue-900">User Management</p>
                        <p class="text-xs text-blue-700">Manage all system users</p>
                    </div>
                </a>
                
                <button onclick="openCreateUserModal()" class="flex items-center gap-3 p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-lg border border-green-200 hover:from-green-100 hover:to-green-200 transition-all duration-200">
                    <div class="p-2 bg-green-500 rounded-lg">
                        <i class="fas fa-user-plus text-white"></i>
                    </div>
                    <div>
                        <p class="font-medium text-green-900">Create User</p>
                        <p class="text-xs text-green-700">Add new system user</p>
                    </div>
                </button>
                
                <a href="{{ route('system-admin.system-logs') }}" class="flex items-center gap-3 p-4 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg border border-purple-200 hover:from-purple-100 hover:to-purple-200 transition-all duration-200">
                    <div class="p-2 bg-purple-500 rounded-lg">
                        <i class="fas fa-file-alt text-white"></i>
                    </div>
                    <div>
                        <p class="font-medium text-purple-900">System Logs</p>
                        <p class="text-xs text-purple-700">View system activity</p>
                    </div>
                </a>
            </div>
        </div>

        <!-- Recent Activity -->
        @if(isset($recentUsers) && $recentUsers->count() > 0)
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200 p-6">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h3 class="text-lg font-bold text-gray-900">Recent User Registrations</h3>
                    <p class="text-sm text-gray-600">Latest system users</p>
                </div>
                <div class="p-3 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-xl shadow-lg">
                    <i class="fas fa-clock text-white text-xl"></i>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="border-b border-gray-200">
                            <th class="text-left py-3 px-4 font-medium text-gray-700">User</th>
                            <th class="text-left py-3 px-4 font-medium text-gray-700">Role</th>
                            <th class="text-left py-3 px-4 font-medium text-gray-700">Barangay</th>
                            <th class="text-left py-3 px-4 font-medium text-gray-700">Created</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($recentUsers as $user)
                        <tr class="border-b border-gray-100 hover:bg-gray-50">
                            <td class="py-3 px-4">
                                <div>
                                    <p class="font-medium text-gray-900">{{ $user->first_name }} {{ $user->last_name }}</p>
                                    <p class="text-sm text-gray-600">{{ $user->email }}</p>
                                </div>
                            </td>
                            <td class="py-3 px-4">
                                <span class="px-2 py-1 rounded-full text-xs font-medium
                                    @if($user->role === 'system_admin') bg-slate-100 text-slate-800
                                    @elseif($user->role === 'super_admin') bg-blue-100 text-blue-800
                                    @else bg-green-100 text-green-800 @endif">
                                    {{ $user->getRoleDisplayName() }}
                                </span>
                            </td>
                            <td class="py-3 px-4 text-sm text-gray-600">{{ $user->barangay }}</td>
                            <td class="py-3 px-4 text-sm text-gray-600">{{ $user->created_at->format('M d, Y') }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
        @endif
    </div>
</div>

<!-- Create User Modal -->
<div id="createUserModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-white rounded-xl shadow-2xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div class="p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-bold text-gray-900">Create New User</h3>
                <button onclick="closeCreateUserModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="createUserForm" onsubmit="createUser(event)">
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                            <input type="text" name="first_name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                            <input type="text" name="last_name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Middle Name</label>
                        <input type="text" name="middle_name" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <input type="email" name="email" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                        <input type="password" name="password" required minlength="8" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Role</label>
                        <select name="role" required onchange="toggleLocationFields()" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Select Role</option>
                            <option value="system_admin">Technical Administrator</option>
                            <option value="super_admin">CDRRMC (City-Level)</option>
                            <option value="admin">BDRRMC (Barangay-Level)</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Position</label>
                        <input type="text" name="position" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div id="cityField" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-1">City</label>
                        <select name="city" onchange="loadBarangays()" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Select City</option>
                        </select>
                    </div>

                    <div id="barangayField" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Barangay</label>
                        <select name="barangay" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Select Barangay</option>
                        </select>
                    </div>
                </div>
                
                <div class="flex gap-3 mt-6">
                    <button type="button" onclick="closeCreateUserModal()" class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        Cancel
                    </button>
                    <button type="submit" class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        Create User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openCreateUserModal() {
    document.getElementById('createUserModal').classList.remove('hidden');
}

function closeCreateUserModal() {
    document.getElementById('createUserModal').classList.add('hidden');
    document.getElementById('createUserForm').reset();
    document.getElementById('cityField').classList.add('hidden');
    document.getElementById('barangayField').classList.add('hidden');
}

function toggleLocationFields() {
    const roleSelect = document.querySelector('select[name="role"]');
    const cityField = document.getElementById('cityField');
    const barangayField = document.getElementById('barangayField');
    const citySelect = document.querySelector('select[name="city"]');
    const barangaySelect = document.querySelector('select[name="barangay"]');

    if (roleSelect.value === 'super_admin' || roleSelect.value === 'admin') {
        // Show city field for both super_admin and admin
        cityField.classList.remove('hidden');
        citySelect.required = true;
        loadCities();

        if (roleSelect.value === 'admin') {
            // Show barangay field only for admin (BDRRMC)
            barangayField.classList.remove('hidden');
            barangaySelect.required = true;
        } else {
            // Hide barangay field for super_admin (CDRRMC)
            barangayField.classList.add('hidden');
            barangaySelect.required = false;
            barangaySelect.value = '';
        }
    } else {
        // Hide both fields for system_admin
        cityField.classList.add('hidden');
        barangayField.classList.add('hidden');
        citySelect.required = false;
        barangaySelect.required = false;
        citySelect.value = '';
        barangaySelect.value = '';
    }
}

async function createUser(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData.entries());

    // Handle conditional fields based on role
    if (data.role === 'system_admin') {
        delete data.city;
        delete data.barangay;
    } else if (data.role === 'super_admin') {
        delete data.barangay;
    }
    
    try {
        const response = await fetch('{{ route("system-admin.create-user") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('User created successfully!');
            closeCreateUserModal();
            location.reload();
        } else {
            alert('Error: ' + result.message);
        }
    } catch (error) {
        alert('An error occurred while creating the user.');
        console.error(error);
    }
}

async function loadCities() {
    try {
        const response = await fetch('{{ route("api.psgc.cities") }}', {
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            }
        });

        const result = await response.json();
        const citySelect = document.querySelector('select[name="city"]');

        // Clear existing options except the first one
        citySelect.innerHTML = '<option value="">Select City</option>';

        if (result.success && result.data) {
            result.data.forEach(city => {
                const option = document.createElement('option');
                option.value = city.code;
                option.textContent = city.name;
                citySelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Failed to load cities:', error);
    }
}

// City-Barangay filter interaction
document.addEventListener('DOMContentLoaded', function() {
    const citySelect = document.getElementById('city');
    const barangaySelect = document.getElementById('barangay');

    if (citySelect && barangaySelect) {
        citySelect.addEventListener('change', function() {
            loadBarangays();
        });
    }
});

async function loadBarangays() {
    const citySelect = document.getElementById('city');
    const barangaySelect = document.getElementById('barangay');

    // Clear barangay options
    barangaySelect.innerHTML = '<option value="">All Barangays</option>';

    if (!citySelect.value) {
        return;
    }

    try {
        const response = await fetch(`{{ route('system-admin.get-barangays-by-city') }}?city=${encodeURIComponent(citySelect.value)}`, {
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Accept': 'application/json'
            }
        });

        const result = await response.json();

        if (result.barangays && Array.isArray(result.barangays)) {
            result.barangays.forEach(barangay => {
                const option = document.createElement('option');
                option.value = barangay;
                option.textContent = barangay;
                barangaySelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Failed to load barangays:', error);
    }
}

// Initialize User Statistics Chart
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('userStatsChart');
    if (ctx) {
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['Total Centers', 'Total Alerts', 'CDRRMC Users', 'BDRRMC Users'],
                datasets: [{
                    label: 'Count',
                    data: [
                        {{ $stats['total_evacuation_centers'] }},
                        {{ $stats['total_notifications'] }},
                        {{ $userStats['cdrrmc_users'] }},
                        {{ $userStats['bdrrmc_users'] }}
                    ],
                    backgroundColor: [
                        'rgba(34, 197, 94, 0.8)',  // Green for centers
                        'rgba(239, 68, 68, 0.8)',   // Red for alerts
                        'rgba(59, 130, 246, 0.8)',  // Blue for CDRRMC
                        'rgba(34, 197, 94, 0.8)'    // Green for BDRRMC
                    ],
                    borderColor: [
                        '#22c55e',
                        '#ef4444',
                        '#3b82f6',
                        '#22c55e'
                    ],
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#374151',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: false,
                        callbacks: {
                            title: function(context) {
                                return context[0].label;
                            },
                            label: function(context) {
                                return 'Count: ' + context.parsed.y.toLocaleString();
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: function(context) {
                            const maxValue = Math.max(...context.chart.data.datasets[0].data);
                            return Math.max(maxValue + 5, 10); // Ensure minimum range of 10
                        },
                        grid: {
                            color: 'rgba(156, 163, 175, 0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            color: '#6b7280',
                            font: {
                                size: 12
                            },
                            callback: function(value) {
                                return Number.isInteger(value) ? value : '';
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6b7280',
                            font: {
                                size: 12,
                                weight: 'bold'
                            }
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }
});
</script>
@endsection
