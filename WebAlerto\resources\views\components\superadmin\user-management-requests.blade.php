@extends('layout.app')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-sky-100 py-8">
    <!-- Main Container -->
    <div class="max-w-[1920px] mx-auto px-6 sm:px-8 lg:px-12">
        <!-- Header Section -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6 mb-8">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-4">
                    <div class="p-3 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl shadow-lg">
                        <i class="fas fa-gavel text-white text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Chairman User Management Requests</h1>
                        <p class="text-gray-600 mt-1">Review and approve Chairman requests for user management</p>
                    </div>
                </div>
                <a href="{{ route('superadmin.dashboard') }}"
                   class="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white rounded-lg font-medium shadow-lg transition-all duration-200">
                    <i class="fas fa-arrow-left"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="bg-white/90 backdrop-blur-sm rounded-xl shadow-xl border border-sky-200 p-6 mb-6">
            <form action="{{ route('superadmin.user-management-requests') }}" method="GET" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="status" class="block text-sm font-semibold text-gray-700 mb-2">Filter by Status</label>
                    <select name="status" id="status" onchange="this.form.submit()"
                            class="w-full rounded-lg border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 bg-white/90 py-3 px-4 text-base text-gray-700 font-medium transition-all duration-200">
                        <option value="pending" {{ $status === 'pending' ? 'selected' : '' }}>Pending Requests</option>
                        <option value="all" {{ $status === 'all' ? 'selected' : '' }}>All Requests</option>
                        <option value="approved" {{ $status === 'approved' ? 'selected' : '' }}>Approved</option>
                        <option value="rejected" {{ $status === 'rejected' ? 'selected' : '' }}>Rejected</option>
                    </select>
                </div>
                <div>
                    <label for="barangay" class="block text-sm font-semibold text-gray-700 mb-2">Filter by Barangay</label>
                    <select name="barangay" id="barangay" onchange="this.form.submit()"
                            class="w-full rounded-lg border-2 border-sky-200 shadow-sm focus:border-sky-500 focus:ring-sky-500 bg-white/90 py-3 px-4 text-base text-gray-700 font-medium transition-all duration-200">
                        <option value="">All Barangays</option>
                        @foreach($barangays as $brgy)
                            <option value="{{ $brgy }}" {{ $barangay === $brgy ? 'selected' : '' }}>{{ $brgy }}</option>
                        @endforeach
                    </select>
                </div>
            </form>
        </div>

        <!-- Requests Table -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 overflow-hidden">
            <!-- Section Header -->
            <div class="bg-gradient-to-r from-purple-500 to-indigo-600 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <i class="fas fa-clipboard-check text-white text-xl"></i>
                        <h2 class="text-xl font-bold text-white">Management Requests</h2>
                    </div>
                    <div class="text-white text-sm">
                        <span class="font-medium">Total:</span> {{ $requests->total() }} requests
                    </div>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50/80">
                        <tr>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Chairman
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Target User
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Action
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Reason
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Submitted
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white/60 divide-y divide-gray-200">
                        @forelse($requests as $request)
                        <tr class="hover:bg-purple-50/80 transition-all duration-200">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-gradient-to-br from-yellow-400 to-orange-500 flex items-center justify-center">
                                            <i class="fas fa-crown text-white text-sm"></i>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-semibold text-gray-900">
                                            {{ $request->requester->first_name }} {{ $request->requester->last_name }}
                                        </div>
                                        <div class="text-sm text-gray-600">{{ $request->requester->barangay }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        @if($request->isRegistrationRequest())
                                            <div class="h-10 w-10 rounded-full bg-gradient-to-br from-green-400 to-emerald-500 flex items-center justify-center">
                                                <span class="text-white font-bold text-sm">
                                                    {{ strtoupper(substr($request->requested_first_name, 0, 1) . substr($request->requested_last_name, 0, 1)) }}
                                                </span>
                                            </div>
                                        @else
                                            <div class="h-10 w-10 rounded-full bg-gradient-to-br from-sky-400 to-blue-500 flex items-center justify-center">
                                                <span class="text-white font-bold text-sm">
                                                    {{ strtoupper(substr($request->targetUser->first_name, 0, 1) . substr($request->targetUser->last_name, 0, 1)) }}
                                                </span>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="ml-4">
                                        @if($request->isRegistrationRequest())
                                            <div class="text-sm font-semibold text-gray-900">
                                                {{ $request->requested_first_name }} {{ $request->requested_last_name }}
                                                <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 ml-2">
                                                    <i class="fas fa-user-plus mr-1"></i>New User
                                                </span>
                                            </div>
                                            <div class="text-sm text-gray-600">{{ $request->requested_email }}</div>
                                            <div class="text-xs text-gray-500">Position: {{ $request->requested_position }}</div>
                                            <div class="text-xs text-gray-500">Barangay: {{ $request->requested_barangay }}</div>
                                        @else
                                            <div class="text-sm font-semibold text-gray-900">
                                                {{ $request->targetUser->first_name }} {{ $request->targetUser->last_name }}
                                            </div>
                                            <div class="text-sm text-gray-600">{{ $request->targetUser->email }}</div>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                @if($request->action_type === 'registration')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-user-plus mr-1"></i>
                                        Registration
                                    </span>
                                @elseif($request->action_type === 'deactivate')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-pause mr-1"></i>
                                        Deactivate
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-trash mr-1"></i>
                                        Delete
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900 max-w-xs">
                                    <button onclick="showReason('{{ addslashes($request->reason) }}')" 
                                            class="text-left hover:text-blue-600 transition-colors">
                                        {{ Str::limit($request->reason, 50) }}
                                        @if(strlen($request->reason) > 50)
                                            <i class="fas fa-expand-alt ml-1 text-xs"></i>
                                        @endif
                                    </button>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    @if($request->status === 'pending') bg-blue-100 text-blue-800
                                    @elseif($request->status === 'approved') bg-green-100 text-green-800
                                    @else bg-red-100 text-red-800 @endif">
                                    <i class="fas 
                                        @if($request->status === 'pending') fa-clock
                                        @elseif($request->status === 'approved') fa-check
                                        @else fa-times @endif mr-1"></i>
                                    {{ ucfirst($request->status) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-600">
                                {{ $request->created_at->format('M d, Y H:i') }}
                            </td>
                            <td class="px-6 py-4">
                                @if($request->status === 'pending')
                                    <div class="flex gap-2">
                                        @php
                                            $userName = $request->isRegistrationRequest()
                                                ? $request->requested_first_name . ' ' . $request->requested_last_name
                                                : $request->targetUser->first_name . ' ' . $request->targetUser->last_name;
                                        @endphp
                                        <button onclick="reviewRequest({{ $request->id }}, 'approve', '{{ $userName }}', '{{ $request->action_type }}')"
                                                class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded-lg text-xs font-medium transition-colors duration-200">
                                            <i class="fas fa-check mr-1"></i>
                                            Approve
                                        </button>
                                        <button onclick="reviewRequest({{ $request->id }}, 'reject', '{{ $userName }}', '{{ $request->action_type }}')"
                                                class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-lg text-xs font-medium transition-colors duration-200">
                                            <i class="fas fa-times mr-1"></i>
                                            Reject
                                        </button>
                                    </div>
                                @else
                                    <div class="text-xs text-gray-500">
                                        @if($request->reviewer)
                                            Reviewed by {{ $request->reviewer->first_name }}
                                        @endif
                                        @if($request->reviewed_at)
                                            <br>{{ $request->reviewed_at->format('M d, Y H:i') }}
                                        @endif
                                    </div>
                                @endif
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center">
                                    <div class="text-gray-400 text-4xl mb-3">📋</div>
                                    <p class="text-gray-500 font-medium">No management requests found</p>
                                    <p class="text-gray-400 text-sm mt-1">Chairman requests will appear here for review</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($requests->hasPages())
            <div class="bg-gray-50/80 px-6 py-4 border-t border-gray-200">
                {{ $requests->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Review Modal -->
<div id="reviewModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center p-4">
    <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full">
        <div class="bg-gradient-to-r from-purple-500 to-indigo-600 px-6 py-4">
            <h3 id="reviewModalTitle" class="text-xl font-bold text-white">Review Request</h3>
        </div>
        <div class="p-6">
            <form id="reviewForm" method="POST">
                @csrf
                @method('PATCH')
                <input type="hidden" id="reviewAction" name="action">
                
                <div class="mb-4">
                    <p id="reviewText" class="text-gray-700"></p>
                </div>
                
                <div class="mb-6">
                    <label for="reviewNotes" class="block text-sm font-medium text-gray-700 mb-2">Review Notes (Optional)</label>
                    <textarea id="reviewNotes" name="review_notes" rows="3"
                              class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500"
                              placeholder="Add any notes about your decision..."></textarea>
                </div>
                
                <div class="flex gap-3">
                    <button type="button" onclick="closeReviewModal()" class="flex-1 px-4 py-2 border border-gray-300 rounded-lg">Cancel</button>
                    <button type="submit" id="reviewSubmitBtn" class="flex-1 px-4 py-2 rounded-lg text-white font-medium">Submit</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reason Modal -->
<div id="reasonModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center p-4">
    <div class="bg-white rounded-2xl shadow-2xl max-w-lg w-full">
        <div class="bg-gradient-to-r from-gray-500 to-gray-600 px-6 py-4">
            <div class="flex items-center justify-between">
                <h3 class="text-xl font-bold text-white">Request Reason</h3>
                <button onclick="closeReasonModal()" class="text-white/80 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="p-6">
            <p id="reasonText" class="text-gray-700 whitespace-pre-wrap"></p>
        </div>
    </div>
</div>

<script>
function reviewRequest(requestId, action, userName, actionType) {
    const modal = document.getElementById('reviewModal');
    const form = document.getElementById('reviewForm');
    const title = document.getElementById('reviewModalTitle');
    const text = document.getElementById('reviewText');
    const actionInput = document.getElementById('reviewAction');
    const submitBtn = document.getElementById('reviewSubmitBtn');

    form.action = `/superadmin/user-management-requests/${requestId}/review`;
    actionInput.value = action;

    if (action === 'approve') {
        title.textContent = 'Approve Request';

        if (actionType === 'registration') {
            text.textContent = `Are you sure you want to approve the registration request for "${userName}"? An invitation email will be sent to set up their account.`;
        } else {
            text.textContent = `Are you sure you want to approve the request to ${actionType} user "${userName}"? This action will be executed immediately.`;
        }

        submitBtn.className = 'flex-1 px-4 py-2 bg-green-500 hover:bg-green-600 rounded-lg text-white font-medium';
        submitBtn.textContent = 'Approve & Execute';
    } else {
        title.textContent = 'Reject Request';

        if (actionType === 'registration') {
            text.textContent = `Are you sure you want to reject the registration request for "${userName}"?`;
        } else {
            text.textContent = `Are you sure you want to reject the request to ${actionType} user "${userName}"?`;
        }

        submitBtn.className = 'flex-1 px-4 py-2 bg-red-500 hover:bg-red-600 rounded-lg text-white font-medium';
        submitBtn.textContent = 'Reject Request';
    }

    modal.classList.remove('hidden');
    modal.classList.add('flex');
}

function closeReviewModal() {
    document.getElementById('reviewModal').classList.add('hidden');
    document.getElementById('reviewModal').classList.remove('flex');
    document.getElementById('reviewForm').reset();
}

function showReason(reason) {
    document.getElementById('reasonText').textContent = reason;
    document.getElementById('reasonModal').classList.remove('hidden');
    document.getElementById('reasonModal').classList.add('flex');
}

function closeReasonModal() {
    document.getElementById('reasonModal').classList.add('hidden');
    document.getElementById('reasonModal').classList.remove('flex');
}
</script>
@endsection
