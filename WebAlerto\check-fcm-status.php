<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\DeviceToken;
use App\Models\Notification;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== WebAlerto FCM Status Check ===\n\n";

// 1. Check Device Tokens
echo "1. Device Tokens Status:\n";
$totalTokens = DeviceToken::count();
$activeTokens = DeviceToken::where('is_active', true)->count();
$inactiveTokens = DeviceToken::where('is_active', false)->count();

echo "   📱 Total tokens: $totalTokens\n";
echo "   ✅ Active tokens: $activeTokens\n";
echo "   ❌ Inactive tokens: $inactiveTokens\n";

if ($activeTokens > 0) {
    echo "\n   Active tokens breakdown:\n";
    $tokensByType = DeviceToken::where('is_active', true)
        ->selectRaw('device_type, count(*) as count')
        ->groupBy('device_type')
        ->get();
    
    foreach ($tokensByType as $type) {
        echo "   - {$type->device_type}: {$type->count}\n";
    }
    
    echo "\n   Recent active tokens:\n";
    $recentTokens = DeviceToken::where('is_active', true)
        ->orderBy('created_at', 'desc')
        ->limit(5)
        ->get();
    
    foreach ($recentTokens as $token) {
        $userInfo = $token->user_id ? "User {$token->user_id}" : "Anonymous";
        echo "   - ID: {$token->id}, Type: {$token->device_type}, {$userInfo}, Created: {$token->created_at}\n";
    }
}

// 2. Check Notifications
echo "\n2. Notifications Status:\n";
$totalNotifications = Notification::count();
$sentNotifications = Notification::where('sent', true)->count();
$pendingNotifications = Notification::where('sent', false)->count();

echo "   📧 Total notifications: $totalNotifications\n";
echo "   ✅ Sent notifications: $sentNotifications\n";
echo "   ⏳ Pending notifications: $pendingNotifications\n";

if ($totalNotifications > 0) {
    echo "\n   Recent notifications:\n";
    $recentNotifications = Notification::orderBy('created_at', 'desc')
        ->limit(5)
        ->get();
    
    foreach ($recentNotifications as $notification) {
        $status = $notification->sent ? "✅ Sent" : "⏳ Pending";
        echo "   - ID: {$notification->id}, \"{$notification->title}\", {$status}, Created: {$notification->created_at}\n";
    }
}

// 3. Check Firebase Configuration
echo "\n3. Firebase Configuration:\n";
$serviceAccountPath = storage_path('firebase-service-account.json');
if (file_exists($serviceAccountPath)) {
    $serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);
    echo "   ✅ Service account file exists\n";
    echo "   📋 Project ID: " . $serviceAccount['project_id'] . "\n";
    echo "   📧 Client email: " . $serviceAccount['client_email'] . "\n";
} else {
    echo "   ❌ Service account file not found at: $serviceAccountPath\n";
}

// 4. Check SSL Certificate
echo "\n4. SSL Certificate Status:\n";
$caCertPath = storage_path('certs/cacert.pem');
if (file_exists($caCertPath)) {
    $certSize = filesize($caCertPath);
    $certModified = date('Y-m-d H:i:s', filemtime($caCertPath));
    echo "   ✅ CA certificate exists\n";
    echo "   📁 Size: " . number_format($certSize) . " bytes\n";
    echo "   📅 Modified: $certModified\n";
} else {
    echo "   ❌ CA certificate not found at: $caCertPath\n";
}

// 5. Check Environment Configuration
echo "\n5. Environment Configuration:\n";
$firebaseConfig = [
    'FIREBASE_PROJECT_ID' => env('FIREBASE_PROJECT_ID'),
    'FIREBASE_MESSAGING_SENDER_ID' => env('FIREBASE_MESSAGING_SENDER_ID'),
    'FIREBASE_APP_ID' => env('FIREBASE_APP_ID'),
    'FIREBASE_DATABASE_URL' => env('FIREBASE_DATABASE_URL'),
    'FIREBASE_CREDENTIALS' => env('FIREBASE_CREDENTIALS')
];

foreach ($firebaseConfig as $key => $value) {
    $status = $value ? "✅" : "❌";
    $displayValue = $value ? (strlen($value) > 50 ? substr($value, 0, 50) . "..." : $value) : "Not set";
    echo "   $status $key: $displayValue\n";
}

// 6. Recommendations
echo "\n6. Recommendations:\n";
$recommendations = [];

if ($activeTokens === 0) {
    $recommendations[] = "📱 No active device tokens found. Ensure mobile app is registering FCM tokens.";
}

if (!file_exists($serviceAccountPath)) {
    $recommendations[] = "🔑 Firebase service account file is missing. Add it to storage/ directory.";
}

if (!file_exists($caCertPath)) {
    $recommendations[] = "🔒 SSL certificate is missing. Run the FCM service to auto-download it.";
}

if ($pendingNotifications > 0) {
    $recommendations[] = "📧 There are pending notifications. Check FCM service functionality.";
}

if (empty($recommendations)) {
    echo "   🎉 Everything looks good! FCM should be working properly.\n";
} else {
    foreach ($recommendations as $recommendation) {
        echo "   $recommendation\n";
    }
}

echo "\n=== Status Check Complete ===\n";
