<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\MobileUser;
use App\Models\Barangay;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();
        $selectedBarangay = $request->input('barangay');
        $searchQuery = $request->input('search');

        // Get list of barangays based on user role using PSGC-connected BarangayService
        $barangays = [];
        if ($user->hasRole('super_admin')) {
            $barangayService = app(\App\Services\BarangayService::class);
            $barangays = $barangayService->getActiveBarangays();
        }

        if ($user->hasRole('super_admin')) {
            // CDRRMC sees fellow CDRRMC users in their city
            $query = User::where('city', $user->city)
                         ->where('role', 'super_admin'); // Only CDRRMC users

            // Apply barangay filter for CDRRMC
            if ($selectedBarangay) {
                $query->where('barangay', $selectedBarangay);
            }

            // Apply search filter for CDRRMC users
            if ($searchQuery) {
                $query->where(function($q) use ($searchQuery) {
                    $q->where('first_name', 'like', "%{$searchQuery}%")
                      ->orWhere('last_name', 'like', "%{$searchQuery}%")
                      ->orWhere('email', 'like', "%{$searchQuery}%")
                      ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%{$searchQuery}%"]);
                });
            }

            $users = $query->paginate(10);
            $userType = 'cdrrmc';
        } else {
            // Barangay users see other barangay users from users table (excluding mobile_user role)
            $query = User::where('barangay', $user->barangay)
                         ->whereNotIn('role', ['mobile_user']); // Exclude mobile users

            // Apply search filter for barangay users
            if ($searchQuery) {
                $query->where(function($q) use ($searchQuery) {
                    $q->where('first_name', 'like', "%{$searchQuery}%")
                      ->orWhere('last_name', 'like', "%{$searchQuery}%")
                      ->orWhere('email', 'like', "%{$searchQuery}%")
                      ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%{$searchQuery}%"]);
                });
            }

            $users = $query->paginate(10);
            $userType = 'barangay';
        }

        return view('components.user_management.user-management', compact('users', 'barangays', 'selectedBarangay', 'searchQuery', 'userType'));
    }

    
public function deactivate($id)
{
    $user = Auth::user();

    // CDRRMC can deactivate fellow CDRRMC users in their city
    if ($user->hasRole('super_admin')) {
        $targetUser = User::where('city', $user->city)
                         ->where('role', 'super_admin')
                         ->findOrFail($id);
    } else {
        // BDRRMC can only deactivate users in their barangay
        $targetUser = User::where('barangay', $user->barangay)
                         ->whereNotIn('role', ['mobile_user'])
                         ->findOrFail($id);
    }

    // Check if user has authority to deactivate
    if (!$user->canManageUser($targetUser)) {
        return redirect()->back()->with('error', 'You are not authorized to deactivate users.');
    }

    $targetUser->status = 'Inactive';
    $targetUser->save();

    // Redirect back to user management page
    return redirect()->route('components.user-management')->with('success', 'User deactivated successfully.');
}

    public function search(Request $request)
    {
        $user = Auth::user();
        $query = strtoupper($request->input('query'));

        if ($user->hasRole('super_admin')) {
            // CDRRMC searches fellow CDRRMC users in their city
            $users = User::where('city', $user->city)
                        ->where('role', 'super_admin')
                        ->where(function($q) use ($query) {
                            $q->where('first_name', 'like', '%' . $query . '%')
                              ->orWhere('last_name', 'like', '%' . $query . '%')
                              ->orWhere('email', 'like', '%' . $query . '%')
                              ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ['%' . $query . '%']);
                        })->get();
        } else {
            // Barangay users search other barangay users from their barangay
            $users = User::where('barangay', $user->barangay)
                        ->whereNotIn('role', ['mobile_user'])
                        ->where(function($q) use ($query) {
                            $q->where('first_name', 'like', '%' . $query . '%')
                              ->orWhere('last_name', 'like', '%' . $query . '%')
                              ->orWhere('email', 'like', '%' . $query . '%')
                              ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ['%' . $query . '%']);
                        })
                        ->get();
        }

        return response()->json(['users' => $users]);
    }

    
public function destroy($id)
{
    if (!auth()->check()) {
        return redirect()->route('login');
    }

    $user = Auth::user();

    if ($user->hasRole('super_admin')) {
        // CDRRMC can delete fellow CDRRMC users in their city
        $targetUser = User::where('city', $user->city)
                         ->where('role', 'super_admin')
                         ->findOrFail($id);
        $targetUser->delete();
        return redirect()->route('components.user-management')
            ->with('success', 'CDRRMC user deleted successfully!');
    } else {
        // Barangay users can only delete other barangay users from their barangay
        $targetUser = User::where('barangay', $user->barangay)
                         ->whereNotIn('role', ['mobile_user'])
                         ->findOrFail($id);

        // Additional authorization check
        if (!$user->canManageUser($targetUser)) {
            return redirect()->route('components.user-management')
                ->with('error', 'You are not authorized to delete this user.');
        }

        $targetUser->delete();
        return redirect()->route('components.user-management')
            ->with('success', 'Barangay user deleted successfully!');
    }
}
    public function edit($id)
    {
        $currentUser = auth()->user();

        if ($currentUser->hasRole('super_admin')) {
            // CDRRMC edits fellow CDRRMC users in their city
            $userToEdit = User::where('city', $currentUser->city)
                             ->where('role', 'super_admin')
                             ->findOrFail($id);

            // Get barangays for the city using BarangayService
            $barangayService = app(\App\Services\BarangayService::class);
            $barangays = $barangayService->getAccessibleBarangays($currentUser);

            return view('components.user_management.edit-user', ['user' => $userToEdit, 'barangays' => $barangays]);
        } else {
            // Barangay users edit other barangay users
            $userToEdit = User::where('barangay', $currentUser->barangay)
                             ->whereNotIn('role', ['mobile_user'])
                             ->findOrFail($id);

            // Additional authorization check
            if ($currentUser->barangay != $userToEdit->barangay) {
                abort(403, 'Unauthorized access.');
            }

            return view('components.user_management.edit-user', ['user' => $userToEdit]);
        }
    }

    public function update(Request $request, $id)
{
    $currentUser = auth()->user();

    if ($currentUser->hasRole('super_admin')) {
        // CDRRMC updates fellow CDRRMC users in their city
        $user = User::where('city', $currentUser->city)
                   ->where('role', 'super_admin')
                   ->findOrFail($id);

        $rules = [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'middle_name' => 'nullable|string|max:255',
            'email' => 'required|email|unique:users,email,' . $id,
            'position' => 'required|string|max:255',
            'barangay' => ['nullable', 'string', 'max:255', new \App\Rules\ValidBarangay()],
        ];

        $request->validate($rules);

        $user->update($request->only([
            'first_name', 'last_name', 'middle_name', 'email', 'position', 'barangay'
        ]));

        return redirect()->route('components.user-management')
            ->with('success', 'CDRRMC user updated successfully!');
    } else {
        // Barangay users update other barangay users
        $user = User::where('barangay', $currentUser->barangay)
                   ->whereNotIn('role', ['mobile_user'])
                   ->findOrFail($id);

        $rules = [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $id,
            'position' => 'nullable|string|max:255',
        ];

        $request->validate($rules);

        $user->update($request->only([
            'first_name', 'last_name', 'email', 'position'
        ]));

        return redirect()->route('components.user-management')
            ->with('success', 'Barangay user updated successfully!');
    }
}

    /**
     * Show the form for editing the authenticated user's profile
     */
    public function editProfile()
    {
        $user = auth()->user();
        return view('components.profile.edit-profile', compact('user'));
    }

    /**
     * Update the authenticated user's profile
     */
    public function updateProfile(Request $request)
    {
        $user = auth()->user();

        // Validation rules
        $rules = [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'middle_name' => 'nullable|string|max:255',
            'title' => 'nullable|string|max:50',
            'suffix' => 'nullable|string|max:50',
            'position' => 'nullable|string|max:255',
        ];

        $request->validate($rules);

        // Update user data
        $user->update($request->only([
            'first_name', 'last_name', 'email', 'middle_name', 'title', 'suffix', 'position'
        ]));

        return redirect()->route('profile.edit')
            ->with('success', 'Profile updated successfully!');
    }

    public function view($id)
    {
        $currentUser = auth()->user();

        if ($currentUser->hasRole('super_admin')) {
            // CDRRMC views fellow CDRRMC users in their city
            $user = User::where('city', $currentUser->city)
                       ->where('role', 'super_admin')
                       ->findOrFail($id);
            return view('components.user_management.view-user', compact('user'));
        } else {
            // BDRRMC views users in their barangay
            $user = User::where('barangay', $currentUser->barangay)
                       ->whereNotIn('role', ['mobile_user'])
                       ->findOrFail($id);

            if ($currentUser->barangay != $user->barangay) {
                abort(403, 'Unauthorized access.');
            }

            return view('components.user_management.view-user', compact('user'));
        }
    }
}