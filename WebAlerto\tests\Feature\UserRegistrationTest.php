<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Services\PSGCService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use App\Mail\AdminRegistrationMail;

class UserRegistrationTest extends TestCase
{
    use RefreshDatabase;

    protected $systemAdmin;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a system administrator for testing
        $this->systemAdmin = User::create([
            'first_name' => 'System',
            'last_name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'system_admin',
            'position' => 'Technical Administrator',
            'status' => 'Active',
        ]);
    }

    /**
     * Test CDRRMC (Super Admin) registration with city only
     */
    public function test_cdrrmc_registration_with_city_only()
    {
        Mail::fake();

        $response = $this->actingAs($this->systemAdmin)
            ->postJson('/system-admin/create-user', [
                'first_name' => '<PERSON>',
                'last_name' => 'Doe',
                'middle_name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'role' => 'super_admin',
                'position' => 'CDRRMC Director',
                'city' => 'Cebu City',
                // No barangay required for super_admin
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify user was created correctly
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertEquals('super_admin', $user->role);
        $this->assertEquals('Cebu City', $user->city);
        $this->assertNull($user->barangay);
        $this->assertEquals('John Smith Doe', $user->first_name . ' ' . $user->middle_name . ' ' . $user->last_name);

        // Verify email was sent
        Mail::assertSent(AdminRegistrationMail::class);
    }

    /**
     * Test BDRRMC (Admin) registration with city and barangay
     */
    public function test_bdrrmc_registration_with_city_and_barangay()
    {
        Mail::fake();

        $response = $this->actingAs($this->systemAdmin)
            ->postJson('/system-admin/create-user', [
                'first_name' => 'Jane',
                'last_name' => 'Smith',
                'middle_name' => 'Cruz',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'role' => 'admin',
                'position' => 'Barangay Chairman',
                'city' => 'Cebu City',
                'barangay' => 'Lahug',
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify user was created correctly
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertEquals('admin', $user->role);
        $this->assertEquals('Cebu City', $user->city);
        $this->assertEquals('Lahug', $user->barangay);
        $this->assertEquals('Jane Cruz Smith', $user->first_name . ' ' . $user->middle_name . ' ' . $user->last_name);

        // Verify email was sent
        Mail::assertSent(AdminRegistrationMail::class);
    }

    /**
     * Test Technical Administrator registration without location fields
     */
    public function test_technical_admin_registration_without_location()
    {
        Mail::fake();

        $response = $this->actingAs($this->systemAdmin)
            ->postJson('/system-admin/create-user', [
                'first_name' => 'Tech',
                'last_name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'role' => 'system_admin',
                'position' => 'System Administrator',
                // No city or barangay for system_admin
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify user was created correctly
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertEquals('system_admin', $user->role);
        $this->assertNull($user->city);
        $this->assertNull($user->barangay);

        // Verify email was sent
        Mail::assertSent(AdminRegistrationMail::class);
    }

    /**
     * Test validation: Admin role requires barangay
     */
    public function test_admin_role_requires_barangay()
    {
        $response = $this->actingAs($this->systemAdmin)
            ->postJson('/system-admin/create-user', [
                'first_name' => 'Test',
                'last_name' => 'User',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'role' => 'admin',
                'position' => 'Officer',
                'city' => 'Cebu City',
                // Missing barangay for admin role
            ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['barangay']);
    }

    /**
     * Test validation: Super admin doesn't require barangay
     */
    public function test_super_admin_doesnt_require_barangay()
    {
        Mail::fake();

        $response = $this->actingAs($this->systemAdmin)
            ->postJson('/system-admin/create-user', [
                'first_name' => 'Super',
                'last_name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'role' => 'super_admin',
                'position' => 'Director',
                'city' => 'Cebu City',
                // No barangay - should be valid
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
    }

    /**
     * Test PSGC API endpoints
     */
    public function test_psgc_api_endpoints()
    {
        // Test cities endpoint
        $response = $this->actingAs($this->systemAdmin)
            ->getJson('/api/psgc/cities');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                '*' => [
                    'name',
                    'code',
                    'description',
                    'province',
                    'status'
                ]
            ]
        ]);

        // Test barangays endpoint with Cebu City code
        $response = $this->actingAs($this->systemAdmin)
            ->getJson('/api/psgc/barangays/072217000');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data'
        ]);
    }

    /**
     * Test role display names in email templates
     */
    public function test_role_display_names()
    {
        $systemAdmin = User::factory()->create(['role' => 'system_admin']);
        $superAdmin = User::factory()->create(['role' => 'super_admin']);
        $admin = User::factory()->create(['role' => 'admin']);

        $this->assertEquals('Technical Administrator', $systemAdmin->getRoleDisplayName());
        $this->assertEquals('CDRRMC (City-Level)', $superAdmin->getRoleDisplayName());
        $this->assertEquals('BDRRMC (Barangay-Level)', $admin->getRoleDisplayName());
    }

    /**
     * Test email template data
     */
    public function test_email_template_data()
    {
        $user = User::create([
            'first_name' => 'John',
            'middle_name' => 'Cruz',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'position' => 'Chairman',
            'city' => 'Cebu City',
            'barangay' => 'Lahug',
            'status' => 'Active',
        ]);

        $mail = new AdminRegistrationMail($user, 'temp123');
        $content = $mail->content();

        $this->assertEquals('emails.admin-registration', $content->html);
        $this->assertEquals('emails.admin-registration-text', $content->text);
        $this->assertArrayHasKey('user', $content->with);
        $this->assertArrayHasKey('temporaryPassword', $content->with);
        $this->assertArrayHasKey('loginUrl', $content->with);
    }
}
