<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Barangay;
use App\Services\PSGCService;
use Illuminate\Support\Facades\DB;

class RefreshBarangaysFromPSGC extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'barangays:refresh-from-psgc {--force : Force refresh without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Refresh barangay data from PSGC API with latest official data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🌐 PSGC Barangay Data Refresh Tool');
        $this->info('=====================================');

        // Check if force flag is used
        if (!$this->option('force')) {
            if (!$this->confirm('This will replace all existing barangay data with fresh data from PSGC API. Continue?')) {
                $this->info('Operation cancelled.');
                return 0;
            }
        }

        $this->info('🔄 Fetching latest barangay data from PSGC API...');

        // Initialize PSGC service
        $psgcService = new PSGCService();

        // Clear cache to ensure fresh data
        $psgcService->clearCache();

        // Fetch barangays from PSGC API
        $barangays = $psgcService->getCebuCityBarangays();

        if (empty($barangays)) {
            $this->error('❌ Failed to fetch barangays from PSGC API');
            return 1;
        }

        $this->info("📍 Found " . count($barangays) . " barangays for Cebu City");

        // Show progress bar
        $bar = $this->output->createProgressBar(count($barangays));
        $bar->start();

        try {
            // Disable foreign key checks
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');

            // Truncate the table
            Barangay::truncate();

            // Re-enable foreign key checks
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');

            // Insert new data
            $successCount = 0;
            foreach ($barangays as $barangay) {
                try {
                    Barangay::create(array_merge($barangay, [
                        'created_at' => now(),
                        'updated_at' => now()
                    ]));
                    $successCount++;
                } catch (\Exception $e) {
                    $this->error("\nFailed to create barangay: {$barangay['name']} - " . $e->getMessage());
                }
                $bar->advance();
            }

            $bar->finish();
            $this->newLine(2);

            $this->info("✅ Successfully refreshed {$successCount} barangays from PSGC API");

            if ($successCount < count($barangays)) {
                $this->warn("⚠️  Some barangays failed to update. Check logs for details.");
            }

            return 0;

        } catch (\Exception $e) {
            $this->error("❌ Failed to refresh barangay data: " . $e->getMessage());
            return 1;
        }
    }
}
