{"version": 3, "sources": ["../../../../../../node_modules/@capacitor/geolocation/dist/esm/index.js"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\nimport { exposeSynapse } from '@capacitor/synapse';\nconst Geolocation = registerPlugin('Geolocation', {\n  web: () => import('./web').then(m => new m.GeolocationWeb())\n});\nexposeSynapse();\nexport * from './definitions';\nexport { Geolocation };\n"], "mappings": ";;;;;;;;;AAEA,IAAM,cAAc,eAAe,eAAe;AAAA,EAChD,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,eAAe,CAAC;AAC7D,CAAC;AACD,EAAc;", "names": []}