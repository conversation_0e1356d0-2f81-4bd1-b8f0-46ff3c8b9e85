<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Popup-Based Routing Functionality</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.css" />
    <style>
        #map {
            height: 500px;
            width: 100%;
        }
        .controls {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .leaflet-popup-content {
            margin: 0;
            padding: 1rem;
            font-family: 'Arial', sans-serif;
        }
        .routing-controls button {
            transition: all 0.2s ease-in-out;
            width: 100%;
            margin: 2px 0;
        }
        .routing-controls button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        .routing-controls button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        .route-info-section {
            transition: all 0.3s ease-in-out;
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
    </style>
</head>
<body>
    <h1>Evacuation Center Popup-Based Routing Test</h1>
    
    <div class="controls">
        <p><strong>Instructions:</strong></p>
        <ol>
            <li>Click on any evacuation center marker (red dots)</li>
            <li>In the popup, click "Get My Location" to detect your position</li>
            <li>Then click "Show Route" to see the route to that center</li>
            <li>View travel time and distance in the popup</li>
        </ol>
    </div>
    
    <div id="map"></div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.js"></script>
    
    <script>
        // Test evacuation centers (sample data)
        const testCenters = [
            {
                id: 1,
                name: "Mabolo Elementary School",
                address: "Mabolo, Cebu City, Philippines",
                latitude: 10.3200,
                longitude: 123.9000,
                capacity: 500,
                status: "Active",
                disaster_type: ["Typhoon", "Flood"],
                contact: "123-456-7890"
            },
            {
                id: 2,
                name: "Mabolo National High School",
                address: "Mabolo, Cebu City, Philippines",
                latitude: 10.3180,
                longitude: 123.9020,
                capacity: 800,
                status: "Active",
                disaster_type: ["Fire", "Earthquake"],
                contact: "123-456-7891"
            },
            {
                id: 3,
                name: "Mabolo Barangay Hall",
                address: "Mabolo, Cebu City, Philippines",
                latitude: 10.3220,
                longitude: 123.8980,
                capacity: 300,
                status: "Active",
                disaster_type: ["Landslide"],
                contact: "123-456-7892"
            }
        ];

        let map;
        let userLocation = null;
        let userMarker = null;
        let centerRoutes = {};

        // Initialize map
        function initMap() {
            map = L.map('map').setView([10.3157, 123.8854], 13);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                maxZoom: 19,
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);

            // Add test evacuation centers
            testCenters.forEach(center => {
                const marker = L.circleMarker([center.latitude, center.longitude], {
                    radius: 8,
                    fillColor: '#ef4444',
                    color: '#fff',
                    weight: 2,
                    opacity: 1,
                    fillOpacity: 0.8
                }).addTo(map);

                const popupContent = `
                    <div>
                        <h3>${center.name}</h3>
                        <p><strong>Address:</strong> ${center.address}</p>
                        <p><strong>Capacity:</strong> ${center.capacity}</p>
                        <p><strong>Status:</strong> ${center.status}</p>
                        <p><strong>Disaster Type:</strong> ${center.disaster_type.join(', ')}</p>
                        <p><strong>Contact:</strong> ${center.contact}</p>
                        
                        <!-- Route Information Section -->
                        <div id="route-info-${center.id}" class="route-info-section" style="display: none;">
                            <div style="font-weight: bold; color: #333; margin-bottom: 8px;">Route Information:</div>
                            <div id="route-details-${center.id}">
                                <div style="font-size: 12px; color: #666; margin-bottom: 4px;">
                                    ⏱️ Travel time: <span id="travel-time-${center.id}">Calculating...</span>
                                </div>
                                <div style="font-size: 12px; color: #666;">
                                    🛣️ Distance: <span id="distance-${center.id}">Calculating...</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Routing Controls -->
                        <div class="routing-controls" style="margin-top: 12px; padding-top: 12px; border-top: 1px solid #e5e7eb;">
                            <div style="display: flex; flex-direction: column; gap: 4px;">
                                <button onclick="getUserLocationForCenter(${center.id})" id="get-location-${center.id}" class="btn" style="background: #10b981;">
                                    📍 Get My Location
                                </button>
                                <button onclick="showRouteToCenter(${center.id})" id="show-route-${center.id}" class="btn" style="background: #3b82f6;" disabled>
                                    🛣️ Show Route
                                </button>
                                <button onclick="clearRouteToCenter(${center.id})" id="clear-route-${center.id}" class="btn" style="background: #6b7280; display: none;">
                                    ❌ Clear Route
                                </button>
                            </div>
                        </div>
                        
                        <div style="margin-top: 12px; padding-top: 12px; border-top: 1px solid #e5e7eb;">
                            <!-- Admin delete button would go here if needed -->
                        </div>
                    </div>
                `;

                marker.bindPopup(popupContent);
            });
        }

        // Get user location for a specific center
        function getUserLocationForCenter(centerId) {
            const getLocationBtn = document.getElementById(`get-location-${centerId}`);
            const showRouteBtn = document.getElementById(`show-route-${centerId}`);
            
            if (!navigator.geolocation) {
                alert('Geolocation is not supported by this browser.');
                return;
            }

            getLocationBtn.disabled = true;
            getLocationBtn.textContent = '📍 Getting Location...';

            navigator.geolocation.getCurrentPosition(
                function(position) {
                    userLocation = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    };

                    // Update button states
                    getLocationBtn.disabled = false;
                    getLocationBtn.textContent = '✅ Location Found';
                    getLocationBtn.style.background = '#059669';
                    showRouteBtn.disabled = false;

                    // Add user marker to map if not already present
                    if (!userMarker) {
                        addUserMarker();
                    } else {
                        userMarker.setLatLng([userLocation.lat, userLocation.lng]);
                    }

                    // Pan to user location
                    map.setView([userLocation.lat, userLocation.lng], 15);

                    console.log('User location obtained for center:', centerId, userLocation);
                },
                function(error) {
                    console.error('Error getting location:', error);
                    alert('Unable to get your location. Please check your browser settings and try again.');
                    getLocationBtn.disabled = false;
                    getLocationBtn.textContent = '📍 Get My Location';
                    getLocationBtn.style.background = '#10b981';
                }
            );
        }

        // Add user marker
        function addUserMarker() {
            if (userMarker) {
                map.removeLayer(userMarker);
            }

            const userIcon = L.divIcon({
                className: 'user-location-marker',
                html: `<div style="width: 32px; height: 32px; background: #3b82f6; border-radius: 50%; border: 3px solid white; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; box-shadow: 0 2px 8px rgba(0,0,0,0.3);">📍</div>`,
                iconSize: [32, 32],
                iconAnchor: [16, 32]
            });

            userMarker = L.marker([userLocation.lat, userLocation.lng], { icon: userIcon })
                .addTo(map)
                .bindPopup('<div><strong>Your Location</strong><br>Click on evacuation centers to get routes!</div>');
        }

        // Show route to a specific center
        function showRouteToCenter(centerId) {
            if (!userLocation) {
                alert('Please get your location first by clicking "Get My Location"');
                return;
            }

            const center = testCenters.find(c => c.id === centerId);
            if (!center) {
                alert('Center not found');
                return;
            }

            // Clear any existing route for this center
            clearRouteToCenter(centerId);

            // Create route to this center
            const routingControl = L.Routing.control({
                waypoints: [
                    L.latLng(userLocation.lat, userLocation.lng),
                    L.latLng(center.latitude, center.longitude)
                ],
                routeWhileDragging: false,
                showAlternatives: false,
                fitSelectedRoutes: false,
                lineOptions: {
                    styles: [{ color: '#3b82f6', opacity: 0.8, weight: 4 }]
                },
                createMarker: function() { return null; },
                addWaypoints: false,
                draggableWaypoints: false
            }).addTo(map);

            // Store routing control for this center
            centerRoutes[centerId] = routingControl;

            // Update button states
            const showRouteBtn = document.getElementById(`show-route-${centerId}`);
            const clearRouteBtn = document.getElementById(`clear-route-${centerId}`);
            const routeInfoSection = document.getElementById(`route-info-${centerId}`);
            
            showRouteBtn.style.display = 'none';
            clearRouteBtn.style.display = 'block';
            routeInfoSection.style.display = 'block';

            // Listen for route calculation completion
            routingControl.on('routesfound', function(e) {
                const routes = e.routes;
                if (routes && routes.length > 0) {
                    const route = routes[0];
                    const summary = route.summary;
                    
                    const travelTimeMinutes = Math.round(summary.totalTime / 60);
                    const distanceKm = (summary.totalDistance / 1000).toFixed(1);
                    
                    // Update route info in popup
                    const travelTimeSpan = document.getElementById(`travel-time-${centerId}`);
                    const distanceSpan = document.getElementById(`distance-${centerId}`);
                    
                    if (travelTimeSpan) travelTimeSpan.textContent = `${travelTimeMinutes} minutes`;
                    if (distanceSpan) distanceSpan.textContent = `${distanceKm} km`;
                    
                    console.log(`Route to ${center.name}: ${travelTimeMinutes} min, ${distanceKm} km`);
                }
            });

            routingControl.on('routingerror', function(e) {
                console.error('Routing error for center:', center.name, e);
                
                const travelTimeSpan = document.getElementById(`travel-time-${centerId}`);
                const distanceSpan = document.getElementById(`distance-${centerId}`);
                
                if (travelTimeSpan) travelTimeSpan.textContent = 'Unable to calculate';
                if (distanceSpan) distanceSpan.textContent = 'Unable to calculate';
            });
        }

        // Clear route to a specific center
        function clearRouteToCenter(centerId) {
            if (centerRoutes[centerId]) {
                const routingControl = centerRoutes[centerId];
                if (map.hasLayer(routingControl)) {
                    map.removeLayer(routingControl);
                }
                delete centerRoutes[centerId];
            }

            // Update button states
            const showRouteBtn = document.getElementById(`show-route-${centerId}`);
            const clearRouteBtn = document.getElementById(`clear-route-${centerId}`);
            const routeInfoSection = document.getElementById(`route-info-${centerId}`);
            
            if (showRouteBtn) {
                showRouteBtn.style.display = 'block';
                showRouteBtn.disabled = !userLocation;
            }
            if (clearRouteBtn) clearRouteBtn.style.display = 'none';
            if (routeInfoSection) routeInfoSection.style.display = 'none';
        }

        // Initialize map when page loads
        document.addEventListener('DOMContentLoaded', initMap);
    </script>
</body>
</html> 