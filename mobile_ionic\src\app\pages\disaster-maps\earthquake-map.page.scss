#earthquake-map {
  height: 100%;
  width: 100%;
  z-index: 1;
}

.map-controls {
  position: absolute;
  top: 80px;
  right: 10px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-btn {
  --background: rgba(255, 255, 255, 0.95);
  --color: #ff9500;
  --border-radius: 12px;
  width: 50px;
  height: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 149, 0, 0.2);

  &:hover {
    --background: rgba(255, 149, 0, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }
}

.control-icon {
  width: 28px;
  height: 28px;
  object-fit: contain;
}

.floating-info {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 250px;

  ion-card {
    margin: 0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }

  ion-card-content {
    padding: 12px;
  }

  .info-row {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--ion-color-warning);
    margin-bottom: 4px;

    ion-icon {
      font-size: 18px;
    }
  }

  .info-text {
    font-size: 12px;
    color: var(--ion-color-medium);
    line-height: 1.3;
  }
}

// Header styling
ion-header {
  ion-toolbar {
    --background: #ff9500;
    --color: white;
    --border-width: 0;

    .header-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      font-size: 18px;

      ion-icon {
        font-size: 20px;
      }
    }
  }
}

// Route Footer (shows when marker is clicked)
.route-footer {
  position: fixed;
  bottom: -100px;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid var(--ion-color-light);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1500;
  transition: bottom 0.3s ease-in-out;

  &.show {
    bottom: 0;
  }

  .footer-content {
    padding: 16px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .route-summary {
      display: flex;
      align-items: center;
      gap: 12px;
      flex: 1;

      .transport-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--ion-color-warning-tint);
        display: flex;
        align-items: center;
        justify-content: center;

        ion-icon {
          font-size: 20px;
          color: var(--ion-color-warning);
        }
      }

      .route-details {
        flex: 1;

        .destination {
          font-size: 16px;
          font-weight: 600;
          color: var(--ion-color-dark);
          margin-bottom: 2px;
          line-height: 1.2;
        }

        .route-info-footer {
          display: flex;
          align-items: center;
          gap: 8px;

          .time {
            font-size: 14px;
            font-weight: 600;
            color: var(--ion-color-warning);
          }

          .distance {
            font-size: 12px;
            color: var(--ion-color-medium);
          }
        }
      }
    }

    .footer-actions {
      ion-button {
        --border-radius: 20px;
        height: 36px;
        font-weight: 600;
      }
    }
  }
}

// All Centers Panel (slides from right)
.all-centers-panel {
  position: fixed;
  top: 0;
  right: -100%;
  width: 350px;
  height: 100vh;
  background: white;
  z-index: 2100;
  transition: right 0.3s ease-in-out;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);

  &.show {
    right: 0;
  }

  .panel-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px;
    padding-top: 60px;
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 2px solid var(--ion-color-warning-tint);

    .header-info {
      flex: 1;

      h3 {
        margin: 0 0 4px 0;
        font-size: 20px;
        font-weight: 700;
        color: var(--ion-color-warning);
        line-height: 1.2;
      }

      p {
        margin: 0;
        font-size: 14px;
        color: var(--ion-color-medium);
        font-weight: 500;
      }
    }

    ion-button {
      --color: var(--ion-color-medium);
      margin: 0;
    }
  }

  .centers-list {
    flex: 1;
    overflow-y: auto;
    padding-right: 4px;

    .center-item {
      display: flex;
      align-items: center;
      padding: 16px;
      margin-bottom: 12px;
      border: 2px solid var(--ion-color-light);
      border-radius: 12px;
      background: white;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

      &:hover {
        border-color: var(--ion-color-warning-tint);
        background: var(--ion-color-warning-tint);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(255, 149, 0, 0.15);
      }

      .center-info {
        flex: 1;

        h4 {
          margin: 0 0 4px 0;
          font-size: 16px;
          font-weight: 600;
          color: var(--ion-color-dark);
          line-height: 1.2;
        }

        .address {
          margin: 0 0 8px 0;
          font-size: 13px;
          color: var(--ion-color-medium);
          line-height: 1.3;
        }

        .center-details {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .distance, .capacity {
            font-size: 12px;
            font-weight: 500;
            color: var(--ion-color-warning);
          }
        }
      }

      .center-actions {
        ion-icon {
          font-size: 20px;
          color: var(--ion-color-medium);
        }
      }
    }
  }
}

// Navigation Panel (slides from right)
.navigation-panel {
  position: fixed;
  top: 0;
  right: -100%;
  width: 320px;
  height: 100vh;
  background: white;
  z-index: 2000;
  transition: right 0.3s ease-in-out;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);

  &.show {
    right: 0;
  }

  .panel-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px;
    padding-top: 60px; // Account for status bar
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--ion-color-light);

    .center-info {
      flex: 1;

      h3 {
        margin: 0 0 4px 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--ion-color-dark);
        line-height: 1.2;
      }

      p {
        margin: 0;
        font-size: 14px;
        color: var(--ion-color-medium);
        line-height: 1.3;
      }
    }

    ion-button {
      --color: var(--ion-color-medium);
      margin: 0;
    }
  }

  .transport-options {
    flex: 1;

    .option-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;
      font-weight: 600;
      color: var(--ion-color-warning);

      ion-icon {
        font-size: 20px;
      }
    }

    .transport-buttons {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-bottom: 24px;
    }

    .transport-btn {
      display: flex;
      align-items: center;
      padding: 16px;
      border: 2px solid var(--ion-color-light);
      border-radius: 12px;
      background: white;
      cursor: pointer;
      transition: all 0.2s ease;
      position: relative;

      &:hover {
        border-color: var(--ion-color-warning-tint);
        background: var(--ion-color-warning-tint);
      }

      &.active {
        border-color: var(--ion-color-warning);
        background: var(--ion-color-warning-tint);

        ion-icon {
          color: var(--ion-color-warning);
        }
      }

      ion-icon {
        font-size: 24px;
        margin-right: 12px;
        color: var(--ion-color-medium);
        transition: color 0.2s ease;
      }

      > span {
        font-size: 16px;
        font-weight: 500;
        color: var(--ion-color-dark);
        flex: 1;
      }

      .route-info {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 2px;

        .time {
          font-size: 16px;
          font-weight: 600;
          color: var(--ion-color-warning);
        }

        .distance {
          font-size: 12px;
          color: var(--ion-color-medium);
        }
      }
    }

    .start-navigation-btn {
      width: 100%;
      padding: 16px;
      background: var(--ion-color-warning);
      color: white;
      border: none;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      cursor: pointer;
      transition: background 0.2s ease;

      &:hover {
        background: var(--ion-color-warning-shade);
      }

      ion-icon {
        font-size: 20px;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .map-controls {
    top: 70px;
    right: 8px;
  }

  .all-centers-panel {
    width: 100%;
    right: -100%;

    &.show {
      right: 0;
    }
  }

  .navigation-panel {
    width: 100%;
    right: -100%;

    &.show {
      right: 0;
    }
  }

  .route-footer {
    .footer-content {
      padding: 12px 16px;

      .route-summary {
        gap: 10px;

        .transport-icon {
          width: 36px;
          height: 36px;

          ion-icon {
            font-size: 18px;
          }
        }

        .route-details {
          .destination {
            font-size: 14px;
          }

          .route-info-footer {
            .time {
              font-size: 13px;
            }

            .distance {
              font-size: 11px;
            }
          }
        }
      }
    }
  }
}

// Popup styling for earthquake centers
:global(.leaflet-popup-content) {
  .evacuation-popup {
    text-align: center;
    min-width: 200px;

    h3 {
      margin: 0 0 8px 0;
      color: var(--ion-color-warning);
      font-size: 16px;
      font-weight: 600;
    }

    p {
      margin: 4px 0;
      font-size: 14px;

      strong {
        color: var(--ion-color-dark);
      }
    }
  }
}
