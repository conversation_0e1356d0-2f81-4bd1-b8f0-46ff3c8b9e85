@import "tailwindcss";

@layer base {
    :root {
        --font-sans: 'Inter', ui-sans-serif, system-ui, sans-serif;
    }
    body {
        font-family: var(--font-sans);
    }
}

@layer components {
    .sidebar-link {
        @apply flex items-center gap-3 text-gray-700 hover:bg-red-50 p-3 transition-all duration-200;
    }
    .sidebar-link.active {
        background-color: rgb(254 242 242);
        font-weight: 600;
        color: rgb(220 38 38);
    }
    .sidebar-link i {
        @apply w-6 text-center;
    }
    .plain-card, .dashboard-card, .container, .card {
        @apply bg-white border border-gray-200 shadow-none p-6 m-2;
        min-width: 200px;
        min-height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
    .dashboard-cards {
        @apply flex gap-4;
    }
    .sidebar {
        @apply w-56 min-w-56 max-w-56 h-screen bg-white border-r border-gray-200 shadow-none;
    }
    .header {
        @apply w-full h-16 bg-blue-600 flex items-center px-6 border-b border-gray-200 shadow-none;
    }
    button, input, select, textarea, .search-bar {
        @apply rounded-none border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200;
    }
}
