<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\DeviceToken;
use App\Models\Notification;
use App\Services\FCMService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Cache;

class FCMController extends Controller
{
    protected $fcmService;
    protected $maxNotificationsPerMinute = 60;
    protected $maxTestNotificationsPerMinute = 10;

    public function __construct(FCMService $fcmService)
    {
        $this->fcmService = $fcmService;
    }

    public function storeToken(Request $request)
    {
        $request->validate([
            'token' => 'required|string',
            'user_id' => 'nullable|integer',
            'device_type' => 'nullable|string|in:android,ios,web',
        ]);

        $token = DeviceToken::updateOrCreate(
            [
                'token' => $request->token,
            ],
            [
                'user_id' => $request->user_id,
                'device_type' => $request->device_type ?? 'android',
                'is_active' => true,
            ]
        );

        return response()->json([
            'success' => true,
            'message' => 'Token saved successfully',
            'data' => $token
        ], 201);
    }

    /**
     * Test sending a notification to a specific token or all tokens
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function testNotification(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'body' => 'required|string',
            'category' => 'nullable|string',
            'severity' => 'nullable|string|in:low,medium,high',
            'token' => 'nullable|string',
        ]);

        // Create a notification record
        $notification = Notification::create([
            'title' => $request->title,
            'message' => $request->body,
            'category' => $request->category ?? 'General',
            'severity' => $request->severity ?? 'medium',
            'barangay' => $request->barangay ?? 'All Areas',
            'sent' => false
        ]);

        // Get tokens - either the specific token or all active tokens
        $tokens = [];
        if ($request->has('token') && $request->token) {
            $tokens = [$request->token];

            // Make sure the token is registered
            DeviceToken::updateOrCreate(
                ['token' => $request->token],
                ['is_active' => true]
            );
        }

        try {
            // Send notification using FCM service
            $result = $this->fcmService->sendNotification($notification, $tokens);

            if (!$result['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => 'Test notification sent successfully',
                'notification_id' => $notification->id,
                'tokens_count' => count($tokens),
                'success_count' => $result['success_count'],
                'failure_count' => $result['failure_count'],
                'invalid_tokens' => $result['invalid_tokens']
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send test notification', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send test notification: ' . $e->getMessage()
            ], 500);
        }
    }

    public function sendNotification(Request $request)
    {
        $request->validate([
            'title' => 'required|string',
            'body' => 'required|string',
            'category' => 'nullable|string',
            'severity' => 'nullable|string|in:low,medium,high',
            'device_type' => 'nullable|string|in:android,ios,web,all',
            'data' => 'nullable|array',
            'user_id' => 'nullable|integer',
        ]);

        try {
            // Create a notification record in the database first
            $notification = Notification::create([
                'title' => $request->title,
                'message' => $request->body,
                'category' => $request->category ?? 'general',
                'severity' => $request->severity ?? 'medium',
                'barangay' => $request->barangay ?? 'All Areas',
                'sent' => false
            ]);

            // Query to get active tokens
            $query = DeviceToken::where('is_active', true);

            // Filter by device type if specified
            if ($request->has('device_type') && $request->device_type !== 'all') {
                $query->where('device_type', $request->device_type);
            }

            // Filter by user_id if specified
            if ($request->has('user_id') && $request->user_id) {
                $query->where('user_id', $request->user_id);
            }

            $tokens = $query->pluck('token')->toArray();

            if (empty($tokens)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active device tokens found',
                ], 404);
            }

            // Send notification using FCM service
            $result = $this->fcmService->sendNotification($notification, $tokens);

            if (!$result['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => 'Notification sent successfully',
                'notification_id' => $notification->id,
                'tokens_count' => count($tokens),
                'success_count' => $result['success_count'],
                'failure_count' => $result['failure_count'],
                'invalid_tokens' => $result['invalid_tokens']
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send notification', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send notification',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function sendTestNotification(Request $request)
    {
        try {
            // Rate limiting for test notifications
            $key = 'test_notification_' . $request->ip();
            if (RateLimiter::tooManyAttempts($key, $this->maxTestNotificationsPerMinute)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Too many test notifications. Please try again later.',
                    'retry_after' => RateLimiter::availableIn($key)
                ], 429);
            }
            RateLimiter::hit($key);

            // Validate request
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'message' => 'required|string|max:1000',
                'severity' => 'nullable|string|in:low,normal,high',
                'category' => 'nullable|string|max:50',
                'data' => 'nullable|array',
                'sound' => 'nullable|string|max:50',
                'badge' => 'nullable|string|max:10',
                'icon' => 'nullable|string|max:255',
                'color' => 'nullable|string|max:50',
                'tag' => 'nullable|string|max:50',
                'group' => 'nullable|string|max:50',
                'silent' => 'nullable|boolean',
                'vibrate' => 'nullable|boolean',
                'actions' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Create notification
            $notification = Notification::create([
                'title' => $request->title,
                'message' => $request->message,
                'severity' => $request->severity ?? 'medium',
                'category' => $request->category ?? 'General',
                'barangay' => 'All Areas',
                'sent' => false
            ]);

            // Send notification
            $result = $this->fcmService->sendNotification($notification);

            if (!$result['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                    'error' => $result['message']
                ], 500);
            }

            $notification->update(['sent' => true]);

            return response()->json([
                'success' => true,
                'message' => 'Test notification sent successfully',
                'data' => [
                    'notification' => $notification,
                    'result' => $result
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send test notification', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send test notification',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function sendBulkNotifications(Request $request)
    {
        try {
            // Rate limiting for bulk notifications
            $key = 'bulk_notification_' . $request->ip();
            if (RateLimiter::tooManyAttempts($key, $this->maxNotificationsPerMinute)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Too many bulk notifications. Please try again later.',
                    'retry_after' => RateLimiter::availableIn($key)
                ], 429);
            }
            RateLimiter::hit($key);

            // Validate request
            $validator = Validator::make($request->all(), [
                'notifications' => 'required|array|max:100',
                'notifications.*.title' => 'required|string|max:255',
                'notifications.*.message' => 'required|string|max:1000',
                'notifications.*.severity' => 'nullable|string|in:low,normal,high',
                'notifications.*.category' => 'nullable|string|max:50',
                'notifications.*.data' => 'nullable|array',
                'notifications.*.sound' => 'nullable|string|max:50',
                'notifications.*.badge' => 'nullable|string|max:10',
                'notifications.*.icon' => 'nullable|string|max:255',
                'notifications.*.color' => 'nullable|string|max:50',
                'notifications.*.tag' => 'nullable|string|max:50',
                'notifications.*.group' => 'nullable|string|max:50',
                'notifications.*.silent' => 'nullable|boolean',
                'notifications.*.vibrate' => 'nullable|boolean',
                'notifications.*.actions' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $results = [];
            $successCount = 0;
            $failureCount = 0;

            foreach ($request->notifications as $notificationData) {
                try {
                    // Create notification
                    $notification = Notification::create([
                        'title' => $notificationData['title'],
                        'message' => $notificationData['message'],
                        'severity' => $notificationData['severity'] ?? 'medium',
                        'category' => $notificationData['category'] ?? 'General',
                        'barangay' => 'All Areas',
                        'sent' => false
                    ]);

                    // Send notification
                    $result = $this->fcmService->sendNotification($notification);

                    if ($result['success']) {
                        $notification->update(['sent' => true]);
                        $successCount++;
                    } else {
                        $failureCount++;
                    }

                    $results[] = [
                        'notification' => $notification,
                        'result' => $result
                    ];

                } catch (\Exception $e) {
                    Log::error('Failed to process notification in bulk send', [
                        'error' => $e->getMessage(),
                        'notification_data' => $notificationData
                    ]);
                    $failureCount++;
                }
            }

            return response()->json([
                'success' => true,
                'message' => "Bulk notification process completed. Success: {$successCount}, Failures: {$failureCount}",
                'data' => [
                    'success_count' => $successCount,
                    'failure_count' => $failureCount,
                    'results' => $results
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send bulk notifications', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send bulk notifications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getNotificationStatus($id)
    {
        try {
            $notification = Notification::findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $notification->id,
                    'title' => $notification->title,
                    'message' => $notification->message,
                    'sent' => $notification->sent,
                    'created_at' => $notification->created_at,
                    'updated_at' => $notification->updated_at
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get notification status', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get notification status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send a notification to a specific user
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendNotificationToUser(Request $request)
    {
        $request->validate([
            'title' => 'required|string',
            'body' => 'required|string',
            'category' => 'nullable|string',
            'severity' => 'nullable|string|in:low,medium,high',
            'user_id' => 'required|integer|exists:users,id',
            'data' => 'nullable|array',
        ]);

        try {
            // Create a notification record in the database first
            $notification = Notification::create([
                'title' => $request->title,
                'message' => $request->body,
                'category' => $request->category ?? 'general',
                'severity' => $request->severity ?? 'medium',
                'barangay' => $request->barangay ?? 'All Areas',
                'sent' => false
            ]);

            // Get the user's active tokens
            $tokens = DeviceToken::where('user_id', $request->user_id)
                ->where('is_active', true)
                ->pluck('token')
                ->toArray();

            if (empty($tokens)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active device tokens found for this user',
                ], 404);
            }

            // Send notification using FCM service
            $result = $this->fcmService->sendNotification($notification, $tokens);

            if (!$result['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => 'Notification sent successfully to user',
                'notification_id' => $notification->id,
                'tokens_count' => count($tokens),
                'success_count' => $result['success_count'],
                'failure_count' => $result['failure_count'],
                'invalid_tokens' => $result['invalid_tokens']
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send notification to user', [
                'user_id' => $request->user_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send notification to user',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send test earthquake notification for deep linking testing
     */
    public function sendTestEarthquakeNotification(Request $request)
    {
        try {
            // Rate limiting for test notifications
            $key = 'test_earthquake_notification:' . $request->ip();
            if (RateLimiter::tooManyAttempts($key, $this->maxTestNotificationsPerMinute)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Too many test notifications. Please wait before trying again.'
                ], 429);
            }

            RateLimiter::hit($key, 60); // 1 minute window

            // Create test earthquake notification
            $notification = Notification::create([
                'title' => '🚨 EARTHQUAKE ALERT - TEST',
                'message' => 'Magnitude 6.2 earthquake detected in Cebu City. This is a test notification for deep linking.',
                'category' => 'earthquake',
                'severity' => 'high',
                'barangay' => 'All Areas',
                'sent' => true,
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Get all active device tokens
            $activeTokens = DeviceToken::where('is_active', true)->get();

            if ($activeTokens->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active device tokens found. Please register a device first.',
                    'notification_id' => $notification->id
                ], 404);
            }

            $results = [];
            $successCount = 0;
            $failureCount = 0;

            foreach ($activeTokens as $deviceToken) {
                try {
                    $result = $this->fcmService->sendToToken($deviceToken->token, $notification);

                    if ($result['success']) {
                        $successCount++;
                        $results[] = [
                            'token' => substr($deviceToken->token, 0, 20) . '...',
                            'status' => 'success',
                            'message_id' => $result['message_id'] ?? null
                        ];
                    } else {
                        $failureCount++;
                        $results[] = [
                            'token' => substr($deviceToken->token, 0, 20) . '...',
                            'status' => 'failed',
                            'error' => $result['error'] ?? 'Unknown error'
                        ];
                    }
                } catch (\Exception $e) {
                    $failureCount++;
                    $results[] = [
                        'token' => substr($deviceToken->token, 0, 20) . '...',
                        'status' => 'failed',
                        'error' => $e->getMessage()
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Test earthquake notification sent',
                'notification_id' => $notification->id,
                'summary' => [
                    'total_tokens' => $activeTokens->count(),
                    'successful' => $successCount,
                    'failed' => $failureCount
                ],
                'results' => $results,
                'expected_behavior' => [
                    'notification_appears' => 'Notification should appear in mobile app',
                    'tap_action' => 'Tapping notification should navigate to earthquake map',
                    'emergency_alert' => 'Emergency alert should show with auto-routing option',
                    'deep_link_route' => '/tabs/earthquake-map?emergency=true&autoRoute=true&notification=true&category=earthquake',
                    'map_view' => 'Specific earthquake evacuation centers with auto-routing'
                ]
            ], 200);

        } catch (\Exception $e) {
            Log::error('Test earthquake notification failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send test earthquake notification',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send test flood notification for deep linking testing
     */
    public function sendTestFloodNotification(Request $request)
    {
        return $this->sendTestDisasterNotification('flood', [
            'title' => '🌊 FLOOD ALERT - TEST',
            'message' => 'Heavy rainfall causing severe flooding in multiple areas. Evacuate to higher ground immediately.',
            'severity' => 'high'
        ]);
    }

    /**
     * Send test typhoon notification for deep linking testing
     */
    public function sendTestTyphoonNotification(Request $request)
    {
        return $this->sendTestDisasterNotification('typhoon', [
            'title' => '🌪️ TYPHOON ALERT - TEST',
            'message' => 'Typhoon approaching with strong winds and heavy rain. Seek shelter in evacuation centers.',
            'severity' => 'high'
        ]);
    }

    /**
     * Send test fire notification for deep linking testing
     */
    public function sendTestFireNotification(Request $request)
    {
        return $this->sendTestDisasterNotification('fire', [
            'title' => '🔥 FIRE ALERT - TEST',
            'message' => 'Large fire spreading rapidly in residential area. Evacuate immediately to designated centers.',
            'severity' => 'high'
        ]);
    }

    /**
     * Send test landslide notification for deep linking testing
     */
    public function sendTestLandslideNotification(Request $request)
    {
        return $this->sendTestDisasterNotification('landslide', [
            'title' => '⛰️ LANDSLIDE ALERT - TEST',
            'message' => 'Landslide risk due to heavy rainfall. Move to safe evacuation areas immediately.',
            'severity' => 'high'
        ]);
    }

    /**
     * Send test "other" disaster notification for deep linking testing
     */
    public function sendTestOtherNotification(Request $request)
    {
        return $this->sendTestDisasterNotification('other', [
            'title' => '⚠️ EMERGENCY ALERT - TEST',
            'message' => 'General emergency situation detected. Please proceed to nearest evacuation centers.',
            'severity' => 'medium'
        ]);
    }

    /**
     * Generic method to send test disaster notifications
     */
    private function sendTestDisasterNotification(string $category, array $notificationData)
    {
        try {
            // Rate limiting for test notifications
            $key = "test_{$category}_notification:" . request()->ip();
            if (RateLimiter::tooManyAttempts($key, $this->maxTestNotificationsPerMinute)) {
                return response()->json([
                    'success' => false,
                    'message' => "Too many test {$category} notifications. Please wait before trying again."
                ], 429);
            }

            RateLimiter::hit($key, 60); // 1 minute window

            // Create test notification
            $notification = Notification::create([
                'title' => $notificationData['title'],
                'message' => $notificationData['message'],
                'category' => $category,
                'severity' => $notificationData['severity'],
                'barangay' => 'All Areas',
                'sent' => true,
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Get all active device tokens
            $activeTokens = DeviceToken::where('is_active', true)->get();

            if ($activeTokens->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active device tokens found. Please register a device first.',
                    'notification_id' => $notification->id
                ], 404);
            }

            $results = [];
            $successCount = 0;
            $failureCount = 0;

            foreach ($activeTokens as $deviceToken) {
                try {
                    $result = $this->fcmService->sendToToken($deviceToken->token, $notification);

                    if ($result['success']) {
                        $successCount++;
                        $results[] = [
                            'token' => substr($deviceToken->token, 0, 20) . '...',
                            'status' => 'success',
                            'message_id' => $result['message_id'] ?? null
                        ];
                    } else {
                        $failureCount++;
                        $results[] = [
                            'token' => substr($deviceToken->token, 0, 20) . '...',
                            'status' => 'failed',
                            'error' => $result['error'] ?? 'Unknown error'
                        ];
                    }
                } catch (\Exception $e) {
                    $failureCount++;
                    $results[] = [
                        'token' => substr($deviceToken->token, 0, 20) . '...',
                        'status' => 'failed',
                        'error' => $e->getMessage()
                    ];
                }
            }

            // Determine expected route based on disaster type
            $specificDisasters = ['earthquake', 'flood', 'typhoon', 'fire', 'landslide'];
            $expectedRoute = in_array($category, $specificDisasters) ? "/tabs/{$category}-map" : '/tabs/map';
            $mapDescription = in_array($category, $specificDisasters) ? "Specific {$category} evacuation centers" : 'All evacuation centers (general map)';

            return response()->json([
                'success' => true,
                'message' => "Test {$category} notification sent",
                'notification_id' => $notification->id,
                'category' => $category,
                'summary' => [
                    'total_tokens' => $activeTokens->count(),
                    'successful' => $successCount,
                    'failed' => $failureCount
                ],
                'results' => $results,
                'expected_behavior' => [
                    'notification_appears' => 'Notification should appear in mobile app',
                    'tap_action' => "Tapping notification should navigate to " . (in_array($category, $specificDisasters) ? "{$category} specific map" : 'general map'),
                    'emergency_alert' => "Emergency alert should show {$category} details",
                    'deep_link_route' => "{$expectedRoute}?emergency=true&autoRoute=true&notification=true&category={$category}",
                    'map_view' => $mapDescription,
                    'routing_logic' => in_array($category, $specificDisasters) ? 'Specific disaster map with auto-routing' : 'General map with all evacuation centers'
                ]
            ], 200);

        } catch (\Exception $e) {
            Log::error("Test {$category} notification failed", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => "Failed to send test {$category} notification",
                'error' => $e->getMessage()
            ], 500);
        }
    }
}