// import 'leaflet/dist/leaflet.css';
import { MapManager } from './push_notification/map/MapManager.js';
import { MapSearch } from './push_notification/map/MapSearch.js';
import { MapZones, SeverityManager } from './push_notification/map/MapZone.js';

class NotificationApp {
    constructor() {
        this.mapManager = new MapManager('map');
        this.mapSearch = new MapSearch(this.mapManager);
        this.severityManager = new SeverityManager();
        this.mapZones = new MapZones(this.mapManager, this.severityManager);
    }

    initialize() {
        this.mapManager.initialize();
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('search');
        if (searchInput) {
            searchInput.addEventListener('input', this.handleSearch.bind(this));
        }

        // Severity changes
        document.querySelectorAll('input[name="severity"]').forEach(radio => {
            radio.addEventListener('change', this.handleSeverityChange.bind(this));
        });
    }

    async handleSearch(event) {
        const query = event.target.value.trim();
        if (query.length < 2) return;

        try {
            // Use server-side search endpoint instead of direct API calls
            const response = await fetch(`/api/geocode?address=${encodeURIComponent(query)}`);
            const data = await response.json();
            this.displaySearchResults(data.features || []);
        } catch (error) {
            this.handleError(error);
        }
    }

    displaySearchResults(results) {
        const resultsContainer = document.getElementById('search-results');
        if (!resultsContainer) return;

        resultsContainer.innerHTML = '';
        results.forEach((result, idx) => {
            const btn = document.createElement('button');
            btn.textContent = result.display_name;
            btn.className = 'block w-full text-left px-2 py-1 hover:bg-gray-200';
            btn.onclick = () => {
                resultsContainer.innerHTML = '';
            };
            resultsContainer.appendChild(btn);
        });
    }

    handleSeverityChange(event) {
        const severity = event.target.value;
        this.mapZones.updateSeverity(severity);
    }

    handleError(error) {
        console.error(error);
        // Implement your error handling UI here
    }
}

// Initialize the application
const app = new NotificationApp();
app.initialize();

// Standardized filter functionality for all features
function initializeStandardFilters() {
    // Get all filter elements
    const filterSelects = document.querySelectorAll('select[data-filter="true"], select[name="barangay"], select[name="status"], select[name="disaster_type"], select[name="month"], select[name="year"]');
    const filterInputs = document.querySelectorAll('input[data-filter="true"], input[name="search"]');
    
    // Add change event listeners to all filter selects
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            // If the select is inside a form, submit the form
            if (this.form) {
                this.form.submit();
            } else {
                // For selects not in forms, trigger a custom event
                const event = new CustomEvent('filterChanged', {
                    detail: {
                        name: this.name,
                        value: this.value,
                        element: this
                    }
                });
                document.dispatchEvent(event);
            }
        });
        
        // Also add click event for better browser compatibility
        select.addEventListener('click', function() {
            // For browsers that don't trigger change if the same value is selected
            // This ensures the form submits even if the same value is clicked
            if (this.form) {
                setTimeout(() => {
                    this.form.submit();
                }, 100);
            }
        });
    });
    
    // Add input event listeners to search inputs (with debouncing)
    filterInputs.forEach(input => {
        let timeout;
        input.addEventListener('input', function() {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                if (this.form) {
                    this.form.submit();
                } else {
                    // For inputs not in forms, trigger a custom event
                    const event = new CustomEvent('filterChanged', {
                        detail: {
                            name: this.name,
                            value: this.value,
                            element: this
                        }
                    });
                    document.dispatchEvent(event);
                }
            }, 500); // 500ms debounce
        });
    });
}

// Initialize standard filters when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeStandardFilters();
});


