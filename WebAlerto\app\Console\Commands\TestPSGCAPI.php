<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class TestPSGCAPI extends Command
{
    protected $signature = 'test:psgc-api';
    protected $description = 'Test PSGC API endpoints to find the correct structure';

    public function handle()
    {
        $this->info('🧪 Testing PSGC API Endpoints');
        $this->info('==============================');

        $baseUrl = 'https://psgc.gitlab.io/api';

        // Test different endpoints
        $endpoints = [
            'Root API' => '',
            'Island Groups' => '/island-groups',
            'Regions' => '/regions',
            'Provinces' => '/provinces',
            'Cities/Municipalities' => '/cities-municipalities',
            'Barangays' => '/barangays',
            'Cebu Province' => '/provinces/0722',
            'Cebu City Direct' => '/cities-municipalities/072217000',
            'Cebu City Barangays' => '/cities-municipalities/072217000/barangays',
        ];

        foreach ($endpoints as $name => $endpoint) {
            $this->info("\n🔍 Testing: {$name}");
            $this->info("URL: {$baseUrl}{$endpoint}");

            try {
                $response = Http::timeout(10)->get($baseUrl . $endpoint);

                if ($response->successful()) {
                    $data = $response->json();
                    $this->info("✅ Success - Status: {$response->status()}");

                    if (is_array($data)) {
                        $this->info("📊 Data type: Array with " . count($data) . " items");
                        if (!empty($data)) {
                            $this->info("🔍 First item structure:");
                            $this->line(json_encode($data[0], JSON_PRETTY_PRINT));
                        }
                    } else {
                        $this->info("📊 Data type: " . gettype($data));
                        $this->line(json_encode($data, JSON_PRETTY_PRINT));
                    }
                } else {
                    $this->error("❌ Failed - Status: {$response->status()}");
                    $this->error("Response: " . $response->body());
                }

            } catch (\Exception $e) {
                $this->error("❌ Exception: " . $e->getMessage());
            }

            $this->info(str_repeat('-', 50));
        }

        return 0;
    }
}
