<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Transport Modes - Walking, Biking, Driving</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .controls {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .transport-modes {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .transport-mode-btn {
            padding: 10px 20px;
            border: 2px solid #ddd;
            background: #f8f9fa;
            color: #666;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .transport-mode-btn.active {
            background: #007bff;
            color: white;
            border-color: #0056b3;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
        }
        
        .transport-mode-btn:hover:not(.active) {
            background: #e9ecef;
            border-color: #adb5bd;
        }
        
        .routing-controls {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        #map {
            height: 600px;
            width: 100%;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .info-panel {
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-top: 20px;
        }
        
        .route-info {
            display: none;
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        
        .route-info.show {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚗🚶🚴 Transport Mode Routing Test</h1>
        
        <div class="controls">
            <h3>Transport Mode Selection</h3>
            <div class="transport-modes">
                <button onclick="setTransportMode('driving')" id="transport-driving" class="transport-mode-btn active">
                    <i class="fas fa-car mr-2"></i>Driving
                </button>
                <button onclick="setTransportMode('walking')" id="transport-walking" class="transport-mode-btn">
                    <i class="fas fa-walking mr-2"></i>Walking
                </button>
                <button onclick="setTransportMode('cycling')" id="transport-cycling" class="transport-mode-btn">
                    <i class="fas fa-bicycle mr-2"></i>Cycling
                </button>
            </div>
            
            <h3>Routing Controls</h3>
            <div class="routing-controls">
                <button onclick="getUserLocation()" id="get-location" class="btn btn-success">
                    <i class="fas fa-location-arrow mr-2"></i>Get My Location
                </button>
                <button onclick="showRoute()" id="show-route" class="btn btn-primary" disabled>
                    <i class="fas fa-route mr-2"></i>Show Route
                </button>
                <button onclick="clearRoute()" id="clear-route" class="btn btn-secondary" disabled>
                    <i class="fas fa-times mr-2"></i>Clear Route
                </button>
            </div>
        </div>
        
        <div id="map"></div>
        
        <div class="info-panel">
            <h3>Instructions</h3>
            <ol>
                <li>Click "Get My Location" to detect your current position</li>
                <li>Select a transport mode (Driving, Walking, or Cycling)</li>
                <li>Click "Show Route" to see the route to the test destination</li>
                <li>Try different transport modes to see how routes change</li>
                <li>Use "Clear Route" to remove the current route</li>
            </ol>

            <div class="alert" style="background: #e3f2fd; border: 1px solid #2196f3; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <h4 style="color: #1976d2; margin: 0 0 10px 0;">✅ Implementation Complete</h4>
                <p style="margin: 0; color: #1565c0;">
                    The main WebAlerto application now has transport mode selection in the map header.
                    Users can select Walking, Biking, or Driving modes, and when they click "Show Route"
                    on individual evacuation center markers, the routing details will differ based on the selected transport mode.
                </p>
            </div>

            <div id="route-info" class="route-info">
                <h4>Route Information</h4>
                <div id="route-details"></div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.js"></script>
    <script>
        let map;
        let userLocation = null;
        let userMarker = null;
        let destinationMarker = null;
        let routingControl = null;
        let currentTransportMode = 'driving';
        
        // Test destination (Cebu City Hall)
        const testDestination = {
            lat: 10.293,
            lng: 123.902,
            name: "Cebu City Hall"
        };
        
        // Initialize map
        function initMap() {
            map = L.map('map').setView([10.3157, 123.8854], 13);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                maxZoom: 19,
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);
            
            // Add destination marker
            destinationMarker = L.marker([testDestination.lat, testDestination.lng])
                .addTo(map)
                .bindPopup(`<strong>${testDestination.name}</strong><br>Test destination for routing`);
        }
        
        // Get router configuration for different transport modes
        function getRouterForTransportMode(mode) {
            const osrmUrl = 'https://router.project-osrm.org/route/v1';
            
            switch(mode) {
                case 'walking':
                    return L.Routing.osrmv1({
                        serviceUrl: `${osrmUrl}/foot`,
                        profile: 'foot'
                    });
                case 'cycling':
                    return L.Routing.osrmv1({
                        serviceUrl: `${osrmUrl}/bike`, 
                        profile: 'bike'
                    });
                case 'driving':
                default:
                    return L.Routing.osrmv1({
                        serviceUrl: `${osrmUrl}/driving`,
                        profile: 'driving'
                    });
            }
        }
        
        // Set transport mode
        function setTransportMode(mode) {
            currentTransportMode = mode;
            
            // Update button states
            document.querySelectorAll('.transport-mode-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            const activeBtn = document.getElementById(`transport-${mode}`);
            if (activeBtn) {
                activeBtn.classList.add('active');
            }
            
            // If route is currently shown, refresh it with new transport mode
            if (routingControl) {
                clearRoute();
                setTimeout(() => {
                    showRoute();
                }, 100);
            }
        }
        
        // Get user location
        function getUserLocation() {
            if (!navigator.geolocation) {
                alert('Geolocation is not supported by this browser.');
                return;
            }
            
            const btn = document.getElementById('get-location');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Getting Location...';
            
            navigator.geolocation.getCurrentPosition(
                function(position) {
                    userLocation = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    };
                    
                    // Add user marker
                    if (userMarker) {
                        map.removeLayer(userMarker);
                    }
                    
                    userMarker = L.marker([userLocation.lat, userLocation.lng], {
                        icon: L.divIcon({
                            className: 'user-location-marker',
                            html: '<div style="width: 20px; height: 20px; background: #007bff; border: 3px solid white; border-radius: 50%; box-shadow: 0 2px 6px rgba(0,0,0,0.3);"></div>',
                            iconSize: [20, 20],
                            iconAnchor: [10, 10]
                        })
                    }).addTo(map).bindPopup('<strong>Your Location</strong>');
                    
                    // Center map to show both user location and destination
                    const group = new L.featureGroup([userMarker, destinationMarker]);
                    map.fitBounds(group.getBounds().pad(0.1));
                    
                    // Enable route button
                    document.getElementById('show-route').disabled = false;
                    
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-location-arrow mr-2"></i>Location Found';
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-primary');
                },
                function(error) {
                    console.error('Error getting location:', error);
                    showLocationPermissionModal(error);
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-location-arrow mr-2"></i>Get My Location';
                }
            );
        }
        
        // Show route
        function showRoute() {
            if (!userLocation) {
                alert('Please get your location first.');
                return;
            }
            
            if (routingControl) {
                clearRoute();
            }
            
            routingControl = L.Routing.control({
                waypoints: [
                    L.latLng(userLocation.lat, userLocation.lng),
                    L.latLng(testDestination.lat, testDestination.lng)
                ],
                routeWhileDragging: false,
                showAlternatives: false,
                router: getRouterForTransportMode(currentTransportMode),
                lineOptions: {
                    styles: [{
                        color: currentTransportMode === 'driving' ? '#007bff' : 
                               currentTransportMode === 'walking' ? '#28a745' : '#ffc107',
                        opacity: 0.8,
                        weight: 5
                    }]
                },
                createMarker: function() { return null; }
            }).addTo(map);
            
            routingControl.on('routesfound', function(e) {
                const route = e.routes[0];
                const summary = route.summary;
                
                const travelTimeMinutes = Math.round(summary.totalTime / 60);
                const distanceKm = (summary.totalDistance / 1000).toFixed(1);
                
                document.getElementById('route-details').innerHTML = `
                    <p><strong>Transport Mode:</strong> ${currentTransportMode.charAt(0).toUpperCase() + currentTransportMode.slice(1)}</p>
                    <p><strong>Distance:</strong> ${distanceKm} km</p>
                    <p><strong>Estimated Time:</strong> ${travelTimeMinutes} minutes</p>
                `;
                
                document.getElementById('route-info').classList.add('show');
            });
            
            document.getElementById('show-route').disabled = true;
            document.getElementById('clear-route').disabled = false;
        }
        
        // Clear route
        function clearRoute() {
            if (routingControl) {
                map.removeControl(routingControl);
                routingControl = null;
            }
            
            document.getElementById('route-info').classList.remove('show');
            document.getElementById('show-route').disabled = false;
            document.getElementById('clear-route').disabled = true;
        }
        
        // Enhanced Location Permission Modal
        function showLocationPermissionModal(error) {
            // Remove any existing modal
            const existingModal = document.getElementById('locationPermissionModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Determine error message and icon based on error type
            let title, message, icon, iconColor;

            if (error.code === 1) {
                title = 'Location Permission Required';
                message = 'To enable routing features and show directions to evacuation centers, we need access to your location. Please allow location access when prompted by your browser.';
                icon = '📍';
                iconColor = 'text-blue-500';
            } else if (error.code === 2) {
                title = 'Location Unavailable';
                message = 'Your location is currently unavailable. Please check your device\'s location settings and ensure GPS is enabled to use routing features.';
                icon = '🛰️';
                iconColor = 'text-orange-500';
            } else if (error.code === 3) {
                title = 'Location Request Timeout';
                message = 'The location request timed out. Please try again to enable routing features and get directions to evacuation centers.';
                icon = '⏱️';
                iconColor = 'text-yellow-500';
            } else {
                title = 'Location Not Supported';
                message = 'Your browser doesn\'t support location services. Routing features will not be available. Please try using a modern browser like Chrome, Firefox, or Safari.';
                icon = '🚫';
                iconColor = 'text-red-500';
            }

            // Create modal HTML
            const modalHTML = `
                <div id="locationPermissionModal" class="fixed inset-0 z-50 flex items-center justify-center p-4" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.5); z-index: 9999;">
                    <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all" style="background: white; border-radius: 16px; box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); max-width: 28rem; width: 100%; margin: 0 1rem;">
                        <!-- Header -->
                        <div style="padding: 1.5rem; text-align: center; border-bottom: 1px solid #f3f4f6;">
                            <div style="font-size: 2.5rem; margin-bottom: 0.75rem;">${icon}</div>
                            <h3 style="font-size: 1.25rem; font-weight: bold; color: #111827; margin-bottom: 0.5rem;">${title}</h3>
                            <p style="color: #6b7280; font-size: 0.875rem; line-height: 1.5;">${message}</p>
                        </div>

                        <!-- Features Section -->
                        <div style="padding: 1.5rem; background: linear-gradient(to bottom right, #eff6ff, #e0e7ff);">
                            <h4 style="font-weight: 600; color: #374151; margin-bottom: 0.75rem; display: flex; align-items: center;">
                                <span style="color: #3b82f6; margin-right: 0.5rem;">🗺️</span>
                                Routing Features Include:
                            </h4>
                            <ul style="list-style: none; padding: 0; margin: 0;">
                                <li style="display: flex; align-items: center; margin-bottom: 0.5rem; font-size: 0.875rem; color: #374151;">
                                    <span style="color: #10b981; margin-right: 0.5rem;">✓</span>
                                    Turn-by-turn directions to evacuation centers
                                </li>
                                <li style="display: flex; align-items: center; margin-bottom: 0.5rem; font-size: 0.875rem; color: #374151;">
                                    <span style="color: #10b981; margin-right: 0.5rem;">✓</span>
                                    Multiple transport modes (walking, driving, cycling)
                                </li>
                                <li style="display: flex; align-items: center; margin-bottom: 0.5rem; font-size: 0.875rem; color: #374151;">
                                    <span style="color: #10b981; margin-right: 0.5rem;">✓</span>
                                    Real-time distance and travel time estimates
                                </li>
                                <li style="display: flex; align-items: center; font-size: 0.875rem; color: #374151;">
                                    <span style="color: #10b981; margin-right: 0.5rem;">✓</span>
                                    Find nearest evacuation centers to your location
                                </li>
                            </ul>
                        </div>

                        <!-- Action Buttons -->
                        <div style="padding: 1.5rem; display: flex; justify-content: center;">
                            <button onclick="closeLocationModal()" style="background: linear-gradient(to right, #2563eb, #1d4ed8); color: white; font-weight: 600; padding: 0.75rem 1.5rem; border-radius: 0.75rem; border: none; cursor: pointer; transition: all 0.2s; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                                OK
                            </button>
                        </div>

                        <!-- Help Text -->
                        <div style="padding: 0 1.5rem 1.5rem;">
                            <div style="background: #fefbf2; border: 1px solid #fde68a; border-radius: 0.5rem; padding: 0.75rem;">
                                <p style="font-size: 0.75rem; color: #92400e; display: flex; align-items: flex-start; margin: 0;">
                                    <span style="color: #f59e0b; margin-right: 0.5rem; margin-top: 0.125rem;">💡</span>
                                    <span>
                                        <strong>Need help?</strong> Look for a location icon in your browser's address bar and click "Allow" when prompted.
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Add modal to page
            document.body.insertAdjacentHTML('beforeend', modalHTML);
        }



        // Close location modal
        function closeLocationModal() {
            const modal = document.getElementById('locationPermissionModal');
            if (modal) {
                modal.style.opacity = '0';
                setTimeout(() => {
                    modal.remove();
                }, 200);
            }
        }

        // Initialize map when page loads
        window.onload = function() {
            initMap();
        };
    </script>
</body>
</html>
