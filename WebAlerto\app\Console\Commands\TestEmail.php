<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use App\Mail\AdminRegistrationMail;
use App\Models\User;

class TestEmail extends Command
{
    protected $signature = 'email:test {email} {--multiple : Test multiple domains}';
    protected $description = 'Test email delivery to different domains';

    public function handle()
    {
        $email = $this->argument('email');
        $testMultiple = $this->option('multiple');

        if ($testMultiple) {
            return $this->testMultipleDomains($email);
        }

        return $this->testSingleEmail($email);
    }

    private function testSingleEmail($email)
    {
        $this->info("🧪 Testing email delivery to: {$email}");
        $domain = substr(strrchr($email, "@"), 1);
        $this->info("📧 Domain: {$domain}");

        // Create a dummy user for testing
        $testUser = new User();
        $testUser->first_name = 'Test';
        $testUser->last_name = 'User';
        $testUser->email = $email;
        $testUser->role = 'admin';
        $testUser->position = 'Test Administrator';
        $testUser->barangay = 'Test Barangay';

        $temporaryPassword = 'TestPass123!';

        try {
            Mail::to($email)->send(new AdminRegistrationMail($testUser, $temporaryPassword));
            $this->info("✅ Test email sent successfully to: {$email}");
            $this->info("📧 Check your inbox (and spam folder)");
            return 0;
        } catch (\Exception $e) {
            $this->error("❌ Failed to send email: " . $e->getMessage());
            $this->error("🔍 Error details: " . $e->getTraceAsString());
            return 1;
        }
    }

    private function testMultipleDomains($baseEmail)
    {
        // Extract username from email
        $username = substr($baseEmail, 0, strpos($baseEmail, '@'));

        // Test different domains
        $testDomains = [
            'gmail.com',
            'yahoo.com',
            'outlook.com',
            'hotmail.com',
            'protonmail.com',
            'icloud.com'
        ];

        $this->info("🧪 Testing email delivery to multiple domains...");
        $this->info("👤 Base username: {$username}");

        $results = [];

        foreach ($testDomains as $domain) {
            $testEmail = $username . '@' . $domain;
            $this->info("\n📧 Testing: {$testEmail}");

            try {
                // Create test user
                $testUser = new User();
                $testUser->first_name = 'Test';
                $testUser->last_name = 'User';
                $testUser->email = $testEmail;
                $testUser->role = 'admin';
                $testUser->position = 'Test Administrator';
                $testUser->barangay = 'Test Barangay';

                // Send email
                Mail::to($testEmail)->send(new AdminRegistrationMail($testUser, 'TestPass123!'));

                $this->info("   ✅ SUCCESS");
                $results[$domain] = 'SUCCESS';

            } catch (\Exception $e) {
                $this->error("   ❌ FAILED: " . $e->getMessage());
                $results[$domain] = 'FAILED: ' . $e->getMessage();
            }

            // Small delay between sends
            sleep(1);
        }

        // Summary
        $this->info("\n📊 SUMMARY:");
        foreach ($results as $domain => $result) {
            $status = str_contains($result, 'SUCCESS') ? '✅' : '❌';
            $this->info("   {$status} {$domain}: {$result}");
        }

        return 0;
    }
}
