.register-bg {
  --background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.register-wrapper {
  width: 100%;
  max-width: 350px;
  padding: 2rem;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.logo-container {
  margin-bottom: 3rem;
}

.app-logo {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  padding: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.register-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.input-group {
  width: 100%;
}

.modern-input {
  --background: transparent;
  --color: black;
  --placeholder-color: rgba(0, 0, 0, 0.7);
  --padding-start: 0;
  --padding-end: 0;
  border: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.5);
  border-radius: 0;
  font-size: 1rem;
  padding: 12px 0;
}

.modern-input:focus-within {
  border-bottom-color: black;
}

.forgot-link {
  text-align: right;
 
  font-size: 0.95rem;
}
.forgot-link a {
  color: #1565c0;
  text-decoration: none;
}

.register-link a {
  color: #1565c0;
  text-decoration: none;
  font-weight: 600;
}



.register-link {
 
  font-size: 1rem;
  color: #444;
}
.register-link a {
  color: #1565c0;
  text-decoration: none;
  font-weight: 600;
}

.terms-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 1rem 0;
  color: black;
  font-size: 0.95rem;
}

.terms-checkbox {
  --background: transparent;
  --border-color: rgba(0, 0, 0, 0.7);
  --checkmark-color: black;
}

.terms-text {
  flex: 1;
  text-align: left;
}

.terms-notice {
  margin-top: 8px;
  text-align: center;

  small {
    color: rgba(0, 0, 0, 0.7);
    font-style: italic;
  }
}

.modern-btn {
  --background: #007bff;
  --color: white;
  --border-radius: 25px;
  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  font-weight: 600;
  font-size: 1.1rem;
  height: 50px;
  width: 100%;
  margin-top: 1rem;
}

.modern-btn:disabled {
  --background: rgba(255, 255, 255, 0.5);
  --color: rgba(79, 172, 254, 0.5);
}

.login-link {
  margin-top: 1.5rem;
  color: black;
  font-size: 1rem;
}

.login-link a {
  color: #007bff;
  text-decoration: underline;
  font-weight: 600;
}

.terms-link {
  color: #007bff;
  text-decoration: underline;
  cursor: pointer;
  font-weight: 600;
}

.terms-link:hover {
  color: rgba(0, 123, 255, 0.8);
}

// Terms and Conditions Modal Styles
.terms-modal {
  --height: 90%;
  --border-radius: 16px;

  ion-header {
    ion-toolbar {
      --background: var(--ion-color-light);

      ion-title {
        font-size: 18px;
        font-weight: 600;
      }
    }
  }

  .terms-content {
    h1.modal-section-title {
      color: #4facfe;
      text-align: center;
      margin-bottom: 10px;
      font-size: 1.5rem;
    }

    .effective-date {
      text-align: center;
      color: #666;
      font-style: italic;
      margin-bottom: 20px;
    }

    .welcome {
      color: #444;
      line-height: 1.6;
      margin-bottom: 20px;
      text-align: justify;
    }

    section {
      margin-bottom: 20px;

      h2.modal-section-title {
        color: #4facfe;
        margin-bottom: 10px;
        font-size: 1.1rem;
        font-weight: 600;
      }

      p {
        color: #444;
        line-height: 1.6;
        margin-bottom: 12px;
        text-align: justify;
      }
    }
  }

  .modal-buttons {
    margin-top: 30px;
    display: flex;
    flex-direction: column;
    gap: 10px;

    .accept-btn {
      --background: #4facfe;
      --color: white;
      font-weight: 600;
    }
  }
}