// Prepare data from backend
const rawData = window.monthlyCategoryCounts || [];

// Define consistent colors for each disaster type matching the notification system
const disasterColors = {
    'typhoon': 'rgba(34, 197, 94, 0.8)',        // Green for typhoon (wind/nature)
    'flood': 'rgba(59, 130, 246, 0.8)',         // Blue for flood (water)
    'fire': 'rgba(239, 68, 68, 0.8)',           // Red for fire (danger)
    'earthquake': 'rgba(245, 158, 11, 0.8)',    // Amber for earthquake (earth)
    'landslide': 'rgba(161, 98, 7, 0.8)',       // Brown for landslide (earth/soil)
    'others': 'rgba(147, 51, 234, 0.8)',        // Purple for others (custom disasters)
    'volcanic': 'rgba(168, 85, 247, 0.8)',      // Violet for volcanic
    'tsunami': 'rgba(6, 182, 212, 0.8)',        // Cyan for tsunami
    'drought': 'rgba(217, 119, 6, 0.8)',        // Orange for drought
    'storm': 'rgba(71, 85, 105, 0.8)',          // Slate for storm
    'other': 'rgba(107, 114, 128, 0.8)'         // Gray for fallback
};

// Border colors for better chart definition
const disasterBorderColors = {
    'typhoon': '#22c55e',        // Green border
    'flood': '#3b82f6',          // Blue border
    'fire': '#ef4444',           // Red border
    'earthquake': '#f59e0b',     // Amber border
    'landslide': '#a16207',      // Brown border
    'others': '#9333ea',         // Purple border
    'volcanic': '#a855f7',       // Violet border
    'tsunami': '#06b6d4',        // Cyan border
    'drought': '#d97706',        // Orange border
    'storm': '#475569',          // Slate border
    'other': '#6b7280'           // Gray border
};

// Function to generate a color for unknown disaster types
function generateColorForType(type, index) {
    const fallbackColors = [
        'rgba(99, 102, 241, 0.8)',   // Indigo
        'rgba(236, 72, 153, 0.8)',   // Pink
        'rgba(14, 165, 233, 0.8)',   // Sky
        'rgba(34, 197, 94, 0.8)',    // Emerald
        'rgba(251, 146, 60, 0.8)',   // Orange
        'rgba(139, 92, 246, 0.8)',   // Violet
        'rgba(6, 182, 212, 0.8)',    // Cyan
        'rgba(245, 101, 101, 0.8)'   // Rose
    ];

    const fallbackBorders = [
        '#6366f1', '#ec4899', '#0ea5e9', '#22c55e',
        '#fb923c', '#8b5cf6', '#06b6d4', '#f56565'
    ];

    const colorIndex = index % fallbackColors.length;
    return {
        background: fallbackColors[colorIndex],
        border: fallbackBorders[colorIndex]
    };
}

// Extract unique months and categories
const months = [...new Set(rawData.map(item => item.month))];
const categories = [...new Set(rawData.map(item => item.category))];

// Prepare datasets for each category
const datasets = categories.map((category, index) => {
    const categoryKey = category.toLowerCase();

    // Handle custom "others" categories (e.g., "others:volcanic eruption")
    const colorKey = categoryKey.startsWith('others:') ? 'others' : categoryKey;

    // Create proper label for display
    let displayLabel;
    if (categoryKey.startsWith('others:')) {
        // Extract custom disaster type from "others:custom_type"
        displayLabel = category.replace(/^others:/i, '').trim();
        displayLabel = displayLabel.charAt(0).toUpperCase() + displayLabel.slice(1);
    } else {
        displayLabel = category.charAt(0).toUpperCase() + category.slice(1);
    }

    // Get colors - use predefined or generate fallback
    let backgroundColor, borderColor;
    if (disasterColors[colorKey]) {
        backgroundColor = disasterColors[colorKey];
        borderColor = disasterBorderColors[colorKey];
    } else {
        const fallbackColors = generateColorForType(colorKey, index);
        backgroundColor = fallbackColors.background;
        borderColor = fallbackColors.border;
    }

    return {
        label: displayLabel,
        data: months.map(month => {
            const found = rawData.find(item => item.month === month && item.category === category);
            return found ? found.count : 0;
        }),
        backgroundColor: backgroundColor,
        borderColor: borderColor,
        borderWidth: 2,
        borderRadius: 6,
        borderSkipped: false,
        hoverBackgroundColor: backgroundColor.replace('0.8', '0.9'),
        hoverBorderColor: borderColor,
        hoverBorderWidth: 3
    };
});

const ctx = document.getElementById('notifChart').getContext('2d');

// Determine chart type based on number of categories
const chartType = categories.length > 4 ? 'bar' : 'bar'; // Keep as bar chart but adjust layout

new Chart(ctx, {
    type: chartType,
    data: {
        labels: months,
        datasets: datasets
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: true,
                position: categories.length > 4 ? 'right' : 'bottom',
                align: 'start',
                labels: {
                    padding: categories.length > 4 ? 10 : 15,
                    usePointStyle: true,
                    pointStyle: 'rect',
                    font: {
                        size: categories.length > 6 ? 10 : 12,
                        weight: '600'
                    },
                    color: '#374151',
                    boxWidth: categories.length > 6 ? 12 : 15,
                    boxHeight: categories.length > 6 ? 12 : 15,
                    generateLabels: function(chart) {
                        const original = Chart.defaults.plugins.legend.labels.generateLabels;
                        const labels = original.call(this, chart);

                        // Ensure proper color mapping for legend
                        labels.forEach((label, index) => {
                            const dataset = chart.data.datasets[index];
                            if (dataset) {
                                label.fillStyle = dataset.backgroundColor;
                                label.strokeStyle = dataset.borderColor;
                                label.lineWidth = 2;
                            }
                        });

                        return labels;
                    }
                }
            },
            title: {
                display: true,
                text: 'Disaster Notifications Analytics',
                font: {
                    size: 16,
                    weight: 'bold'
                },
                color: '#1f2937',
                padding: {
                    top: 10,
                    bottom: 20
                }
            },
            tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#fff',
                bodyColor: '#fff',
                borderColor: '#374151',
                borderWidth: 1,
                cornerRadius: 8,
                displayColors: true,
                callbacks: {
                    title: function(context) {
                        return `Month: ${context[0].label}`;
                    },
                    label: function(context) {
                        return `${context.dataset.label}: ${context.parsed.y} notification${context.parsed.y !== 1 ? 's' : ''}`;
                    }
                }
            }
        },
        scales: {
            x: {
                stacked: false,
                grid: {
                    display: false
                },
                ticks: {
                    font: {
                        size: categories.length > 6 ? 10 : 12,
                        weight: '500'
                    },
                    color: '#6b7280',
                    maxRotation: categories.length > 6 ? 45 : 0,
                    minRotation: 0
                },
                title: {
                    display: true,
                    text: 'Month',
                    font: {
                        size: 13,
                        weight: '600'
                    },
                    color: '#374151'
                }
            },
            y: {
                stacked: false,
                beginAtZero: true,
                grid: {
                    color: 'rgba(156, 163, 175, 0.2)',
                    drawBorder: false
                },
                ticks: {
                    font: {
                        size: 12,
                        weight: '500'
                    },
                    color: '#6b7280',
                    stepSize: 1,
                    callback: function(value) {
                        return Number.isInteger(value) ? value : '';
                    }
                },
                title: {
                    display: true,
                    text: 'Number of Notifications',
                    font: {
                        size: 13,
                        weight: '600'
                    },
                    color: '#374151'
                }
            }
        },
        interaction: {
            intersect: false,
            mode: 'index'
        },
        animation: {
            duration: 1200,
            easing: 'easeInOutQuart'
        },
        maintainAspectRatio: false
    }
});