<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Alerto - Disaster Management System</title>
  <!-- Google Fonts - Poppins -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- AOS Animation Library -->
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet" />
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            'poppins': ['Poppins', 'sans-serif'],
          },
          colors: {
            primary: {
              50: '#eef2ff',
              100: '#e0e7ff',
              400: '#818cf8',
              500: '#6366f1',
              600: '#4f46e5',
              700: '#4338ca',
            },
            secondary: {
              50: '#f8fafc',
              100: '#f1f5f9',
              600: '#475569',
              700: '#334155',
              800: '#1e293b',
              900: '#0f172a',
            }
          },
          animation: {
            'float': 'float 3s ease-in-out infinite',
            'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
            'bounce-slow': 'bounce 3s infinite',
            'slide-up': 'slideUp 0.5s ease-out',
          },
          keyframes: {
            float: {
              '0%, 100%': { transform: 'translateY(0)' },
              '50%': { transform: 'translateY(-10px)' },
            },
            slideUp: {
              '0%': { transform: 'translateY(20px)', opacity: '0' },
              '100%': { transform: 'translateY(0)', opacity: '1' },
            }
          }
        }
      }
    }
  </script>
  <style>
    body {
      font-family: 'Poppins', sans-serif;
      scroll-snap-type: y mandatory;
      overflow-y: scroll;
    }
    section {
      scroll-snap-align: start;
    }
  </style>
</head>
<body class="bg-gradient-to-br from-primary-50 to-white min-h-screen">

  <!-- Header/Navigation -->
  <header class="fixed w-full z-50 transition-all duration-300 header-blur" id="main-header">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-end items-center h-20">
        <div class="flex items-center space-x-4">
          @auth
            <a href="{{ route('logout') }}"
               class="bg-white rounded-full text-red-500 px-5 py-2 hover:bg-red-50 transition-all duration-200 flex items-center justify-center transform hover:scale-110 font-medium">
              Logout
            </a>
          @else
            <a href="{{ route('login') }}"
               class="bg-white rounded-full text-blue-700 px-5 py-2 hover:bg-blue-50 transition-all duration-200 flex items-center justify-center transform hover:scale-110 font-medium">
              Login
            </a>
          @endauth
        </div>
      </div>
    </div>
  </header>

  <!-- Hero Section -->
<section class="relative pt-16 pb-16 overflow-hidden bg-white min-h-screen flex items-center snap-start">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative w-full">
    <div class="flex flex-col items-center justify-center text-center">

      <div class="w-full flex flex-col items-center justify-center mt-6 mb-0" data-aos="zoom-in" data-aos-duration="1000">
        <img src="/image/ALERTO Logo.png" alt="ALERTO Mobile App"
             class="mx-auto max-h-[250px] w-auto transition duration-300 hover:scale-110 transform scale-150 bg-transparent" />
      </div>

      <div class="w-full max-w-5xl text-center mt-2 mb-6" data-aos="fade-up" data-aos-duration="1000">
        <h2 class="text-4xl sm:text-5xl lg:text-6xl text-red-600 font-extrabold font-poppins leading-tight">
          Assessing Locations Ensuring Rapid Tactical Operations
        </h2>
      </div>

      <div class="w-full max-w-2xl mx-auto" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="200">
        <p class="text-lg sm:text-xl text-gray-700 mb-8 leading-relaxed text-center">
          Stay safe with real-time alerts, smart evacuation mapping, and community-driven disaster response. Join thousands of users who trust ALERTO for their safety.
        </p>
      </div>

      <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="400">
        @guest
          <a href="{{ route('login') }}"
             class="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-3 rounded-xl inline-flex items-center justify-center group shadow-md transform transition-all duration-200 hover:scale-105 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-400">
            Get Started
            <i class="fas fa-arrow-right ml-2 transform group-hover:translate-x-1 transition-transform"></i>
          </a>
          <a href="#features"
             class="bg-white text-blue-600 font-semibold px-8 py-3 rounded-xl inline-flex items-center justify-center group shadow-md transform transition-all duration-200 hover:bg-blue-50 hover:text-blue-700 hover:scale-105 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-400">
            Learn More
            <i class="fas fa-chevron-down ml-2 transform group-hover:translate-y-1 transition-transform"></i>
          </a>
        @endguest
        @auth
          <a href="{{ route('components.dashboard') }}"
             class="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-3 rounded-xl inline-flex items-center justify-center group shadow-md transform transition-all duration-200 hover:scale-105 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-400">
            Go to Dashboard
            <i class="fas fa-arrow-right ml-2 transform group-hover:translate-x-1 transition-transform"></i>
          </a>
        @endauth
      </div>

    </div>
  </div>
</section>


<section id="features" class="py-16 bg-gray-50 min-h-screen flex items-center snap-start"> 
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12">
        <h2 class="text-4xl font-bold text-gray-800 mb-4 font-poppins" data-aos="fade-up">
          Key Features
        </h2>
        <p class="text-center text-gray-600 max-w-2xl mx-auto font-poppins" data-aos="fade-up">
          Discover the powerful features of ALERTO.
        </p>
      </div>
      <div class="grid gap-8 lg:grid-cols-3 md:grid-cols-2 sm:grid-cols-1">

      <!-- Feature Box 1 -->
      <div class="bg-white/70 backdrop-blur-md border border-sky-200 shadow-xl rounded-2xl transition hover:scale-105 hover:shadow-2xl p-8" data-aos="fade-up" data-aos-delay="100">
        <div class="flex items-start gap-4 mb-4">
          <div class="flex-shrink-0 w-12 h-12 bg-red-600 rounded-lg flex items-center justify-center text-white text-2xl shadow-lg">
            <i class="fas fa-bell"></i>
          </div>
          <h3 class="text-xl font-semibold text-gray-800 font-poppins">Real-Time Alerts</h3>
        </div>
        <p class="text-gray-600 font-poppins">Stay informed with instant updates during emergencies.</p>
      </div>

      <!-- Feature Box 2 -->
      <div class="bg-white/70 backdrop-blur-md border border-sky-200 shadow-xl rounded-2xl transition hover:scale-105 hover:shadow-2xl p-8" data-aos="fade-up" data-aos-delay="200">
        <div class="flex items-start gap-4 mb-4">
          <div class="flex-shrink-0 w-12 h-12 bg-orange-600 rounded-lg flex items-center justify-center text-white text-2xl shadow-lg">
            <i class="fas fa-map-marker-alt"></i>
          </div>
          <h3 class="text-xl font-semibold text-gray-800 font-poppins">Smart Evacuation Mapping</h3>
        </div>
        <p class="text-gray-600 font-poppins">Quickly locate safe evacuation routes and centers.</p>
      </div>

      <!-- Feature Box 3 -->
      <div class="bg-white/70 backdrop-blur-md border border-sky-200 shadow-xl rounded-2xl transition hover:scale-105 hover:shadow-2xl p-8" data-aos="fade-up" data-aos-delay="300">
        <div class="flex items-start gap-4 mb-4">
          <div class="flex-shrink-0 w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center text-white text-2xl shadow-lg">
            <i class="fas fa-users"></i>
          </div>
          <h3 class="text-xl font-semibold text-gray-800 font-poppins">Community Collaboration</h3>
        </div>
        <p class="text-gray-600 font-poppins">Work together with your community for disaster readiness.</p>
      </div>

    </div>
  </div>
</section>

 
  <div class="py-20 bg-gradient-to-br from-gray-50 to-white snap-start min-h-screen flex items-center">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <h2 class="text-4xl font-bold text-center text-gray-800 mb-4 font-poppins" data-aos="fade-up">
        Emergency Response Protocol
      </h2>
      <p class="text-center text-gray-600 mb-16 max-w-2xl mx-auto font-poppins" data-aos="fade-up">
        Follow these essential steps to ensure your safety during emergencies
      </p>
      
      <div class="max-w-4xl mx-auto space-y-6">
        <div class="emergency-step flex items-start gap-6 bg-white/70 backdrop-blur-md border border-sky-200 shadow-xl rounded-2xl transition hover:scale-105 hover:shadow-2xl p-6" data-aos="fade-up" data-aos-delay="100">
          <div class="flex-shrink-0 w-12 h-12 bg-red-600 rounded-lg flex items-center justify-center text-white text-xl font-bold shadow-lg">1</div>
          <div class="flex-grow">
            <div class="flex items-center gap-3 mb-3">
              <i class="fas fa-user-shield text-2xl text-red-600"></i>
              <h3 class="text-xl font-semibold text-gray-800">Emergency Profile Setup</h3>
            </div>
            <p class="text-gray-600">Create your emergency profile to receive critical alerts and access evacuation information.</p>
          </div>
        </div>

        <div class="emergency-step flex items-start gap-6 bg-white/70 backdrop-blur-md border border-sky-200 shadow-xl rounded-2xl transition hover:scale-105 hover:shadow-2xl p-6" data-aos="fade-up" data-aos-delay="200">
          <div class="flex-shrink-0 w-12 h-12 bg-orange-600 rounded-lg flex items-center justify-center text-white text-xl font-bold shadow-lg">2</div>
          <div class="flex-grow">
            <div class="flex items-center gap-3 mb-3">
              <i class="fas fa-map-marker-alt text-2xl text-orange-600"></i>
              <h3 class="text-xl font-semibold text-gray-800">Location Registration</h3>
            </div>
            <p class="text-gray-600">Set your current location to receive targeted emergency alerts and find nearby evacuation centers.</p>
          </div>
        </div>

        <div class="emergency-step flex items-start gap-6 bg-white/70 backdrop-blur-md border border-sky-200 shadow-xl rounded-2xl transition hover:scale-105 hover:shadow-2xl p-6" data-aos="fade-up" data-aos-delay="300">
          <div class="flex-shrink-0 w-12 h-12 bg-yellow-600 rounded-lg flex items-center justify-center text-white text-xl font-bold shadow-lg">3</div>
          <div class="flex-grow">
            <div class="flex items-center gap-3 mb-3">
              <i class="fas fa-bell text-2xl text-yellow-600"></i>
              <h3 class="text-xl font-semibold text-gray-800">Emergency Alerts</h3>
            </div>
            <p class="text-gray-600">Receive real-time notifications about disasters, evacuation orders, and safety instructions.</p>
          </div>
        </div>

        <div class="emergency-step flex items-start gap-6 bg-white/70 backdrop-blur-md border border-sky-200 shadow-xl rounded-2xl transition hover:scale-105 hover:shadow-2xl p-6" data-aos="fade-up" data-aos-delay="400">
          <div class="flex-shrink-0 w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center text-white text-xl font-bold shadow-lg">4</div>
          <div class="flex-grow">
            <div class="flex items-center gap-3 mb-3">
              <i class="fas fa-shield-alt text-2xl text-green-600"></i>
              <h3 class="text-xl font-semibold text-gray-800">Safety Protocol</h3>
            </div>
            <p class="text-gray-600">Follow emergency guidelines, access evacuation routes, and stay informed with critical updates.</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  

  <!-- Footer -->
  <footer class="bg-secondary-900 text-white py-16">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-12">
        <!-- About Section -->
        <div class="col-span-1 md:col-span-2">
          <h4 class="text-2xl font-bold mb-4 font-poppins">About Alerto</h4>
          <p class="text-secondary-400 leading-relaxed font-poppins mb-6">
            ALERTO is a comprehensive disaster management system designed to help communities stay informed and prepared during emergencies. It provides real-time alerts, evacuation guidance, and essential communication tools to ensure safety and quick response.
          </p>
          <div class="flex space-x-4">
            <a href="#" class="text-secondary-400 hover:text-white transition-colors">
              <i class="fab fa-facebook text-2xl"></i>
            </a>
            <a href="#" class="text-secondary-400 hover:text-white transition-colors">
              <i class="fab fa-twitter text-2xl"></i>
            </a>
            <a href="#" class="text-secondary-400 hover:text-white transition-colors">
              <i class="fab fa-instagram text-2xl"></i>
            </a>
            <a href="#" class="text-secondary-400 hover:text-white transition-colors">
              <i class="fab fa-linkedin text-2xl"></i>
            </a>
          </div>
        </div>

        
        <!-- Contact Section -->
        <div>
          <h4 class="text-xl font-bold mb-4 font-poppins">Contact Us</h4>
          <ul class="space-y-2 text-secondary-400 font-poppins">
            <li class="flex items-center gap-2">
              <i class="fas fa-envelope"></i>
              <a href="mailto:<EMAIL>" class="hover:text-white transition-colors"><EMAIL></a>
            </li>
            <li class="flex items-center gap-2">
              <i class="fas fa-phone"></i>
              <span>+63 ************</span>
            </li>
            <li class="flex items-center gap-2">
              <i class="fas fa-map-marker-alt"></i>
              <span>Manila, Philippines</span>
            </li>
          </ul>
        </div>
      </div>

      <!-- Divider -->
      <div class="border-t border-secondary-700 mt-12 pt-8 text-center text-sm text-secondary-500 font-poppins">
        &copy; 2024 ALERTO. All rights reserved.
      </div>
    </div>
  </footer>

  <script>
    AOS.init({
      duration: 800,
      once: false,
      offset: 100
    });
  </script>
  <script>
    window.addEventListener('load', function() {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    });
  </script>
</body>
</html>