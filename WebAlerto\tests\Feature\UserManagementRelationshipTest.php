<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\UserManagementRequest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;

class UserManagementRelationshipTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that User model has managementRequests relationship
     */
    public function test_user_has_management_requests_relationship()
    {
        // Create a user
        $user = User::create([
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'position' => 'Chairman',
            'barangay' => 'Test Barangay',
            'status' => 'Active',
        ]);

        // Create another user to be the target
        $targetUser = User::create([
            'first_name' => 'Target',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'position' => 'Officer',
            'barangay' => 'Test Barangay',
            'status' => 'Active',
        ]);

        // Create a management request
        $request = UserManagementRequest::create([
            'requester_id' => $user->id,
            'target_user_id' => $targetUser->id,
            'action_type' => 'deactivate',
            'reason' => 'Test reason',
            'status' => 'pending',
        ]);

        // Test the relationship
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $user->managementRequests);
        $this->assertEquals(1, $user->managementRequests->count());
        $this->assertEquals($request->id, $user->managementRequests->first()->id);
    }

    /**
     * Test that User model has targetedRequests relationship
     */
    public function test_user_has_targeted_requests_relationship()
    {
        // Create users
        $requester = User::create([
            'first_name' => 'Requester',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'position' => 'Chairman',
            'barangay' => 'Test Barangay',
            'status' => 'Active',
        ]);

        $targetUser = User::create([
            'first_name' => 'Target',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'position' => 'Officer',
            'barangay' => 'Test Barangay',
            'status' => 'Active',
        ]);

        // Create a management request targeting the user
        $request = UserManagementRequest::create([
            'requester_id' => $requester->id,
            'target_user_id' => $targetUser->id,
            'action_type' => 'deactivate',
            'reason' => 'Test reason',
            'status' => 'pending',
        ]);

        // Test the relationship
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $targetUser->targetedRequests);
        $this->assertEquals(1, $targetUser->targetedRequests->count());
        $this->assertEquals($request->id, $targetUser->targetedRequests->first()->id);
    }

    /**
     * Test that User model has reviewedRequests relationship
     */
    public function test_user_has_reviewed_requests_relationship()
    {
        // Create users
        $requester = User::create([
            'first_name' => 'Requester',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'position' => 'Chairman',
            'barangay' => 'Test Barangay',
            'status' => 'Active',
        ]);

        $targetUser = User::create([
            'first_name' => 'Target',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'position' => 'Officer',
            'barangay' => 'Test Barangay',
            'status' => 'Active',
        ]);

        $reviewer = User::create([
            'first_name' => 'Reviewer',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'super_admin',
            'position' => 'SuperAdmin',
            'status' => 'Active',
        ]);

        // Create a management request reviewed by the user
        $request = UserManagementRequest::create([
            'requester_id' => $requester->id,
            'target_user_id' => $targetUser->id,
            'action_type' => 'deactivate',
            'reason' => 'Test reason',
            'status' => 'approved',
            'reviewed_by' => $reviewer->id,
            'review_notes' => 'Approved for testing',
            'reviewed_at' => now(),
        ]);

        // Test the relationship
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $reviewer->reviewedRequests);
        $this->assertEquals(1, $reviewer->reviewedRequests->count());
        $this->assertEquals($request->id, $reviewer->reviewedRequests->first()->id);
    }

    /**
     * Test System Admin user management view loads without errors
     */
    public function test_system_admin_user_management_view_loads()
    {
        // Create a system admin user
        $systemAdmin = User::create([
            'first_name' => 'System',
            'last_name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'system_admin',
            'position' => 'Technical Administrator',
            'status' => 'Active',
        ]);

        // Create some test users
        User::create([
            'first_name' => 'Test',
            'last_name' => 'User1',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'super_admin',
            'position' => 'CDRRMC Director',
            'city' => 'Cebu City',
            'status' => 'Active',
        ]);

        User::create([
            'first_name' => 'Test',
            'last_name' => 'User2',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'position' => 'Barangay Chairman',
            'city' => 'Cebu City',
            'barangay' => 'Lahug',
            'status' => 'Active',
        ]);

        // Test the user management page loads
        $response = $this->actingAs($systemAdmin)
            ->get('/system-admin/user-management');

        $response->assertStatus(200);
        $response->assertViewIs('components.system-admin.user-management');
        $response->assertViewHas('users');
    }
}
