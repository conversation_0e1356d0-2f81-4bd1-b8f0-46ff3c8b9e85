import {
  __async
} from "./chunk-EAE2VPRF.js";

// node_modules/@capacitor/synapse/dist/synapse.mjs
function s(t) {
  t.CapacitorUtils.Synapse = new Proxy({}, {
    get(e, o) {
      return new Proxy({}, {
        get(w, r) {
          return (c, p, n) => {
            const i = t.Capacitor.Plugins[o];
            if (i === void 0) {
              n(new Error(`Capacitor plugin ${o} not found`));
              return;
            }
            if (typeof i[r] != "function") {
              n(new Error(`Method ${r} not found in Capacitor plugin ${o}`));
              return;
            }
            (() => __async(this, null, function* () {
              try {
                const a = yield i[r](c);
                p(a);
              } catch (a) {
                n(a);
              }
            }))();
          };
        }
      });
    }
  });
}
function u(t) {
  t.CapacitorUtils.Synapse = new Proxy({}, {
    get(e, o) {
      return t.cordova.plugins[o];
    }
  });
}
function y(t = false) {
  window.CapacitorUtils = window.CapacitorUtils || {}, window.Capacitor !== void 0 && !t ? s(window) : window.cordova !== void 0 && u(window);
}

export {
  y
};
//# sourceMappingURL=chunk-LVBQUHDE.js.map
