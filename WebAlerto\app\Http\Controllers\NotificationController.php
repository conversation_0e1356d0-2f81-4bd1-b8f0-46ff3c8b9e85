<?php
namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\DeviceToken;
use App\Models\Barangay;
use App\Services\FCMService;
use App\Services\BarangayService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

class NotificationController extends Controller
{
    protected $fcmService;
    protected $barangayService;

    public function __construct(FCMService $fcmService, BarangayService $barangayService)
    {
        $this->fcmService = $fcmService;
        $this->barangayService = $barangayService;
    }

    // List all notifications
    public function index(Request $request)
    {
        $user = Auth::user();
        $selectedCity = $request->input('city');
        $selectedBarangay = $request->input('barangay');
        $searchQuery = $request->input('search');

        // Get list of accessible cities and barangays for filter dropdown
        $cities = [];
        $barangays = [];

        if ($user->hasRole('super_admin')) {
            // CDRRMC: get barangays in their city/municipality
            $barangays = $this->barangayService->getAccessibleBarangays($user);
        } elseif ($user->hasRole('system_admin')) {
            // System Admin: get all cities and barangays province-wide
            $cities = $this->barangayService->getAllProvincialCities();
            if ($selectedCity) {
                $barangays = $this->barangayService->getBarangaysByCity($selectedCity);
            } else {
                $barangays = $this->barangayService->getAllProvincialBarangays();
            }
        }

        // Base query
        $query = Notification::query();

        // Apply role-based filtering
        if ($user->hasRole('admin')) {
            // BDRRMC users: only their barangay
            $query->where('barangay', $user->barangay);
        } elseif ($user->hasRole('super_admin')) {
            // CDRRMC users: filter by selected barangay or all barangays in their city
            if ($selectedBarangay) {
                $query->where('barangay', $selectedBarangay);
            } else {
                // Filter by barangays in their city
                $barangayService = app(\App\Services\BarangayService::class);
                $accessibleBarangays = $barangayService->getAccessibleBarangays($user);
                if (!empty($accessibleBarangays)) {
                    $query->whereIn('barangay', $accessibleBarangays);
                }
            }
        } elseif ($user->hasRole('system_admin')) {
            // System Admin: province-wide access, filter by barangay only
            // Note: City filtering not supported as notifications table doesn't have city column
            if ($selectedBarangay) {
                $query->where('barangay', $selectedBarangay);
            }
        }
        // System admin: no filter, see all notifications

        // Apply search filter if provided
        if ($searchQuery) {
            $query->where(function($q) use ($searchQuery) {
                $q->where('title', 'like', "%{$searchQuery}%")
                  ->orWhere('message', 'like', "%{$searchQuery}%")
                  ->orWhere('barangay', 'like', "%{$searchQuery}%");
            });
        }

        $notifications = $query->orderBy('created_at', 'desc')->paginate(10);
        
        // Group by month and category for the filtered notifications
        // Apply barangay filter if not admin/super_admin or if a barangay is selected by admin
        // Normalize "others:*" categories to just "others" for analytics
        $monthlyCategoryCountsQuery = Notification::selectRaw('
            DATE_FORMAT(created_at, "%Y-%m") as month,
            CASE
                WHEN category LIKE "others:%" THEN "others"
                ELSE category
            END as category,
            COUNT(*) as count
        ')
        ->groupBy('month', 'category')
        ->orderBy('month', 'asc');

        // Apply same filtering logic as main query
        if ($user->hasRole('admin')) {
            $monthlyCategoryCountsQuery->where('barangay', $user->barangay);
        } elseif ($user->hasRole('super_admin')) {
            if ($selectedBarangay) {
                $monthlyCategoryCountsQuery->where('barangay', $selectedBarangay);
            } else {
                $accessibleBarangays = $this->barangayService->getAccessibleBarangays($user);
                if (!empty($accessibleBarangays)) {
                    $monthlyCategoryCountsQuery->whereIn('barangay', $accessibleBarangays);
                }
            }
        } elseif ($user->hasRole('system_admin')) {
            // System Admin: province-wide access, filter by barangay only
            if ($selectedBarangay) {
                $monthlyCategoryCountsQuery->where('barangay', $selectedBarangay);
            }
        }

        $monthlyCategoryCounts = $monthlyCategoryCountsQuery->get();

        // Get recent alerts for the sidebar
        // Apply barangay filter if not admin/super_admin or if a barangay is selected by admin
        $recentAlertsQuery = Notification::orderBy('created_at', 'desc')
            ->take(3);

        // Apply same filtering logic as main query
        if ($user->hasRole('admin')) {
            $recentAlertsQuery->where('barangay', $user->barangay);
        } elseif ($user->hasRole('super_admin')) {
            if ($selectedBarangay) {
                $recentAlertsQuery->where('barangay', $selectedBarangay);
            } else {
                $accessibleBarangays = $this->barangayService->getAccessibleBarangays($user);
                if (!empty($accessibleBarangays)) {
                    $recentAlertsQuery->whereIn('barangay', $accessibleBarangays);
                }
            }
        } elseif ($user->hasRole('system_admin')) {
            // System Admin: province-wide access, filter by barangay only
            if ($selectedBarangay) {
                $recentAlertsQuery->where('barangay', $selectedBarangay);
            }
        }

        $recentAlerts = $recentAlertsQuery->get()
            ->map(function ($notification) {
                return (object) [
                    'type' => $notification->category,
                    'message' => $notification->title,
                    'location' => $notification->barangay,
                    'time_ago' => $notification->created_at->diffForHumans(),
                ];
            });

        // Pass filter variables to the view
        return view('components.notification.index', compact('notifications', 'monthlyCategoryCounts', 'recentAlerts', 'cities', 'barangays', 'selectedCity', 'selectedBarangay', 'searchQuery'));
    }

    public function view($id)
    {
        $notification = Notification::find($id);
        return view('components.notification.create', compact('notification'));
    }

    // Show the form to create a new notification
    public function create()
    {
        $user = Auth::user();

        // No barangay selection needed - notifications go to all users
        return view('components.notification.create');
    }

    // Store a new notification
    public function store(Request $request)
    {
        $user = Auth::user();

        // Set barangay based on user role and city hierarchy
        if ($user->hasRole('system_admin')) {
            // System admin notifications go to all areas
            $request->merge(['barangay' => 'All Areas']);
        } elseif ($user->hasRole('super_admin')) {
            // CDRRMC notifications go to their city areas
            $request->merge(['barangay' => $user->city ?? 'All Areas']);
        } else {
            // BDRRMC users restricted to their barangay
            $request->merge(['barangay' => $user->barangay]);
        }

        // Validate form
        $validationRules = [
            'title' => 'required|string|max:255',
            'category' => 'required|string|in:typhoon,flood,fire,earthquake,landslide,others',
            'message' => 'required|string',
            'severity' => 'required|string|in:low,medium,high',
            'send_push_notification' => 'required|in:1', // Always send push notifications
            'target_devices' => 'required|string|in:all', // Always send to all devices
            'custom_disaster_type' => 'nullable|required_if:category,others|string|max:255',
        ];

        $request->validate($validationRules);

        // Handle custom disaster type for "others" category
        $category = $request->category;
        if ($category === 'others' && !empty($request->custom_disaster_type)) {
            $category = 'others:' . trim($request->custom_disaster_type);
        }

        // Create notification in database
        $notification = Notification::create([
            'title' => $request->title,
            'category' => $category,
            'message' => $request->message,
            'severity' => $request->severity,
            'sent' => false,
            'barangay' => $request->barangay,
            'user_id' => $user->id // Save the user who created it
        ]);

        // Always send push notifications to all users
        $targetDevices = 'all'; // Always send to all devices

        try {
            // Get device tokens based on target devices only (send to ALL users regardless of barangay)
            $tokenQuery = DeviceToken::where('is_active', true);

            // Filter by device type if not 'all'
            if ($targetDevices !== 'all') {
                $tokenQuery->where('device_type', $targetDevices);
            }

            $tokens = $tokenQuery->pluck('token')->toArray();

            if (empty($tokens)) {
                return redirect()->route('components.notification.index')
                    ->with('warning', "Notification created but no active device tokens found. Please ensure mobile app users are connected.");
            }

            // Send notification using FCM service
            $result = $this->fcmService->sendNotification($notification, $tokens);

            if (!$result['success']) {
                return redirect()->route('components.notification.index')
                    ->with('warning', 'Notification created but ' . $result['message']);
            }

            // Mark notification as sent
            $notification->update(['sent' => true]);

            return redirect()->route('components.notification.index')
                ->with('success', "Notification created and sent to {$result['success_count']} users" .
                    ($result['failure_count'] > 0 ? " ({$result['failure_count']} failures)" : ""));
        } catch (\Exception $e) {
            Log::error('Failed to send notification', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => $user->id,
                'admin_barangay' => $user->barangay,
                'target_devices' => $targetDevices,
                'notification_id' => $notification->id
            ]);

            return redirect()->route('components.notification.index')
                ->with('error', 'Notification created but failed to send: ' . $e->getMessage());
        }
    }
}
