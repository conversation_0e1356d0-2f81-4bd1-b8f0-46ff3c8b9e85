import{b as E}from"./chunk-3J7GGTVR.js";import{c as P,d as x}from"./chunk-ICWJVXBH.js";import{h as d}from"./chunk-B7O3QC5Z.js";function R(f){let g=f.split("/").filter(r=>r!=="."),e=[];return g.forEach(r=>{r===".."&&e.length>0&&e[e.length-1]!==".."?e.pop():e.push(r)}),e.join("/")}function q(f,g){f=R(f),g=R(g);let e=f.split("/"),r=g.split("/");return f!==g&&e.every((t,i)=>t===r[i])}var D=(()=>{class f extends P{constructor(){super(...arguments),this.DB_VERSION=1,this.DB_NAME="Disc",this._writeCmds=["add","put","delete"],this.downloadFile=e=>d(this,null,function*(){var r,t;let i=x(e,e.webFetchExtra),n=yield fetch(e.url,i),s;if(!e.progress)s=yield n.blob();else if(!n?.body)s=new Blob;else{let c=n.body.getReader(),o=0,h=[],l=n.headers.get("content-type"),w=parseInt(n.headers.get("content-length")||"0",10);for(;;){let{done:p,value:b}=yield c.read();if(p)break;h.push(b),o+=b?.length||0;let m={url:e.url,bytes:o,contentLength:w};this.notifyListeners("progress",m)}let y=new Uint8Array(o),u=0;for(let p of h)typeof p>"u"||(y.set(p,u),u+=p.length);s=new Blob([y.buffer],{type:l||void 0})}return{path:(yield this.writeFile({path:e.path,directory:(r=e.directory)!==null&&r!==void 0?r:void 0,recursive:(t=e.recursive)!==null&&t!==void 0?t:!1,data:s})).uri,blob:s}})}readFileInChunks(e,r){throw this.unavailable("Method not implemented.")}initDb(){return d(this,null,function*(){if(this._db!==void 0)return this._db;if(!("indexedDB"in window))throw this.unavailable("This browser doesn't support IndexedDB");return new Promise((e,r)=>{let t=indexedDB.open(this.DB_NAME,this.DB_VERSION);t.onupgradeneeded=f.doUpgrade,t.onsuccess=()=>{this._db=t.result,e(t.result)},t.onerror=()=>r(t.error),t.onblocked=()=>{console.warn("db blocked")}})})}static doUpgrade(e){let t=e.target.result;switch(e.oldVersion){case 0:case 1:default:t.objectStoreNames.contains("FileStorage")&&t.deleteObjectStore("FileStorage"),t.createObjectStore("FileStorage",{keyPath:"path"}).createIndex("by_folder","folder")}}dbRequest(e,r){return d(this,null,function*(){let t=this._writeCmds.indexOf(e)!==-1?"readwrite":"readonly";return this.initDb().then(i=>new Promise((n,s)=>{let o=i.transaction(["FileStorage"],t).objectStore("FileStorage")[e](...r);o.onsuccess=()=>n(o.result),o.onerror=()=>s(o.error)}))})}dbIndexRequest(e,r,t){return d(this,null,function*(){let i=this._writeCmds.indexOf(r)!==-1?"readwrite":"readonly";return this.initDb().then(n=>new Promise((s,a)=>{let l=n.transaction(["FileStorage"],i).objectStore("FileStorage").index(e)[r](...t);l.onsuccess=()=>s(l.result),l.onerror=()=>a(l.error)}))})}getPath(e,r){let t=r!==void 0?r.replace(/^[/]+|[/]+$/g,""):"",i="";return e!==void 0&&(i+="/"+e),r!==""&&(i+="/"+t),i}clear(){return d(this,null,function*(){(yield this.initDb()).transaction(["FileStorage"],"readwrite").objectStore("FileStorage").clear()})}readFile(e){return d(this,null,function*(){let r=this.getPath(e.directory,e.path),t=yield this.dbRequest("get",[r]);if(t===void 0)throw Error("File does not exist.");return{data:t.content?t.content:""}})}writeFile(e){return d(this,null,function*(){let r=this.getPath(e.directory,e.path),t=e.data,i=e.encoding,n=e.recursive,s=yield this.dbRequest("get",[r]);if(s&&s.type==="directory")throw Error("The supplied path is a directory.");let a=r.substr(0,r.lastIndexOf("/"));if((yield this.dbRequest("get",[a]))===void 0){let l=a.indexOf("/",1);if(l!==-1){let w=a.substr(l);yield this.mkdir({path:w,directory:e.directory,recursive:n})}}if(!i&&!(t instanceof Blob)&&(t=t.indexOf(",")>=0?t.split(",")[1]:t,!this.isBase64String(t)))throw Error("The supplied data is not valid base64 content.");let o=Date.now(),h={path:r,folder:a,type:"file",size:t instanceof Blob?t.size:t.length,ctime:o,mtime:o,content:t};return yield this.dbRequest("put",[h]),{uri:h.path}})}appendFile(e){return d(this,null,function*(){let r=this.getPath(e.directory,e.path),t=e.data,i=e.encoding,n=r.substr(0,r.lastIndexOf("/")),s=Date.now(),a=s,c=yield this.dbRequest("get",[r]);if(c&&c.type==="directory")throw Error("The supplied path is a directory.");if((yield this.dbRequest("get",[n]))===void 0){let l=n.indexOf("/",1);if(l!==-1){let w=n.substr(l);yield this.mkdir({path:w,directory:e.directory,recursive:!0})}}if(!i&&!this.isBase64String(t))throw Error("The supplied data is not valid base64 content.");if(c!==void 0){if(c.content instanceof Blob)throw Error("The occupied entry contains a Blob object which cannot be appended to.");c.content!==void 0&&!i?t=btoa(atob(c.content)+atob(t)):t=c.content+t,a=c.ctime}let h={path:r,folder:n,type:"file",size:t.length,ctime:a,mtime:s,content:t};yield this.dbRequest("put",[h])})}deleteFile(e){return d(this,null,function*(){let r=this.getPath(e.directory,e.path);if((yield this.dbRequest("get",[r]))===void 0)throw Error("File does not exist.");if((yield this.dbIndexRequest("by_folder","getAllKeys",[IDBKeyRange.only(r)])).length!==0)throw Error("Folder is not empty.");yield this.dbRequest("delete",[r])})}mkdir(e){return d(this,null,function*(){let r=this.getPath(e.directory,e.path),t=e.recursive,i=r.substr(0,r.lastIndexOf("/")),n=(r.match(/\//g)||[]).length,s=yield this.dbRequest("get",[i]),a=yield this.dbRequest("get",[r]);if(n===1)throw Error("Cannot create Root directory");if(a!==void 0)throw Error("Current directory does already exist.");if(!t&&n!==2&&s===void 0)throw Error("Parent directory must exist");if(t&&n!==2&&s===void 0){let h=i.substr(i.indexOf("/",1));yield this.mkdir({path:h,directory:e.directory,recursive:t})}let c=Date.now(),o={path:r,folder:i,type:"directory",size:0,ctime:c,mtime:c};yield this.dbRequest("put",[o])})}rmdir(e){return d(this,null,function*(){let{path:r,directory:t,recursive:i}=e,n=this.getPath(t,r),s=yield this.dbRequest("get",[n]);if(s===void 0)throw Error("Folder does not exist.");if(s.type!=="directory")throw Error("Requested path is not a directory");let a=yield this.readdir({path:r,directory:t});if(a.files.length!==0&&!i)throw Error("Folder is not empty");for(let c of a.files){let o=`${r}/${c.name}`;(yield this.stat({path:o,directory:t})).type==="file"?yield this.deleteFile({path:o,directory:t}):yield this.rmdir({path:o,directory:t,recursive:i})}yield this.dbRequest("delete",[n])})}readdir(e){return d(this,null,function*(){let r=this.getPath(e.directory,e.path),t=yield this.dbRequest("get",[r]);if(e.path!==""&&t===void 0)throw Error("Folder does not exist.");let i=yield this.dbIndexRequest("by_folder","getAllKeys",[IDBKeyRange.only(r)]);return{files:yield Promise.all(i.map(s=>d(this,null,function*(){let a=yield this.dbRequest("get",[s]);return a===void 0&&(a=yield this.dbRequest("get",[s+"/"])),{name:s.substring(r.length+1),type:a.type,size:a.size,ctime:a.ctime,mtime:a.mtime,uri:a.path}})))}})}getUri(e){return d(this,null,function*(){let r=this.getPath(e.directory,e.path),t=yield this.dbRequest("get",[r]);return t===void 0&&(t=yield this.dbRequest("get",[r+"/"])),{uri:t?.path||r}})}stat(e){return d(this,null,function*(){let r=this.getPath(e.directory,e.path),t=yield this.dbRequest("get",[r]);if(t===void 0&&(t=yield this.dbRequest("get",[r+"/"])),t===void 0)throw Error("Entry does not exist.");return{name:t.path.substring(r.length+1),type:t.type,size:t.size,ctime:t.ctime,mtime:t.mtime,uri:t.path}})}rename(e){return d(this,null,function*(){yield this._copy(e,!0)})}copy(e){return d(this,null,function*(){return this._copy(e,!1)})}requestPermissions(){return d(this,null,function*(){return{publicStorage:"granted"}})}checkPermissions(){return d(this,null,function*(){return{publicStorage:"granted"}})}_copy(e,r=!1){return d(this,null,function*(){let{toDirectory:t}=e,{to:i,from:n,directory:s}=e;if(!i||!n)throw Error("Both to and from must be provided");t||(t=s);let a=this.getPath(s,n),c=this.getPath(t,i);if(a===c)return{uri:c};if(q(a,c))throw Error("To path cannot contain the from path");let o;try{o=yield this.stat({path:i,directory:t})}catch{let u=i.split("/");u.pop();let p=u.join("/");if(u.length>0&&(yield this.stat({path:p,directory:t})).type!=="directory")throw new Error("Parent directory of the to path is a file")}if(o&&o.type==="directory")throw new Error("Cannot overwrite a directory with a file");let h=yield this.stat({path:n,directory:s}),l=(y,u,p)=>d(this,null,function*(){let b=this.getPath(t,y),m=yield this.dbRequest("get",[b]);m.ctime=u,m.mtime=p,yield this.dbRequest("put",[m])}),w=h.ctime?h.ctime:Date.now();switch(h.type){case"file":{let y=yield this.readFile({path:n,directory:s});r&&(yield this.deleteFile({path:n,directory:s}));let u;!(y.data instanceof Blob)&&!this.isBase64String(y.data)&&(u=E.UTF8);let p=yield this.writeFile({path:i,directory:t,data:y.data,encoding:u});return r&&(yield l(i,w,h.mtime)),p}case"directory":{if(o)throw Error("Cannot move a directory over an existing object");try{yield this.mkdir({path:i,directory:t,recursive:!1}),r&&(yield l(i,w,h.mtime))}catch{}let y=(yield this.readdir({path:n,directory:s})).files;for(let u of y)yield this._copy({from:`${n}/${u.name}`,to:`${i}/${u.name}`,directory:s,toDirectory:t},r);r&&(yield this.rmdir({path:n,directory:s}))}}return{uri:c}})}isBase64String(e){try{return btoa(atob(e))==e}catch{return!1}}}return f._debug=!0,f})();export{D as FilesystemWeb};
