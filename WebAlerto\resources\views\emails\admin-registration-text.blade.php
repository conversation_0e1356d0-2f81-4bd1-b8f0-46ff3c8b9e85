WebAlerto Admin Account - Access Granted

Dear {{ $user->first_name }} {{ $user->middle_name ? $user->middle_name . ' ' : '' }}{{ $user->last_name }},

Your administrator account for WebAlerto Emergency Management System has been created by the system administrator. You have been granted access to manage emergency alerts and evacuation resources{{ $user->barangay ? ' for ' . $user->barangay . ' Barangay' : '' }}{{ $user->city ? ' in ' . $user->city : '' }}.

This is an official system notification from your organization's emergency management team.

LOGIN CREDENTIALS:
==================
Email: {{ $user->email }}
Temporary Password: {{ $temporaryPassword }}
Role: {{ $user->getRoleDisplayName() }}
Position: {{ $user->position }}
@if($user->city)
City: {{ $user->city }}
@endif
@if($user->barangay)
Barangay: {{ $user->barangay }}
@endif

IMPORTANT SECURITY NOTICE:
=========================
- This is a temporary password. Please change it immediately after your first login.
- Do not share these credentials with anyone.
- Always log out when finished using the system.

ACCESS YOUR ACCOUNT:
===================
Login URL: {{ $loginUrl }}

NEED HELP?
==========
If you have any questions or need assistance, please contact the system administrator.

System: WebAlerto Emergency Management
Environment: {{ config('app.env') === 'production' ? 'Production' : 'Development' }}

---
This email was sent automatically by the WebAlerto system.
© {{ date('Y') }} WebAlerto Emergency Alert & Management System
