<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Models\Barangay;
use App\Services\PSGCService;
use Illuminate\Support\Facades\Log;

class ValidBarangay implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (empty($value)) {
            return; // Let required rule handle empty values
        }

        // First check if barangay exists in PSGC-connected database
        $exists = Barangay::where('name', $value)
                          ->where('status', true)
                          ->exists();

        if ($exists) {
            return; // Valid barangay found in database
        }

        // If not found in database, check against PSGC service as fallback (all Cebu Province)
        try {
            $psgcService = new PSGCService();
            $cities = $psgcService->getCebuProvinceCities();

            foreach ($cities as $city) {
                $cityBarangays = $psgcService->getCityBarangays($city['code'], $city['name']);
                $validBarangays = collect($cityBarangays)->pluck('name')->toArray();

                if (in_array($value, $validBarangays)) {
                    Log::warning("Barangay '{$value}' exists in PSGC ({$city['name']}) but not in database. Consider running barangay refresh.");
                    return; // Valid according to PSGC
                }
            }
        } catch (\Exception $e) {
            Log::error('Failed to validate barangay against PSGC service', [
                'barangay' => $value,
                'error' => $e->getMessage()
            ]);
        }

        $fail("The selected {$attribute} is not a valid barangay in Cebu Province.");
    }
}
