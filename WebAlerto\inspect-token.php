<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\DeviceToken;

echo "=== Token Inspection ===\n\n";

try {
    $activeToken = DeviceToken::where('is_active', true)->first();
    
    if (!$activeToken) {
        echo "❌ No active token found\n";
        exit(1);
    }
    
    echo "🔍 Active Token Details:\n";
    echo "   ID: {$activeToken->id}\n";
    echo "   Device Type: {$activeToken->device_type}\n";
    echo "   Project ID: {$activeToken->project_id}\n";
    echo "   User ID: " . ($activeToken->user_id ?? 'Anonymous') . "\n";
    echo "   Created: {$activeToken->created_at}\n";
    echo "   Last Used: {$activeToken->last_used_at}\n";
    echo "   Full Token: {$activeToken->token}\n\n";
    
    // Check if it's a test token
    if (strpos($activeToken->token, 'test-token') !== false) {
        echo "🚨 THIS IS A TEST TOKEN!\n";
        echo "   The mobile app is not generating real FCM tokens.\n";
        echo "   This means Firebase is not properly configured in the mobile app.\n\n";
        
        echo "🔧 SOLUTIONS:\n";
        echo "1. Check if google-services.json is properly placed in mobile_ionic/android/app/\n";
        echo "2. Verify the package name in google-services.json matches: com.webalerto.app\n";
        echo "3. Rebuild the mobile app: ionic capacitor sync android\n";
        echo "4. Check Android Studio Logcat for Firebase initialization errors\n";
        echo "5. Make sure the mobile app is running on a real device (not browser)\n\n";
    } else {
        echo "✅ This appears to be a real FCM token\n";
        echo "   Length: " . strlen($activeToken->token) . " characters\n";
        
        // Real FCM tokens are usually 152+ characters and contain specific patterns
        if (strlen($activeToken->token) > 150) {
            echo "   ✅ Token length looks correct for FCM\n";
        } else {
            echo "   ❌ Token too short for real FCM token\n";
        }
        
        if (preg_match('/^[a-zA-Z0-9:_\-]+$/', $activeToken->token)) {
            echo "   ✅ Token format looks valid\n";
        } else {
            echo "   ❌ Token contains invalid characters\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== Inspection Complete ===\n";
