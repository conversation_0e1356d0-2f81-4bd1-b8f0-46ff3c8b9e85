<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Services\OrganizationalPositionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;

class OrganizationalPositionTest extends TestCase
{
    use RefreshDatabase;

    protected $systemAdmin;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a system administrator for testing
        $this->systemAdmin = User::create([
            'first_name' => 'System',
            'last_name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'system_admin',
            'position' => 'Technical Administrator',
            'status' => 'Active',
        ]);
    }

    /**
     * Test OrganizationalPositionService returns positions for all roles
     */
    public function test_organizational_position_service_returns_all_positions()
    {
        $allPositions = OrganizationalPositionService::getAllPositions();
        
        $this->assertIsArray($allPositions);
        $this->assertArrayHasKey('system_admin', $allPositions);
        $this->assertArrayHasKey('super_admin', $allPositions);
        $this->assertArrayHasKey('admin', $allPositions);
    }

    /**
     * Test system admin positions
     */
    public function test_system_admin_positions()
    {
        $positions = OrganizationalPositionService::getSystemAdminPositions();
        
        $this->assertIsArray($positions);
        $this->assertArrayHasKey('Technical Administrator', $positions);
        $this->assertArrayHasKey('System Administrator', $positions);
        $this->assertArrayHasKey('IT Manager', $positions);
    }

    /**
     * Test CDRRMC positions
     */
    public function test_cdrrmc_positions()
    {
        $positions = OrganizationalPositionService::getCDRRMCPositions();
        
        $this->assertIsArray($positions);
        $this->assertArrayHasKey('CDRRMC Chairperson', $positions);
        $this->assertArrayHasKey('City Mayor', $positions);
        $this->assertArrayHasKey('Fire Chief', $positions);
        $this->assertArrayHasKey('CDRRMC Operations Manager', $positions);
    }

    /**
     * Test BDRRMC positions
     */
    public function test_bdrrmc_positions()
    {
        $positions = OrganizationalPositionService::getBDRRMCPositions();
        
        $this->assertIsArray($positions);
        $this->assertArrayHasKey('BDRRMC Chairperson', $positions);
        $this->assertArrayHasKey('Barangay Captain', $positions);
        $this->assertArrayHasKey('Emergency Response Team Leader', $positions);
        $this->assertArrayHasKey('Barangay Health Worker', $positions);
    }

    /**
     * Test position validation for specific roles
     */
    public function test_position_validation_for_roles()
    {
        // Test valid positions
        $this->assertTrue(OrganizationalPositionService::isValidPositionForRole('Technical Administrator', 'system_admin'));
        $this->assertTrue(OrganizationalPositionService::isValidPositionForRole('CDRRMC Chairperson', 'super_admin'));
        $this->assertTrue(OrganizationalPositionService::isValidPositionForRole('Barangay Captain', 'admin'));
        
        // Test invalid positions
        $this->assertFalse(OrganizationalPositionService::isValidPositionForRole('Barangay Captain', 'system_admin'));
        $this->assertFalse(OrganizationalPositionService::isValidPositionForRole('Technical Administrator', 'admin'));
    }

    /**
     * Test getting positions for specific role via API
     */
    public function test_get_positions_for_role_api()
    {
        $response = $this->actingAs($this->systemAdmin)
            ->post('/system-admin/get-positions', [
                'role' => 'super_admin'
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        
        $data = $response->json();
        $this->assertArrayHasKey('categories', $data);
        $this->assertIsArray($data['categories']);
    }

    /**
     * Test non-system admin cannot access positions API
     */
    public function test_non_system_admin_cannot_access_positions_api()
    {
        $regularUser = User::create([
            'first_name' => 'Regular',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'position' => 'Barangay Captain',
            'status' => 'Active',
        ]);

        $response = $this->actingAs($regularUser)
            ->post('/system-admin/get-positions', [
                'role' => 'admin'
            ]);

        $response->assertStatus(403);
    }

    /**
     * Test user creation with legitimate position
     */
    public function test_user_creation_with_legitimate_position()
    {
        $userData = [
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'position' => 'Barangay Captain', // Legitimate BDRRMC position
            'city' => 'Cebu City',
            'barangay' => 'Lahug',
        ];

        $response = $this->actingAs($this->systemAdmin)
            ->post('/system-admin/create-user', $userData);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify user was created with correct position
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'position' => 'Barangay Captain',
            'role' => 'admin'
        ]);
    }

    /**
     * Test user creation with custom position
     */
    public function test_user_creation_with_custom_position()
    {
        $userData = [
            'first_name' => 'Custom',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'position' => 'Custom Emergency Coordinator', // Custom position
            'city' => 'Cebu City',
            'barangay' => 'Lahug',
        ];

        $response = $this->actingAs($this->systemAdmin)
            ->post('/system-admin/create-user', $userData);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify user was created with custom position
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'position' => 'Custom Emergency Coordinator',
            'role' => 'admin'
        ]);
    }

    /**
     * Test position categories structure
     */
    public function test_position_categories_structure()
    {
        $categories = OrganizationalPositionService::getPositionCategories('admin');
        
        $this->assertIsArray($categories);
        $this->assertArrayHasKey('Executive Level', $categories);
        $this->assertArrayHasKey('BDRRMC Officers', $categories);
        $this->assertArrayHasKey('Emergency Response Teams', $categories);
        
        // Check that each category contains positions
        foreach ($categories as $categoryName => $positions) {
            $this->assertIsArray($positions);
            $this->assertNotEmpty($positions);
        }
    }

    /**
     * Test flat positions array
     */
    public function test_flat_positions_array()
    {
        $flatPositions = OrganizationalPositionService::getAllPositionsFlat();
        
        $this->assertIsArray($flatPositions);
        $this->assertNotEmpty($flatPositions);
        
        // Check that it contains positions from all roles
        $this->assertContains('Technical Administrator', $flatPositions);
        $this->assertContains('CDRRMC Chairperson', $flatPositions);
        $this->assertContains('Barangay Captain', $flatPositions);
        
        // Check that there are no duplicates
        $this->assertEquals(count($flatPositions), count(array_unique($flatPositions)));
    }

    /**
     * Test invalid role in API request
     */
    public function test_invalid_role_in_positions_api()
    {
        $response = $this->actingAs($this->systemAdmin)
            ->post('/system-admin/get-positions', [
                'role' => 'invalid_role'
            ]);

        $response->assertStatus(422); // Validation error
    }

    /**
     * Test missing role in API request
     */
    public function test_missing_role_in_positions_api()
    {
        $response = $this->actingAs($this->systemAdmin)
            ->post('/system-admin/get-positions', []);

        $response->assertStatus(422); // Validation error
    }

    /**
     * Test comprehensive position coverage
     */
    public function test_comprehensive_position_coverage()
    {
        // Test that we have adequate coverage for each role
        $systemAdminPositions = OrganizationalPositionService::getSystemAdminPositions();
        $cdrrMCPositions = OrganizationalPositionService::getCDRRMCPositions();
        $bdrrMCPositions = OrganizationalPositionService::getBDRRMCPositions();
        
        // System Admin should have at least 5 positions
        $this->assertGreaterThanOrEqual(5, count($systemAdminPositions));
        
        // CDRRMC should have at least 20 positions (comprehensive city-level roles)
        $this->assertGreaterThanOrEqual(20, count($cdrrMCPositions));
        
        // BDRRMC should have at least 30 positions (comprehensive barangay-level roles)
        $this->assertGreaterThanOrEqual(30, count($bdrrMCPositions));
    }
}
