<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\FCMService;
use App\Models\DeviceToken;
use App\Models\Notification;
use Illuminate\Support\Facades\Log;

class TestFCM extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fcm:test {--token= : Specific FCM token to test}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test FCM notification delivery';

    protected $fcmService;

    public function __construct(FCMService $fcmService)
    {
        parent::__construct();
        $this->fcmService = $fcmService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔥 Testing FCM Notification System...');

        // Test 1: Check Firebase initialization
        $this->info('📋 Step 1: Testing Firebase initialization...');
        try {
            // Create a dummy notification to trigger Firebase initialization
            $dummyNotification = new Notification([
                'title' => 'Test',
                'message' => 'Test',
                'category' => 'general',
                'barangay' => 'Test',
                'severity' => 'medium'
            ]);

            // This will trigger Firebase initialization without actually sending
            $result = $this->fcmService->sendNotification($dummyNotification, []);
            $this->info('✅ Firebase initialized successfully');
        } catch (\Exception $e) {
            $this->error('❌ Firebase initialization failed: ' . $e->getMessage());
            return 1;
        }

        // Test 2: Check device tokens
        $this->info('📋 Step 2: Checking device tokens...');
        $tokenCount = DeviceToken::where('is_active', true)->count();
        $this->info("📱 Found {$tokenCount} active device tokens");

        if ($tokenCount === 0) {
            $this->warn('⚠️  No active device tokens found. Make sure mobile app is registered.');
            return 1;
        }

        // Test 3: Get test token
        $testToken = $this->option('token');
        if (!$testToken) {
            $deviceToken = DeviceToken::where('is_active', true)->first();
            $testToken = $deviceToken ? $deviceToken->token : null;
        }

        if (!$testToken) {
            $this->error('❌ No test token available');
            return 1;
        }

        $this->info('🎯 Using test token: ' . substr($testToken, 0, 20) . '...');

        // Test 4: Create test notification
        $this->info('📋 Step 3: Creating test notification...');
        $notification = Notification::create([
            'title' => 'FCM Test Notification',
            'message' => 'This is a test notification sent at ' . now()->format('Y-m-d H:i:s'),
            'category' => 'general',
            'barangay' => 'Test Barangay',
            'severity' => 'medium',
            'sent' => false
        ]);

        $this->info("📝 Created notification ID: {$notification->id}");

        // Test 5: Send notification
        $this->info('📋 Step 4: Sending test notification...');
        try {
            $result = $this->fcmService->sendNotification($notification, [$testToken]);

            if ($result['success']) {
                $this->info('✅ Notification sent successfully!');
                $this->info("📊 Success count: {$result['success_count']}");
                $this->info("📊 Failure count: {$result['failure_count']}");
                $this->info("📊 Invalid tokens: {$result['invalid_tokens']}");
                $this->info("💬 Message: {$result['message']}");
            } else {
                $this->error('❌ Notification sending failed: ' . $result['message']);
                return 1;
            }
        } catch (\Exception $e) {
            $this->error('❌ Exception during notification sending: ' . $e->getMessage());
            Log::error('FCM Test Command Exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }

        // Test 6: Check logs
        $this->info('📋 Step 5: Checking recent logs...');
        $logFile = storage_path('logs/laravel.log');
        if (file_exists($logFile)) {
            $logs = file_get_contents($logFile);
            $recentLogs = substr($logs, -2000); // Last 2000 characters

            if (strpos($recentLogs, 'cURL error 60') !== false) {
                $this->warn('⚠️  SSL certificate issues detected in logs');
            }

            if (strpos($recentLogs, 'FCM notification failure') !== false) {
                $this->warn('⚠️  FCM notification failures detected in logs');
            }

            if (strpos($recentLogs, 'Firebase Messaging initialized successfully') !== false) {
                $this->info('✅ Firebase initialization confirmed in logs');
            }
        }

        $this->info('🎉 FCM test completed successfully!');
        $this->info('📱 Check your mobile device for the test notification.');

        return 0;
    }
}
