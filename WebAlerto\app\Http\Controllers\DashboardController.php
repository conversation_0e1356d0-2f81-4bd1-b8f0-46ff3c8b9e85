<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Evacuation;
use App\Models\User;
use App\Models\Notification;
use App\Models\Barangay;
use App\Services\BarangayService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardController extends Controller
{
    protected $barangayService;

    public function __construct(BarangayService $barangayService)
    {
        $this->barangayService = $barangayService;
    }

    public function index(Request $request)
    {
        $user = Auth::user();

        // Check if admin user is active
        $adminRoles = ['admin', 'super_admin', 'system_admin'];
        if (in_array($user->role, $adminRoles) && $user->status !== 'Active') {
            Auth::logout();
            return redirect()->route('login')->withErrors([
                'email' => 'Your account has been deactivated. Please contact the system administrator.'
            ]);
        }

        // Get filter parameters
        $selectedCity = $request->input('city');
        $selectedBarangay = $request->input('barangay');

        // Get accessible cities and barangays for the filter dropdown
        $cities = [];
        $barangays = [];

        if ($user->hasRole('super_admin')) {
            // CDRRMC: get barangays in their city/municipality
            $barangays = $this->barangayService->getAccessibleBarangays($user);
        } elseif ($user->hasRole('system_admin')) {
            // System Admin: get all cities and barangays province-wide
            $cities = $this->barangayService->getAllProvincialCities();
            if ($selectedCity) {
                $barangays = $this->barangayService->getBarangaysByCity($selectedCity);
            } else {
                $barangays = $this->barangayService->getAllProvincialBarangays();
            }
        }
        
        // Get search and filter parameters for map section
        $searchQuery = $request->input('search');
        $disasterFilter = $request->input('disaster_type', 'All');

        // Base queries with role-based filtering
        $notificationQuery = Notification::query();
        $evacuationQuery = Evacuation::query();

        // Apply role-based filtering
        if ($user->hasRole('admin')) {
            // BDRRMC: only their barangay
            $notificationQuery->where('barangay', $user->barangay);
            $evacuationQuery->where('barangay', $user->barangay);
        } elseif ($user->hasRole('super_admin')) {
            // CDRRMC: filter by selected barangay or all barangays in their city
            if ($selectedBarangay && $selectedBarangay !== '') {
                $notificationQuery->where('barangay', $selectedBarangay);
                $evacuationQuery->where('barangay', $selectedBarangay);
            } else {
                // Filter by barangays in their city
                $barangayService = app(\App\Services\BarangayService::class);
                $accessibleBarangays = $barangayService->getAccessibleBarangays($user);
                if (!empty($accessibleBarangays)) {
                    $notificationQuery->whereIn('barangay', $accessibleBarangays);
                    $evacuationQuery->whereIn('barangay', $accessibleBarangays);
                }
            }
        } elseif ($user->hasRole('system_admin')) {
            // System Admin: province-wide access, filter by selected city and/or barangay
            if ($selectedCity) {
                $evacuationQuery->where('city', $selectedCity);
            }
            if ($selectedBarangay && $selectedBarangay !== '') {
                $notificationQuery->where('barangay', $selectedBarangay);
                $evacuationQuery->where('barangay', $selectedBarangay);
            }
            // No filter for province-wide access when no specific city/barangay selected
        }

        // Apply search filter for map section
        if ($searchQuery) {
            $evacuationQuery->where('name', 'like', '%' . $searchQuery . '%');
        }

        // Apply disaster filter for map section
        if ($disasterFilter !== 'All') {
            $evacuationQuery->whereJsonContains('disaster_type', $disasterFilter);
        }

        // Get total counts
        $totalAlerts = $notificationQuery->count();
        $activeCenters = (clone $evacuationQuery)->where('status', 'Active')->count();

        // Get recent alerts (last 5) with user relationship
        $recentAlerts = (clone $notificationQuery)->with('user')
                                  ->orderBy('created_at', 'desc')
                                  ->take(5)
                                  ->get()
                                  ->map(function($notification) {
                                      return (object) [
                                          'type' => $notification->category,
                                          'message' => $notification->title,
                                          'barangay' => $notification->barangay,
                                          'user' => $notification->user,
                                          'time_ago' => $notification->created_at->diffForHumans()
                                      ];
                                  });

        // Get evacuation centers with coordinates (using same approach as MappingSystemController)
        $evacuationQuery = Evacuation::query();

        // Apply role-based filtering with city-barangay hierarchy
        if ($user->hasRole('admin')) {
            // BDRRMC users: only their barangay
            $evacuationQuery->where('barangay', $user->barangay);
        } elseif ($user->hasRole('super_admin')) {
            // CDRRMC users: filter by selected barangay or all barangays in their city
            if ($selectedBarangay && $selectedBarangay !== '') {
                $evacuationQuery->where('barangay', $selectedBarangay);
            } else {
                // Filter by barangays in their city
                $barangayService = app(\App\Services\BarangayService::class);
                $accessibleBarangays = $barangayService->getAccessibleBarangays($user);
                if (!empty($accessibleBarangays)) {
                    $evacuationQuery->whereIn('barangay', $accessibleBarangays);
                }
            }
        } elseif ($user->hasRole('system_admin')) {
            // System Admin: province-wide access, filter by city if selected
            if ($selectedCity) {
                $evacuationQuery->where('city', $selectedCity);
            }
            if ($selectedBarangay) {
                $evacuationQuery->where('barangay', $selectedBarangay);
            }
        }
        // No additional filters for system admin if no city/barangay selected

        $evacuationCenters = $evacuationQuery->get()->map(function($center) {
            // Create a formatted address from the separate location fields (same as MappingSystemController)
            $addressParts = [
                $center->building_name,
                $center->street_name,
                $center->barangay,
                $center->city,
                $center->province,
            ];
            
            // Clean up parts and remove duplicates/empties
            $addressParts = array_map('trim', $addressParts);
            $addressParts = array_filter($addressParts);
            $addressParts = array_unique($addressParts);
            
            $address = implode(', ', $addressParts);
            
            // Ensure "Philippines" is at the end, but only once
            $address = trim(str_ireplace('Philippines', '', $address), ' ,');
            $address .= ', Philippines';
            
            return [
                'id' => $center->id,
                'name' => $center->name,
                'address' => $address,
                'latitude' => $center->latitude,
                'longitude' => $center->longitude,
                'capacity' => $center->capacity,
                'status' => $center->status,
                'disaster_type' => $center->disaster_type,
                'contact' => $center->contact,
                'barangay' => $center->barangay
            ];
        });

        // Get total centers count with same filtering logic
        $totalCentersQuery = Evacuation::query();
        if ($user->hasRole('admin')) {
            $totalCentersQuery->where('barangay', $user->barangay);
        } elseif ($user->hasRole('super_admin')) {
            // CDRRMC users: filter by selected barangay or all barangays in their city
            if ($selectedBarangay && $selectedBarangay !== '') {
                $totalCentersQuery->where('barangay', $selectedBarangay);
            } else {
                // Filter by barangays in their city
                $barangayService = app(\App\Services\BarangayService::class);
                $accessibleBarangays = $barangayService->getAccessibleBarangays($user);
                if (!empty($accessibleBarangays)) {
                    $totalCentersQuery->whereIn('barangay', $accessibleBarangays);
                }
            }
        } elseif ($user->hasRole('system_admin')) {
            // System Admin: province-wide access, filter by city and/or barangay
            if ($selectedCity) {
                $totalCentersQuery->where('city', $selectedCity);
            }
            if ($selectedBarangay && $selectedBarangay !== '') {
                $totalCentersQuery->where('barangay', $selectedBarangay);
            }
        }
        $totalCenters = $totalCentersQuery->count();
        $totalBarangays = count($barangays);

        // Chart Data - Monthly Trends (Last 6 months)
        $monthlyData = $this->getMonthlyTrends($user, $selectedCity, $selectedBarangay);
        
        // Chart Data - Disaster Type Distribution
        $disasterTypeData = $this->getDisasterTypeDistribution($user, $selectedBarangay);
        
        // Chart Data - Barangay-wise Statistics
        $barangayStatsData = $this->getBarangayStatistics($user, $barangays, $selectedBarangay);

        // Get additional statistics for summary cards with city hierarchy
        $barangayUserRoles = ['chairman', 'officer'];
        $barangayUsersQuery = User::whereIn('role', $barangayUserRoles);
        if ($user->hasRole('admin')) {
            $barangayUsersQuery->where('barangay', $user->barangay);
        } elseif ($user->hasRole('super_admin')) {
            if ($selectedBarangay) {
                $barangayUsersQuery->where('barangay', $selectedBarangay);
            } else {
                // Filter by barangays in their city
                $barangayService = app(\App\Services\BarangayService::class);
                $accessibleBarangays = $barangayService->getAccessibleBarangays($user);
                if (!empty($accessibleBarangays)) {
                    $barangayUsersQuery->whereIn('barangay', $accessibleBarangays);
                }
            }
        }
        $totalBarangayUsers = $barangayUsersQuery->count();

        // Calculate role-specific user counts for Overview Statistics Chart and Total Users card
        $bdrrmoUsers = 0;
        $cdrrmoUsers = 0;
        $totalUsers = 0;

        if ($user->hasRole('admin')) {
            // BDRRMC: Show BDRRMC users in their barangay
            $bdrrmoUsers = User::where('role', 'admin')
                              ->where('barangay', $user->barangay)
                              ->count();
            $totalUsers = $bdrrmoUsers;
        } elseif ($user->hasRole('super_admin')) {
            // CDRRMC: Show BDRRMC and CDRRMC users in their city
            $barangayService = app(\App\Services\BarangayService::class);
            $accessibleBarangays = $barangayService->getAccessibleBarangays($user);

            // Apply barangay filter if selected
            if ($selectedBarangay && $selectedBarangay !== '') {
                $bdrrmoUsers = User::where('role', 'admin')
                                  ->where('barangay', $selectedBarangay)
                                  ->count();
                $cdrrmoUsers = 0; // No CDRRMC users in specific barangay
            } else {
                // All barangays in their city
                if (!empty($accessibleBarangays)) {
                    $bdrrmoUsers = User::where('role', 'admin')
                                      ->whereIn('barangay', $accessibleBarangays)
                                      ->count();
                }
                $cdrrmoUsers = User::where('role', 'super_admin')
                                  ->where('city', $user->city)
                                  ->count();
            }
            $totalUsers = $bdrrmoUsers + $cdrrmoUsers;
        } elseif ($user->hasRole('system_admin')) {
            // System Admin: Show CDRRMC and BDRRMC users province-wide
            if ($selectedCity) {
                $cdrrmoUsers = User::where('role', 'super_admin')
                                  ->where('city', $selectedCity)
                                  ->count();

                // Get barangays for the selected city and count BDRRMC users
                $barangayService = app(\App\Services\BarangayService::class);
                $cityBarangays = $barangayService->getBarangaysByCity($selectedCity);
                if (!empty($cityBarangays)) {
                    if ($selectedBarangay && $selectedBarangay !== '') {
                        // Specific barangay selected
                        $bdrrmoUsers = User::where('role', 'admin')
                                          ->where('barangay', $selectedBarangay)
                                          ->count();
                        $cdrrmoUsers = 0; // No CDRRMC users in specific barangay
                    } else {
                        // All barangays in selected city
                        $bdrrmoUsers = User::where('role', 'admin')
                                          ->whereIn('barangay', $cityBarangays)
                                          ->count();
                    }
                }
            } elseif ($selectedBarangay && $selectedBarangay !== '') {
                // Only barangay filter applied (no city)
                $bdrrmoUsers = User::where('role', 'admin')
                                  ->where('barangay', $selectedBarangay)
                                  ->count();
                $cdrrmoUsers = 0; // No CDRRMC users in specific barangay
            } else {
                // No filters - province-wide
                $cdrrmoUsers = User::where('role', 'super_admin')->count();
                $bdrrmoUsers = User::where('role', 'admin')->count();
            }
            $totalUsers = $bdrrmoUsers + $cdrrmoUsers;
        }

        // Calculate mobile users stats with city hierarchy
        $totalMobileUsers = 0;
        if ($user->hasRole('super_admin') || $user->hasRole('system_admin')) {
            $mobileUsersQuery = User::where('role', 'mobile_user');
            if ($user->hasRole('super_admin')) {
                if ($selectedBarangay) {
                    $mobileUsersQuery->where('barangay', $selectedBarangay);
                } else {
                    // Filter by barangays in their city
                    $barangayService = app(\App\Services\BarangayService::class);
                    $accessibleBarangays = $barangayService->getAccessibleBarangays($user);
                    if (!empty($accessibleBarangays)) {
                        $mobileUsersQuery->whereIn('barangay', $accessibleBarangays);
                    }
                }
            }
            $totalMobileUsers = $mobileUsersQuery->count();
        }

        return view('components.dashboard', compact(
            'totalAlerts',
            'activeCenters',
            'recentAlerts',
            'evacuationCenters',
            'cities',
            'barangays',
            'selectedCity',
            'selectedBarangay',
            'totalCenters',
            'totalBarangays',
            'monthlyData',
            'disasterTypeData',
            'barangayStatsData',
            'searchQuery',
            'disasterFilter',
            'totalBarangayUsers',
            'totalMobileUsers',
            'bdrrmoUsers',
            'cdrrmoUsers',
            'totalUsers'
        ));
    }

    private function getMonthlyTrends($user, $selectedCity, $selectedBarangay)
    {
        $months = [];
        $alertData = [];
        $centerData = [];

        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthName = $date->format('M Y');
            $months[] = $monthName;

            // Get alerts for this month based on user role and city hierarchy
            $alertQuery = Notification::query();
            if ($user->hasRole('admin')) {
                // BDRRMC user - only their barangay
                $alertQuery->where('barangay', $user->barangay);
            } elseif ($user->hasRole('super_admin')) {
                if ($selectedBarangay && $selectedBarangay !== '') {
                    // CDRRMC with selected barangay filter
                    $alertQuery->where('barangay', $selectedBarangay);
                } else {
                    // CDRRMC without filter - all barangays in their city
                    $accessibleBarangays = $this->barangayService->getAccessibleBarangays($user);
                    if (!empty($accessibleBarangays)) {
                        $alertQuery->whereIn('barangay', $accessibleBarangays);
                    }
                }
            } elseif ($user->hasRole('system_admin')) {
                // System Admin: province-wide access with city/barangay filtering
                if ($selectedCity) {
                    // Filter notifications by city through user relationship
                    $alertQuery->whereHas('user', function($q) use ($selectedCity) {
                        $q->where('city', $selectedCity);
                    });
                }
                if ($selectedBarangay && $selectedBarangay !== '') {
                    $alertQuery->where('barangay', $selectedBarangay);
                }
            }
            // System admin without filter - all barangays (no additional where clause)
            
            $alertCount = $alertQuery
                ->whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)
                ->count();
            $alertData[] = $alertCount;
            
            // Get active centers for this month based on user role and city hierarchy
            $centerQuery = Evacuation::query();
            if ($user->hasRole('admin')) {
                // BDRRMC user - only their barangay
                $centerQuery->where('barangay', $user->barangay);
            } elseif ($user->hasRole('super_admin')) {
                if ($selectedBarangay && $selectedBarangay !== '') {
                    // CDRRMC with selected barangay filter
                    $centerQuery->where('barangay', $selectedBarangay);
                } else {
                    // CDRRMC without filter - all barangays in their city
                    $accessibleBarangays = $this->barangayService->getAccessibleBarangays($user);
                    if (!empty($accessibleBarangays)) {
                        $centerQuery->whereIn('barangay', $accessibleBarangays);
                    }
                }
            } elseif ($user->hasRole('system_admin')) {
                // System Admin: province-wide access with city/barangay filtering
                if ($selectedCity) {
                    $centerQuery->where('city', $selectedCity);
                }
                if ($selectedBarangay && $selectedBarangay !== '') {
                    $centerQuery->where('barangay', $selectedBarangay);
                }
            }
            
            $centerCount = $centerQuery
                ->where('status', 'Active')
                ->whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)
                ->count();
            $centerData[] = $centerCount;
        }
        
        return [
            'months' => $months,
            'alerts' => $alertData,
            'centers' => $centerData
        ];
    }

    private function getDisasterTypeDistribution($user, $selectedBarangay)
    {
        $disasterQuery = Evacuation::query();
        
        // Apply role-based filtering with city hierarchy
        if ($user->hasRole('admin')) {
            // BDRRMC user - only their barangay
            $disasterQuery->where('barangay', $user->barangay);
        } elseif ($user->hasRole('super_admin')) {
            if ($selectedBarangay && $selectedBarangay !== '') {
                // CDRRMC with selected barangay filter
                $disasterQuery->where('barangay', $selectedBarangay);
            } else {
                // CDRRMC without filter - all barangays in their city
                $barangayService = app(\App\Services\BarangayService::class);
                $accessibleBarangays = $barangayService->getAccessibleBarangays($user);
                if (!empty($accessibleBarangays)) {
                    $disasterQuery->whereIn('barangay', $accessibleBarangays);
                }
            }
        }
        // System admin without filter - all barangays (no additional where clause)
        
        // Get all active evacuation centers
        $centers = $disasterQuery
            ->where('status', 'Active')
            ->get();
        
        // Process disaster types from JSON arrays
        $disasterTypeCounts = [];
        $colors = [
            'Typhoon' => '#22c55e',
            'Flood' => '#3b82f6',
            'Fire' => '#ef4444',
            'Earthquake' => '#f59e42',
            'Landslide' => '#a16207',
            'Others' => '#9333ea'
        ];
        
        foreach ($centers as $center) {
            $disasterTypes = $center->disaster_type;
            
            // Handle both array and string formats
            if (is_string($disasterTypes)) {
                $disasterTypes = json_decode($disasterTypes, true);
            }
            
            if (is_array($disasterTypes)) {
                foreach ($disasterTypes as $type) {
                    // Handle "Others: custom_type" format
                    if (strpos($type, 'Others:') === 0) {
                        $type = 'Others';
                    }
                    
                    if (!isset($disasterTypeCounts[$type])) {
                        $disasterTypeCounts[$type] = 0;
                    }
                    $disasterTypeCounts[$type]++;
                }
            } else {
                // Handle single string value
                $type = $disasterTypes;
                if (strpos($type, 'Others:') === 0) {
                    $type = 'Others';
                }
                
                if (!isset($disasterTypeCounts[$type])) {
                    $disasterTypeCounts[$type] = 0;
                }
                $disasterTypeCounts[$type]++;
            }
        }
        
        $labels = [];
        $data = [];
        $chartColors = [];
        
        foreach ($disasterTypeCounts as $type => $count) {
            $labels[] = $type;
            $data[] = $count;
            $chartColors[] = $colors[$type] ?? '#6b7280'; // Default gray for unknown types
        }
        
        return [
            'labels' => $labels,
            'data' => $data,
            'colors' => $chartColors
        ];
    }

    private function getBarangayStatistics($user, $specificBarangays, $selectedBarangay)
    {
        // Initialize queries based on user role and filters
        if ($user->hasRole('admin')) {
            // BDRRMC: Only their barangay
            $barangayQuery = User::where('barangay', $user->barangay);
            $centerQuery = Evacuation::where('barangay', $user->barangay);
            $notificationQuery = Notification::where('barangay', $user->barangay);
        } elseif ($user->hasRole('super_admin')) {
            // CDRRMC: Their city's barangays
            if ($selectedBarangay && $selectedBarangay !== '') {
                // Specific barangay selected
                $barangayQuery = User::where('barangay', $selectedBarangay);
                $centerQuery = Evacuation::where('barangay', $selectedBarangay);
                $notificationQuery = Notification::where('barangay', $selectedBarangay);
            } else {
                // All barangays in their city
                $barangayQuery = User::whereIn('barangay', $specificBarangays);
                $centerQuery = Evacuation::whereIn('barangay', $specificBarangays);
                $notificationQuery = Notification::whereIn('barangay', $specificBarangays);
            }
        } elseif ($user->hasRole('system_admin')) {
            // System Admin: Province-wide with filters
            if ($selectedBarangay && $selectedBarangay !== '') {
                // Specific barangay selected
                $barangayQuery = User::where('barangay', $selectedBarangay);
                $centerQuery = Evacuation::where('barangay', $selectedBarangay);
                $notificationQuery = Notification::where('barangay', $selectedBarangay);
            } else {
                // All barangays (province-wide or city-wide if city filter applied)
                $barangayQuery = User::query();
                $centerQuery = Evacuation::query();
                $notificationQuery = Notification::query();

                // Apply city filter if selected (for evacuation centers only)
                if (request('city')) {
                    $centerQuery->where('city', request('city'));
                }
            }
        } else {
            // Default fallback
            $barangayQuery = User::whereIn('barangay', $specificBarangays);
            $centerQuery = Evacuation::whereIn('barangay', $specificBarangays);
            $notificationQuery = Notification::whereIn('barangay', $specificBarangays);
        }
        
        // Get evacuation centers statistics
        $totalCenters = $centerQuery->count();
        $activeCenters = (clone $centerQuery)->where('status', 'Active')->count();
        
        // Get notifications statistics
        $totalNotifications = $notificationQuery->count();
        
        // Get barangay users statistics (BDRRMO Officer)
        $barangayUserRoles = ['officer'];
        $barangayUsersQuery = (clone $barangayQuery)->whereIn('role', $barangayUserRoles);
        $totalBarangayUsers = $barangayUsersQuery->count();
        $activeBarangayUsers = (clone $barangayUsersQuery)->where('status', 'Active')->count();
        
        // Get mobile users statistics
        $totalMobileUsers = 0;
        $activeMobileUsers = 0;

        // Mobile users are stored in a separate table and don't have barangay filtering
        if ($user->hasRole('super_admin') || $user->hasRole('system_admin')) {
            $totalMobileUsers = \App\Models\MobileUser::count();
            $activeMobileUsers = \App\Models\MobileUser::where('status', 'Active')->count();
        }

        // Return overall statistics as a single data point
        return [
            [
                'activeCenters' => $activeCenters,
                'totalCenters' => $totalCenters,
                'totalNotifications' => $totalNotifications,
                'totalBarangayUsers' => $totalBarangayUsers,
                'activeBarangayUsers' => $activeBarangayUsers,
                'totalMobileUsers' => $totalMobileUsers,
                'activeMobileUsers' => $activeMobileUsers,
                'totalAlerts' => $totalNotifications // For backward compatibility with chart
            ]
        ];
    }

    /**
     * Get admin registration data for dashboard
     */
    public function getAdminRegistrationData()
    {
        $user = Auth::user();
        
        // Only System Administrators can access registration data
        if (!$user->hasRole('system_admin')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied. Only System Administrators can access registration data.'
            ], 403);
        }

        // Get available barangays for dropdown using BarangayService
        $barangayService = app(\App\Services\BarangayService::class);
        $barangays = $barangayService->getActiveBarangays();


        // Get roles with descriptions
        $roles = [
            'admin' => 'Administrator (Barangay-specific access)'
        ];

        // Get access information
        $accessInfo = [
            'super_admin' => 'System Administrator - Full system access',
            'officer' => 'BDRRMO Officer - Barangay-specific access only'
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'barangays' => $barangays,
                'roles' => $roles,
                'access_info' => $accessInfo
            ]
        ]);
    }
}



