<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\URL;

class AdminInvitationNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The invitation data.
     *
     * @var array
     */
    protected $invitationData;

    /**
     * Create a new notification instance.
     */
    public function __construct(array $invitationData)
    {
        $this->invitationData = $invitationData;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $invitationUrl = URL::temporarySignedRoute(
            'admin.invitation.accept',
            now()->addDays(7),
            [
                'email' => $this->invitationData['email'],
                'token' => $this->invitationData['token']
            ]
        );

        return (new MailMessage)
            ->subject('ALERTO Admin Account Invitation')
            ->greeting('Hello!')
            ->line('You have been invited to join the ALERTO Disaster Management System as an administrator.')
            ->line('Account Details:')
            ->line('- Position: ' . $this->invitationData['position'])
            ->line('- Barangay: ' . $this->invitationData['barangay'])
            ->line('- Role: ' . $this->invitationData['role'])
            ->line('Please click the button below to set up your account.')
            ->line('You will be required to:')
            ->line('• Enter your personal information (name, title, etc.)')
            ->line('• Set a secure password for your account')
            ->line('• Complete your profile setup')
            ->action('Set Up Account', $invitationUrl)
            ->line('This invitation link will expire in 7 days.')
            ->line('If you did not expect this invitation, please ignore this email.')
            ->salutation('Best regards, ALERTO Team');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'invitation_data' => $this->invitationData,
            'email' => $this->invitationData['email'],
        ];
    }
} 