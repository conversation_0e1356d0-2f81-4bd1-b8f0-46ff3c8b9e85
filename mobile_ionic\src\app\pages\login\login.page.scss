.login-bg {
  --background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.login-wrapper {
  width: 100%;
  max-width: 350px;
  padding: 2rem;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.logo-container {
  margin-bottom: 3rem;
  align-self: center;
}

.app-logo {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  padding: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.login-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.input-group {
  width: 100%;
}

.modern-input {
  --background: transparent;
  --color: black;
  --placeholder-color: rgba(0, 0, 0, 0.7);
  --padding-start: 0;
  --padding-end: 0;
  border: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.5);
  border-radius: 0;
  font-size: 1rem;
  padding: 12px 0;
}

.modern-input:focus-within {
  border-bottom-color: black;
}

.modern-btn {
  --background: #007bff;
  --color: white;
  --border-radius: 25px;
  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  font-weight: 600;
  font-size: 1.1rem;
  height: 50px;
  width: 100%;
  margin-top: 1rem;
}

.register-link {
  margin-top: 1.5rem;
  color: black;
  font-size: 1rem;
}

.register-link a {
  color: #007bff;
  text-decoration: underline;
  font-weight: 600;
}

.troubleshooting-section {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--ion-color-light);
}

.troubleshooting-section ion-button {
  margin-bottom: 8px;
}

/* Alert styling */
.login-alert {
  --backdrop-opacity: 0.8;

  .alert-wrapper {
    border-radius: 15px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  }

  .alert-head {
    padding-bottom: 10px;
  }

  .alert-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #d9534f;
  }

  .alert-message {
    font-size: 1rem;
    color: #333;
  }

  .alert-button {
    color: #3880ff;
    font-weight: 500;
  }
}

/* Success Alert styling */
.login-success-alert {
  --backdrop-opacity: 0.8;

  .alert-wrapper {
    border-radius: 15px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  }

  .alert-head {
    padding-bottom: 10px;
  }

  .alert-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #5cb85c; /* Green color for success */
  }

  .alert-message {
    font-size: 1rem;
    color: #333;
  }

  .alert-button {
    color: #3880ff;
    font-weight: 500;
  }
}