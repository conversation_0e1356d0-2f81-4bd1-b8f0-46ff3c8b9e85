#!/bin/bash

echo "========================================"
echo "WebAlerto Localhost Geolocation Setup"
echo "========================================"
echo

echo "1. Checking current environment..."
echo "Current directory: $(pwd)"
echo

echo "2. Getting your local IP address..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    LOCAL_IP=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -1)
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    LOCAL_IP=$(hostname -I | awk '{print $1}')
else
    LOCAL_IP="Unable to detect"
fi

echo "Local IP: $LOCAL_IP"
echo

echo "3. Starting Laravel development server..."
echo
echo "Server will be available at:"
echo "  - http://localhost:8000"
echo "  - http://127.0.0.1:8000"
if [ "$LOCAL_IP" != "Unable to detect" ]; then
    echo "  - http://$LOCAL_IP:8000 (for mobile testing)"
fi
echo

echo "IMPORTANT: After the server starts:"
echo
echo "  Chrome Users:"
echo "  1. Visit: chrome://settings/content/location"
echo "  2. Add http://localhost:8000 to allowed sites"
echo "  3. OR click the lock icon when visiting the site"
echo
echo "  Firefox Users:"
echo "  1. Visit the site and click the shield icon"
echo "  2. Allow location access when prompted"
echo
echo "  Safari Users:"
echo "  1. Safari → Preferences → Websites → Location Services"
echo "  2. Set localhost to 'Allow'"
echo
echo "  Test Page: http://localhost:8000/test-geolocation"
echo
echo "Press Ctrl+C to stop the server"
echo "========================================"
echo

php artisan serve --host=0.0.0.0 --port=8000
