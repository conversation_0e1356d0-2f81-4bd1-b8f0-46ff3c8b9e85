<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;

class SystemAdminRegistrationTest extends TestCase
{
    use RefreshDatabase;

    protected $systemAdmin;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a system administrator for testing
        $this->systemAdmin = User::create([
            'first_name' => 'System',
            'last_name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'system_admin',
            'position' => 'Technical Administrator',
            'status' => 'Active',
        ]);
    }

    /**
     * Test email availability check for available email
     */
    public function test_email_availability_check_available()
    {
        $response = $this->actingAs($this->systemAdmin)
            ->post('/system-admin/check-email', [
                'email' => '<EMAIL>'
            ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'available' => true,
            'message' => 'Email is available'
        ]);
    }

    /**
     * Test email availability check for existing email
     */
    public function test_email_availability_check_existing()
    {
        // Create a user with existing email
        User::create([
            'first_name' => 'Existing',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'position' => 'Officer',
            'status' => 'Active',
        ]);

        $response = $this->actingAs($this->systemAdmin)
            ->post('/system-admin/check-email', [
                'email' => '<EMAIL>'
            ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'available' => false,
            'message' => 'Email is already in use'
        ]);
    }

    /**
     * Test non-system admin cannot check email availability
     */
    public function test_non_system_admin_cannot_check_email()
    {
        $regularUser = User::create([
            'first_name' => 'Regular',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'position' => 'Officer',
            'status' => 'Active',
        ]);

        $response = $this->actingAs($regularUser)
            ->post('/system-admin/check-email', [
                'email' => '<EMAIL>'
            ]);

        $response->assertStatus(403);
    }

    /**
     * Test creating user without password (auto-generated)
     */
    public function test_create_user_without_password()
    {
        Mail::fake(); // Prevent actual email sending

        $userData = [
            'first_name' => 'New',
            'last_name' => 'User',
            'middle_name' => 'Middle',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'position' => 'BDRRMC Officer',
            'city' => 'Cebu City',
            'barangay' => 'Lahug',
        ];

        $response = $this->actingAs($this->systemAdmin)
            ->post('/system-admin/create-user', $userData);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify user was created
        $this->assertDatabaseHas('users', [
            'first_name' => 'New',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'position' => 'BDRRMC Officer',
            'city' => 'Cebu City',
            'barangay' => 'Lahug',
            'status' => 'Active',
        ]);

        // Verify password was auto-generated (not empty)
        $createdUser = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($createdUser->password);
        $this->assertNotEmpty($createdUser->password);
    }

    /**
     * Test creating system admin user (no city/barangay required)
     */
    public function test_create_system_admin_user()
    {
        Mail::fake();

        $userData = [
            'first_name' => 'Another',
            'last_name' => 'Admin',
            'email' => '<EMAIL>',
            'role' => 'system_admin',
            'position' => 'Technical Administrator',
        ];

        $response = $this->actingAs($this->systemAdmin)
            ->post('/system-admin/create-user', $userData);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify user was created without city/barangay
        $this->assertDatabaseHas('users', [
            'first_name' => 'Another',
            'last_name' => 'Admin',
            'email' => '<EMAIL>',
            'role' => 'system_admin',
            'position' => 'Technical Administrator',
            'city' => null,
            'barangay' => null,
            'status' => 'Active',
        ]);
    }

    /**
     * Test creating super admin user (city required, no barangay)
     */
    public function test_create_super_admin_user()
    {
        Mail::fake();

        $userData = [
            'first_name' => 'Super',
            'last_name' => 'Admin',
            'email' => '<EMAIL>',
            'role' => 'super_admin',
            'position' => 'CDRRMC Director',
            'city' => 'Cebu City',
        ];

        $response = $this->actingAs($this->systemAdmin)
            ->post('/system-admin/create-user', $userData);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify user was created with city but no barangay
        $this->assertDatabaseHas('users', [
            'first_name' => 'Super',
            'last_name' => 'Admin',
            'email' => '<EMAIL>',
            'role' => 'super_admin',
            'position' => 'CDRRMC Director',
            'city' => 'Cebu City',
            'barangay' => null,
            'status' => 'Active',
        ]);
    }

    /**
     * Test validation errors for missing required fields
     */
    public function test_validation_errors_missing_fields()
    {
        $response = $this->actingAs($this->systemAdmin)
            ->post('/system-admin/create-user', [
                'first_name' => '', // Required field empty
                'email' => 'invalid-email', // Invalid email format
                'role' => 'invalid_role', // Invalid role
            ]);

        $response->assertStatus(422); // Validation error
    }

    /**
     * Test duplicate email validation
     */
    public function test_duplicate_email_validation()
    {
        // Create a user first
        User::create([
            'first_name' => 'Existing',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'position' => 'Officer',
            'status' => 'Active',
        ]);

        // Try to create another user with same email
        $userData = [
            'first_name' => 'New',
            'last_name' => 'User',
            'email' => '<EMAIL>', // Duplicate email
            'role' => 'admin',
            'position' => 'Officer',
            'city' => 'Cebu City',
            'barangay' => 'Lahug',
        ];

        $response = $this->actingAs($this->systemAdmin)
            ->post('/system-admin/create-user', $userData);

        $response->assertStatus(422); // Validation error
    }

    /**
     * Test barangay required for admin role
     */
    public function test_barangay_required_for_admin_role()
    {
        $userData = [
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'position' => 'BDRRMC Officer',
            'city' => 'Cebu City',
            // Missing barangay for admin role
        ];

        $response = $this->actingAs($this->systemAdmin)
            ->post('/system-admin/create-user', $userData);

        $response->assertStatus(422); // Validation error
    }

    /**
     * Test city required for super_admin and admin roles
     */
    public function test_city_required_for_non_system_admin_roles()
    {
        // Test super_admin without city
        $userData = [
            'first_name' => 'Super',
            'last_name' => 'Admin',
            'email' => '<EMAIL>',
            'role' => 'super_admin',
            'position' => 'CDRRMC Director',
            // Missing city for super_admin role
        ];

        $response = $this->actingAs($this->systemAdmin)
            ->post('/system-admin/create-user', $userData);

        $response->assertStatus(422); // Validation error
    }

    /**
     * Test successful message includes email notification info
     */
    public function test_success_message_includes_email_info()
    {
        Mail::fake();

        $userData = [
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'position' => 'Officer',
            'city' => 'Cebu City',
            'barangay' => 'Lahug',
        ];

        $response = $this->actingAs($this->systemAdmin)
            ->post('/system-admin/create-user', $userData);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        
        $responseData = $response->json();
        $this->assertStringContainsString('User account created successfully', $responseData['message']);
        $this->assertStringContainsString('<EMAIL>', $responseData['message']);
    }
}
