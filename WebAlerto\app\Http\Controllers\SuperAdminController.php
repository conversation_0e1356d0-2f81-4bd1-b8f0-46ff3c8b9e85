<?php

namespace App\Http\Controllers;


use App\Models\User;
use App\Models\Notification;
use App\Models\Evacuation;

use App\Models\Barangay;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Hash;
use App\Services\EmailNotificationService;

use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Mail\AdminRegistrationMail;


class SuperAdminController extends Controller
{

    public function dashboard()
    {
        $user = Auth::user();
        if (!$user->hasRole('super_admin') && !$user->hasRole('system_admin')) {
            abort(403, 'Unauthorized action.');
        }

        // Get barangay-specific stats using PSGC-connected BarangayService
        $barangayService = app(\App\Services\BarangayService::class);

        // Get barangays based on user's city (for CDRRMC users) or all barangays (for system-wide users)
        $barangays = $barangayService->getAccessibleBarangays($user);
        $barangayStats = [];

        foreach ($barangays as $barangay) {
            try {
                // Filter users, notifications, and centers based on user's city if applicable
                $userQuery = User::where('barangay', $barangay);
                $notificationQuery = Notification::where('barangay', $barangay);
                $centerQuery = Evacuation::where('barangay', $barangay);

                // If user has a specific city, also filter by city to ensure data consistency
                if ($user->city && $user->city !== 'All') {
                    $userQuery->where('city', $user->city);
                    // Note: Notifications and Evacuations don't have city field,
                    // but barangay filtering should be sufficient for city-specific data
                }

                $barangayStats[$barangay] = [
                    'total_users' => $userQuery->count(),
                    'active_users' => $userQuery->where('status', 'Active')->count(),
                    'total_notifications' => $notificationQuery->count(),
                    'active_centers' => $centerQuery->where('status', 'active')->count(),
                    'recent_notifications' => $notificationQuery
                        ->orderBy('created_at', 'desc')
                        ->take(5)
                        ->get()
                ];
            } catch (\Exception $e) {
                \Log::warning("Failed to get stats for barangay: $barangay", ['error' => $e->getMessage()]);
                $barangayStats[$barangay] = [
                    'total_users' => 0,
                    'active_users' => 0,
                    'total_notifications' => 0,
                    'active_centers' => 0,
                    'recent_notifications' => collect()
                ];
            }
        }

        // Get main dashboard stats with error handling - filter by city if applicable
        try {
            $userStatsQuery = User::query();

            // Apply city filtering for CDRRMC users (super_admin with specific city)
            if ($user->hasRole('super_admin') && $user->city && $user->city !== 'All') {
                $userStatsQuery->where('city', $user->city);
            }
            // System administrators see all users (no filtering)

            $stats = [
                'total_users' => $userStatsQuery->count(),
                'active_users' => $userStatsQuery->where('status', 'Active')->count(),
                'barangay_stats' => $barangayStats
            ];
        } catch (\Exception $e) {
            \Log::error('Failed to get dashboard stats', ['error' => $e->getMessage()]);
            $stats = [
                'total_users' => 0,
                'active_users' => 0,
                'barangay_stats' => $barangayStats
            ];
        }

        return view('components.superadmin.dashboard', compact('stats'));
    }








    /**
     * Show admin user management page
     */
    public function adminUsers(Request $request)
    {
        $user = Auth::user();
        if (!$user->hasRole('super_admin')) {
            abort(403, 'Unauthorized action.');
        }

        // Get filters from request
        $selectedBarangay = $request->input('barangay');
        $searchQuery = $request->input('search');

        // Get barangay service for RBAC filtering
        $barangayService = app(\App\Services\BarangayService::class);
        $accessibleBarangays = $barangayService->getAccessibleBarangays($user);

        // Build query for BDRRMC admin users only (exclude CDRRMC users)
        $query = User::where('role', 'admin'); // Only BDRRMC users

        // Filter by CDRRMC user's accessible barangays (their city's barangays)
        if (!empty($accessibleBarangays)) {
            $query->whereIn('barangay', $accessibleBarangays);
        }

        // Apply barangay filter if selected
        if ($selectedBarangay) {
            $query->where('barangay', $selectedBarangay);
        }

        // Apply search filter if provided
        if ($searchQuery) {
            $query->where(function($q) use ($searchQuery) {
                $q->where('first_name', 'like', "%{$searchQuery}%")
                  ->orWhere('last_name', 'like', "%{$searchQuery}%")
                  ->orWhere('email', 'like', "%{$searchQuery}%")
                  ->orWhere('position', 'like', "%{$searchQuery}%")
                  ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%{$searchQuery}%"])
                  ->orWhereRaw("CONCAT(title, ' ', first_name, ' ', last_name) LIKE ?", ["%{$searchQuery}%"]);
            });
        }

        $adminUsers = $query->orderBy('created_at', 'desc')->paginate(10);

        // Use the accessible barangays for the filter dropdown (CDRRMC user's city barangays only)
        $barangays = $accessibleBarangays;

        return view('components.superadmin.admin-users', compact('adminUsers', 'barangays', 'selectedBarangay', 'searchQuery'));
    }

    /**
     * Show create admin user form
     */
    public function createAdminUser()
    {
        $user = Auth::user();
        if (!$user->hasRole('super_admin')) {
            abort(403, 'Unauthorized action.');
        }

        // Get barangays for the dropdown using PSGC-connected BarangayService
        $barangayService = app(\App\Services\BarangayService::class);
        $barangays = $barangayService->getActiveBarangays();

        return view('components.superadmin.create-admin-user', compact('barangays'));
    }

    /**
     * Store new admin user
     */

    public function storeAdminUser(Request $request)
    {
        $user = Auth::user();
        if (!$user->hasRole('super_admin')) {
            abort(403, 'Unauthorized action.');
        }


        \Log::info('Admin user creation attempt', [
            'request_data' => $request->except(['password']),
            'user_id' => $user->id
        ]);

        $validator = Validator::make($request->all(), [
            'title' => 'nullable|string|in:Atty.,Engr.,Arch.,Dr.,Dir.',
            'first_name' => 'required|string|max:255',
            'middle_name' => 'nullable|string|max:255',
            'last_name' => 'required|string|max:255',
            'suffix' => 'nullable|string|in:Jr.,Sr.,II,III,IV,V',
            'position' => 'required|string|max:255',
            'barangay' => ['required', 'string', 'max:255', new \App\Rules\ValidBarangay()],
            'email' => 'required|email|unique:users,email',
            'role' => 'required|string|in:admin,super_admin,officer',
        ]);

        if ($validator->fails()) {
            \Log::warning('Admin user creation validation failed', [
                'errors' => $validator->errors()->toArray(),
                'request_data' => $request->except(['password'])
            ]);

            // Check if email already exists and provide helpful message
            if ($validator->errors()->has('email')) {
                $existingUser = User::where('email', $request->email)->first();
                if ($existingUser) {
                    $errorMessage = "The email '{$request->email}' is already registered to: " .
                                  ($existingUser->first_name ?? '') . ' ' . ($existingUser->last_name ?? '') .
                                  " (Role: " . ($existingUser->role ?? 'N/A') . ", Status: " . ($existingUser->status ?? 'N/A') . ")";

                    return redirect()->back()
                        ->with('error', $errorMessage)
                        ->withInput();
                }
            }

            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Generate temporary password
            $temporaryPassword = Str::random(12);

            // Create the admin user
            $newUser = User::create([
                'title' => $request->title,
                'first_name' => $request->first_name,
                'middle_name' => $request->middle_name,
                'last_name' => $request->last_name,
                'suffix' => $request->suffix,
                'position' => $request->position,
                'barangay' => $request->barangay,
                'email' => $request->email,
                'password' => Hash::make($temporaryPassword),
                'role' => $request->role,
                'status' => 'Active'
            ]);

            // Send registration email
            try {
                Mail::to($newUser->email)->send(new AdminRegistrationMail($newUser, $temporaryPassword));
                $emailStatus = 'sent';
            } catch (\Exception $e) {
                \Log::error('Failed to send admin registration email', [
                    'user_id' => $newUser->id,
                    'email' => $newUser->email,
                    'error' => $e->getMessage()
                ]);
                $emailStatus = 'failed';
            }

            // Log the action (removed SystemLog dependency)

            $message = 'Admin user created successfully!';
            if ($emailStatus === 'failed') {
                $message .= ' However, the registration email could not be sent. Please provide the credentials manually.';
                $message .= " Temporary password: {$temporaryPassword}";
            }

            \Log::info('Admin user created, redirecting to admin users list', [
                'new_user_id' => $newUser->id,
                'new_user_email' => $newUser->email,
                'redirect_route' => 'superadmin.admin-users'
            ]);

            return redirect()->route('superadmin.admin-users')
                ->with('success', $message)
                ->with('new_user_created', true);

        } catch (\Exception $e) {
            \Log::error('Failed to create admin user', [
                'error' => $e->getMessage(),
                'request_data' => $request->except(['password'])
            ]);

            return redirect()->back()
                ->with('error', 'Failed to create admin user: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Check if email is available for registration
     */
    public function checkEmail(Request $request)
    {
        $request->validate([
            'email' => 'required|email'
        ]);

        $existingUser = User::where('email', $request->email)->first();

        if ($existingUser) {
            return response()->json([
                'available' => false,
                'user_info' => ($existingUser->first_name ?? '') . ' ' . ($existingUser->last_name ?? '') .
                              ' (' . ($existingUser->role ?? 'N/A') . ')'
            ]);
        }

        return response()->json([
            'available' => true
        ]);
    }

    /**
     * Deactivate admin user
     */
    public function deactivateAdminUser($id)
    {
        $user = Auth::user();
        if (!$user->hasRole('super_admin')) {
            abort(403, 'Unauthorized action.');
        }

        $adminUser = User::findOrFail($id);

        // Prevent self-deactivation
        if ($adminUser->id === $user->id) {
            return redirect()->back()->with('error', 'You cannot modify your own account status.');
        }

        // Only allow deactivating admin users
        if (!in_array($adminUser->role, ['admin', 'super_admin', 'officer'])) {
            return redirect()->back()->with('error', 'Only admin users can be deactivated.');
        }

        $adminUser->update([
            'status' => 'Inactive'
        ]);

        // Revoke all active sessions/tokens for the deactivated user
        try {
            // Revoke all Sanctum tokens
            $adminUser->tokens()->delete();
        } catch (\Exception $e) {
            \Log::warning('Failed to revoke tokens for deactivated user', [
                'user_id' => $adminUser->id,
                'error' => $e->getMessage()
            ]);
        }

        // Log the action (removed SystemLog dependency)

        return redirect()->back()
            ->with('success', "Admin user '{$adminUser->first_name} {$adminUser->last_name}' has been deactivated successfully. They will no longer be able to access the system.");
    }

    /**
     * Reactivate admin user
     */
    public function reactivateAdminUser($id)
    {
        $user = Auth::user();
        if (!$user->hasRole('super_admin')) {
            abort(403, 'Unauthorized action.');
        }

        $adminUser = User::findOrFail($id);
        
        // Prevent self-deactivation
        if ($adminUser->id === $user->id) {
            return redirect()->back()->with('error', 'You cannot modify your own account status.');
        }

        // Only allow reactivating admin users
        if (!in_array($adminUser->role, ['admin', 'super_admin', 'officer'])) {
            return redirect()->back()->with('error', 'Only admin users can be reactivated.');
        }

        $adminUser->update([
            'status' => 'Active',
            'is_active' => true
        ]);

        SystemLog::log('admin_user_reactivated', $user, $adminUser, "Admin user {$adminUser->email} reactivated");

        return redirect()->back()->with('success', 'Admin user reactivated successfully.');
    }

    public function deleteAdminUser($id)
    {
        $user = Auth::user();
        if (!$user->hasRole('super_admin')) {
            abort(403, 'Unauthorized action.');
        }

        $adminUser = User::findOrFail($id);
        
        // Prevent self-deletion
        if ($adminUser->id === $user->id) {
            return redirect()->back()->with('error', 'You cannot delete your own account.');
        }

        // Only allow deleting admin users
        if (!in_array($adminUser->role, ['admin', 'super_admin', 'officer'])) {
            return redirect()->back()->with('error', 'Only admin users can be deleted.');
        }

        // Log the deletion before actually deleting (removed SystemLog dependency)

        // Permanently delete the user
        $adminUser->delete();

        return redirect()->back()->with('success', 'Admin user deleted successfully.');
    }

    /**
     * Show user management requests from Chairmen
     */
    public function userManagementRequests(Request $request)
    {
        $user = Auth::user();
        if (!$user->hasRole('super_admin')) {
            abort(403, 'Unauthorized action.');
        }

        $status = $request->input('status', 'pending');
        $barangay = $request->input('barangay');

        $query = \App\Models\UserManagementRequest::with(['requester', 'targetUser', 'reviewer']);

        if ($status !== 'all') {
            $query->where('status', $status);
        }

        if ($barangay) {
            $query->whereHas('requester', function($q) use ($barangay) {
                $q->where('barangay', $barangay);
            });
        }

        $requests = $query->orderBy('created_at', 'desc')->paginate(15);

        // Get barangays for filter
        $barangayService = app(\App\Services\BarangayService::class);
        $barangays = $barangayService->getActiveBarangays();

        return view('components.superadmin.user-management-requests', compact('requests', 'barangays', 'status', 'barangay'));
    }

    /**
     * Approve or reject a user management request
     */
    public function reviewUserManagementRequest(Request $request, $id)
    {
        $user = Auth::user();
        if (!$user->hasRole('super_admin')) {
            abort(403, 'Unauthorized action.');
        }

        $request->validate([
            'action' => 'required|in:approve,reject',
            'review_notes' => 'nullable|string|max:500'
        ]);

        $managementRequest = \App\Models\UserManagementRequest::with(['requester', 'targetUser'])
            ->where('status', 'pending')
            ->findOrFail($id);

        \DB::beginTransaction();
        try {
            // Update the request
            $managementRequest->update([
                'status' => $request->action === 'approve' ? 'approved' : 'rejected',
                'reviewed_by' => $user->id,
                'review_notes' => $request->review_notes,
                'reviewed_at' => now()
            ]);

            // If approved, execute the action
            if ($request->action === 'approve') {
                if ($managementRequest->action_type === 'registration') {
                    // Handle registration request using invitation system
                    $emailService = new EmailNotificationService();

                    $invitationData = [
                        'email' => $managementRequest->requested_email,
                        'title' => null,
                        'first_name' => $managementRequest->requested_first_name,
                        'middle_name' => null,
                        'last_name' => $managementRequest->requested_last_name,
                        'suffix' => null,
                        'position' => $managementRequest->requested_position,
                        'barangay' => $managementRequest->requested_barangay,
                        'role' => 'officer'
                    ];

                    $invitation = $emailService->sendAdminInvitation($invitationData, $user);

                    if (!$invitation) {
                        throw new \Exception('Failed to send invitation email to the new user.');
                    }

                    $actionMessage = 'invitation sent';
                } else {
                    // Handle user management requests (deactivate/delete)
                    $targetUser = $managementRequest->targetUser;

                    if ($managementRequest->action_type === 'deactivate') {
                        $targetUser->update(['status' => 'Inactive']);
                        $actionMessage = 'deactivated';
                    } elseif ($managementRequest->action_type === 'delete') {
                        $targetUser->delete();
                        $actionMessage = 'deleted';
                    }
                }

                // Log the action (removed SystemLog dependency)
            }

            \DB::commit();

            if ($request->action === 'approve') {
                if ($managementRequest->action_type === 'registration') {
                    $message = "Registration request approved successfully. Invitation email has been sent to {$managementRequest->requested_email}. The user will receive setup instructions to create their account.";
                } else {
                    $message = "Request approved and user {$actionMessage} successfully.";
                }
            } else {
                $message = 'Request rejected successfully.';
            }

            return redirect()->back()->with('success', $message);

        } catch (\Exception $e) {
            \DB::rollback();
            \Log::error('Failed to review user management request', [
                'request_id' => $id,
                'error' => $e->getMessage()
            ]);

            return redirect()->back()->with('error', 'Failed to process request: ' . $e->getMessage());
        }
    }

    /**
     * Get notification count for SuperAdmin Chairman Requests
     */
    public function getChairmanRequestsNotificationCount()
    {
        // Count pending requests for SuperAdmin notification
        $pendingCount = UserManagementRequest::where('status', 'pending')->count();

        return response()->json(['count' => $pendingCount]);
    }
}
