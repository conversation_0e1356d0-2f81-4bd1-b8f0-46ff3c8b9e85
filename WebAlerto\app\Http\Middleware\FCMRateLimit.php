<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use App\Http\Responses\FCMResponse;

class FCMRateLimit
{
    public function handle(Request $request, Closure $next)
    {
        $key = $this->resolveRequestSignature($request);
        $maxAttempts = $this->getMaxAttempts($request);
        $decaySeconds = config('fcm.rate_limits.window', 60);

        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            return FCMResponse::error(
                'Too many requests. Please try again later.',
                429,
                [
                    'retry_after' => RateLimiter::availableIn($key),
                    'max_attempts' => $maxAttempts,
                    'decay_seconds' => $decaySeconds
                ]
            );
        }

        RateLimiter::hit($key, $decaySeconds);

        $response = $next($request);

        return $response->withHeaders([
            'X-RateLimit-Limit' => $maxAttempts,
            'X-RateLimit-Remaining' => RateLimiter::remaining($key, $maxAttempts),
            'X-RateLimit-Reset' => RateLimiter::availableIn($key),
        ]);
    }

    protected function resolveRequestSignature(Request $request): string
    {
        $user = $request->user();
        $route = $request->route()->getName();
        
        return sha1(implode('|', [
            $user ? $user->id : $request->ip(),
            $route,
            $request->input('token', '')
        ]));
    }

    protected function getMaxAttempts(Request $request): int
    {
        $route = $request->route()->getName();
        
        return match ($route) {
            'fcm.test' => config('fcm.rate_limits.test_notifications', 10),
            'fcm.bulk' => config('fcm.rate_limits.bulk_notifications', 100),
            default => 60,
        };
    }
} 