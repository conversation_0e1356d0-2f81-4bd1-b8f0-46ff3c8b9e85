<?php

namespace App\Console\Commands;

use App\Services\EmailNotificationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CleanupExpiredInvitations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'invitations:cleanup-expired';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up expired admin invitations';

    protected $emailService;

    /**
     * Create a new command instance.
     */
    public function __construct(EmailNotificationService $emailService)
    {
        parent::__construct();
        $this->emailService = $emailService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting cleanup of expired invitations...');

        try {
            $expiredCount = $this->emailService->cleanupExpiredInvitations();

            if ($expiredCount !== false) {
                $this->info("Successfully cleaned up {$expiredCount} expired invitations.");
                Log::info("Cleanup command completed: {$expiredCount} expired invitations cleaned up");
            } else {
                $this->error('Failed to cleanup expired invitations.');
                Log::error('Cleanup command failed: Could not cleanup expired invitations');
                return 1;
            }

            return 0;
        } catch (\Exception $e) {
            $this->error('An error occurred during cleanup: ' . $e->getMessage());
            Log::error('Cleanup command error: ' . $e->getMessage());
            return 1;
        }
    }
}
