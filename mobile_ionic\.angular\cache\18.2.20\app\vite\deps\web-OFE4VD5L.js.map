{"version": 3, "sources": ["../../../../../../node_modules/@capacitor/haptics/dist/esm/web.js"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\nimport { ImpactStyle, NotificationType } from './definitions';\nexport class HapticsWeb extends WebPlugin {\n  constructor() {\n    super(...arguments);\n    this.selectionStarted = false;\n  }\n  async impact(options) {\n    const pattern = this.patternForImpact(options === null || options === void 0 ? void 0 : options.style);\n    this.vibrateWithPattern(pattern);\n  }\n  async notification(options) {\n    const pattern = this.patternForNotification(options === null || options === void 0 ? void 0 : options.type);\n    this.vibrateWithPattern(pattern);\n  }\n  async vibrate(options) {\n    const duration = (options === null || options === void 0 ? void 0 : options.duration) || 300;\n    this.vibrateWithPattern([duration]);\n  }\n  async selectionStart() {\n    this.selectionStarted = true;\n  }\n  async selectionChanged() {\n    if (this.selectionStarted) {\n      this.vibrateWithPattern([70]);\n    }\n  }\n  async selectionEnd() {\n    this.selectionStarted = false;\n  }\n  patternForImpact(style = ImpactStyle.Heavy) {\n    if (style === ImpactStyle.Medium) {\n      return [43];\n    } else if (style === ImpactStyle.Light) {\n      return [20];\n    }\n    return [61];\n  }\n  patternForNotification(type = NotificationType.Success) {\n    if (type === NotificationType.Warning) {\n      return [30, 40, 30, 50, 60];\n    } else if (type === NotificationType.Error) {\n      return [27, 45, 50];\n    }\n    return [35, 65, 21];\n  }\n  vibrateWithPattern(pattern) {\n    if (navigator.vibrate) {\n      navigator.vibrate(pattern);\n    } else {\n      throw this.unavailable('Browser does not support the vibrate API');\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;AAEO,IAAM,aAAN,cAAyB,UAAU;AAAA,EACxC,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACM,OAAO,SAAS;AAAA;AACpB,YAAM,UAAU,KAAK,iBAAiB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,KAAK;AACrG,WAAK,mBAAmB,OAAO;AAAA,IACjC;AAAA;AAAA,EACM,aAAa,SAAS;AAAA;AAC1B,YAAM,UAAU,KAAK,uBAAuB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,IAAI;AAC1G,WAAK,mBAAmB,OAAO;AAAA,IACjC;AAAA;AAAA,EACM,QAAQ,SAAS;AAAA;AACrB,YAAM,YAAY,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,aAAa;AACzF,WAAK,mBAAmB,CAAC,QAAQ,CAAC;AAAA,IACpC;AAAA;AAAA,EACM,iBAAiB;AAAA;AACrB,WAAK,mBAAmB;AAAA,IAC1B;AAAA;AAAA,EACM,mBAAmB;AAAA;AACvB,UAAI,KAAK,kBAAkB;AACzB,aAAK,mBAAmB,CAAC,EAAE,CAAC;AAAA,MAC9B;AAAA,IACF;AAAA;AAAA,EACM,eAAe;AAAA;AACnB,WAAK,mBAAmB;AAAA,IAC1B;AAAA;AAAA,EACA,iBAAiB,QAAQ,YAAY,OAAO;AAC1C,QAAI,UAAU,YAAY,QAAQ;AAChC,aAAO,CAAC,EAAE;AAAA,IACZ,WAAW,UAAU,YAAY,OAAO;AACtC,aAAO,CAAC,EAAE;AAAA,IACZ;AACA,WAAO,CAAC,EAAE;AAAA,EACZ;AAAA,EACA,uBAAuB,OAAO,iBAAiB,SAAS;AACtD,QAAI,SAAS,iBAAiB,SAAS;AACrC,aAAO,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IAC5B,WAAW,SAAS,iBAAiB,OAAO;AAC1C,aAAO,CAAC,IAAI,IAAI,EAAE;AAAA,IACpB;AACA,WAAO,CAAC,IAAI,IAAI,EAAE;AAAA,EACpB;AAAA,EACA,mBAAmB,SAAS;AAC1B,QAAI,UAAU,SAAS;AACrB,gBAAU,QAAQ,OAAO;AAAA,IAC3B,OAAO;AACL,YAAM,KAAK,YAAY,0CAA0C;AAAA,IACnE;AAAA,EACF;AACF;", "names": []}